name: om-e2e
volumes:
  tapioca_data:

services:

  #########################
  #      BACKEND          #
  #########################

  order-management-service:
    image: ${ORDER_MANAGEMENT_SERVICE_IMAGE:-order-management-http:latest}
    depends_on:
      - order-management-postgres
      - tapioca
      - broker
    ports:
      - 8086:8081
      - 8087:8080
    environment:
      - "SPRING_PROFILES_ACTIVE=local"
      - "SPRING_ZIPKIN_ENABLED=false"
      - "DB_URL=************************************************************"
      - "DB_USERNAME=${ORDER_MANAGEMENT_DB_USERNAME:-root}"
      - "DB_PASSWORD=${ORDER_MANAGEMENT_DB_PASSWORD:-123456}"
      - "AUTH_SERVICE_JWT_SECRET_KEY=some_long_secret_key_at_least_256_bits"
      - "TAPIOCA_BASE_URL=http://localhost:80/en/api"
      - "API_ALLOWED_ISSUERS=r2a543nd-6o21-47m7-a979-5b3302b8e9d6"
      - "PLANNED_ORIGIN_ISSUERS=r2a543nd-6o21-47m7-a979-5b3302b8e9d6"
      - "AUTH_SERVICE_BASE_URL=http://localhost:1919"
      - "AUTH_SERVICE_CLIENT_ID=cliend_id"
      - "AUTH_SERVICE_CLIENT_SECRET=client_secret"
      - "ICS_TICKETS_URL=http://localhost/api/v2/tickets/"
      - "ICS_TICKETS_USERNAME=ics_tickets_username"
      - "KAFKA_BROKER_HOST=broker"

  order-management-postgres:
    image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/order-management-service:e2e-db-2025-05-15
    ports:
      - 5434:5432
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${ORDER_MANAGEMENT_DB_USERNAME:-root} -d order_management" ]
      interval: 30s
      timeout: 30s
      retries: 3
    environment:
      LC_ALL: C.UTF-8
      POSTGRES_USER: "${ORDER_MANAGEMENT_DB_USERNAME:-root}"
      POSTGRES_PASSWORD: "${ORDER_MANAGEMENT_DB_PASSWORD:-123456}"
      POSTGRES_DB: order_management

  broker:
    image: bitnami/kafka:2.8.0
    hostname: broker
    container_name: broker
    depends_on:
      - zookeeper
    ports:
      - '29092:29092'
      - '9092:9092'
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_INTER_BROKER_LISTENER_NAME: LISTENER_DOCKER_INTERNAL
      ALLOW_PLAINTEXT_LISTENER: 'yes'
      KAFKA_CFG_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: LISTENER_DOCKER_INTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_CFG_ADVERTISED_LISTENERS: LISTENER_DOCKER_INTERNAL://broker:19092,PLAINTEXT://localhost:9092,EXTERNAL://localhost:29092
      KAFKA_CFG_LISTENERS: LISTENER_DOCKER_INTERNAL://:19092,PLAINTEXT://:9092,EXTERNAL://:29092
      KAFKA_HEAP_OPTS: "-Xmx2048m -Xms1024m -XX:-TieredCompilation -XX:+UseStringDeduplication -noverify"
    volumes:
      - ../docker/dev:/usr/src
    healthcheck:
      test:
        - CMD-SHELL
        - /usr/src/create-topics.sh "public.planning.facility.v1"
      interval: 30s
      timeout: 120s
      retries: 5
      start_period: 15s

  zookeeper:
    image: bitnami/zookeeper:3.7.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - '2181:2181'
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ALLOW_ANONYMOUS_LOGIN: 'yes'

  #########################
  #      FRONTENDS          #
  #########################

  order-management-fragment:
    image: "489198589229.dkr.ecr.eu-west-1.amazonaws.com/scm-order-management-fragment:${ORDER_MANAGEMENT_FRONTEND_IMAGE_TAG:-latest}"
    ports:
      - 3000:3000
    environment:
      PORT: 3000
      ENV: "production"
      NEXT_PUBLIC_CLIENT_ID: 1234
      JWT_KEY_SIGN: "secret"
      REDIRECT_DOMAIN: "http://localhost:3000"
      REWRITES_DOMAIN: "http://localhost:8080"
      API_GATEWAY: "http://localhost:3000/gw"
      NEXT_PUBLIC_STATSIG_API_KEY: "${NEXT_PUBLIC_STATSIG_API_KEY}"
      NODE_ENV: "production"
      NEXT_PUBLIC_API_MOCKING: "disabled"
    stop_signal: SIGTERM

  ordering-legacy-fragment:
    image: "489198589229.dkr.ecr.eu-west-1.amazonaws.com/scm-ordering-legacy-fragment:${ORDERING_LEGACY_FRONTEND_IMAGE_TAG:-latest}"
    ports:
      - 3001:3001
    environment:
      PORT: 3001
      ENV: "production"
      NEXT_PUBLIC_CLIENT_ID: 1234
      JWT_KEY_SIGN: "secret"
      REDIRECT_DOMAIN: "http://localhost:3001"
      REWRITES_DOMAIN: "http://localhost:8080"
      API_GATEWAY: "http://localhost:3001/gw"
      NEXT_PUBLIC_STATSIG_API_KEY: "${NEXT_PUBLIC_STATSIG_API_KEY}"
      NODE_ENV: "production"
      NEXT_PUBLIC_API_MOCKING: "disabled"
    stop_signal: SIGTERM


  #########################
  #      ROUTING          #
  #########################

  nginx:
    image: nginx:alpine
    depends_on:
      - tapioca
      - order-management-service
    volumes:
      - tapioca_data:/server/http:delegated
      - ./nginx/:/etc/nginx/conf.d/:delegated
    ports:
      - 8080:8080
      - 80:80
    restart: always
    stop_signal: SIGINT
    # temporary hack to health check order management service (no /bin/sh in distroless base image)
    healthcheck:
      test: "curl http://localhost:80/api/ping && curl --fail http://order-management-service:8081/actuator/health/readiness || exit 1"
      interval: 10s
      timeout: 5s
      retries: 40
      start_period: 30s


  #########################
  #      MOCKS          #
  #########################

  wiremock:
    image: wiremock/wiremock
    ports:
      - 9999:8080
    stop_signal: SIGINT
    command:
      - "--verbose"
    volumes:
      - ./mocks:/home/<USER>/mappings

  #########################
  #      TAPIOCA          #
  #########################

  tapioca:
    image: "489198589229.dkr.ecr.eu-west-1.amazonaws.com/tapioca:${TAPIOCA_IMAGE_TAG:-latest}"
    depends_on:
      - postgres
    volumes:
      - tapioca_data:/server/http:delegated
    ports:
      - 9090:9000
    env_file:
      - .env.tapioca

  postgres:
    image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/ordering-db:gha-staging-latest
    ports:
      - 5432:5432

  pushgateway:
    image: prom/pushgateway:v1.3.0
    ports:
      - 9091
