# Symfony configuration
APP__NAME="SCM Ordering Tool"
APP__COPYRIGHT="HelloFresh GmbH"
APP__PURCHASE_MAILING_LIST=<EMAIL>
APP__HOST=operations.hellofresh.local
SECRET=iAmADevelopmentEnvironment
# Operations -- used in email templates
OPS_HOST=https://staging-operations.hellofresh.com
# XDebug
XDEBUG_MODE="debug"
XDEBUG_SESSION=1
PHP_IDE_CONFIG="serverName=operations.hellofresh.local"
XDEBUG_CONFIG="client_host=${HOST_IP:-host.docker.internal}"
# Logging
LOG__LEVEL=debug
LOG__PATH=/tmp/stdout
# Metrics
PROMETHEUS__IN_MEMORY='false'
PROMETHEUS__PUSHGATEWAY_URL='pushgateway:9091'
# Database
DATABASE__NAME=scm_ordering
DATABASE__HOST=postgres
DATABASE__PORT=5432
DATABASE__USER=scm_dev
DATABASE__PASSWORD=scm_dev
# DDM
DDM_CRUD_PRODUCT=DE
DDM_CRUD_SUPPLIER=DE
# Culinary SKU Forecast Kafka Outbox
DB_OUTBOX_PREFIX=csku_forecast
DB_MIGRATIONS_TABLE="${DB_OUTBOX_PREFIX}_outbox_migrations_kafka"
DB_OUTBOX_TABLE="${DB_OUTBOX_PREFIX}_outbox_message_kafka"
DB_OUTBOX_TABLE_RAW="${DB_OUTBOX_PREFIX}_outbox_message_kafka_raw"
DB_OUTBOX_TABLE_RAW_ERR="${DB_OUTBOX_PREFIX}_outbox_message_kafka_raw_err"
# RabbitMQ
RABBIT__HOST=rabbitmq
RABBIT__PORT=5672
RABBIT__USERNAME=guest
RABBIT__PASSWORD=guest
RABBIT__VHOST=/
# ElasticSearch
ELASTIC__IP=elastic
# Api Gateway
API_GATEWAY__URL=http://ordering.hellofresh.local
API_GATEWAY__PREFIX=/api
# Translations
TRANSLATION__HOSTNAME=https://gw-staging.hellofresh.com/translations
# Mailer
MAILER__TRANSPORT=smtp
MAILER__AUTH_MODE=plain
MAILER__HOST=mailcatcher
MAILER__PORT=1025
MAILER__USER=""
MAILER__PASSWORD=""
# SKU Mapper
SKUMAPPER__HOSTNAME="https://sku-mapper.staging-k8s.hellofresh.io"
# Auth service
AUTH_SERVICE__CLIENT_ID=1234
AUTH_SERVICE__CLIENT_SECRET=aabbccdd
AUTH_SERVICE__KEY=secret
AUTH_SERVICE__ALLOWED_CLIENTS=1234
AUTH_SERVICE__AZURE_TENANT_ID=74590b7d-28d8-4cbc-aa47-c81b1e969ae8
# statsd (to be deprecated)
STATSD__HOST=deprecated-host
STATSD__PORT=8125
STATSD__NAMESPACE=ordering-tool
# Composer
COMPOSER_AUTH={"github-oauth":{"github.com":"${GITHUB_TOKEN}"}}
# Feature flags
ENABLE_SUMMARY_DELIVERIES_ORDER_ITEMS_LINKING="1"
AWS__S3_UPLOADS_REGION=""
AWS__S3_UPLOADS_BUCKET=""
#For obtaining access token for s2s communication (for ex. OT -> OM)
AUTH_SERVICE_BASE_URL=""
AUTH_SERVICE_CLIENT_ID=1234
AUTH_SERVICE_CLIENT_SECRET=aabbccdd
ORDER_MANAGEMENT_SERVICE_BASE_URL=""
