log_format logstash_json escape=json '{ "timestamp": "$time_iso8601", '
    '"host": "$host", '
    '"message": "$request_method $request_uri $status", '
    '"remote_addr": "$remote_addr", '
    '"body_bytes_sent": "$body_bytes_sent", '
    '"request_time": "$request_time", '
    '"status": "$status", '
    '"request": "$request", '
    '"request_method": "$request_method", '
    '"http_referrer": "$http_referer", '
    '"request_id": "$http_x_request_id", '
    '"trace_id": "$http_x_b3_traceid", '
    '"x-forwarded-for": "$http_x_forwarded_for", '
    '"http_user_agent": "$http_user_agent" }';
