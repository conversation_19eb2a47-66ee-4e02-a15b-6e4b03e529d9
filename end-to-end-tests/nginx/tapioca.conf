server {
    listen 80;
    root /server/http/web;
    server_name localhost

    index index.html index.htm index.php;

    charset utf-8;

    # http://nginx.org/en/docs/http/ngx_http_fastcgi_module.html#fastcgi_buffers
    fastcgi_buffers 16 8k;
    fastcgi_buffer_size 16k;
    fastcgi_read_timeout 600;
    fastcgi_send_timeout 600;

    client_max_body_size 50M;

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    location /metrics {
        # we cannot use rewrite directive, as fastcgi_params will use $request_uri, which is readonly
        proxy_set_header Host $host;
        proxy_pass http://pushgateway:9091/metrics;
    }

    location = / {
      return 302 /en/;
    }
    location = /en {
      return 302 /en/;
    }
    location /en/ {
      proxy_set_header Host $host;
      proxy_pass http://localhost/;
    }
    location /ordering/en/ {
      proxy_set_header Host $host;
      proxy_pass http://localhost/ordering/;
    }

    location / {
        # try to serve file directly, fallback to app.php
        try_files $uri /app.php$is_args$args;
    }

    location ~ ^/app\.php(/|$) {
        fastcgi_pass  tapioca:9000;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        # When you are using symlinks to link the document root to the
        # current version of your application, you should pass the real
        # application path instead of the path to the symlink to PHP
        # FPM.
        # Otherwise, PHP's OPcache may not properly detect changes to
        # your PHP files (see https://github.com/zendtech/ZendOptimizerPlus/issues/126
        # for more information).
        fastcgi_param  SCRIPT_FILENAME  $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        # Prevents URIs that include the front controller. This will 404:
        # http://domain.tld/app.php/some-path
        # Remove the internal directive to allow URIs like this
        internal;
    }

    error_log /var/log/nginx/api-skeleton_error.log;
    access_log /dev/stdout logstash_json;
}
