server {
    listen 8080;

    location ~ /order-management-service {
        proxy_pass         http://order-management-service:8080;
        rewrite            (/gw/scm/order-management-service)(.*) $2 break;
    }

    location ~* ^/gw/scm/dc-registry/dcs$ {
        proxy_pass         http://wiremock:8080;
    }

    location ~* ^/(ordering|assets)(/.*) {
        proxy_pass         http://localhost:80;
    }

    location ~* ^(/gw)(/.*$){
        proxy_pass         http://localhost:80;
        rewrite            (/gw/scm)(.*) /api$2 break;
    }

    location ~ ^/(ordering-tool)(/.*) {
        proxy_pass         http://ordering-legacy-fragment:3001;
    }

    location ~ ^/(order-management)(/.*) {
        proxy_pass         http://order-management-fragment:3000;
    }
}



