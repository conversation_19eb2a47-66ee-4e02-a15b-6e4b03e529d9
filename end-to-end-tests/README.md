# End to end testing framework

This module is end to end test framework for order management.

## Prerequisite

-   [install npm](https://formulae.brew.sh/formula/node)
-   [install cypress](https://docs.cypress.io/guides/getting-started/installing-cypress#What-you-ll-learn)
-   Run `cp .env.dist .env`
-   Set the missing secrets in the `.env` file.

## Run tests

### Spin up test environment

-   Login to ECR
    ```sh
    aws ecr get-login-password --region eu-west-1 --profile sso-hf-it-developer | docker login --username AWS --password-stdin https://************.dkr.ecr.eu-west-1.amazonaws.com
    ```
-   <PERSON> docker compose
    ```sh
    docker compose up --wait
    ```

### Run cypress tests - Interactive mode (dashboard)

```sh
npm test:open
```

### Run cypress tests - Headless (with command line)

```sh
npm test:ci
```

## To do

-   Find a way keep database schema/fixtures up to date
