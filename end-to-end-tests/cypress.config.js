require("dotenv").config();
const { defineConfig } = require("cypress");
const plugins = require("./cypress/plugins");

module.exports = defineConfig({
    projectId: "c7xa96",
    defaultCommandTimeout: 100000,
    viewportHeight: 900,
    viewportWidth: 1440,
    retries: {
        runMode: 2,
        openMode: 0,
    },
    e2e: {
        baseUrl: "http://localhost:8080/",
        setupNodeEvents(on) {
            return plugins(on);
        },
        specPattern: "cypress/e2e/**/*.feature",
    },
});
