{"request": {"method": "GET", "urlPathPattern": "^/gw/scm/dc-registry/dcs"}, "response": {"status": 200, "jsonBody": {"dcs": [{"code": "NJ", "name": "HelloFresh United States - NJ", "enabled": true, "id": "37876723-7d47-4a77-a6f0-fd5cd68222a4", "timezone": "America/New_York", "legacyCountryCode": "US", "deliveryAddress": {"regionCode": "US", "number": "60", "addressLines": ["Lister Avenue"], "administrativeArea": "NJ", "locality": "Newark", "postalCode": "07105", "organization": null}, "billingAddress": {"regionCode": "US", "number": "28", "addressLines": ["Liberty St, 10th Floor"], "administrativeArea": "New York", "locality": "New York", "postalCode": "10005", "organization": "HelloFresh"}, "thirdPartyManaged": false, "markets": [{"code": "us", "name": "United States"}], "wmsSystem": "HIGH_JUMP", "parentId": null, "global3pwDcId": null}], "currentPage": 0, "totalPages": 1, "totalItems": 1}, "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "*", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Headers": "Accept, Content-Type, Content-Encoding, Server, Transfer-Encoding", "X-Content-Type-Options": "nosniff"}}}