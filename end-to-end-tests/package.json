{"name": "end-to-end-tests", "description": "End to end tests for ordering management", "version": "1.0.0", "author": "", "license": "ISC", "scripts": {"test:ci": "cypress run", "test:open": "cypress open"}, "devDependencies": {"cypress": "13.17.0", "cypress-cucumber-preprocessor": "^4.3.1"}, "cypress-cucumber-preprocessor": {"step_definitions": "./cypress/e2e", "nonGlobalStepDefinitions": true}, "dependencies": {"dotenv": "^16.0.3", "jwt-simple": "^0.5.6"}}