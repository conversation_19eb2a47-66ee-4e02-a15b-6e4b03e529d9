const encode = require('jwt-simple').encode;

const loginWithRoles = (sessionId, roles, market = 'US', dc = 'NJ') => {
    cy.session(sessionId, () => {
        const payload = {
            id: 'ac0bdc04-0b28-4aad-bcc7-01a521263cc2',
            username: '<EMAIL>+internal',
            email: '<EMAIL>',
            country: 'us',
            blocked: false,
            roles: [...roles, 'operation_access'],
        };

        const accessToken = encode(payload, 'secret');

        window.localStorage.setItem('operation.azure.token', accessToken);
        cy.setCookie('hf_token', accessToken); // needed for Tapioca
        cy.setCookie('operation.azure_user_default_country', market);
        cy.setCookie('operation.azure_user_default_dc', dc);
        cy.setCookie('operation.azure_user_default_page', '/manager/user-settings');
    });
};

module.exports = {
    loginWithRoles,
};
