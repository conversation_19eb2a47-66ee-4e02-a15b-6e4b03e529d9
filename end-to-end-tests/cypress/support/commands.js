const setPlaygroundDefaults = () => {
    Cypress.SelectorPlayground.defaults({
        onElement: ($el) => {
            const customId = $el.attr("data-testid");

            if (customId) {
                return `[data-testid=${customId}]`;
            }
        },
    });
    Cypress.SelectorPlayground.defaults({
        selectorPriority: ["attributes", "id", "class"],
    });
};

Cypress.Commands.add("removeThirdPartyNetworkLogs", () => {
    cy.intercept("https://stats.g.doubleclick.net/**", { log: false });
    cy.intercept("https://www.google-analytics.com/**", { log: false });
    cy.intercept("https://api.sprig.com/**", { log: false });

    cy.on('uncaught:exception', (err, runnable, promise) => {
        // when the exception originated from an unhandled promise
        // rejection, the promise is provided as a third argument
        // you can turn off failing the test in this case
        if (promise) {
            return false;
        }

        // we don't want cypress to fail on external scripts errors
        if (err.message?.includes('Script error')) {
            return false;
        }

        // we still want to ensure there are no other unexpected
        // errors, so we let them fail the test
    });
});

Cypress.Commands.add("getByTestId", (selector, ...args) => cy.get(`[data-testid="${selector}"]`, ...args));
