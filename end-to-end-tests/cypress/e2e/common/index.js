import { Given } from "cypress-cucumber-preprocessor/steps";
import { loginWithRoles } from '../../utils/index.js';

const URLS = {
    "Manual Order Creator": "/order-management/orders/create",
    "EPO Creator": "/ordering-tool/epo-creator",
};

Given("I navigate to {string} page", (page) => {
    cy.visit(URLS[page]);
});

When("I click {string} button", (value) => {
    cy.get("button").contains(value).click();
});

Given('I am logged in as a procurement US user', () => {
    loginWithRoles('Purchasing US User', ['purchasing.us.all.manager']);
});
