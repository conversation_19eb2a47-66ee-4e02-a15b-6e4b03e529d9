import { Given, When, Then, And } from "cypress-cucumber-preprocessor/steps";

// Background steps
Given("interceptors are set", () => {
    // Set up API intercepts for monitoring
    cy.intercept("GET", "**/scm/order-management-service/orders**").as("getOrders");
    cy.intercept("GET", "**/scm/order-management-service/suppliers**").as("getSuppliers");
    cy.intercept("GET", "**/scm/order-management-service/distribution-centers**").as("getDCs");
});

Given("I am logged in with Purchasing roles", () => {
    cy.visit("/order-management/orders");
});

Given("I have the correct week value in the cookies", () => {
    cy.setCookie("hf_week", "2024-W01");
});

Given("I navigate to {string} page", (page) => {
    const routes = {
        Orders: "/order-management/orders",
    };
    cy.visit(routes[page]);
});

Given("there are purchase orders with different statuses, suppliers, and DCs", () => {
    cy.wait("@getOrders");
});

// Filter by status scenarios
When("I select {string} from the status tabs", (status) => {
    cy.get(`[aria-label="${status}"]`).click();
});

When("I select {string} and {string} from the status filter dropdown", (status1, status2) => {
    cy.getByTestId("status-filter-dropdown").click();
    cy.contains(status1).click();
    cy.contains(status2).click();
    cy.get("body").click(0, 0);
});

Then("I should see only orders with {string} status", (status) => {
    cy.wait("@getOrders");
    cy.getByTestId("order-item-row").each(($row) => {
        cy.wrap($row).should("contain", status);
    });
});

And("the {string} order count should reflect {string} results", (status, count) => {
    cy.contains(`${status} orders (${count})`);
});

// Filter by supplier scenarios
Given("there are orders from multiple suppliers", () => {
    cy.wait("@getOrders");
});

When("I select {string} from the supplier filter dropdown", (supplier) => {
    cy.getByTestId("supplier-search").type(supplier).click();
    cy.getByTestId("search-result").contains(supplier).click();
});

Then("I should see only orders from {string}", (supplier) => {
    cy.wait("@getOrders");
    cy.getByTestId("order-item-row").each(($row) => {
        cy.wrap($row).should("contain", supplier);
    });
});

// Filter by DC scenarios
Given("there are orders for an initial distribution center", () => {
    cy.wait("@getOrders");
});

When("I select {string} from the DC filter dropdown", (dc) => {
    cy.contains("label", "Distribution Center").parent().find("input").click();
    cy.contains(dc).click();
});

Then("I should see only orders for {string}", (dc) => {
    cy.wait("@getOrders");
    cy.getByTestId("order-item-row").each(($row) => {
        cy.wrap($row).should("contain", dc);
    });
});

// Filter by production week scenarios
Given("there are orders for different production weeks", () => {
    cy.wait("@getOrders");
});

When("I select {string} from the production week filter", (week) => {
    cy.getByTestId("input-week").click();
    cy.contains(week).click();
    cy.get('[aria-label="Select"]').click();
});

Then("I should see the week group for {string}", (week) => {
    cy.wait("@getOrders");
    cy.getByTestId("week-section-accordion").each(($group) => {
        cy.wrap($group).should("contain", week);
    });
});

// Multiple filters scenario
Then("I should see only orders that match all selected criteria", () => {
    cy.wait("@getOrders");
    cy.getByTestId("order-item-row").should("exist");
    cy.contains("TEST-003").should("exist");
    cy.contains("DC-GL-001").should("exist");
    cy.getByTestId("order-item-row").each(($order) => {
        cy.wrap($order).should("contain", "Supplier F");
    });
});

// Clear filters scenarios

When("I remove the Supplier filter", () => {
    cy.getByTestId("supplier-search").find("[aria-label='Clear']").click();
});

When("I clear all filters", () => {
    cy.contains("Clear all filters").click();
});

Then("all filters should be cleared", () => {
    cy.getByTestId("supplier-search").should("not.have.class", "filter-active");
});

// Search scenarios
When("I enter a valid order number in the search box", () => {
    cy.getByTestId("po-number-search").type("ORD-12345");
});

Then("I should see only the order matching that number", () => {
    cy.wait("@getOrders");
    cy.getByTestId("order-item-row").should("have.length", 1);
    cy.getByTestId("order-item-row").should("contain", "ORD-12345");
});

// No results scenario
When("I apply filters that match no existing orders", () => {
    cy.getByTestId("supplier-search").type("Supplier E");
    cy.getByTestId("search-result").contains("Supplier E").click();
});

Then("I should see an empty state message", () => {
    cy.getByTestId("week-section-accordion").first().click();
    cy.contains("No orders found");
});

And("I should see a {string} suggestion", (suggestionText) => {
    cy.contains(suggestionText).should("be.visible");
});
