Feature: Purchase Order List Filters
    As a procurement user,
    I should be able to filter and search through Purchase Orders using various filter parameters

    Background:
        Given I am logged in as a procurement US user
        And I have the correct week value in the cookies
        And interceptors are set
        And I navigate to 'Orders' page
        And there are purchase orders with different statuses, suppliers, and DCs

    Scenario: Search by order number
        When I enter a valid order number in the search box
        Then I should see only the order matching that number
        And the "All" order count should reflect "1" results

    Scenario: Filter by order status
        When I select "Initial" from the status tabs
        Then I should see only orders with "Initial" status
        And the "Initial" order count should reflect "48" results

    Scenario: Filter by supplier
        Given there are orders from multiple suppliers
        When I select "Supplier D" from the supplier filter dropdown
        Then I should see only orders from "Supplier D"
        And the "All" order count should reflect "7" results

    Scenario: Filter by distribution center
        Given there are orders for an initial distribution center
        When I select "GL" from the DC filter dropdown
        Then I should see only orders for "GreenLeaf"
        And the "All" order count should reflect "4" results

    Scenario: Filter by production week
        Given there are orders for different production weeks
        When I select "Week 3" from the production week filter
        Then I should see the week group for "2024-W03"
        And the "All" order count should reflect "32" results

    Scenario: Apply multiple filters simultaneously
        When I select "Initial" from the status tabs
        And I select "Supplier F" from the supplier filter dropdown
        And I select "GL" from the DC filter dropdown
        Then I should see only orders that match all selected criteria
        And the "Initial" order count should reflect "2" results

    Scenario: Clear individual filter
        When I select "Supplier D" from the supplier filter dropdown
        Then I should see only orders from "Supplier D"
        And the "All" order count should reflect "7" results
        When I remove the Supplier filter
        And the "All" order count should reflect "63" results

    Scenario: Clear all filters
        Given I select "Supplier D" from the supplier filter dropdown
        And I enter a valid order number in the search box
        When I clear all filters
        And the "All" order count should reflect "63" results

    Scenario: No results found
        When I apply filters that match no existing orders
        Then I should see an empty state message
        And the "All" order count should reflect "0" results
        And I should see a "Clear all filters" suggestion
