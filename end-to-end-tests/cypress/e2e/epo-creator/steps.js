import { Given, And, Then } from "cypress-cucumber-preprocessor/steps";

Given("I see the Create EPO form", () => {
    cy.getByTestId("epo-creator").should("be.visible");
});

Then("I fill in the base info with supplier {string}", (value) => {
    cy.getByTestId("supplier").click();
    cy.contains(value).click();

    cy.getByTestId("shippingMethod").click();
    cy.contains("Crossdock").click();

    cy.getByTestId("deliveryDate").click().type("2030/08/12");
    cy.getByTestId("deliveryStartTime").click().type("110000");
    cy.getByTestId("deliveryEndTime").click().type("120000");

    cy.getByTestId("emergencyReason").click();
    cy.contains("Add-Ons").click();
});

And("I add a product", () => {
    cy.getByTestId("sku").find("input").type("Nuts");
    cy.contains("Cashew Nuts - 1 Ounce (oz) - SPI-10-10071-1").click();

    cy.getByTestId("orderUnit").click();
    cy.contains("Unit").click();

    cy.getByTestId("orderSize").type(5);

    cy.getByTestId("buffer").type(5);

    cy.getByTestId("agreedPrice").type(5);

    cy.getByTestId("addToOrder").click();
});

And("I add a product measured in weight", () => {
    cy.getByTestId("sku").find("input").type("Nuts");
    cy.contains("Cashew Nuts - 1 Ounce (oz) - SPI-10-10071-1").click();

    cy.getByTestId("orderUnit").click();
    cy.contains("Case (weight)").click();

    cy.getByTestId("orderSize").type(5);

    cy.getByTestId("packingSize").type(3.5);

    cy.getByTestId("packingUnit").click();
    cy.contains("lb").click();

    cy.getByTestId("buffer").type(5);

    cy.getByTestId("agreedPrice").type(5);

    cy.getByTestId("addToOrder").click();
});

Then("I verify the computed total", () => {
    cy.getByTestId("epoTotal").should("have.text", "$25");
});

Then("I should see the confirmation popup", () => {
    cy.contains("Order was created successfully");
});

And("I should get redirected", () => {
    cy.wait(2000).location("href").should("contain", `/ordering-tool/emergency-orders/`);
});

And("I see that the order for {string} was created", (value) => {
    cy.wait(5000); // wait Iframe to be ready
    const iframe = cy.get("#tapioca_frame").its("0.contentDocument.body").should("be.visible").then(cy.wrap);
    iframe.contains(value);
});
