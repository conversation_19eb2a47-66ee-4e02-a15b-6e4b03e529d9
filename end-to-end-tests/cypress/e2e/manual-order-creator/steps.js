import { And, Then, When } from "cypress-cucumber-preprocessor/steps";

Then("I can see the form", () => {
    cy.contains("Create Manual Order");
    cy.contains("Step 1 | Basic Information");
});

When("I fill up the basic information", () => {
    cy.getByTestId("autocomplete-distributionCenter").click();
    cy.contains("HelloFresh United States - NJ").click();
    cy.contains("60 Lister Avenue");

    cy.getByTestId("autocomplete-supplier").type("E2E").click();
    cy.contains("E2E Supplier B").click();

    cy.getByTestId("dropdown-shippingMethod").click();
    cy.contains("Crossdock").click();

    cy.getByTestId("autocomplete-reason").find("button").click();
    cy.contains("Repackaging").click();
});

Then("I can go to the next step", () => {
    cy.contains("Next").click();
    cy.contains("Step 2 | Add SKUs");
});

When("I add some ingredients of different types", () => {
    cy.contains("button", "Add SKUs").click();
    cy.get(".MuiSwitch-input").click();
    cy.getByTestId("autocomplete-sku").type("E2E");
    cy.contains("E2E Chicken, Sausage").click();

    cy.getByTestId("input-price").clear().type(10);
    cy.getByTestId("input-orderSize").clear().type(20);
    cy.getByTestId("input-buffer").clear().type(50);
    cy.contains("300.00 USD");
    cy.getByTestId("button-add-sku").click();

    cy.contains("button", "Add SKUs").click();
    cy.getByTestId("autocomplete-sku").type("E2E");
    cy.contains("Freebies Mixed").click();
    cy.get("button").contains("Cases").click();

    cy.getByTestId("input-price").clear().type(20);
    cy.getByTestId("input-orderSize").clear().type(30);
    cy.getByTestId("input-caseSize").clear().type(35);
    cy.getByTestId("input-buffer").clear().type(80);
    cy.contains("1,080.00 USD");
    cy.getByTestId("button-add-sku").click();
});

Then("I can go to the final step", () => {
    cy.contains("Next").click();
    cy.contains("Step 3 | Review order");
});

Then("I see that all fields are correct", () => {
    cy.contains("E2E Repackaging");
    cy.contains("Crossdock");
    cy.contains("E2E Supplier B");
    cy.contains("193 Consequuntur in lore");
    cy.contains("E2E Chicken, Sausage");
    cy.contains("E2E SE Freebies Mixed");
    cy.contains("1,380.00 USD");
});

When("I click save", () => {
    cy.contains("button", "Create").click();
});

Then("I see the succesfull message and get redirected", () => {
    cy.contains("Order successfully created");
    cy.url().should("include", "order-management/orders");
});

When("I click on add SKU", () => {
    cy.contains("button", "Add SKUs").click();
});

Then("I can fill all the fields and select a unit of measure", () => {
    cy.get(".MuiSwitch-input").click();
    cy.getByTestId("autocomplete-sku").type("E2E");
    cy.contains("Chicken, Sausage").click();
    cy.getByTestId("dropdown-uom").click();
    cy.contains("GAL").click();
    cy.getByTestId("input-price").clear().type(20);
    cy.getByTestId("input-orderSize").clear().type(30);
    cy.getByTestId("input-caseSize").clear().type(35);
    cy.getByTestId("input-buffer").clear().type(50);
    cy.contains("900.00 USD");
});

Then("I can add it correctly to the list", () => {
    cy.getByTestId("button-add-sku").click();
    cy.contains("Chicken, Sausage");
    cy.contains("45 Case(s)");
    cy.contains("35 GAL");
    cy.contains("1575 GAL");
    cy.contains("900.00 USD");
});

When("I add an SKU", () => {
    cy.contains("button", "Add SKUs").click();
    cy.get(".MuiSwitch-input").click();
    cy.getByTestId("autocomplete-sku").type("E2E").click();
    cy.contains("Chicken, Sausage").first().click();
    cy.getByTestId("input-price").clear().type(10);
    cy.getByTestId("input-orderSize").clear().type(20);
    cy.getByTestId("input-buffer").clear().type(50);
    cy.contains("300.00 USD");
    cy.getByTestId("button-add-sku").click();
});

Then("I can click on the edit button", () => {
    cy.getByTestId("EditIcon").click();
});

And("I can update its information", () => {
    cy.getByTestId("input-price").clear().type(30);
    cy.getByTestId("input-orderSize").clear().type(15);
    cy.getByTestId("input-buffer").clear().type(100);
    cy.contains("900.00 USD");
    cy.getByTestId("button-add-sku").click();
});

And("I can see the changes reflected correctly", () => {
    cy.contains("E2E Chicken, Sausage");
    cy.contains("30 unit(s)");
    cy.contains("900.00 USD");
});

And("I click on the delete button", () => {
    cy.getByTestId("DeleteIcon").click();
});

Then("the SKU is no longer present", () => {
    cy.contains("No SKUs yet");
});
