import { Given, Then } from "cypress-cucumber-preprocessor/steps";

Given("I am on the Manual Order Edit Page", () => {
    cy.visit("/order-management/orders/edit/9918NJ021004");
});

Then("I should see the order details correctly loaded", () => {
    cy.contains("Basic Information");
    cy.contains("2023-W18");
    cy.contains("E2E Ordering Error");
    cy.contains("Vendor delivered");
    cy.getByTestId("datepicker-deliveryDate").find("input").should("have.value", "04.29.2023");
    cy.getByTestId("timepicker-startTime").find("input").should("have.value", "10:00");
    cy.getByTestId("timepicker-endTime").find("input").should("have.value", "11:00");
    cy.contains("Delivery date is in the past");
    cy.contains("E2E delivery date reason 2");
    cy.contains("E2E Supplier A");
    cy.contains("193 Consequuntur in lore");
    cy.contains("60 Lister Avenue");
});

Then("I can change the basic information data", () => {
    cy.getByTestId("dropdown-reason").click();
    cy.contains("E2E Repackaging").click();
    cy.getByTestId("dropdown-shippingMethod").click();
    cy.contains("Crossdock").click();
    cy.getByTestId("datepicker-deliveryDate").find("input").type("30.07.2023");
    cy.getByTestId("timepicker-startTime").find("input").type("10:00");
    cy.getByTestId("timepicker-endTime").find("input").type("08:00");
    cy.getByTestId("dropdown-deliveryDateChangeReason").click();
    cy.contains("E2E delivery date reason 1").click();
    cy.contains("+1 day");
    cy.getByTestId("input-comment").type("This is a test comment");
});

When("I see the orders lines correctly loaded", () => {
    cy.contains("DO NOT USE - X3B- Bun, Brioche (1pc)");
    cy.contains("38 Case(s)");
    cy.contains("190.00 USD");
});

Then("I can add a new SKU", () => {
    cy.contains("button", "Add SKUs").click();
    cy.get('.MuiSwitch-input').click();
    cy.getByTestId("autocomplete-sku").click().type("E2E");
    cy.contains("PTN-10-10101-7").click();

    cy.getByTestId("input-price").clear().type(10);
    cy.getByTestId("input-orderSize").clear().type(20);
    cy.getByTestId("input-buffer").clear().type(50);
    cy.contains("300.00 USD");
    cy.getByTestId("button-add-sku").click();
});

Then("I can edit an existing SKU", () => {
    cy.getByTestId("edit-button").first().click();
    cy.getByTestId("input-price").clear().type(30);
    cy.getByTestId("input-orderSize").clear().type(15);
    cy.getByTestId("input-buffer").clear().type(100);
    cy.contains("900.00 USD");
    cy.getByTestId("button-add-sku").click();
});

Then("I can remove one of the SKUs", () => {
    cy.getByTestId("delete-button").first().click();
});

Then("the total quantity is calculated correctly", () => {
    cy.contains("300.00 USD");
});

Then("I see the change order reason dropdown and choose one of them", () => {
    cy.contains("Order Items change reason");
    cy.getByTestId("dropdown-orderItemsChangeReason").click();
    cy.contains("order change reason 1").click();
});

When("I click on the save button", () => {
    cy.contains("button", "Save").click();
});

Then("I can see the success modal", () => {
    cy.contains("Your changes are saved.");
});
