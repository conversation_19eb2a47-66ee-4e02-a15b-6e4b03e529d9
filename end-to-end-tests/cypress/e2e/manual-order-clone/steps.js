import { When, Given } from "cypress-cucumber-preprocessor/steps";

Given("I navigate to 'Manual Order Creator' page with an Order Number", () => {
    cy.visit("/order-management/orders/create/2023-W30/9918NJ021005");
});

Then("the basic information correctly loaded", () => {
    cy.getByTestId("autocomplete-distributionCenter").find("input").should("have.value", "NJ");
    cy.contains("60 Lister Avenue");
    cy.getByTestId("autocomplete-supplier").find("input").should("have.value", "E2E Supplier A");
    cy.contains("Vendor delivered");

});

And('the PO reason is empty', () => {
    cy.getByTestId("autocomplete-reason").find("input").should("be.empty");
})

When("I change basic information data", () => {
    cy.getByTestId("autocomplete-distributionCenter").click();
    cy.contains("EveryPlate - Arizona").click();
    cy.contains("1850 S 71st Avenue");

    cy.getByTestId("autocomplete-supplier").type("E2E");
    cy.contains("E2E Supplier B").click();

    cy.getByTestId("dropdown-shippingMethod").click();
    cy.contains("Crossdock").click();

    cy.getByTestId("autocomplete-reason").click();
    cy.contains("E2E Repackaging").click();
});

Then("I can go to the next step", () => {
    cy.contains("Next").click();
    cy.contains("Step 2 | Add SKUs");
});

Then("I see the orders correctly loaded", () => {
    cy.contains("X3B- Bun, Brioche (1pc)");
    cy.contains("38 Case(s)");
    cy.contains("190.00 USD");
});

Then("I can add a new SKU", () => {
    cy.contains("button", "Add SKUs").click();
    cy.get('.MuiSwitch-input').click();
    cy.getByTestId("autocomplete-sku").type("E2E");
    cy.contains("PTN-10-10101-7").click();

    cy.getByTestId("input-price").clear().type(10);
    cy.getByTestId("input-orderSize").clear().type(20);
    cy.getByTestId("input-buffer").clear().type(50);
    cy.contains("300.00 USD");
    cy.getByTestId("button-add-sku").click();
});

Then("I can edit an existing SKU", () => {
    cy.getByTestId("EditIcon").first().click();
    cy.getByTestId("input-price").clear().type(30);
    cy.getByTestId("input-orderSize").clear().type(15);
    cy.getByTestId("input-buffer").clear().type(100);
    cy.contains("900.00 USD");
    cy.getByTestId("button-add-sku").click();
});

Then("I can remove one of the SKUs", () => {
    cy.getByTestId("DeleteIcon").first().click();
});

Then("I can go to the final step", () => {
    cy.contains("Next").click();
    cy.contains("Step 3 | Review order");
});

Then("I see all the changes in the review reflected", () => {
    cy.contains("2023-W18");
    cy.contains("E2E Repackaging");
    cy.contains("Crossdock");
    cy.contains("E2E Supplier B");
    cy.contains("193 Consequuntur in lore");
    cy.contains("IG11 7PS London");
    cy.contains("1850 S 71st Avenue");
    cy.contains("85043 Phoenix");
    cy.contains("E2E Chicken, Sausage");
    cy.contains("300.00 USD");
});

Then("I can create the order correctly", () => {
    cy.contains("button", "Create").click();
    cy.contains("Order successfully created");
    cy.url().should("include", "order-management/orders");
});
