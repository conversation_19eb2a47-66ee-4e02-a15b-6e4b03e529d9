name: end-to-end
description: Run end to end tests

inputs:
  orderManagementFrontendTag:
    description: "OM FE Fragment image tag"
    default: "latest"
  orderManagementBackendTag:
    description: "Order Management Service BE image tag"
    default: "order-management-http-latest"
  orderingLegacyFrontendTag:
    description: "OT FE Fragment image tag"
    default: "latest"
  tapiocaImageTag:
    description: "Tapioca image tag"
    default: "latest"

outputs:
  testResult:
    description: "Test result"
    value: ${{ steps.cypress.outcome }}
  resultUrls:
    description: "Cypress dashboard url"
    value: ${{ steps.cypress.outputs.resultsUrl }}

runs:
  using: "composite"
  steps:
    - name: 🚪 Checkout source code
      uses: actions/checkout@v3

    - name: Build order-management local image
      if: ${{ inputs.orderManagementBackendTag == 'build' }}
      shell: bash
      run: gradle order-management:jibDockerBuild

    - name: Resolve order-management-service image
      id: order-management-be-image
      shell: bash
      env:
        OMS_TAG: ${{ inputs.orderManagementBackendTag }}
      run: |
        if [ $OMS_TAG == 'build' ]; then
          IMAGE_TAG="order-management-http:latest";
        else
          IMAGE_TAG="489198589229.dkr.ecr.eu-west-1.amazonaws.com/order-management-service:$OMS_TAG";
        fi
        echo "image=$IMAGE_TAG" >> $GITHUB_OUTPUT;

    - name: Spin up database
      shell: bash
      run: docker compose -f ./end-to-end-tests/docker-compose.yaml up -d order-management-postgres --wait

    - name: Migrate Order-Management DB
      shell: bash
      run: gradle migrateEndtoEndDb
      env:
        DB_URL_LOCAL: "*************************************************"
        DB_USERNAME_LOCAL: "root"
        DB_PASSWORD_LOCAL: "123456"

    - name: Spin up rest of the environment
      shell: bash
      run: docker compose -f ./end-to-end-tests/docker-compose.yaml up --wait
      env:
        ORDER_MANAGEMENT_FRONTEND_IMAGE_TAG: ${{ inputs.orderManagementFrontendTag }}
        ORDER_MANAGEMENT_SERVICE_IMAGE: ${{ steps.order-management-be-image.outputs.image }}
        ORDERING_LEGACY_FRONTEND_IMAGE_TAG: ${{ inputs.orderingLegacyFrontendTag }}
        TAPIOCA_IMAGE_TAG: ${{ inputs.tapiocaImageTag }}

    - name: Install dependencies
      run: npm install
      shell: bash
      working-directory: end-to-end-tests

    - name: Check docker containers
      shell: bash
      run: docker ps

    - name: Cypress run
      id: cypress
      uses: cypress-io/github-action@v6
      with:
        working-directory: end-to-end-tests
        group: "e2e-scm-order-management"
        record: true
      env:
        COMMIT_INFO_MESSAGE: Tests for ${{ github.event.number || github.workflow }} "${{ github.event.pull_request.title || github.ref }}"
        COMMIT_INFO_SHA: ${{ github.event.pull_request.head.sha || github.sha }}
        ENV: e2e
        NODE_ENV: e2e
