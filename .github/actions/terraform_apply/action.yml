name: "Prepare modified modules"
description: "Collects modified modules into collection"
runs:
  using: "composite"
  steps:
    - name: 🚪 Checkout source code
      uses: actions/checkout@v3

    - name: 🤫 Import secrets
      id: vault-secrets
      uses: hellofresh/jetstream-ci-scripts/actions/vault@master
      with:
        export-token: true
        shared-secrets: |
          common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN;

    - name: 🏎 Staging terraform apply
      id: terraform-apply-staging
      uses: hellofresh/jetstream-ci-scripts/actions/terraform@master
      with:
        vault_token: ${{ env.VAULT_TOKEN }}
        vault_address: ${{ steps.vault-secrets.outputs.vault-address }}
        github_token: ${{ env.GITHUB_TOKEN }}
        directory: ${{ github.workspace }}/terraform/staging
        action: apply -no-color -var-file="env.tfvars" -auto-approve

    - name: 🚀 Live terraform apply
      id: terraform-apply-live
      uses: hellofresh/jetstream-ci-scripts/actions/terraform@master
      with:
        vault_token: ${{ env.VAULT_TOKEN }}
        vault_address: ${{ steps.vault-secrets.outputs.vault-address }}
        github_token: ${{ env.GITHUB_TOKEN }}
        directory: ${{ github.workspace }}/terraform/live
        action: apply -no-color -var-file="env.tfvars" -auto-approve
