name: 'Deploy'
description: 'Deploy an artifact version to a specific environment'
inputs:
  module:
    description: "Module name"
    required: true
  helm_chart_name:
    description: "The name of the generated helm chart directory"
    required: true
  version:
    description: "Version to be deployed"
    required: true
  environment:
    description: "Environment to be deployed, possible values are staging|live"
    required: true

runs:
  using: "composite"
  steps:
    - name: 🤫 Import Secrets
      id: vault-secrets
      uses: ./.github/actions/vault
      with:
        secrets: |
          common/key-value/data/secrets SLACK_URL | SLACK_URL;
        shared-secrets: |
          common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN;

    - name: ☸ Setup k8s
      uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
      with:
        environment: ${{ inputs.environment }}

    - name: 🚢☸ Deploy helm chart
      uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
      env:
        GITHUB_TOKEN : ${{ env.GITHUB_TOKEN }}
      with:
        release-name: ${{ inputs.helm_chart_name }}
        chart-name: ${{ inputs.helm_chart_name }}
        version: ${{ inputs.version }}
        values-path: '${{ inputs.module }}/src/main/helm/values-${{ inputs.environment }}.yaml'
        namespace: 'scm'
        set-string: 'tag="${{ inputs.module }}-${{ inputs.version }}"'

    - name: ☸🔎 Ensure k8s rollout
      uses: hellofresh/jetstream-ci-scripts/actions/ensure-k8s-rollout@master
      with:
        deployment-selector: "release=${{ inputs.helm_chart_name }}"
        deployment-namespace: scm

    - name: 📣 Slack notification about job failure
      if: ${{ failure() }}
      uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
      with:
        slack-url: ${{ env.SLACK_URL }}
        channel: "#squad-purchase-order-lifecycle-alerts-live"
        icon-emoji: ":deploy:"
        color: "danger"
        pretext: "${{ inputs.helm_chart_name }} failed to deploy to ${{ inputs.environment }}"
        text: "${{ inputs.helm_chart_name }} failed to deploy version ${{ inputs.version }} to ${{ inputs.environment }}"
