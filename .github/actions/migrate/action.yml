name: "Run flyway migrations"
description: "Runs flyway migrations on specified environment"
inputs:
  env:
    description: "Environment (lowercase) to run the migrations on"
    required: true

runs:
  using: "composite"
  steps:
    - name: 🚪 Checkout source code
      uses: actions/checkout@v4

    - name: 🤫 Import Secrets
      id: vault-secrets
      uses: ./.github/actions/vault
      with:
        secrets: |
          common/key-value/data/secrets SLACK_URL;
          ${{ inputs.env }}/key-value/data/secrets DB_URL;
          ${{ inputs.env }}/key-value/data/secrets DB_USERNAME;
          ${{ inputs.env }}/key-value/data/secrets DB_PASSWORD;
        shared-secrets: |
          common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
          common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;

    - name: 💾 Migrate ${{ inputs.env }} DB
      shell: bash
      run: gradle migrate${{ inputs.env }}Db
      env:
        DB_URL: ${{ env.DB_URL }}
        DB_USERNAME: ${{ env.DB_USERNAME }}
        DB_PASSWORD: ${{ env.DB_PASSWORD }}

    - name: 📣 Slack notification about job failure
      if: failure()
      uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
      with:
        slack-url: ${{ env.SLACK_URL }}
        channel: "#squad-purchase-order-lifecycle-alerts-live"
        icon-emoji: ":blue-database:"
        color: "danger"
        pretext: "Failed to run migration"
        text: "Failed to run migration on ${{ inputs.env }} environment"
        buttons: |
          https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|Build Log
