name: "Release a module"
description: "Create a release for a module"
inputs:
  module:
    description: "Module to be released"
    required: true
outputs:
  version:
    description: "The version of the released module"
    value: ${{ steps.version.outputs.new_version }}

runs:
  using: "composite"
  steps:
    - name: 🚪 Checkout source code
      uses: actions/checkout@v3

    - name: 🤫 Import Secrets
      id: vault-secrets
      uses: ./.github/actions/vault
      with:
        secrets: |
          common/key-value/data/secrets SLACK_URL | SLACK_URL;
        shared-secrets: |
          common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN;
          common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
          common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;

    - name: 🪪 Generate Version
      id: version
      uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master

    - name: ️ Replace calver
      shell: bash
      run: |
        sed -i "s/@calver@/${{ steps.version.outputs.new_version }}/g" ${{ inputs.module }}/src/main/helm/*.yaml

    - name: 🏗 Build and push docker image to ECR
      shell: bash
      run: |
        gradle ${{ inputs.module }}:jib --image=489198589229.dkr.ecr.eu-west-1.amazonaws.com/order-management-service \
          -Djib.to.tags=${{ inputs.module }}-${{ steps.version.outputs.new_version }},${{ inputs.module }}-latest

    - name: 🪪 Save Version
      shell: bash
      env:
        MODULE: ${{ inputs.module }}
      run: |
        version=${{ steps.version.outputs.new_version }}
        echo "new_version=$version" >> $GITHUB_OUTPUT
        echo $version > ${MODULE}-version.txt

    - name: 📥 Upload version artifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{ inputs.module }}-version
        path: ./${{ inputs.module }}-version.txt

    - name: 📥 Upload chart
      uses: hellofresh/jetstream-ci-scripts/actions/helm3-build-and-push@master
      env:
        CHART_NAME: oms-${{ inputs.module }}
        CHART_PATH: ${{ inputs.module }}/src/main/helm
        VERSION: ${{ steps.version.outputs.new_version }}
        ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
        ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
        GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}

    - name: 📣 Slack notification about job failure
      if: failure()
      uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
      with:
        slack-url: ${{ env.SLACK_URL }}
        channel: "#squad-purchase-order-lifecycle-alerts-live"
        icon-emoji: ":hammer_and_wrench:"
        color: "danger"
        pretext: "Failed to build ${{ inputs.module }}"
        text: "Failed to build version ${{ steps.version.outputs.new_version }} of ${{ inputs.module }}"
