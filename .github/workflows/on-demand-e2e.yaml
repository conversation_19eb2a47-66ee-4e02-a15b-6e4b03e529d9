---
name: On Demand E2E

concurrency:
    group: E2E-${{ github.workflow }}-${{ github.head_ref }}
    cancel-in-progress: true

on:
    workflow_dispatch:
        inputs:
          orderManagementFrontendTag:
              type: string
              description: "OM FE Fragment image tag"
              default: "latest"
          orderManagementBackendTag:
              type: string
              description: "Order Management Service BE image tag"
              default: "order-management-http-latest"
          orderingLegacyFrontendTag:
              type: string
              description: "OT FE Fragment image tag"
              default: "latest"
          tapiocaImageTag:
              description: "Tapioca image tag"
              type: string
              default: "latest"
          repository:
              description: "Repository name"
              type: string
              default: "hellofresh/order-management-service"
          prNumber:
              description: "Pull request number"
              type: number
              default: -1
          postMessageReport:
              description: "Post message report"
              type: boolean
              default: false


permissions:
    id-token: write
    contents: read

jobs:
    run-end-to-end:
      runs-on: [self-hosted, heavy]
      steps:
        - name: 🚪 Checkout source code
          uses: actions/checkout@v3

        - name: 🖨️ Print Summary
          if: ${{ github.event.inputs.postMessageReport != 'false' }}
          run: |
            echo "### Running E2E tests for: ${{ github.event.inputs.repository }} ${{ github.event.inputs.prNumber }}" >> $GITHUB_STEP_SUMMARY
            echo "PR Number: ${{ github.event.inputs.prNumber }}" >> $GITHUB_STEP_SUMMARY
            echo "Order Management Fragment Tag: ${{ github.event.inputs.orderManagementFrontendTag }}" >> $GITHUB_STEP_SUMMARY
            echo "See PR [here](${{ github.server_url }}/${{ github.event.inputs.repository }}/pull/${{ github.event.inputs.prNumber }})" >> $GITHUB_STEP_SUMMARY

        - name: 🤫 Import Shared Secrets
          id: shared-secrets
          uses: hellofresh/jetstream-ci-scripts/actions/vault@master
          with:
            shared-secrets: |
              common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
              common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;
              common/data/defaults GITHUB_TOKEN  | GITHUB_TOKEN;
            namespace: services/order-management-service
            secrets: |
              common/key-value/data/ci CYPRESS_RECORD_KEY | CYPRESS_RECORD_KEY;
              common/key-value/data/ci NEXT_PUBLIC_STATSIG_API_KEY | NEXT_PUBLIC_STATSIG_API_KEY;

        - name: 🔎 Find Comment
          if: ${{ github.event.inputs.postMessageReport != 'false' }}
          uses: peter-evans/find-comment@v3
          id: find-comment
          with:
              token: ${{ env.GITHUB_TOKEN }}
              issue-number: ${{ github.event.inputs.prNumber }}
              comment-author: 'hf-ghactions-bot'
              repository: ${{ github.event.inputs.repository }}
              body-includes: "E2E tests report"

        - name: 📝 Create or update running comment
          if: ${{ github.event.inputs.postMessageReport != 'false' }}
          uses: peter-evans/create-or-update-comment@v4
          id: running-comment
          with:
              token: ${{ env.GITHUB_TOKEN }}
              comment-id: ${{ steps.find-comment.outputs.comment-id }}
              issue-number: ${{ github.event.inputs.prNumber }}
              repository: ${{ github.event.inputs.repository }}
              body: |
                ## E2E tests report

                E2E test suite is running...

                See workflow run [here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
              edit-mode: replace

        - name: 🏃‍ Run end-to-end tests
          id: tests
          uses: ./.github/actions/end-to-end
          with:
            orderManagementFrontendTag: ${{ github.event.inputs.orderManagementFrontendTag }}
            orderManagementBackendTag: ${{ github.event.inputs.orderManagementBackendTag }}
            orderingLegacyFrontendTag: ${{ github.event.inputs.orderingLegacyFrontendTag }}
            tapiocaImageTag: ${{ github.event.inputs.tapiocaImageTag }}

        - name: 📝 Create or update result comment
          if: ${{ always() && github.event.inputs.postMessageReport != 'false' }}
          uses: peter-evans/create-or-update-comment@v4
          with:
              token: ${{ env.GITHUB_TOKEN }}
              comment-id: ${{ steps.running-comment.outputs.comment-id }}
              issue-number: ${{ github.event.inputs.prNumber }}
              repository: ${{ github.event.inputs.repository }}
              body: |
                ## E2E tests report

                E2E tests finished with: **${{ steps.tests.outputs.testResult }}**

                See results at [Cypress cloud dashboard](${{ steps.tests.outputs.resultUrls }})
                See workflow run [here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
              edit-mode: replace

