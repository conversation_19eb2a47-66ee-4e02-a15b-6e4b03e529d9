name: PR - Terraform Plan

permissions:
  id-token: write
  contents: read

on: [ pull_request ]

jobs:
  terraform-plan:
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v3

      - name: 🤫 Import Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          export-token: true
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN;
            common/data/defaults TERRAFORM_VAULT_TOKEN | VAULT_TOKEN;

      - name: 🌎 Terraform plan staging
        id: terraform-plan-staging
        uses: hellofresh/jetstream-ci-scripts/actions/terraform@master
        with:
          vault_token: ${{ env.VAULT_TOKEN }}
          vault_address: ${{ steps.vault-secrets.outputs.vault-address }}
          github_token: ${{ env.GITHUB_TOKEN }}
          directory: ${{ github.workspace }}/terraform/staging
          action: plan -no-color -var-file="env.tfvars"

      - name: 🌍 Terraform plan live
        id: terraform-plan-live
        uses: hellofresh/jetstream-ci-scripts/actions/terraform@master
        with:
          vault_token: ${{ env.VAULT_TOKEN }}
          vault_address: ${{ steps.vault-secrets.outputs.vault-address }}
          github_token: ${{ env.GITHUB_TOKEN }}
          directory: ${{ github.workspace }}/terraform/live
          action: plan -no-color -var-file="env.tfvars"
