name: Move Kafka Consumer Group Offset

on:
  workflow_dispatch:
    inputs:
      env:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - live
      consumer-group:
        description: 'Consumer group'
        required: true
        type: string
      topic:
        description: 'Topic'
        required: true
        type: string
      offset:
        description: 'Offset'
        required: true
        type: number

permissions:
  id-token: write
  contents: read

jobs:
  verify_offset:
    name: 📋 Verify offset
    runs-on: [ self-hosted, default ]
    steps:
      - name: 📋 Check if offset is valid
        shell: bash
        run: |
          if ! [[ ${{ inputs.offset }} =~ ^-?[0-9]+$ ]];
            then echo "error: Not a number"
            exit 1
          fi

  move-kafka-consumer-group:
    name: 🚚 Move Kafka Consumer Group Offset
    runs-on: [ self-hosted, default ]
    needs: [ verify_offset ]
    timeout-minutes: 15
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v4

      - name: 🚚 Move Kafka Consumer Group Offset
        uses: ./.github/actions/kafka_move_consumer_group_offset
        with:
          env: ${{ inputs.env }}
          consumer-group: ${{ inputs.consumer-group }}
          topic: ${{ inputs.topic }}
          offset: ${{ inputs.offset }}
