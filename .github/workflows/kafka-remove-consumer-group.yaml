name: Delete Kafka Consumer Group

on:
  workflow_dispatch:
    inputs:
      env:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - live
      consumer-group:
        description: 'Consumer group'
        required: true
        type: string
      topic:
        description: 'Topic'
        required: true
        type: string

permissions:
  id-token: write
  contents: read

jobs:
  Delete-kafka-consumer-group:
    name: ❌ Delete Kafka Consumer Group
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v4

      - name: ❌ Delete Kafka Consumer Group
        uses: ./.github/actions/kafka_delete_consumer_group
        with:
          env: ${{ inputs.env }}
          consumer-group: ${{ inputs.consumer-group }}
          topic: ${{ inputs.topic }}
