name: Reset Kafka Consumer Group Offset

on:
  workflow_dispatch:
    inputs:
      env:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - live
      consumer-group:
        description: 'Consumer group'
        required: true
        type: string
      topic:
        description: 'Topic'
        required: true
        type: string
      time:
        description: 'Reset offset to datetime'
        required: true
        type: string

permissions:
  id-token: write
  contents: read

jobs:
  verify_datetime:
    name: 📆 Verify datetime
    runs-on: [ self-hosted, default ]
    steps:
      - name: 🕰️ Check if time is valid
        shell: bash
        run: |
          date -d "${{ inputs.time }}" > /dev/null 2>&1
          if [ $? -ne 0 ]; then
            echo "Invalid time format. Please use ISO 8601 format (e.g. 2020-11-11T00:00:00.000+0900)"
            exit 1
          fi

  reset-kafka-consumer-group:
    name: Resets Kafka Consumer Group
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    needs: verify_datetime
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v4

      - name: 🎬 Reset Kafka Consumer Group
        uses: ./.github/actions/kafka_reset_offset
        with:
          env: ${{ inputs.env }}
          consumer-group: ${{ inputs.consumer-group }}
          time: ${{ inputs.time }}
          topic: ${{ inputs.topic }}
