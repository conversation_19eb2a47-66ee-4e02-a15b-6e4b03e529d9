# This workflow is centrally managed and generated from
# https://github.com/hellofresh/github-automation/blob/master/modules/repository/required-workflows/required-workflow-wrapper.yaml.tmpl
---
name: "PR: Company Required Workflows"

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - labeled
      - unlabeled

    branches:
      - master

permissions:
  contents: read
  pull-requests: write
  id-token: write
  
jobs:

  check-ai-generated-commits:
    name: Check AI Generated Commits
    uses: hellofresh/workflow-pr-label-for-ai-code/.github/workflows/reusable.yml@master

  check-repository-ownership:
    name: Check repository ownership
    uses: hellofresh/workflow-validate-repository-ownership/.github/workflows/reusable.yml@master

  check-terraform-ownership:
    name: Check terraform ownership
    uses: hellofresh/workflow-tf-ownership-check/.github/workflows/reusable.yml@master

  check-tribe-squad-labels:
    name: Check tribe squad labels
    uses: hellofresh/workflow-validate-tribe-squad-labels/.github/workflows/reusable.yml@master

  release-engineering-checks:
    name: Release Engineering Checks
    uses: hellofresh/workflow-release-engineering-checks/.github/workflows/reusable.yml@master

  required-workflows-status:
    name: Required workflows status
    if: always()
    runs-on: ubuntu-latest
    needs: [check-ai-generated-commits,check-repository-ownership,check-terraform-ownership,check-tribe-squad-labels,release-engineering-checks]
    steps:
      - if: |
          (needs.check-ai-generated-commits.result != 'success') ||
          (needs.check-repository-ownership.result != 'success') ||
          (needs.check-terraform-ownership.result != 'success') ||
          (needs.check-tribe-squad-labels.result != 'success') ||
          (needs.release-engineering-checks.result != 'success') 

        run: exit 1
