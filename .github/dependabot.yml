version: 2
updates:
  - package-ecosystem: "gradle"
    directory: "/"
    rebase-strategy: "auto"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "10:00"
      timezone: "Europe/Berlin"
    labels:
      - "Dependabot"
      - "version update"
      - "dependencies"
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
  - package-ecosystem: "github-actions"
    directory: "/"
    rebase-strategy: "auto"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "10:00"
      timezone: "Europe/Berlin"
    labels:
      - "Dependabot"
      - "version update"
      - "dependencies"
