complexity:
  ReplaceSafeCallChainWithRun:
    active: true
  StringLiteralDuplication:
    active: true
    excludes:
      - '**/test/**'
      - '**.gradle.kts'
  TooManyFunctions:
    active: true
    excludes:
      - '**/test/**'
      - '**/testIntegration/**'

exceptions:
  ExceptionRaisedInUnexpectedLocation:
    active: true
  PrintStackTrace:
    active: true
  SwallowedException:
    active: true
    ignoredExceptionTypes: []
    allowedExceptionNameRegex: '_'
  ThrowingExceptionFromFinally:
    active: true
  ThrowingExceptionsWithoutMessageOrCause:
    active: true
    exceptions: []
  ThrowingNewInstanceOfSameException:
    active: true
  TooGenericExceptionCaught:
    # Usage of these names:
    #
    # | name  | description                                              |
    # | ----- | -------------------------------------------------------- |
    # | `_`   | Completely ignore the exception                          |
    # | `all` | Uncaught exception handler                               |
    # | `re`  | You want to **re**cord it and immediately **re**throw it |
    allowedExceptionNameRegex: '_|all|re'

formatting:
  EnumEntryNameCase:
    active: true
  ImportOrdering:
    active: true
    layout: '*'
  Indentation:
    active: true
  MaximumLineLength:
    active: false
  NoEmptyFirstLineInMethodBlock:
    active: true
  # Already reported in style by `UnusedImports`.
  NoUnusedImports:
    active: false
  SpacingAroundDoubleColon:
    active: true

naming:
  NonBooleanPropertyPrefixedWithIs:
    active: true

potential-bugs:
  HasPlatformType:
    active: true
  ImplicitDefaultLocale:
    active: true
  MapGetWithNotNullAssertionOperator:
    active: true
  UnnecessaryNotNullOperator:
    active: true
  UnnecessarySafeCall:
    active: true
  UnsafeCast:
    active: true
  UselessPostfixExpression:
    active: true

style:
  ClassOrdering:
    active: true
  CollapsibleIfStatements:
    active: true
  DataClassShouldBeImmutable:
    active: true
  EqualsOnSignatureLine:
    active: true
  ExplicitCollectionElementAccessMethod:
    active: true
  ExplicitItLambdaParameter:
    active: true
  ExpressionBodySyntax:
    active: true
    includeLineWrapping: true
  ForbiddenComment:
    active: false
  MagicNumber:
    active: true
    excludes:
      - '**/test/**'
      - '**/testIntegration/**'
  MaxLineLength:
    active: true
    maxLineLength: 160
    excludePackageStatements: true
    excludeImportStatements: true
    excludeCommentStatements: false
  NestedClassesVisibility:
    active: true
  NoTabs:
    active: true
  OptionalUnit:
    active: true
  PreferToOverPairSyntax:
    active: true
  SerialVersionUIDInSerializableClass:
    active: true
  SpacingBetweenPackageAndImports:
    active: true
  TrailingWhitespace:
    active: true
  UnnecessaryApply:
    active: true
  UnnecessaryLet:
    active: true
  UnnecessaryParentheses:
    active: true
  UntilInsteadOfRangeTo:
    active: true
  UnusedImports:
    active: true
  UnusedPrivateMember:
    active: false
  UseCheckNotNull:
    active: true
  UseCheckOrError:
    active: true
  UseEmptyCounterpart:
    active: true
  UseIfInsteadOfWhen:
    active: true
  UseRequire:
    active: true
  UseRequireNotNull:
    active: true
  VarCouldBeVal:
    active: true
  WildcardImport:
    active: true
    excludes: []
    excludeImports: []
