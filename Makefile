END_TO_END_DIR=end-to-end-tests
include $(END_TO_END_DIR)/.env

DB_USERNAME=root
DB_PASSWORD=123456
DB_URL_LOCAL=*************************************************

MIGRATION_DESCRIPTION ?= add_table
MIGRATION_ENV ?= common

generateMigration:
ifndef MIGRATION_DESCRIPTION
	$(error MIGRATION_DESCRIPTION is required. Usage: make generateMigration MIGRATION_DESCRIPTION=add_user_table)
endif
	./gradlew :db-migration:newFlywayMigration -Pdesc="$(MIGRATION_DESCRIPTION)" -Penv=$(MIGRATION_ENV)

install-deps:
	cd $(END_TO_END_DIR) && npm install

e2e: buildLocalImage build-e2e-environment migrateDb openCypress

e2e-headless: buildLocalImage build-e2e-environment migrateDb runCypress

buildLocalImage:
	./gradlew order-management:jibDockerBuild

build-e2e-environment:
	cd $(END_TO_END_DIR) && source .env && docker compose up --wait -d

migrateDb:
	DB_PASSWORD_LOCAL=$(DB_PASSWORD) DB_USERNAME_LOCAL=$(DB_USERNAME) DB_URL_LOCAL=$(DB_URL_LOCAL) ./gradlew migrateEndtoEndDb

openCypress:
	cd $(END_TO_END_DIR) && npm run test:open

runCypress:
	cd $(END_TO_END_DIR) && npm run test:ci

clean:
	cd $(END_TO_END_DIR) && docker compose stop order-management-postgres && docker compose rm order-management-postgres -f
	make e2e
