import java.text.SimpleDateFormat
import java.util.Date
import org.flywaydb.gradle.task.FlywayMigrateTask

plugins {
    id("org.flywaydb.flyway") version "9.22.3"
    alias(libs.plugins.kotlin.jvm)
}

group = "$group.db-migration"

dependencies {
    implementation(libs.postgresql)
}

tasks.register("migrateStageDb", FlywayMigrateTask::class.java).configure {
    dependsOn(tasks.build)

    url = System.getenv("DB_URL")
    user = System.getenv("DB_USERNAME")
    password = System.getenv("DB_PASSWORD")
    locations = arrayOf("classpath:db/common,classpath:db/staging")
}

tasks.register("migrateProdDb", FlywayMigrateTask::class.java).configure {
    dependsOn(tasks.build)

    url = System.getenv("DB_URL")
    user = System.getenv("DB_USERNAME")
    password = System.getenv("DB_PASSWORD")
    locations = arrayOf("classpath:db/common,classpath:db/live")
}

// Used only locally to migrate docker postgres service (NOTE: credentials need to match the one in docker-compose.yaml)
tasks.register("migrateLocal", FlywayMigrateTask::class.java).configure {
    dependsOn(tasks.build)
    url = "****************************************************************************"
    user = "manager"
    password = "1234"
    locations = arrayOf("classpath:db/common,classpath:db/staging")
}

// TODO: merge with MigrateLocal task
tasks.register("migrateEndtoEndDb", FlywayMigrateTask::class.java).configure {
    dependsOn(tasks.build)
    url = System.getenv("DB_URL_LOCAL")
    user = System.getenv("DB_USERNAME_LOCAL")
    password = System.getenv("DB_PASSWORD_LOCAL")
    outOfOrder = true
    locations = arrayOf("classpath:db/common,classpath:db/staging,classpath:db/e2e")
}

tasks.register("newFlywayMigration") {
    group = "flyway"
    description = "Generates a new Flyway migration script for the specified environment with a timestamped version."

    doLast {
        val dateFormat = SimpleDateFormat("yyyyMMdd_HHmm")
        val version = dateFormat.format(Date())
        val description = project.findProperty("desc")?.toString()?.replace("\\s+".toRegex(), "_") ?: "new_migration"
        val environment = project.findProperty("env")?.toString() ?: "common"
        val allowedEnvironments = setOf("common", "e2e", "live", "staging")

        if (environment !in allowedEnvironments) {
            error("❌ Invalid environment: '$environment'. Allowed values: ${allowedEnvironments.joinToString(", ")}")
        }

        val fileName = "V${version}__$description.sql"
        val migrationsDir = file("src/main/resources/db/$environment")

        if (!migrationsDir.exists()) {
            migrationsDir.mkdirs()
        }

        val migrationFile = File(migrationsDir, fileName)
        migrationFile.writeText("-- Migration: $fileName (Environment: $environment)\n\n")

        println("✅ Created new Flyway migration in '$environment': ${migrationFile.absolutePath}")
    }
}

tasks {
    register("validateFileNames") {
        val pattern = Regex("^V\\d{8}(_\\d{4})?__.*\\.sql\$")

        doLast {
            val files =
                fileTree("src/main/resources/db") {
                    include("**/*.sql")
                }

            files.forEach { file ->
                if (!file.name.matches(pattern)) {
                    throw GradleException("File ${file.parentFile.name}/${file.name} has an invalid name format.")
                }
            }
        }
    }

    check {
        dependsOn("validateFileNames")
    }
}
