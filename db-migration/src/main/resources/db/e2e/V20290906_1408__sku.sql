INSERT INTO public.sku (uuid, market, name, code, status, brands, category)
VALUES (uuid_generate_v4(), 'us', 'E2E Chicken, Sausage', 'PTN-10-10101-7', 'INACTIVE', '{HelloFresh}', 'PTN'),
       (uuid_generate_v4(), 'us', 'E2E SE Freebies Mixed', 'OTH-33-121101-1', 'ONBOARDING', '{HelloFresh}', 'OTH'),
       (uuid_generate_v4(), 'us', 'E2E SE test', 'OTH-33-121101-2', 'ONBOARDING', '{HelloFresh}', 'OTH');

INSERT INTO public.supplier_sku (uuid, parent_supplier_id, sku_id, market, status, supplier_code)
VALUES ('00000000-0000-0000-0000-000000000000', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0001-000000000000', 'us', 'ACTIVE','60091'),
       ('00000000-0000-0000-0000-000000000001', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0002-000000000000', 'us', 'ACTIVE','0002'),
       ('00000000-0000-0000-0000-000000000002', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0003-000000000000', 'us', 'ACTIVE','0003'),
       ('00000000-0000-0000-0000-000000000003', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0004-000000000000', 'us', 'ACTIVE','0003');
