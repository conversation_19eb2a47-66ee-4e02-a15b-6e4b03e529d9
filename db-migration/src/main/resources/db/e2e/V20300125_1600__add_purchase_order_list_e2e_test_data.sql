-- Add additional suppliers for E2E testing
INSERT INTO public.supplier (id, market, code, name, status, currency, type, created_at, updated_at, city, country, state, address, number, post_code, parent_id, dc_codes) VALUES
('f1234567-1234-1234-1234-123456789abc', 'us', '200001', 'Supplier D', 'ACTIVE', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'New York', 'US', 'NY', '123 Main Street', '100', '10001', 'f1234567-1234-1234-1234-123456789abc', '{NJ,GL,CA}'),
('f1234567-1234-1234-1234-123456789abd', 'us', '200002', 'Supplier E', 'ACTIVE', 'USD', 'Manufacturer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Los Angeles', 'US', 'CA', '456 Oak Avenue', '200', '90210', 'f1234567-1234-1234-1234-123456789abd', '{CA,TX}'),
('f1234567-1234-1234-1234-123456789abe', 'us', '200003', 'Supplier F', 'ACTIVE', 'USD', 'Distributor', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'Chicago', 'US', 'IL', '789 Pine Street', '300', '60601', 'f1234567-1234-1234-1234-123456789abe', '{NJ,GL}');

-- Add ship methods for new suppliers
INSERT INTO public.ship_method (uuid, dc_code, supplier_id, method, market) VALUES
(uuid_generate_v4(), 'NJ', 'f1234567-1234-1234-1234-123456789abc', 'VENDOR', 'us'),
(uuid_generate_v4(), 'GL', 'f1234567-1234-1234-1234-123456789abc', 'VENDOR', 'us'),
(uuid_generate_v4(), 'CA', 'f1234567-1234-1234-1234-123456789abc', 'VENDOR', 'us'),
(uuid_generate_v4(), 'CA', 'f1234567-1234-1234-1234-123456789abd', 'VENDOR', 'us'),
(uuid_generate_v4(), 'TX', 'f1234567-1234-1234-1234-123456789abd', 'VENDOR', 'us'),
(uuid_generate_v4(), 'NJ', 'f1234567-1234-1234-1234-123456789abe', 'VENDOR', 'us'),
(uuid_generate_v4(), 'GL', 'f1234567-1234-1234-1234-123456789abe', 'VENDOR', 'us');

-- Add comprehensive purchase orders for E2E testing
INSERT INTO purchase_order
(po_number, id, version, type, year_week, user_id, user_email, status, send_time, supplier_id, supplier_code, dc_code, shipping_method, location_name, street_address, city, region, postal_code, country_code, expected_start_time, expected_end_time, emergency_reason_uuid, created_at, updated_at, total_price_currency, total_price_amount, comment, is_synced, delivery_date_change_reason_id)
VALUES
-- Order for search testing (ORD-12345)
('ORD-12345', 'e3fa59aa-91c1-4c78-bfa5-000000000101', 1, 'EMERGENCY', '2024-W01', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'APPROVED', '2024-01-01 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'NJ', 'VENDOR', 'HelloFresh United States - NJ', '60 Lister Avenue', 'Newark', 'NJ', '07105', 'US', '2024-01-01 10:00:00.000000', '2024-01-01 11:00:00.000000', null, '2024-01-01 09:00:00.000000', '2024-01-01 09:30:00.000000', 'USD', '250.00', 'Test order for search', true, null),

-- Orders with different statuses for status filtering
('TEST-001', 'e3fa59aa-91c1-4c78-bfa5-000000000102', 1, 'EMERGENCY', '2024-W01', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'APPROVED', '2024-01-01 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'NJ', 'VENDOR', 'HelloFresh United States - NJ', '60 Lister Avenue', 'Newark', 'NJ', '07105', 'US', '2024-01-01 10:00:00.000000', '2024-01-01 11:00:00.000000', null, '2024-01-01 09:00:00.000000', '2024-01-01 09:30:00.000000', 'USD', '300.00', 'Ordered status test', true, null),

('TEST-002', 'e3fa59aa-91c1-4c78-bfa5-000000000103', 1, 'EMERGENCY', '2024-W01', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'APPROVED', '2024-01-01 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abd', '200002', 'CA', 'VENDOR', 'HelloFresh United States - CA', '100 Pacific Avenue', 'Los Angeles', 'CA', '90210', 'US', '2024-01-01 10:00:00.000000', '2024-01-01 11:00:00.000000', null, '2024-01-01 09:00:00.000000', '2024-01-01 09:30:00.000000', 'USD', '400.00', 'Sent status test', true, null),

('TEST-003', 'e3fa59aa-91c1-4c78-bfa5-000000000104', 1, 'EMERGENCY', '2024-W01', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', null, 'f1234567-1234-1234-1234-123456789abe', '200003', 'GL', 'VENDOR', 'HelloFresh United States - GL', '200 Green Lane', 'Greenville', 'TX', '75401', 'US', '2024-01-01 10:00:00.000000', '2024-01-01 11:00:00.000000', null, '2024-01-01 09:00:00.000000', '2024-01-01 09:30:00.000000', 'USD', '350.00', 'Initiated status test', true, null),

('TEST-004', 'e3fa59aa-91c1-4c78-bfa5-000000000105', 1, 'EMERGENCY', '2024-W01', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', '2024-01-01 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'NJ', 'VENDOR', 'HelloFresh United States - NJ', '60 Lister Avenue', 'Newark', 'NJ', '07105', 'US', '2024-01-01 10:00:00.000000', '2024-01-01 11:00:00.000000', null, '2024-01-01 09:00:00.000000', '2024-01-01 09:30:00.000000', 'USD', '450.00', 'Delivered status test', true, null),

-- Orders for different suppliers (Supplier D, E, F)
('SUP-A-001', 'e3fa59aa-91c1-4c78-bfa5-000000000106', 1, 'EMERGENCY', '2024-W02', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', '2024-01-08 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'NJ', 'VENDOR', 'HelloFresh United States - NJ', '60 Lister Avenue', 'Newark', 'NJ', '07105', 'US', '2024-01-08 10:00:00.000000', '2024-01-08 11:00:00.000000', null, '2024-01-08 09:00:00.000000', '2024-01-08 09:30:00.000000', 'USD', '200.00', 'Supplier D order', true, null),

('SUP-A-002', 'e3fa59aa-91c1-4c78-bfa5-000000000107', 1, 'EMERGENCY', '2024-W02', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'REJECTED', '2024-01-08 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'GL', 'VENDOR', 'HelloFresh United States - GL', '300 Green Lane', 'Greenville', 'TX', '75401', 'US', '2024-01-08 10:00:00.000000', '2024-01-08 11:00:00.000000', null, '2024-01-08 09:00:00.000000', '2024-01-08 09:30:00.000000', 'USD', '220.00', 'Another Supplier D order', true, null),

('SUP-B-001', 'e3fa59aa-91c1-4c78-bfa5-000000000108', 1, 'EMERGENCY', '2024-W02', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', '2024-01-08 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abd', '200002', 'CA', 'VENDOR', 'HelloFresh United States - CA', '100 Pacific Avenue', 'Los Angeles', 'CA', '90210', 'US', '2024-01-08 10:00:00.000000', '2024-01-08 11:00:00.000000', null, '2024-01-08 09:00:00.000000', '2024-01-08 09:30:00.000000', 'USD', '280.00', 'Supplier D order', true, null),

-- Orders for different DCs (NJ, CA, GL, TX)
('DC-NJ-001', 'e3fa59aa-91c1-4c78-bfa5-000000000109', 1, 'EMERGENCY', '2024-W03', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', '2024-01-15 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'NJ', 'VENDOR', 'HelloFresh United States - NJ', '60 Lister Avenue', 'Newark', 'NJ', '07105', 'US', '2024-01-15 10:00:00.000000', '2024-01-15 11:00:00.000000', null, '2024-01-15 09:00:00.000000', '2024-01-15 09:30:00.000000', 'USD', '320.00', 'NJ DC order', true, null),

('DC-CA-001', 'e3fa59aa-91c1-4c78-bfa5-000000000110', 1, 'EMERGENCY', '2024-W03', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', '2024-01-15 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abd', '200002', 'CA', 'VENDOR', 'HelloFresh United States - CA', '100 Pacific Avenue', 'Los Angeles', 'CA', '90210', 'US', '2024-01-15 10:00:00.000000', '2024-01-15 11:00:00.000000', null, '2024-01-15 09:00:00.000000', '2024-01-15 09:30:00.000000', 'USD', '380.00', 'CA DC order', true, null),

('DC-GL-001', 'e3fa59aa-91c1-4c78-bfa5-000000000111', 1, 'EMERGENCY', '2024-W03', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', '2024-01-15 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abe', '200003', 'GL', 'VENDOR', 'HelloFresh United States - GL', '300 Green Lane', 'Greenville', 'TX', '75401', 'US', '2024-01-15 10:00:00.000000', '2024-01-15 11:00:00.000000', null, '2024-01-15 09:00:00.000000', '2024-01-15 09:30:00.000000', 'USD', '290.00', 'GL DC order', true, null),

-- Orders for different production weeks (2024-W01, W02, W03, W04)
('WEEK-01-001', 'e3fa59aa-91c1-4c78-bfa5-000000000112', 1, 'EMERGENCY', '2024-W01', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', '2024-01-01 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'NJ', 'VENDOR', 'HelloFresh United States - NJ', '60 Lister Avenue', 'Newark', 'NJ', '07105', 'US', '2024-01-01 10:00:00.000000', '2024-01-01 11:00:00.000000', null, '2024-01-01 09:00:00.000000', '2024-01-01 09:30:00.000000', 'USD', '150.00', 'Week 1 order', true, null),

('WEEK-02-001', 'e3fa59aa-91c1-4c78-bfa5-000000000113', 1, 'EMERGENCY', '2024-W02', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'APPROVED', '2024-01-08 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abd', '200002', 'CA', 'VENDOR', 'HelloFresh United States - CA', '100 Pacific Avenue', 'Los Angeles', 'CA', '90210', 'US', '2024-01-08 10:00:00.000000', '2024-01-08 11:00:00.000000', null, '2024-01-08 09:00:00.000000', '2024-01-08 09:30:00.000000', 'USD', '175.00', 'Week 2 order', true, null),

('WEEK-03-001', 'e3fa59aa-91c1-4c78-bfa5-000000000114', 1, 'EMERGENCY', '2024-W03', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'APPROVED', '2024-01-15 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abe', '200003', 'GL', 'VENDOR', 'HelloFresh United States - GL', '300 Green Lane', 'Greenville', 'TX', '75401', 'US', '2024-01-15 10:00:00.000000', '2024-01-15 11:00:00.000000', null, '2024-01-15 09:00:00.000000', '2024-01-15 09:30:00.000000', 'USD', '200.00', 'Week 3 order', true, null),

('WEEK-04-001', 'e3fa59aa-91c1-4c78-bfa5-000000000115', 1, 'EMERGENCY', '2024-W04', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'APPROVED', '2024-01-22 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'NJ', 'VENDOR', 'HelloFresh United States - NJ', '60 Lister Avenue', 'Newark', 'NJ', '07105', 'US', '2024-01-22 10:00:00.000000', '2024-01-22 11:00:00.000000', null, '2024-01-22 09:00:00.000000', '2024-01-22 09:30:00.000000', 'USD', '225.00', 'Week 4 order', true, null),

-- Additional orders for combination filtering tests
('COMBO-001', 'e3fa59aa-91c1-4c78-bfa5-000000000116', 1, 'EMERGENCY', '2024-W01', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'INITIATED', '2024-01-01 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abc', '200001', 'NJ', 'VENDOR', 'HelloFresh United States - NJ', '60 Lister Avenue', 'Newark', 'NJ', '07105', 'US', '2024-01-01 10:00:00.000000', '2024-01-01 11:00:00.000000', null, '2024-01-01 09:00:00.000000', '2024-01-01 09:30:00.000000', 'USD', '500.00', 'Supplier A + Ordered + NJ + Week 1', true, null),

('COMBO-002', 'e3fa59aa-91c1-4c78-bfa5-000000000117', 1, 'EMERGENCY', '2024-W02', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'DELETED', '2024-01-08 10:00:00.000000', 'f1234567-1234-1234-1234-123456789abd', '200002', 'CA', 'VENDOR', 'HelloFresh United States - CA', '100 Pacific Avenue', 'Los Angeles', 'CA', '90210', 'US', '2024-01-08 10:00:00.000000', '2024-01-08 11:00:00.000000', null, '2024-01-08 09:00:00.000000', '2024-01-08 09:30:00.000000', 'USD', '550.00', 'Supplier B + Sent + CA + Week 2', true, null),

-- Orders that should not match certain filters (for negative testing)
('NO-MATCH-001', 'e3fa59aa-91c1-4c78-bfa5-000000000118', 1, 'EMERGENCY', '2024-W05', '3c0a5325-0492-4dc1-bf29-cc7d44630a5b', '<EMAIL>', 'CANCELLED', null, 'f1234567-1234-1234-1234-123456789abe', '200003', 'GL', 'VENDOR', 'HelloFresh United States - GL', '300 Green Lane', 'Greenville', 'TX', '75401', 'US', '2024-01-29 10:00:00.000000', '2024-01-29 11:00:00.000000', null, '2024-01-29 09:00:00.000000', '2024-01-29 09:30:00.000000', 'USD', '100.00', 'Order that should not match common filters', true, null);
