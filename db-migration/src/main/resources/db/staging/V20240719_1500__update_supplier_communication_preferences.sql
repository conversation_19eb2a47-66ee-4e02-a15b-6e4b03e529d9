-- Remove all old entries
TRUNCATE TABLE communication_preference;

-- Insert new entries
-- Communication Preferences by Market
INSERT INTO communication_preference (market, dc_code, supplier_id, preference)
VALUES ('au', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('ca', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('de', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('es', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('eu', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('fr', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('gb', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('ie', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('it', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('nl', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('nz', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('se', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('us', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('dach', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('dkse', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('beneluxfr', NULL, NULL, 'EMAIL_AND_E2OPEN');


