-- ======= Description: Change the UUIDs of the emergency reasons used by OPE to the live UUID =======
-- 1. Insert the ones with the correct UUID
INSERT INTO emergency_order_reason (uuid, market, name, disabled)
VALUES ('4843d54d-3d36-42a2-a467-0c2c28cc880d', 'ca', 'OrderPlanningService-CA', false),
       ('40834361-62ea-472e-8dca-cd50a259ad0a', 'dach', 'OrderPlanningService-DE', false),
       ('770e12eb-780a-4002-996f-bb4ddc59b404', 'es', 'OrderPlanningService-ES', false),
       ('acc19b5c-962d-4d05-bf08-e19bbe23b63a', 'fr', 'OrderPlanningService-FR', false),
       ('4a05f404-3240-43fe-ab97-534b6a4ae37e', 'gb', 'OrderPlanningService-GB', false),
       ('18f0e348-c236-48a3-a9b8-c590dec73b13', 'ie', 'OrderPlanningService-IE', false),
       ('a9986ec6-2870-458c-90ce-80f7bf022102', 'it', 'OrderPlanningService-IT', false),
       ('e2078f6b-3ce3-4194-b879-0a77f2830054', 'beneluxfr', 'OrderPlanningService-NL', false),
       ('a37033a2-6ac7-42d6-89cf-f1ac130b1332', 'dkse', 'OrderPlanningService-SE', false),
       ('ecdd4ac7-0afb-435f-b9ed-1ab0caba27a7', 'us', 'OrderPlanningService-US', false);

-- 2. Change existing POs to use the new UUIDs
UPDATE purchase_order
SET emergency_reason_uuid = '4843d54d-3d36-42a2-a467-0c2c28cc880d'
WHERE emergency_reason_uuid = '00148ce9-7bea-4971-b3a3-ba2967531d1d'; --OrderPlanningService-CA

UPDATE purchase_order
SET emergency_reason_uuid = '40834361-62ea-472e-8dca-cd50a259ad0a'
WHERE emergency_reason_uuid = '817fa948-2428-4204-a87a-5fb8774e6666'; --OrderPlanningService-DE

UPDATE purchase_order
SET emergency_reason_uuid = '770e12eb-780a-4002-996f-bb4ddc59b404'
WHERE emergency_reason_uuid = '9b82f6b0-7d17-4a53-8500-cb70b0cd0248'; --OrderPlanningService-ES

UPDATE purchase_order
SET emergency_reason_uuid = 'acc19b5c-962d-4d05-bf08-e19bbe23b63a'
WHERE emergency_reason_uuid = '217a030b-6d21-4b11-85d4-d0ce0e8419af'; --OrderPlanningService-FR

UPDATE purchase_order
SET emergency_reason_uuid = '4a05f404-3240-43fe-ab97-534b6a4ae37e'
WHERE emergency_reason_uuid = 'f6f9e74a-085d-47c7-9b45-3e6b0606d71d'; --OrderPlanningService-GB

UPDATE purchase_order
SET emergency_reason_uuid = '18f0e348-c236-48a3-a9b8-c590dec73b13'
WHERE emergency_reason_uuid = 'f9592d20-5f88-4f4d-99ab-2043e54a5238'; --OrderPlanningService-IE

UPDATE purchase_order
SET emergency_reason_uuid = 'a9986ec6-2870-458c-90ce-80f7bf022102'
WHERE emergency_reason_uuid = '14c9aff3-66d3-4bcc-9c26-ac6e01c175c7'; --OrderPlanningService-IT

UPDATE purchase_order
SET emergency_reason_uuid = 'e2078f6b-3ce3-4194-b879-0a77f2830054'
WHERE emergency_reason_uuid = 'd5513969-c8e6-4875-af35-5347ab1eb331'; --OrderPlanningService-NL

UPDATE purchase_order
SET emergency_reason_uuid = 'a37033a2-6ac7-42d6-89cf-f1ac130b1332'
WHERE emergency_reason_uuid = '14f69ac2-805b-471f-b579-df8392dd64cb'; --OrderPlanningService-SE

UPDATE purchase_order
SET emergency_reason_uuid = 'ecdd4ac7-0afb-435f-b9ed-1ab0caba27a7'
WHERE emergency_reason_uuid = '6a312d33-bdd2-410b-9f27-2aea9f2f67f2'; --OrderPlanningService-US

-- 2. Delete the ones with the old UUID
DELETE
FROM emergency_order_reason
WHERE UUID IN (
    '00148ce9-7bea-4971-b3a3-ba2967531d1d', -- OrderPlanningService-CA
    '817fa948-2428-4204-a87a-5fb8774e6666', -- OrderPlanningService-DE
    '9b82f6b0-7d17-4a53-8500-cb70b0cd0248', -- OrderPlanningService-ES
    '217a030b-6d21-4b11-85d4-d0ce0e8419af', -- OrderPlanningService-FR
    'f6f9e74a-085d-47c7-9b45-3e6b0606d71d', -- OrderPlanningService-GB
    'f9592d20-5f88-4f4d-99ab-2043e54a5238', -- OrderPlanningService-IE
    '14c9aff3-66d3-4bcc-9c26-ac6e01c175c7', -- OrderPlanningService-IT
    'd5513969-c8e6-4875-af35-5347ab1eb331', -- OrderPlanningService-NL
    '14f69ac2-805b-471f-b579-df8392dd64cb', -- OrderPlanningService-SE
    '6a312d33-bdd2-410b-9f27-2aea9f2f67f2' -- OrderPlanningService-US
);
