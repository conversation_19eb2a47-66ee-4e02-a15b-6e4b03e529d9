INSERT INTO emergency_order_reason(uuid, market, name, disabled)
VALUES  ('e5aaab84-83fb-4f26-bfb0-7018c0b29b7b', 'ca', 'Arranged', false),
        ('2e35f73f-cbca-4db2-98ab-13d3ce45d4a1', 'ca', 'Bulk Order', false),
        ('b78055ee-6c97-48ee-a2aa-78d6b2cc2383', 'ca', 'Cross-docking', false),
        ('77b0182f-45fa-45f6-a294-b93dee495582', 'ca', 'Database Correction', false),
        ('9306ffed-36ee-42ff-abb6-1e496e9a96e2', 'ca', 'Demand Change', false),
        ('a8200cd4-1339-43a3-b8f2-465a809be6b8', 'ca', 'From Storage', false),
        ('e5ccb7b5-5975-404a-bf56-979d77f9fd36', 'ca', 'HelloFresh error', false),
        ('1ccf5435-84fa-43e0-b179-409ac5dbab57', 'ca', 'Historical Upload', false),
        ('e950ea99-c82a-4854-9667-20ace4685bb2', 'ca', 'Long Lead time', false),
        ('1084aa5e-0346-4d34-a730-8a7b4367e4cc', 'ca', 'Packaging', false),
        ('e6810ec4-ff4c-4610-acf7-f3d4459ff730', 'ca', 'Price Error', false),
        ('0df104ff-e139-480c-b144-2f3889a81c02', 'ca', 'Regular', false),
        ('1e686dd4-bc52-4f84-aa31-f102f0d464f5', 'ca', 'Repackaging', false),
        ('287d000a-210e-44df-8523-b8defef85602', 'ca', 'Replenishment', false),
        ('f67b77c7-dac9-43d5-8a06-0a48ac3feac3', 'ca', 'Stock Error', false),
        ('30808ee2-eec9-4be4-a0a2-f9aaf7598eca', 'ca', 'Supplier Error', false),
        ('15e8c7fd-0426-46eb-82fa-60f849abd03f', 'ca', 'Advance Weekly order', false),
        ('eb69a5ec-acee-41db-8316-94989077bfe0', 'ca', 'Repacking- All in', false),
        ('a68bf55b-bacd-4080-a910-e356f42144a2', 'ca', 'Repacking- Service', false),
        ('548b770d-d7c7-460c-9763-ab0783cd3b9d', 'ca', 'Internal Transfer', false),
        ('ccdc638c-ec1a-4d1f-bf16-bbada75d63b6', 'ca', 'Raw ingredients (Bulk)', false),
        ('676e9cb2-1fa1-4830-a990-a9afbef257ca', 'ca', 'Multi Week', false),
        ('00148ce9-7bea-4971-b3a3-ba2967531d1d', 'ca', 'OrderPlanningService-CA', false),
        ('0baa42f3-39d9-4fa1-95fe-5f227a6c7fc0', 'ca', 'Do not show in OPE', false);
