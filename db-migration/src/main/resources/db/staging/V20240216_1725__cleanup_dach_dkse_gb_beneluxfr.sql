-- Clean up GB
DELETE
FROM emergency_order_reason
WHERE uuid NOT IN (
                   '754c6736-ecba-485f-a255-b64258e91078',
                   'fa9fc4a3-d3e1-414c-a6b8-183626eb006d',
                   '9ea86298-ccaa-49ad-a7e7-66404c121771',
                   'a8ff0fca-c84c-4431-b577-f57e4d39268f',
                   'af61f757-132a-4b25-ab20-59b912b354ad',
                   '803613dd-13c4-4c58-bbc6-32da1eafde5b',
                   '2f24e24d-b38f-43de-9760-732e8bfa46be',
                   '4bcde134-7717-47e9-9a74-5099d4d50750',
                   'f4c8bde0-fd34-4da4-9932-40a9ac7ab264',
                   'fbdfcf11-ae3a-413c-a501-f48e4a374100',
                   'eb671356-61dc-4edb-a5d9-4917904954df',
                   '74bf4a05-d31a-4c71-ae0b-e1db801c0388',
                   '*************-448a-87cd-bba60a85cb03',
                   'bc1ebaad-0ad0-4ea2-bc60-f8930f76497d',
                   '01068baa-ff4d-40a4-9e48-6fb0a5449426',
                   '25930fde-0513-4514-80c3-f8f0dbbff8d2',
                   '1e2ccabe-92f9-4de4-bd3b-d583a535dd3e',
                   'c62e3b5f-9bed-48c1-9cdd-80a8c7e55d7b',
                   '4af72498-b6ff-45b1-a95a-0a7a3a8b3e51',
                   'f6f9e74a-085d-47c7-9b45-3e6b0606d71d',
                   'c2303641-8655-485e-9bcc-b7a87af2370a',
                   '667b986f-2fc3-4471-86cd-21d29550871e',
                   'f96cf15f-d3e4-4d0e-9680-b8eb828bc782',
                   'eb8e97e6-9a71-4def-9b36-a4748c134311',
                   '0f8b93e7-9425-4eed-86bf-2dc5878b6ea9',
                   '87b78f57-8436-434b-a91a-718f114ae801',
                   '3a9c9dff-74ff-4f1c-a65a-48d4b372a4c4',
                   '28ea44ef-02c7-4889-8d65-f115ec316c1a',
                   'f9a38fd5-f959-4def-808e-d20829bbe34e',
                   'e95f384a-9e6b-4f61-8481-ac624058bc4b',
                   '41db071e-4c55-4860-8215-8d27fe36a526',
                   '93a2c24d-7ee9-4c21-a11e-5873086c8d89',
                   '4bf600d9-a951-449f-9504-67170d417052',
                   '1f327960-e043-4580-a50b-d38e19e1164a',
                   '597e7ea1-a2dc-4fbf-baf9-9ff7476a9faa'
    )
  AND market = 'gb';

-- Clean up DKSE
DELETE
FROM emergency_order_reason
WHERE emergency_order_reason.market = 'dkse';

INSERT INTO emergency_order_reason (uuid, name, disabled, market)
VALUES ('ee4b6e30-4993-419c-b745-34dbe3adb69d', 'Arranged', FALSE, 'dkse'),
       ('52dba980-6e49-424e-a0cf-add880db75e6', 'Bulk Order', FALSE, 'dkse'),
       ('b7bcb2ef-e1d4-444f-b513-38b6042802a9', 'Cross-docking', FALSE, 'dkse'),
       ('b9f2eb4f-91cd-4fdd-b630-26944a45480d', 'Database Correction', FALSE, 'dkse'),
       ('288f893d-6b6c-4006-9985-5e38c7331c47', 'Demand Change', FALSE, 'dkse'),
       ('86de86d4-bb9c-40e4-9414-e7d7357b6b3c', 'From Storage', FALSE, 'dkse'),
       ('525abdae-e24d-4d13-81d3-aa432aa7167c', 'HelloFresh error', FALSE, 'dkse'),
       ('e5b211ea-06a8-408a-885b-b1de9dc482fa', 'Historical Upload', FALSE, 'dkse'),
       ('9df17087-7c1e-4b74-a434-02b187896ce4', 'Long Lead time', FALSE, 'dkse'),
       ('736ddd94-29f9-442b-82a1-835453488e57', 'Packaging', FALSE, 'dkse'),
       ('8c1b37d3-a534-41e3-9caa-6aef3a70097c', 'Price Error', FALSE, 'dkse'),
       ('b184f08e-9bf9-46de-b494-b0de97c1ec72', 'Regular', FALSE, 'dkse'),
       ('fac8e2ef-e101-49d5-be07-ba9bed454053', 'Repackaging', FALSE, 'dkse'),
       ('f26033ab-dea6-4cd2-8e1e-a4f528be985e', 'Replenishment', FALSE, 'dkse'),
       ('f2f2e525-57ab-4804-83a1-ca8ab70cbe54', 'Stock Error', FALSE, 'dkse'),
       ('6b902d01-7187-4d16-a038-733d215a455b', 'Supplier Error', FALSE, 'dkse'),
       ('8297a2e4-231c-432a-8c82-876bb436456f', 'Stock Error', TRUE, 'dkse'),
       ('dd92dd4a-32fc-429c-b06c-f0718dcdbe61', 'Demand Change', TRUE, 'dkse'),
       ('c9dd434b-aa61-497c-9b97-5c3be1563e3b', 'Error in Ordering', TRUE, 'dkse'),
       ('0f3ce23a-3b05-41f2-b997-85d11cac973c', 'Error in Recipe card', TRUE, 'dkse'),
       ('3241214a-7760-4d71-a2b9-586586dc6b35', 'Long lead time', TRUE, 'dkse'),
       ('17f1542b-866a-4436-92f4-9dd90fc9dcec', 'Supplier error', TRUE, 'dkse'),
       ('15f7efca-189d-4f3b-bac7-4a5521366346', 'Price Correction', TRUE, 'dkse'),
       ('f67d7972-79b6-4d46-be4c-ddc91131ded4', 'Packaging', TRUE, 'dkse'),
       ('2a68380d-53f1-4413-8111-4f58afad8f3c', 'Bulk Order', TRUE, 'dkse'),
       ('be384e01-f6b7-4d7c-9175-0c890478597c', 'Contingency', TRUE, 'dkse'),
       ('5647dc4f-e7eb-4300-b6f5-2916fda52564', 'Historical Upload', TRUE, 'dkse'),
       ('ec883468-2e6e-4b20-9629-a74d56b36346', 'Advance Weekly order', FALSE, 'dkse'),
       ('b6fdba0e-a5e5-45d7-9309-0c2135afbbea', 'Repacking- All in', FALSE, 'dkse'),
       ('76d9989f-9aa1-4334-8e53-d1ccf5a874b8', 'Repacking- Service', FALSE, 'dkse'),
       ('7f9c0894-3ce2-462a-a56e-0042a936c745', 'Internal Transfer', FALSE, 'dkse'),
       ('0d077566-cf54-48d4-82dc-d20f5a4d1da1', 'Raw ingredients (Bulk)', FALSE, 'dkse'),
       ('ef0d9f8d-8101-4f2c-80b8-88786d87011b', 'Multi Week', FALSE, 'dkse'),
       ('14f69ac2-805b-471f-b579-df8392dd64cb', 'OrderPlanningService-SE', TRUE, 'dkse'),
       ('ca25a99b-07b7-4d46-b191-f564f5a0881a', 'Do not show in OPE', FALSE, 'dkse');

-- Clean up dach
DELETE
FROM emergency_order_reason
WHERE emergency_order_reason.market = 'dach';

INSERT INTO emergency_order_reason (uuid, name, disabled, market)
VALUES ('1980efd3-be63-4241-9ac4-04dc5656642a', 'Qualität', TRUE, 'dach'),
       ('8016de0f-0db9-453e-9ce6-5bf400a71d3a', 'Fehlmenge', TRUE, 'dach'),
       ('b7c0a0ae-ec99-4397-acd2-915baba945d7', 'Ausschuss', TRUE, 'dach'),
       ('0adb8479-76da-4a1d-afde-41fdd64935b7', 'Marketplace', TRUE, 'dach'),
       ('df8652ce-f842-4e4b-8912-dbdbfb7bb1a9', 'Sonstiges', TRUE, 'dach'),
       ('6eb7768a-6f01-4663-9abf-9922ec4640c8', 'Historical Upload', TRUE, 'dach'),
       ('312e8ee8-4e5a-48b8-89ea-51b39ce9adc0', 'Merchandising', TRUE, 'dach'),
       ('9c894131-e562-4ce8-bb9a-49e44e29576a', 'Demand change', TRUE, 'dach'),
       ('45b68035-d92b-4efa-9b90-578533ba9125', 'Error in ordering', TRUE, 'dach'),
       ('b0344a3a-633a-49aa-9910-11af8f27c384', 'Error in OT calculation', TRUE, 'dach'),
       ('2ca02b29-cdd9-4486-8fed-55d153e175fa', 'Supplier error', TRUE, 'dach'),
       ('b3edb192-f1a3-48c4-ab84-702b6acdf895', 'Price correction', TRUE, 'dach'),
       ('40cbf30c-5ae3-4172-9cfa-ca2f3ce42719', 'Database correction', TRUE, 'dach'),
       ('236d89b1-3d66-482a-a447-1218ce12d53b', 'Arranged', FALSE, 'dach'),
       ('b37f0030-084d-45c0-b235-b1056ff97386', 'Bulk Order', FALSE, 'dach'),
       ('efaeb77b-5667-4198-92eb-8293bbc17e5d', 'Cross-docking', FALSE, 'dach'),
       ('a7aa1648-e379-4151-b7b2-e797f7fbf184', 'Database Correction', FALSE, 'dach'),
       ('022d3a7e-979d-4530-8e3d-11dd64a60b1e', 'Demand Change', FALSE, 'dach'),
       ('d5f498bb-7a49-44fe-bef2-86cce97c78e1', 'From Storage', FALSE, 'dach'),
       ('9c84fbb6-5eed-4ec0-a66f-69cc2aa880d9', 'HelloFresh error', FALSE, 'dach'),
       ('a761d618-c3f8-4b6d-afe8-981b1bcc4c15', 'Historical Upload', FALSE, 'dach'),
       ('adc155ac-ba34-40c5-b84c-525df4785de5', 'Long Lead time', FALSE, 'dach'),
       ('7af78f1e-6f6e-44f7-8d48-d5fd33eb990a', 'Packaging', FALSE, 'dach'),
       ('f0038e03-fb79-47fa-9531-7449f935a3e5', 'Price Error', FALSE, 'dach'),
       ('c9463a14-8f9c-4a54-b766-13375362b789', 'Regular', FALSE, 'dach'),
       ('612ad8c6-df50-4c91-ac11-a830e1960313', 'Repackaging', FALSE, 'dach'),
       ('146e1474-eaa9-4997-a0c4-9a1d945b20b1', 'Replenishment', FALSE, 'dach'),
       ('a417ba10-c026-4c66-a6d6-6dc8c1fe81e0', 'Sonstiges', FALSE, 'dach'),
       ('82379dfe-d39a-469f-8b14-dad876f6f9f3', 'Stock Error', FALSE, 'dach'),
       ('0dd5892a-1250-411d-a233-115bd27b143a', 'Supplier Error', FALSE, 'dach'),
       ('87c82064-61e1-48f4-ace2-90ecd02a797b', 'Advance Weekly order', FALSE, 'dach'),
       ('ae734d7a-8bcd-4d64-a2e4-3d5096971252', 'Repacking- All in', FALSE, 'dach'),
       ('7c416dd6-402c-4c7e-8946-015f99fcca9c', 'Repacking- Service', FALSE, 'dach'),
       ('2114509f-bba2-4709-8f91-6441f6d303ed', 'Internal Transfer', FALSE, 'dach'),
       ('ddf8fc41-fef4-4c67-b2ef-3e59731e3c16', 'Raw ingredients (Bulk)', FALSE, 'dach'),
       ('099e8ead-e22c-4d81-a882-a2ce91b9b23f', 'Multi Week', FALSE, 'dach'),
       ('817fa948-2428-4204-a87a-5fb8774e6666', 'OrderPlanningService-DE', TRUE, 'dach'),
       ('721a7d5a-3e3c-4833-a92b-5053fae5756d', 'Do not show in OPE', FALSE, 'dach');

-- Cleanup NL
DELETE FROM emergency_order_reason
WHERE emergency_order_reason.market = 'nl';

INSERT INTO emergency_order_reason (uuid, name, disabled, market)
VALUES  ('a8026cd2-e905-4ab7-af51-eb9613a60b33', 'Historical Upload', true, 'beneluxfr'),
        ('73d40bc2-b955-414a-b9f9-1c005901f78e', 'Merchandising', true, 'beneluxfr'),
        ('be7ddb25-e2bd-4dee-becd-4375c947d944', 'Database correction', true, 'beneluxfr'),
        ('f8cc94dd-1bad-4dfe-8d8b-db4e198b0aec', 'Arranged', false, 'beneluxfr'),
        ('80f2a8f0-3bee-4cc7-b21f-a15ff7d3f8ef', 'Bulk Order', false, 'beneluxfr'),
        ('1b1a7f0d-6cc8-4911-a832-db5fd1b74319', 'Cross-docking', false, 'beneluxfr'),
        ('14a0bba6-2c1e-475d-8952-ad6c06ff4e55', 'Database Correction', false, 'beneluxfr'),
        ('33b59648-8418-4057-87d4-31fbaac633ba', 'Demand Change', false, 'beneluxfr'),
        ('d126638a-444d-4d64-a4db-3aa9b5d0255a', 'From Storage', false, 'beneluxfr'),
        ('5b240bd2-7763-4bde-8131-436eed0baae5', 'HelloFresh error', false, 'beneluxfr'),
        ('64fffbb2-cecf-4154-943e-f3d22664d756', 'Historical Upload', false, 'beneluxfr'),
        ('8e544b8c-81a1-4192-a376-dd447210baae', 'Long Lead time', false, 'beneluxfr'),
        ('d07fb1f3-e138-4276-bdac-e07c20c77a0b', 'Packaging', false, 'beneluxfr'),
        ('13ac222e-05ba-4cc9-85c8-6cb04e805723', 'Price Error', false, 'beneluxfr'),
        ('1bfecf52-c3d9-44e4-a763-dfb0b6960324', 'Regular', false, 'beneluxfr'),
        ('3b695761-40b5-44cb-af13-ff379371d795', 'Repackaging', false, 'beneluxfr'),
        ('fcfeb0db-8816-4806-9fdb-7c8a96386647', 'Replenishment', false, 'beneluxfr'),
        ('059d61dc-1d88-41bd-9b3c-49fd1efad4ea', 'Stock Error', false, 'beneluxfr'),
        ('f2a01274-0fe4-4cea-bada-5bc9ac90b58e', 'Supplier Error', false, 'beneluxfr'),
        ('6298404b-d56b-49fd-924c-4d3ad5bc8049', 'Stock error', true, 'beneluxfr'),
        ('4fa82f8c-d36e-4f77-9486-ea7a3001ee64', 'Demand change', true, 'beneluxfr'),
        ('5360bac7-2904-4e32-ae35-f807b823e4cc', 'Error in ordering', true, 'beneluxfr'),
        ('c12dda55-7153-4a9b-a756-f94ab1eb7daf', 'Error in recipe card', true, 'beneluxfr'),
        ('3aa76fe3-7920-4ffb-b639-002ddaa3ed9f', 'Non-food', true, 'beneluxfr'),
        ('52c7f76c-f28d-43c8-bd8a-a6c1b2d0a1bf', 'Supplier error', true, 'beneluxfr'),
        ('441d3347-dac8-4f48-a4fe-74fab480bc3b', 'Multi Week', false, 'beneluxfr'),
        ('b8fc944c-3162-4a8c-9b11-600b38fa5c78', 'Raw ingredients (Bulk)', false, 'beneluxfr'),
        ('d0a51cc5-5c72-41b2-b984-223058d9cb84', 'Internal Transfer', false, 'beneluxfr'),
        ('2d60b2f2-6df7-4864-a8ca-072e3e3fa984', 'Repacking- Service', false, 'beneluxfr'),
        ('faad2e94-c048-4e98-82dc-46cb8e944a06', 'Repacking- All in', false, 'beneluxfr'),
        ('285a25c0-dd20-4faa-a226-a6a5d72692f0', 'Advance Weekly order', false, 'beneluxfr'),
        ('d5513969-c8e6-4875-af35-5347ab1eb331', 'OrderPlanningService-NL', true, 'beneluxfr'),
        ('5e939412-0b90-4cbe-abb9-67d89a70d4c6', 'Do not show in OPE', false, 'beneluxfr');
