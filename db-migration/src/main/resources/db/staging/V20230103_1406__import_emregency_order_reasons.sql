INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('dcb862fc-1386-455b-8f9c-8785dde126fa', 'Supplemental Add-On', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('695e799f-f278-4c8e-a561-435d42ec28c0', 'Pre-PO Shipment Error', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('970fe7cb-038b-44a7-aa4b-4254fd645678', 'Bulk for Auto-bagger', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('817962c4-fcb4-4e35-893d-1fe67a9a87d0', 'Film for Auto-bagger', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('29650dc3-312a-4a35-ae8e-33cb5179b23d', 'Delivery Date Change', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('5cb71b02-73e5-45b3-800c-7a222941a459', 'Truck Capacity Split', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('14002fb1-0c96-413b-839e-93cc5cc300aa', 'Add-Ons', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('53ed3f6b-e45c-4acd-a1fd-9028ef248d46', 'Database correction', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('a255985b-ab93-441c-bd7c-d5a858d287c6', 'Thanksgiving', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('513f5acd-4490-48a1-80ce-e46c047c7b2a', 'Test PO', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ab955e99-7148-47c9-89f7-c5c021a7a430', '3PL - NJ Uncommon Carriers', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('5fbca937-b0c7-42f8-9fd9-304c5a043db5', '3PL - TX Southwest', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('5c90ede9-72a3-44e8-838d-98fcb252de15', 'DC Director Approval', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('283bc875-8dec-4e08-8755-9f963f1c2655', 'Discard/Yield Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2deb4031-aa5b-4e23-98fe-f5c43af9ab20', 'Rejection Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('8ea5ad71-0307-405a-b798-8efd1f4bb6a5', 'Packaging', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('71955fe4-67b6-4da7-8d99-a38436e8ecbe', 'Backhaul', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('abf1d220-117a-4c42-8765-fd2cf326ed72', 'M&D are testing!', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('082483ba-37d4-47d6-a535-aa2931a8dae1', 'Safety Stock', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('486de84a-68ff-476c-b451-8105c4ac338a', 'Retail', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ead622f7-13a1-4bb5-821c-c4c85a1a2acd', 'HF-Managed Freezing', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('b683abe2-59b3-4360-9957-4f7a60e1e3fa', 'Forecast Increase Order', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('eb671356-61dc-4edb-a5d9-4917904954df', 'HelloFresh error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('01068baa-ff4d-40a4-9e48-6fb0a5449426', 'Late delivery', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('eb8e97e6-9a71-4def-9b36-a4748c134311', 'Quality issue', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('41db071e-4c55-4860-8215-8d27fe36a526', 'Short delivery', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('1f327960-e043-4580-a50b-d38e19e1164a', 'Supply issue', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('667b986f-2fc3-4471-86cd-21d29550871e', 'Packaging', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('1e2ccabe-92f9-4de4-bd3b-d583a535dd3e', 'Marketing', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('c62e3b5f-9bed-48c1-9cdd-80a8c7e55d7b', 'Merchandising', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2f24e24d-b38f-43de-9760-732e8bfa46be', 'Database correction', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('754c6736-ecba-485f-a255-b64258e91078', 'Add-ons', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('a8ff0fca-c84c-4431-b577-f57e4d39268f', 'Arranged', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('af61f757-132a-4b25-ab20-59b912b354ad', 'Bulk Order', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('803613dd-13c4-4c58-bbc6-32da1eafde5b', 'Cross-docking', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('4bcde134-7717-47e9-9a74-5099d4d50750', 'Database Correction', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('f4c8bde0-fd34-4da4-9932-40a9ac7ab264', 'Demand Change', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('fbdfcf11-ae3a-413c-a501-f48e4a374100', 'From Storage', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('74bf4a05-d31a-4c71-ae0b-e1db801c0388', 'HelloFresh error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('*************-448a-87cd-bba60a85cb03', 'Historical Upload', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('25930fde-0513-4514-80c3-f8f0dbbff8d2', 'Long Lead time', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('c2303641-8655-485e-9bcc-b7a87af2370a', 'Packaging', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('f96cf15f-d3e4-4d0e-9680-b8eb828bc782', 'Price Error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('87b78f57-8436-434b-a91a-718f114ae801', 'Regular', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3a9c9dff-74ff-4f1c-a65a-48d4b372a4c4', 'Repackaging', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('e95f384a-9e6b-4f61-8481-ac624058bc4b', 'Replenishment', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('93a2c24d-7ee9-4c21-a11e-5873086c8d89', 'Stock Error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('4bf600d9-a951-449f-9504-67170d417052', 'Supplier Error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('9ea86298-ccaa-49ad-a7e7-66404c121771', 'Supply Issue', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('597e7ea1-a2dc-4fbf-baf9-9ff7476a9faa', 'Test PO', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('8b7d2bba-9d3c-454d-9e15-c329c8bb4a33', 'This is a new reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('8ee25cc3-8342-4862-94c6-4a9d5e417b43', 'This is a new reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('9783acb5-11c2-42b6-8472-63113b16beae', 'This is a new reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('9d6b19b3-ac61-400e-bbb2-b7d1865aa31f', 'Thiss is a new reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ef343991-ae98-4797-938e-6a56cfceae44', 'Thiss is a new reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('9395fa57-2224-4581-a0ca-82eb60c99831', '', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('efd41cd4-0560-4595-a17f-efcf160fc846', '', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('5f59f0e2-e13f-4409-847a-5b59a28563d0', 'This is a new reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('83e0c7ea-39f5-4716-9e4e-78d66701eb25', '', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('68c9897c-2ff4-4356-a9d0-d6805e7101aa', 'This is a new reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('17787e33-df0e-49f0-b2e5-c58e8eabb6eb', 'This is a new reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ad121007-c655-4390-9bcf-5f8ba4821495', 'Charity', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('4af72498-b6ff-45b1-a95a-0a7a3a8b3e51', 'Multi Week', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('0f8b93e7-9425-4eed-86bf-2dc5878b6ea9', 'Raw ingredients (Bulk)', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('bc1ebaad-0ad0-4ea2-bc60-f8930f76497d', 'Internal Transfer', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('f9a38fd5-f959-4def-808e-d20829bbe34e', 'Repacking- Service', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('28ea44ef-02c7-4889-8d65-f115ec316c1a', 'Repacking- All in', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('fa9fc4a3-d3e1-414c-a6b8-183626eb006d', 'Advance Weekly order', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('6a312d33-bdd2-410b-9f27-2aea9f2f67f2', 'OrderPlanningService-US', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('72b25ea8-0493-41a3-a629-0cd1c556cd10', 'Offsite Production - MD', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('4149b885-32f1-4689-8ba4-6e90571c71c6', 'Offsite Production - PA', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('23c2f84e-d642-4a5c-87a9-9b303d1da3e0', 'Offsite Production - FreshPoint', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('c847c722-d2c5-4951-a30d-3130d05ab39d', 'Offsite Production - Standard Meats', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('87558ab7-7c88-464d-a8dd-aa584dc8ab79', 'Offsite Production - Deen''s Meats', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('1c5c1339-9502-4c18-9a76-5237c6f7a2b6', 'Offsite Production - GoCheetah', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('52418e51-4de7-4aba-b38c-817fd23888f3', 'Offsite Production - NW', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('472df9bf-ad3f-4a17-8442-4d96a1bc278e', 'Offsite Production - DL', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('df9f6022-2da7-4dc6-a475-81f67f5e3dad', 'Offsite Production - DL', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('fdd67234-56dd-4fa2-a3ed-b80d1bafd818', 'Retail - HK', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2e3b4646-18c6-454a-b0c6-033cb1cd370c', 'Retail - Indy', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('a01f497b-293a-4452-9251-f3396b433215', 'Retail - GFK', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('4cd53272-3e3d-48bf-910f-377c40b4321c', 'Retail - UCC', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('611d6185-e7fc-4f45-8853-de7d85a35d12', 'Retail - Burlington', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('b682bb90-78c2-496e-a58d-528bee82c381', 'Retail - Win', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('44e0fe23-7f2c-4297-8b25-9c87d33942ef', 'Case Shortage Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('10d36aeb-e459-482e-8c5a-b4cfa3e721f7', 'Holiday', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ba384bc8-c98c-481c-9923-bb1312c1e63f', 'Communicated Shortage Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('6fd10cbb-7895-4be3-bc00-651e8b51b5fa', 'Unexpected Shortage Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2d09f981-95ed-447c-87d7-aea4f0d8dd44', 'Future Week Product Pull Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('dfb87f19-4c44-47ce-8f08-2c8ae32e9ff2', '3PW Inventory Pull', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('387c0985-ce26-4a8e-8137-d470e6206c13', 'Marketing', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2799b3ef-62cc-4681-b5e2-b06a60fa5222', 'Insourcing', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('329e2068-5b8c-4ce9-b851-22fc25004edf', 'Charity - T2T', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('f13a6c1b-9608-414c-9d4d-4d61b75943dc', 'Charity - SoNY', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3665c0cb-5b00-4c88-b766-19133a5b3f55', 'Charity - SHA', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3ac7676c-4aca-4a3d-a80a-d5aed68b9381', 'Charity - TAFB', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('27674340-a8f5-43a4-a675-ec92d2d6ab14', 'Charity - SMFB', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('7a82a2d7-e7ef-461b-bec6-7f3398a8f970', 'Unexpected Shortage - FedEx/UPS', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('7ba16eac-31ac-4079-91fd-af0c4b048bbb', 'API test emergency reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('1b1612d4-df82-4314-946d-7147c5f8d411', 'API test emergency reason', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('9afe4ed7-b551-4632-877d-b6a9a7a88390', 'Initial Ordering Non-Emergency', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('b134c334-0029-4fb3-a0c6-f6e6a945e471', 'Sauce Remake', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('a122f100-5381-4de6-9cb6-a76d7826bf86', 'Bulk SKU Order', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('a227eaca-cb04-4676-a09d-ed4c3cf8d4b3', 'Emergency DC Transfer', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('09660c8b-0a05-49fa-9232-65f18e3e8b7e', 'Carrier Delay Supplemental Add On', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('f0d9aa3e-7c34-430c-9274-27356647ebab', 'Vendor Delay Supplemental Add On', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('1ca39b01-1902-4b28-b84f-e0ad73567ea3', 'Autobagger PO', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('4355bc1e-8783-4151-b962-0e98f5b5d840', 'Specialty Box', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('9bcfefc4-3f0c-494f-8451-9aeec8c1f505', 'Protein Offsite Cold Storage', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('e0eaa57b-be80-4f97-b51c-660249edd015', 'Vendor Error Split Delivery', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('25bdf689-f0bd-4c71-a361-f48ca7e00490', 'Charity - WDW', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('f6f9e74a-085d-47c7-9b45-3e6b0606d71d', 'OrderPlanningService-GB', 'gb');
