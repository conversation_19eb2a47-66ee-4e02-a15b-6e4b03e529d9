INSERT INTO distribution_center (code, name, market, status)
VALUES ('R<PERSON>', 'HelloFresh UK - Retail', 'gb', 'ACTIVE'),
       ('MK', 'HelloFresh UK - Marketing', 'gb', 'ACTIVE'),
       ('MG', 'HelloFresh UK - Merchandising', 'gb', 'ACTIVE'),
       ('GR', 'HelloFresh UK - The Granary GR', 'gb', 'ACTIVE'),
       ('BV', 'HelloFresh UK - The Beehive BV', 'gb', 'ACTIVE'),
       ('WW', 'Offsite Storage', 'gb', 'ACTIVE'),
       ('MW', 'Minor, Weir and Willis Limited', 'gb', 'ACTIVE'),
       ('WF', 'Watts Farms Packers Ltd', 'gb', 'ACTIVE'),
       ('FA', 'Farningham', 'gb', 'ACTIVE'),
       ('DR', 'DOMRUSGBTEST', 'gb', 'ACTIVE'),
       ('UA', 'Wates Way', 'gb', 'ACTIVE'),
       ('TO', 'HelloFresh UK - The Orchard TO', 'gb', 'ACTIVE'),
       ('FQ', 'UK - The Farm', 'gb', 'ACTIVE'),
       ('WM', 'Windmill', 'gb', 'ACTIVE');

INSERT INTO dc_address (dc_code, address, number, zip, city, state, company, type)
VALUES ('FQ', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', null, 'DELIVERY'),
       ('WM', 'UNIT 8', 'SMARTPARC', 'DE21 7BS', 'SPONDON', 'DERBY', null, 'DELIVERY'),
       ('MK', 'Unit 3', 'Chalker Way', 'OX16 4XD', 'Banbury', 'Oxfordshire', null, 'DELIVERY'),
       ('DR', '18A', 'Auto Test Address', '12345', 'Automation Test City', 'Auckland', null, 'DELIVERY'),
       ('BV', 'Unit 1', 'St George''s Way Bermuda Park', 'CV10 7JS', 'Nuneaton', 'Warwickshire', null, 'DELIVERY'),
       ('TO', 'Unit 1', 'Collaboration Way Smartparc Food Hub', 'DE21 7UL', 'Spondon', 'Derby', null, 'DELIVERY'),
       ('UA', '9-10', 'Wates Way', 'OX16 3TS', 'Banbury', 'Oxfordshire', null, 'DELIVERY'),
       ('GR', 'Unit 3', 'Chalker Way', 'OX16 4XD', 'Banbury', 'Oxfordshire', null, 'DELIVERY'),
       ('MG', 'Unit 3', 'Chalker Way', 'OX16 4XD', 'Banbury', 'Oxfordshire', null, 'DELIVERY'),
       ('RE', 'Unit 3', 'Chalker Way', 'OX16 4XD', 'Banbury', 'Oxfordshire', null, 'DELIVERY'),
       ('MW', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY'),
       ('WF', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY'),
       ('FA', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY'),
       ('WW', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY'),
       ('RE', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('MK', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('MG', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('GR', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('BV', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('WW', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('MW', '206', 'Deykin Avenue', 'B6 7BH', 'Birmingham', 'West Midlands', 'Minor, Weir and Willis Limited', 'BILLING'),
       ('WF', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('FA', 'Hill Farm', 'Farningham Hill Road', 'DA4 0JR', 'Farningham', 'Kent', 'Watts Farms Packers Ltd', 'BILLING'),
       ('DR', 'Level 29', 'Auto Test Address', '12345', 'Auckland Central', 'Auckland', 'HelloFresh', 'BILLING'),
       ('UA', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('TO', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('FQ', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING'),
       ('WM', '60', 'Worship St', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING');
