UPDATE emergency_order_reason
SET name = '3PL Backhaul'
WHERE uuid = '71955fe4-67b6-4da7-8d99-a38436e8ecbe';

UPDATE emergency_order_reason
SET name = 'Standard Non-Emergency or Safety Stock order'
WHERE uuid = '9afe4ed7-b551-4632-877d-b6a9a7a88390';

UPDATE emergency_order_reason
SET name = 'Forecast Increase Supplemental Order'
WHERE uuid = 'b683abe2-59b3-4360-9957-4f7a60e1e3fa';

UPDATE emergency_order_reason
SET name = 'Emergency UPS/FedEx'
WHERE uuid = '7a82a2d7-e7ef-461b-bec6-7f3398a8f970';

INSERT INTO emergency_order_reason(uuid, name, market)
VALUES ('beb20e50-3e66-428c-99c0-95d41bdfe145', 'Unexpected Shortage Replacement- Procurement Driven', 'us'),
       ('7cb27878-44e2-44ca-9777-1a7d1290c89f', 'DC Communicated Shortage Replacement', 'us'),
       ('b51ee3a2-a377-4b4d-b619-527a9287c9ed', 'Unexpected Shortage Replacement- Transportation Driven', 'us'),
       ('7ae29929-0219-4166-9789-854013e8fcea', 'Unexpected Shortage Replacement- Vendor Driven', 'us');
