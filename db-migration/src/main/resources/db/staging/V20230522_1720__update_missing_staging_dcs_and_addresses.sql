INSERT INTO distribution_center(code, status, name, market)
VALUES ('YX', 'ACTIVE', 'AutoTestDc', 'us'),
       ('YY', 'ACTIVE', 'AutoTestDc', 'us'),
       ('ZP', 'ACTIVE', 'AutoTestDc', 'us'),
       ('ZZ', 'ACTIVE', 'GBWeTrust', 'us');

INSERT INTO dc_address (dc_code, address, number, zip, city, state, company, type, country_code)
VALUES ('YX', 'Auto Test Address', '18A', '12345', 'Automation Test City', 'Test City', null, 'DELIVERY', 'US'),
       ('YY', 'Auto Test Address', '18A', '12345', 'Automation Test City', 'Auckland', null, 'DELIVERY', 'US'),
       ('ZP', 'Auto Test Address', '18A', '12345', 'Automation Test City', 'Auckland', null, 'DELIVERY', 'US'),
       ('ZZ', 'Auto Test Address', '18A', '12345', 'Automation Test City', 'Auckland', null, 'DELIVERY', 'US'),
       ('YX', 'Auto Test Address', 'Level 29', '12345', 'Test City', 'Test State', 'HelloFresh', 'LEGAL', 'US'),
       ('YY', 'Auto Test Address', 'Level 29', '12345', 'Auckland Central', 'Auckland', 'HelloFresh', 'LEGAL', 'US'),
       ('ZP', 'Auto Test Address', 'Level 29', '12345', 'Auckland Central', 'Auckland', 'HelloFresh', 'LEGAL', 'US'),
       ('ZZ', 'Auto Test Address', 'Level 29', '12345', 'Auckland Central', 'Auckland', 'HelloFresh', 'LEGAL', 'US');
