insert into dc_address (dc_code, number, address, zip, city, state, company, type, country_code)
values ('FQ', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', null, 'DELIVERY', 'GB'),
       ('FS', '966', 'Corporate Blvd', '60502', 'Aurora', 'IL', null, 'DELIVERY', 'US'),
       ('PP', '2010', 'S. Great Southwest Pkwy', '75051', 'Grand Prairie', 'TX', null, 'DELIVERY', 'US'),
       ('NA', '2645', 'Mitchell Ave', '18103', 'Allentown', 'PA', null, 'DELIVERY', 'US'),
       ('DF', '13100 E.', 'ACME / FIDELITY PAPER COMPANY, C/O HOWARD LOGISTICS, INC., 38TH AVE.', '80239', 'Denver', 'Colorado', null, 'DELIVERY', 'US'),
       ('AF', '960', 'Corporate Blvd', '60502', 'Aurora', 'IL', null, 'DELIVERY', 'US'),
       ('FV', '966', 'Corporate Blvd.', '60502', 'Aurora', 'IL', null, 'DELIVERY', 'US'),
       ('EZ', '1850', 'S 71st Avenue', '85043', 'Phoenix', 'AZ', null, 'DELIVERY', 'US'),
       ('GM', '200', 'King Mill Rd.', '30253', 'McDonough', 'GA', null, 'DELIVERY', 'US'),
       ('TN', '135', 'Logistics Drive', 'TN 37090', 'Lebanon', 'Tennessee', null, 'DELIVERY', 'US'),
       ('ZE', '847', 'S. River Street', '60506', 'Aurora', 'IL', null, 'DELIVERY', 'US'),
       ('PF', '150', '1432 Wainwright Way', '75007', 'Carrollton', 'TX', null, 'DELIVERY', 'US'),
       ('CW', '4322', 'Moreland Ave', '30288', 'Conley', 'GA', null, 'DELIVERY', 'US'),
       ('CM', '1', 'DONATION PARTNER Rd', '12345', 'Placeholder', 'CO', null, 'DELIVERY', 'US'),
       ('JO', '1101', 'Cherry Hill Road', '60433', 'Joliet', 'Illionis', null, 'DELIVERY', 'US'),
       ('WM', 'UNIT 8', 'SMARTPARC', 'DE21 7BS', 'SPONDON', 'DERBY', null, 'DELIVERY', 'GB'),
       ('GD', '14170', 'West Yuma Rd', '85338', 'Goodyear', 'AZ', null, 'DELIVERY', 'US'),
       ('SR', '489', 'Pratt Rd', '53570', 'Monticello', 'WI', null, 'DELIVERY', 'US'),
       ('NF', '901', 'Murray Rd.', '07006', 'East Hanover', 'NJ', null, 'DELIVERY', 'US'),
       ('AM', '18101 E.', 'Colfax Ave', '80011', 'Aurora', 'CO', null, 'DELIVERY', 'US'),
       ('FL', '3131', 'Caruso Court', '32806', 'Orlando', 'Florida', null, 'DELIVERY', 'US'),
       ('IO', '581', 'Territorial Dr', '60440', 'Bolingbrook', 'Illinois', null, 'DELIVERY', 'US'),
       ('FO', '4555', 'S Racine Ave', '60609', 'Chicago', 'IL', null, 'DELIVERY', 'US'),
       ('LH', '8001 E', '88TH Ave', '80640', 'Henderson', 'CO', null, 'DELIVERY', 'US'),
       ('TF', '960', 'Corporate Blvd', '60502', 'Aurora', 'Illionis', null, 'DELIVERY', 'US'),
       ('FD', '2372 W', 'Indian Trail', '60506', 'Aurora', 'Illinois', null, 'DELIVERY', 'US'),
       ('FE', '340', 'Shore Dr', '60527', 'Burr Ridge', 'Illinois', null, 'DELIVERY', 'US'),
       ('FF', '1325', 'Ensell Rd', '60047', 'Lake Zurich', 'Illinois', null, 'DELIVERY', 'US'),
       ('FH', '8424', '47th St', '60534', 'Lyons', 'Illinois', null, 'DELIVERY', 'US'),
       ('TI', '2700', 'Market Street', '750621', 'Irving', 'TX', null, 'DELIVERY', 'US'),
       ('FK', '2', 'Plum St', '41076', 'Wilder', 'Kentucky', null, 'DELIVERY', 'US'),
       ('MK', 'Unit 3', 'Chalker Way', 'OX16 4XD', 'Banbury', 'Oxfordshire', null, 'DELIVERY', 'GB'),
       ('RC', '--', '--', '--', '--', '--', null, 'DELIVERY', 'US'),
       ('FN', '396N', 'Mill Road', '08360', 'Vineland', 'NJ', null, 'DELIVERY', 'US'),
       ('DC', '3151', 'Regatta Blvd', '94804', 'Richmond', 'CA', null, 'DELIVERY', 'US'),
       ('WD', '11501', 'North Freeway', '76177', 'Fort Worth', 'Texas', null, 'DELIVERY', 'US'),
       ('LP', '367', 'Long Creek Road', 'TX 75182', 'Sunnyvale', 'USA', null, 'DELIVERY', 'US'),
       ('FZ', '10205', 'W Roosevelt Street', '85323', 'Avondale', 'AZ', null, 'DELIVERY', 'US'),
       ('AZ', '1850', 'S 71st Avenue', '85043', 'Phoenix', 'AZ', null, 'DELIVERY', 'US'),
       ('HN', '445', 'Rising Sun Rd', '08505', 'Bordentown', 'NJ', null, 'DELIVERY', 'US'),
       ('GS', '580', 'Port Carteret Dr.', 'NJ 07008', 'Carteret', 'USA', null, 'DELIVERY', 'US'),
       ('HD', '1005', 'N Cockrell Hill Rd', '75211', 'Dallas', 'USA', null, 'DELIVERY', 'US'),
       ('BV', 'Unit 1', 'St George''s Way Bermuda Park', 'CV10 7JS', 'Nuneaton', 'Warwickshire', null, 'DELIVERY', 'GB'),
       ('RM', '23', 'Mack Drive', '08817', 'Edison', 'New Jersey', null, 'DELIVERY', 'US'),
       ('TO', 'Unit 1', 'Collaboration Way Smartparc Food Hub', 'DE21 7UL', 'Spondon', 'Derby', null, 'DELIVERY', 'GB'),
       ('MM', '1', 'DONATION PARTNER Rd', '12345', 'Placeholder', 'NY', null, 'DELIVERY', 'US'),
       ('WL', '14', 'Euclid St', '07105', 'Newark', 'NJ', null, 'DELIVERY', 'US'),
       ('UA', '9-10', 'Wates Way', 'OX16 3TS', 'Banbury', 'Oxfordshire', null, 'DELIVERY', 'GB'),
       ('RT', '145A', 'Talmadge Road', '08817', 'Edison', 'New Jersey', null, 'DELIVERY', 'US'),
       ('SW', '1130', 'Commerce Blvd, Suite 125', '08085', 'Swedesboro', 'NJ', null, 'DELIVERY', 'US'),
       ('GR', 'Unit 3', 'Chalker Way', 'OX16 4XD', 'Banbury', 'Oxfordshire', null, 'DELIVERY', 'GB'),
       ('MG', 'Unit 3', 'Chalker Way', 'OX16 4XD', 'Banbury', 'Oxfordshire', null, 'DELIVERY', 'GB'),
       ('GA', '510', 'International Park', '30265', 'Newnan', 'USA', null, 'DELIVERY', 'US'),
       ('NJ', '60', 'Lister Avenue', '07105', 'Newark', 'NJ', null, 'DELIVERY', 'US'),
       ('RE', 'Unit 3', 'Chalker Way', 'OX16 4XD', 'Banbury', 'Oxfordshire', null, 'DELIVERY', 'GB'),
       ('EC', '2041', 'Factory St', '94801', 'Richmond', 'CA', null, 'DELIVERY', 'US'),
       ('EV', '8', 'Vreeland Ave', '07512', 'Totowa', 'New Jersey', null, 'DELIVERY', 'US'),
       ('ET', '1025', 'Post & Paddock St.', '75050', 'Grand Prairie', 'TX', null, 'DELIVERY', 'US'),
       ('MW', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'GB'),
       ('WF', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'GB'),
       ('FA', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'GB'),
       ('FP', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('WW', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'GB'),
       ('LX', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('GL', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('MF', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('FG', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('FC', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('FT', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('FJ', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('CP', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('GW', '5', 'Obermatten', '5742', 'Kölliken', 'Aargau', null, 'DELIVERY', 'US'),
       ('PI', '1717', 'Pillsbury Road', '18041', 'East Greenville', 'PA', null, 'DELIVERY', 'US'),
       ('CO', '20761', 'E 35th Dr.', '80011', 'Aurora', 'CO', null, 'DELIVERY', 'US'),
       ('NJ', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('RE', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('MK', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('MG', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('RC', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('GR', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('FN', '396N', 'Mill Road', '08360', 'Vineland', 'NJ', 'HelloFresh', 'BILLING', 'US'),
       ('DC', '3151', 'Regatta Blvd', '94804', 'Richmond', 'CA', 'HelloFresh', 'BILLING', 'US'),
       ('GS', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('BV', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('WW', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('LX', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('MF', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('GL', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('MW', '206', 'Deykin Avenue', 'B6 7BH', 'Birmingham', 'West Midlands', 'Minor, Weir and Willis Limited', 'BILLING', 'GB'),
       ('FP', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('FC', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('CP', '28', 'Liberty St,10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('FT', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('WF', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('FJ', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('FA', 'Hill Farm', 'Farningham Hill Road', 'DA4 0JR', 'Farningham', 'Kent', 'Watts Farms Packers Ltd', 'BILLING', 'GB'),
       ('GW', '28', 'Liberty St,10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('FG', '28', 'Liberty St,10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('PI', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('WL', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('WD', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('TI', '28', 'Liberty St,10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('EV', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('EC', '28', 'Liberty St,10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('LP', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('ET', '28', 'Liberty St,10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('HD', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('GA', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('FZ', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('UA', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('RM', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('RT', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('IN', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('AZ', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('IJ', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('TO', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('IS', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('IC', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('IB', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('MM', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('IO', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('CO', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('FD', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING', 'US'),
       ('HN', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('SW', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('FE', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING', 'US'),
       ('FF', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING', 'US'),
       ('FH', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING', 'US'),
       ('FK', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING', 'US'),
       ('SR', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('PP', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('FQ', '60', 'Worship Street', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('FS', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('PF', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('NA', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('GM', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('CW', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('EZ', '10th Floor', '28 Liberty St', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('FO', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('AF', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('TN', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('CM', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('AM', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('LH', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('FV', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING', 'US'),
       ('DF', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('IW', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('IX', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('FL', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('TF', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('ZE', '170', 'S Water Street', '60510', 'Suite A Batavia', 'IL', 'HelloFresh', 'BILLING', 'US'),
       ('WM', '60', 'Worship St', 'EC2A 2EZ', 'Hackney', 'London', 'HelloFresh', 'BILLING', 'GB'),
       ('JO', '170 S', 'Water Street', '60510', 'Batavia', 'Illionis', 'Factor 75 LLC', 'BILLING', 'US'),
       ('NF', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING', 'US'),
       ('GD', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING', 'US'),
       ('IF', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING', 'US');
