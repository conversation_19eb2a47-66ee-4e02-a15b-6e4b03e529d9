create table dc_address
(
    id         uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    dc_code    varchar(2),
    number     varchar not null,
    address    varchar not null,
    zip        varchar not null,
    city       varchar not null,
    state      varchar not null,
    company    varchar,
    type       varchar,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

create table distribution_center
(
    code       varchar(2) not null primary key,
    status     varchar    not null,
    name       varchar    not null,
    market     varchar    not null,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE dc_address
    ADD CONSTRAINT fk_distribution_center FOREIGN KEY (dc_code) REFERENCES distribution_center (code);

INSERT INTO distribution_center (code, name, market, status)
VALUES ('NJ', 'HelloFresh United States - NJ', 'us', 'ACTIVE'),
       ('EZ', 'EveryPlate - Arizona', 'us', 'ACTIVE'),
       ('GD', 'Goodyear (AZ)', 'us', 'ACTIVE'),
       ('TF', 'Fidelity - Factor Chicago', 'us', 'ACTIVE'),
       ('RC', 'Retail - to Copacker', 'us', 'ACTIVE'),
       ('ZE', 'Zenearth', 'us', 'ACTIVE'),
       ('FN', 'First Choice Freezer & Cold Storage - NJ', 'us', 'ACTIVE'),
       ('DC', 'Dreisbach Cold Storage', 'us', 'ACTIVE'),
       ('SC', 'Southwest Cold Storage', 'us', 'ACTIVE'),
       ('RD', 'Retail - HelloFresh DC', 'us', 'ACTIVE'),
       ('SF', 'HelloFresh United States - SF', 'us', 'ACTIVE'),
       ('UC', 'HelloFresh United States - Uncommon Carriers', 'us', 'ACTIVE'),
       ('US', 'HelloFresh United States - Southwest', 'us', 'ACTIVE'),
       ('TX', 'HelloFresh United States - TX', 'us', 'ACTIVE'),
       ('TN', 'Lebanon, TN', 'us', 'ACTIVE'),
       ('LX', 'Lineage Logistics', 'us', 'ACTIVE'),
       ('GL', 'GreenLeaf', 'us', 'ACTIVE'),
       ('MF', 'East Coast Fresh', 'us', 'ACTIVE'),
       ('HN', 'Hermann Warehouse', 'us', 'ACTIVE'),
       ('FP', 'Fidelity - Pennsylvania', 'us', 'ACTIVE'),
       ('FC', 'Fidelity - California', 'us', 'ACTIVE'),
       ('FT', 'Fidelity - Texas', 'us', 'ACTIVE'),
       ('FJ', 'Fidelity - New Jersey', 'us', 'ACTIVE'),
       ('CP', 'Castellini Produce', 'us', 'ACTIVE'),
       ('GW', 'FW Logistics - GA', 'us', 'ACTIVE'),
       ('FG', 'Fidelity - Georgia', 'us', 'ACTIVE'),
       ('GA', 'HelloFresh United States - GA', 'us', 'ACTIVE'),
       ('PI', 'Pratt Retail Specialties', 'us', 'ACTIVE'),
       ('TT', 'United States - T2', 'us', 'ACTIVE'),
       ('DM', 'Deen''s Meats', 'us', 'ACTIVE'),
       ('NX', 'NFI', 'us', 'ACTIVE'),
       ('JK', 'LSG Sky Chefs - JFK', 'us', 'ACTIVE'),
       ('FW', 'FreshPoint', 'us', 'ACTIVE'),
       ('DL', 'LSG Sky Chefs', 'us', 'ACTIVE'),
       ('PA', 'Primo Produce', 'us', 'ACTIVE'),
       ('JO', 'Joliet Illinois', 'us', 'ACTIVE'),
       ('TI', 'United States - TX3', 'us', 'ACTIVE'),
       ('AZ', 'United States - AZ', 'us', 'ACTIVE'),
       ('WL', 'Wuhl Shafman Lieberman', 'us', 'ACTIVE'),
       ('WD', 'Woods Distribution', 'us', 'ACTIVE'),
       ('LP', 'Lineage - Packaging', 'us', 'ACTIVE'),
       ('ET', 'EveryPlate - Texas', 'us', 'ACTIVE'),
       ('EC', 'Everyplate - California', 'us', 'ACTIVE'),
       ('EV', 'EveryPlate-Totowa', 'us', 'ACTIVE'),
       ('GS', 'Garden State Cold Storage', 'us', 'ACTIVE'),
       ('HD', 'Hardie''s Direct', 'us', 'ACTIVE'),
       ('FZ', 'Fidelity - Arizona', 'us', 'ACTIVE'),
       ('RM', 'Romark Mach Drive', 'us', 'ACTIVE'),
       ('RT', 'Romark Talmadge', 'us', 'ACTIVE'),
       ('IS', 'Ice Safety Stock - AZ', 'us', 'ACTIVE'),
       ('IC', 'Ice Safety Stock - CA', 'us', 'ACTIVE'),
       ('IB', 'Ice Safety Stock - GA', 'us', 'ACTIVE'),
       ('IN', 'Ice Safety Stock - NJ', 'us', 'ACTIVE'),
       ('IJ', 'Ice Safety Stock - TX', 'us', 'ACTIVE'),
       ('IO', 'United States - Bolingbrook Illinois', 'us', 'ACTIVE'),
       ('FD', 'Factor Aurora', 'us', 'ACTIVE'),
       ('FE', 'Factor Burr Ridge', 'us', 'ACTIVE'),
       ('FF', 'Factor Lake Zurich', 'us', 'ACTIVE'),
       ('FH', 'Factor Lyons', 'us', 'ACTIVE'),
       ('FK', 'Factor c/o Castellini 3PL', 'us', 'ACTIVE'),
       ('MS', 'Mesa Cold Storage', 'us', 'ACTIVE'),
       ('PS', 'Pacific Seafood Company', 'us', 'ACTIVE'),
       ('SR', 'Sugar River Cold Storage', 'us', 'ACTIVE'),
       ('PP', 'ProPac', 'us', 'ACTIVE'),
       ('MM', 'Meals with Meaning', 'us', 'ACTIVE'),
       ('FS', 'Fidelity - Illinois', 'us', 'ACTIVE'),
       ('PF', 'Precise Food Ingredients Inc', 'us', 'ACTIVE'),
       ('NA', 'Lineage Allentown (Ambient)', 'us', 'ACTIVE'),
       ('GM', 'Lineage McDonough', 'us', 'ACTIVE'),
       ('CW', 'Castellini 3PW', 'us', 'ACTIVE'),
       ('FL', 'Smart Warehousing - FL', 'us', 'ACTIVE'),
       ('NF', 'Fidelity - SWE', 'us', 'ACTIVE'),
       ('FO', 'Factor c/o Testa 3PW', 'us', 'ACTIVE'),
       ('AF', 'Fidelity - Factor Chicago', 'us', 'ACTIVE'),
       ('IF', 'Ice Safety Stock - Factor Chicago', 'us', 'ACTIVE'),
       ('CM', 'Community Fresh Market', 'us', 'ACTIVE'),
       ('AM', 'ACME (Ingredients)', 'us', 'ACTIVE'),
       ('SW', 'Green Chef - Swedesboro', 'us', 'ACTIVE'),
       ('CO', 'Green Chef - Aurora', 'us', 'ACTIVE'),
       ('LH', 'Lineage Henderson', 'us', 'ACTIVE'),
       ('FV', 'Factor c/o Fidelity 3PW', 'us', 'ACTIVE'),
       ('DF', 'Fidelity - Colorado', 'us', 'ACTIVE'),
       ('IW', 'Ice Safety Stock - SWE', 'us', 'ACTIVE'),
       ('IX', 'Ice Safety Stock - CO', 'us', 'ACTIVE'),
       ('RA', 'Lineage Allentown (Freezer/Cooler)', 'us', 'ACTIVE'),
       ('MT', 'Mesa Tolleson Cold Storage', 'us', 'ACTIVE'),
       ('FX', 'Fidelity - TX-2', 'us', 'ACTIVE');

INSERT INTO dc_address (dc_code, address, number, zip, city, state, company, type)
VALUES ('NJ', 'Lister Avenue', '60', '07105', 'Newark', 'NJ', null, 'DELIVERY'),
       ('EZ', 'S 71st Avenue', '1850', '85043', 'Phoenix', 'AZ', null, 'DELIVERY'),
       ('GD', 'West Yuma Road', '14170', '--', 'Goodyear', 'Arizona', null, 'DELIVERY'),
       ('TF', 'Corporate Blvd', '960', '60502', 'Aurora', 'Illionis', null, 'DELIVERY'),
       ('ZE', 'S. River Street', '847', '60506', 'Aurora', 'IL', null, 'DELIVERY'),
       ('FN', 'Mill Road', '396N', '08360', 'Vineland', 'NJ', null, 'DELIVERY'),
       ('DC', 'Regatta Blvd', '3151', '94804', 'Richmond', 'CA', null, 'DELIVERY'),
       ('SC', '9th St Suite 200', '600 E', '76102', 'Fort Worth', 'TX', null, 'DELIVERY'),
       ('SF', 'Factory St', '2041', '94801', 'Richmond', 'CA', null, 'DELIVERY'),
       ('UC', 'Campus Dr', '40', '07032', 'Kearny', 'NJ', null, 'DELIVERY'),
       ('US', 'E 9th Street #200', '600', '76102', 'Fort Worth', 'TX', null, 'DELIVERY'),
       ('TX', 'Post & Paddock St.', '1025', '75050', 'Grand Prairie', 'TX', null, 'DELIVERY'),
       ('TN', 'Logistics Drive', '135', 'TN 37090', 'Lebanon', 'Tennessee', null, 'DELIVERY'),
       ('LX', 'Gold Spike Drive Fort Worth', '5200', '76106', 'Fort Worth', 'TX', null, 'DELIVERY'),
       ('GL', 'Valley Dr', '453', '94005', 'Brisbane', 'CA', null, 'DELIVERY'),
       ('MF', 'Whiskey Bottom Rd', '9001', '20723', 'Laurel', 'MD', null, 'DELIVERY'),
       ('HN', 'Rising Sun Rd', '445', '08505', 'Bordentown', 'NJ', null, 'DELIVERY'),
       ('FP', 'SR 103 North, Bldg 35', '6395', '17044', 'Lewistown', 'PA', null, 'DELIVERY'),
       ('FC', 'Stone Rd.', '539', '94510', 'Benicia', 'CA', null, 'DELIVERY'),
       ('FT', 'Ave H East, Suite 217', '827', '76011', 'Arlington', 'TX', null, 'DELIVERY'),
       ('FJ', 'Murray Rd', '901', '07006', 'East Hanover', 'NJ', null, 'DELIVERY'),
       ('CP', 'Moreland Avenue S.E.', '4322', '30288', 'Conley', 'GA', null, 'DELIVERY'),
       ('GW', 'Airport Drive', '302', '31063', 'Montezuma', 'GA', null, 'DELIVERY'),
       ('FG', 'West Park Drive', '5595', '30336', 'Atlanta', 'GA', null, 'DELIVERY'),
       ('GA', 'International Parkway', '510', '30265', 'Newnan', 'GA', null, 'DELIVERY'),
       ('PI', 'Pillsbury Road', '1717', '18041', 'East Greenville', 'PA', null, 'DELIVERY'),
       ('TT', 'Avenue S', '1350', '75050', 'Grand Prairie', 'TX', null, 'DELIVERY'),
       ('DM', 'E Northside Dr', '813', '76102', 'Fort Worth', 'TX', null, 'DELIVERY'),
       ('NX', 'Danieldale Road', '1901', '75134', 'Lancaster', 'TX', null, 'DELIVERY'),
       ('JK', 'JFK International Airport Bldg #143', '139', '11430', 'Jamaica', 'NY', null, 'DELIVERY'),
       ('FW', 'Simonton Rd', '4721', '75244', 'Dallas', 'TX', null, 'DELIVERY'),
       ('DL', 'W. 33rd St', '2120', '75261', 'Dallas', 'TX', null, 'DELIVERY'),
       ('PA', 'Hoover Ave', '2100', '18109', 'Allentown', 'PA', null, 'DELIVERY'),
       ('JO', 'Cherry Hill Road', '1101', '60433', 'Joliet', 'Illionis', null, 'DELIVERY'),
       ('TI', 'Market Street', '2700', '75062', 'Irving', 'TX', null, 'DELIVERY'),
       ('AZ', 'S 71st Avenue', '1850', '85043', 'Phoenix', 'AZ', null, 'DELIVERY'),
       ('WL', 'Euclid St', '14', '07105', 'Newark', 'NJ', null, 'DELIVERY'),
       ('WD', 'North Freeway', '11501', '76177', 'Fort Worth', 'Texas', null, 'DELIVERY'),
       ('LP', 'Long Creek Road', '367', 'TX 75182', 'Sunnyvale', 'USA', null, 'DELIVERY'),
       ('ET', 'Post & Paddock St.', '1025', '75050', 'Grand Prairie', 'TX', null, 'DELIVERY'),
       ('EC', 'Factory St', '2041', '94801', 'Richmond', 'CA', null, 'DELIVERY'),
       ('EV', 'Vreeland Ave', '8', '07512', 'Totowa', 'NJ', null, 'DELIVERY'),
       ('GS', 'Port Carteret Dr.', '580', 'NJ 07008', 'Carteret', 'USA', null, 'DELIVERY'),
       ('HD', 'N Cockrell Hill Rd', '1005', '75211', 'Dallas', 'USA', null, 'DELIVERY'),
       ('FZ', 'W Roosevelt Street', '10205', '85323', 'Avondale', 'AZ', null, 'DELIVERY'),
       ('RM', 'Mack Drive', '23', '08817', 'Edison', 'New Jersey', null, 'DELIVERY'),
       ('RT', 'Talmadge Road', '145A', '08817', 'Edison', 'New Jersey', null, 'DELIVERY'),
       ('IO', 'Territorial Dr', '581', '60440', 'Bolingbrook', 'Illinois', null, 'DELIVERY'),
       ('FD', 'Indian Trail', '2372 W', '60506', 'Aurora', 'Illinois', null, 'DELIVERY'),
       ('FE', 'Shore Dr', '340', '60527', 'Burr Ridge', 'Illinois', null, 'DELIVERY'),
       ('FF', 'Ensell Rd', '1325', '60047', 'Lake Zurich', 'Illinois', null, 'DELIVERY'),
       ('FH', '47th St', '8424', '60534', 'Lyons', 'Illinois', null, 'DELIVERY'),
       ('FK', 'Plum St', '2', '41076', 'Wilder', 'Kentucky', null, 'DELIVERY'),
       ('MS', 'S Country Club Dr', '146', '85210', 'Mesa', 'AZ', null, 'DELIVERY'),
       ('PS', 'South 63rd Ave', '402', '85043', 'Phoenix', 'USA', null, 'DELIVERY'),
       ('SR', 'Pratt Rd', '489', '53570', 'Monticello', 'WI', null, 'DELIVERY'),
       ('PP', 'S. Great Southwest Pkwy', '2010', '75051', 'Grand Prairie', 'TX', null, 'DELIVERY'),
       ('MM', 'DONATION PARTNER Rd', '1', '12345', 'Placeholder', 'NY', null, 'DELIVERY'),
       ('FS', 'Corporate Blvd', '966', '60502', 'Aurora', 'IL', null, 'DELIVERY'),
       ('PF', '1432 Wainwright Way', '150', '75007', 'Carrollton', 'TX', null, 'DELIVERY'),
       ('NA', 'Mitchell Ave', '2645', '18103', 'Allentown', 'PA', null, 'DELIVERY'),
       ('GM', 'King Mill Rd.', '200', '30253', 'McDonough', 'GA', null, 'DELIVERY'),
       ('CW', 'Moreland Ave', '4322', '30288', 'Conley', 'GA', null, 'DELIVERY'),
       ('FL', 'Caruso Court', '3131', '32806', 'Orlando', 'Florida', null, 'DELIVERY'),
       ('NF', 'Murray Rd.', '901', '07006', 'East Hanover', 'NJ', null, 'DELIVERY'),
       ('FO', 'S Racine Ave', '4555', '60609', 'Chicago', 'IL', null, 'DELIVERY'),
       ('AF', 'Corporate Blvd', '960', '60502', 'Aurora', 'IL', null, 'DELIVERY'),
       ('CM', 'DONATION PARTNER Rd', '1', '12345', 'Placeholder', 'CO', null, 'DELIVERY'),
       ('AM', 'Colfax Ave', '18101 E.', '80011', 'Aurora', 'CO', null, 'DELIVERY'),
       ('SW', 'Commerce Blvd, Suite 125', '1130', '08085', 'Swedesboro', 'NJ', null, 'DELIVERY'),
       ('CO', 'E 35th Dr.', '20761', '80011', 'Aurora', 'CO', null, 'DELIVERY'),
       ('LH', '88TH Ave', '8001 E', '80640', 'Henderson', 'CO', null, 'DELIVERY'),
       ('FV', 'Corporate Blvd.', '966', '60502', 'Aurora', 'IL', null, 'DELIVERY'),
       ('DF', 'ACME / FIDELITY PAPER COMPANY, C/O HOWARD LOGISTICS, INC., 38TH AVE.', '13100 E.', '80239', 'Denver', 'Colorado', null, 'DELIVERY'),
       ('RA', 'Ruppsville Rd.', '7132', '18106', 'Allentown', 'PA', null, 'DELIVERY'),
       ('MT', 'W Buckeye Rd', '9602', '85353', 'Tolleson', 'AZ', null, 'DELIVERY'),
       ('FX', 'SW 14th St.', '310', '75051', 'Grand Prairie', 'TX', null, 'DELIVERY'),
       ('UC', 'Montevideo Rd', '7591', '20794', 'Jessup', 'MD', 'Hearn Kirkwood', 'BILLING'),
       ('US', '28 Liberty St', '10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('LX', 'Lister Avenue', '60', '07105', 'Newark', 'NJ', 'HelloFresh', 'BILLING'),
       ('MF', 'Liberty St,10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING'),
       ('FP', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('FT', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('FG', '28 Liberty St', '10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('DM', 'Liberty St,10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING'),
       ('NX', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('JK', 'Liberty St,10th Floor', '28', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('FW', 'Liberty St,10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING'),
       ('DL', 'Liberty St,10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING'),
       ('TI', '28 Liberty St', '10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('AZ', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING'),
       ('WL', '28 Liberty St', '10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('LP', 'S Water St', '170', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING'),
       ('GS', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING'),
       ('FZ', '28 Liberty St', '10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('RM', '28 Liberty St', '10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('IS', 'Conestoga Court', '5490', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING'),
       ('IC', 'Conestoga Court', '5490', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING'),
       ('IB', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('IN', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('IJ', 'Conestoga Court', '5490', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING'),
       ('IO', 'Conestoga Court', '5490', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING'),
       ('FD', 'Conestoga Court', '5490', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'BILLING'),
       ('FE', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('FF', '28 Liberty St', '10th Floor', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('PS', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING'),
       ('SR', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING'),
       ('MM', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'New York', 'HelloFresh', 'BILLING'),
       ('PF', 'Liberty St, 10th Floor', '28', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING');
