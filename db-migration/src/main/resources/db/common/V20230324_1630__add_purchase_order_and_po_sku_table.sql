create table purchase_order
(
    po_number             varchar primary key,
    id                    uuid not null,
    version               int,
    type                  varchar,
    year_week             varchar,
    user_id               uuid,
    user_email            varchar,
    status                varchar,
    send_time             timestamp,
    supplier_id           uuid,
    supplier_code         varchar,
    dc_code               varchar,
    shipping_method       varchar,
    location_name         varchar,
    street_address        varchar,
    city                  varchar,
    region                varchar,
    postal_code           varchar,
    country_code          varchar,
    expected_start_time   timestamp,
    expected_end_time     timestamp,
    emergency_reason      varchar,
    emergency_reason_uuid uuid,
    created_at            timestamp without time zone,
    updated_at            timestamp without time zone
);

create table purchase_order_sku
(
    po_number             varchar,
    sku_id                uuid,
    total_qty_permyriad   integer,
    price_currency        varchar,
    price_permyriad       integer,
    total_price_currency  varchar,
    total_price_permyriad integer,
    buffer_permyriad      integer,
    correction_reason     varchar,
    packaging_type        varchar,
    case_size_permyriad   integer,
    unit_of_measure       varchar,
    po_id                 uuid,
    raw_qty_permyriad     integer,
    created_at            timestamp without time zone,
    updated_at            timestamp without time zone,
    PRIMARY KEY (po_number, sku_id)
);

