CREATE TABLE supplier_sku_price
(
    uuid             uuid PRIMARY KEY,
    supplier_sku_id  uuid                        NOT NULL,
    dc_codes         TEXT[]                      NOT NULL DEFAULT ARRAY []::TEXT[],
    start_date       TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    end_date         TIMES<PERSON>MP WITHOUT TIME ZONE NOT NULL,
    enabled          BOOLEAN                     NOT NULL,
    cases_per_pallet INTEGER                     NOT NULL,
    price_type       VARCHAR                     NOT NULL,
    market           VARCHAR                     NOT NULL,
    currency         VARCHAR(3)                  NOT NULL,
    price_permyriad  INTEGER                     NOT NULL,
    buffer_permyriad INTEGER                     NOT NULL,
    case_size        INTEGER
);
