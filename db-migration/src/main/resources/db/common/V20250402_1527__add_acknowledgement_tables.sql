-- Migration: V20250402_1527__add_acknowledgement_tables.sql (Environment: common)
CREATE TABLE IF NOT EXISTS acknowledgement(
    id UUID PRIMARY KEY,
    po_id UUID,
    po_number TEXT,
    create_time TIMESTAMP WITH TIME ZONE,

    pickup_time TIMESTAMP WITH TIME ZONE,
    pickup_region TEXT,
    pickup_postal_code TEXT,
    pickup_administrative_area TEXT,
    pickup_locality TEXT,
    pickup_address TEXT[],
    pickup_organization TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS acknowledgement_line_item(
    id UUID PRIMARY KEY,
    acknowledgement_id UUID REFERENCES acknowledgement(id) ON DELETE CASCADE,
    sku_code TEXT,
    sku_id UUID,
    state TEXT,
    pallets_quantity NUMERIC,
    promised_time TIMESTAMP WITH TIME ZONE,
    order_size NUMERIC,
    case_size NUMERIC,
    uom TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_acknowledgement_line_item_fk_acknowledgement_id ON acknowledgement_line_item (acknowledgement_id);
