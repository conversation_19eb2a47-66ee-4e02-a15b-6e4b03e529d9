ALTER TABLE outbox_item ADD COLUMN status_new TEXT;

-- setting everyone to SENT since before this date only SENT was possible
UPDATE outbox_item SET status_new = 'SENT'    WHERE last_status_change_at < '2024-08-20 12:30:00';
-- after new values were added, we need to set the new status based on the old status and the date
UPDATE outbox_item SET status_new = 'PENDING' WHERE status = '0' AND last_status_change_at >= '2024-08-20 12:30:00';
UPDATE outbox_item SET status_new = 'SENT'    WHERE status = '1' AND last_status_change_at >= '2024-08-20 12:30:00';
UPDATE outbox_item SET status_new = 'FAILED'  WHERE status = '2' AND last_status_change_at >= '2024-08-20 12:30:00';
