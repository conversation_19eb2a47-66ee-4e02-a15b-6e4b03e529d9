create table supplier
(
    id         uuid primary key,
    market     varchar not null,
    code       varchar not null,
    name       varchar not null,
    status     varchar not null,
    currency   varchar not null,
    type       varchar not null,
    email      varchar not null,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

create table supplier_address
(
    id          uuid primary key,
    supplier_id uuid    not null,
    city        varchar not null,
    country     varchar not null,
    state       varchar not null,
    address     varchar not null,
    number      varchar not null,
    post_code   varchar not null
);

alter table supplier_address
    add constraint fk_supplier foreign key (supplier_id) references supplier (id) on delete cascade;
