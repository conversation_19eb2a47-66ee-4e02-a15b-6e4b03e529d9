DROP TABLE supplier_address;
ALTER TABLE supplier
    ADD COLUMN city VARCHAR;
ALTER TABLE supplier
    ADD COLUMN country VARCHAR;
ALTER TABLE supplier
    ADD COLUMN state VARCHAR;
ALTER TABLE supplier
    ADD COLUMN address VARCHAR;
ALTER TABLE supplier
    ADD COLUMN number VARCHAR;
ALTER TABLE supplier
    ADD COLUMN post_code VARCHAR;
ALTER TABLE supplier
    ADD COLUMN parent_id uuid NOT NULL;

ALTER TABLE supplier
    ADD COLUMN dc_codes TEXT[] NOT NULL DEFAULT ARRAY []::TEXT[];

ALTER TABLE supplier DROP COLUMN email;

CREATE TABLE supplier_contact
(
    id          uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    email       VARCHAR NOT NULL,
    supplier_id uuid
);

