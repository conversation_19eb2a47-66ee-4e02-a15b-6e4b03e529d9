CREATE TABLE comment (
    id UUID PRIMARY KEY NOT NULL,
    source_id TEXT DEFAULT NULL,
    resource_type TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    domain TEXT NOT NULL,
    brand TEXT NOT NULL,
    dc TEXT NOT NULL,
    year_week TEXT NOT NULL,
    comment TEXT NOT NULL,
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by TEXT NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX comment_week_domain_resource_type_idx ON comment USING BTREE (
    year_week,
    domain,
    resource_type
);
