CREATE TABLE unit_of_measure
(
    uuid   uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name   VARCHAR NOT NULL,
    market VARCHAR NOT NULL,
    type   VARCHAR NOT NULL
);

INSERT INTO "unit_of_measure"(name, type, market)
VALUES ('kg', 'bulk', 'us');
INSERT INTO "unit_of_measure"(name, type, market)
VALUES ('oz', 'bulk', 'us');
INSERT INTO "unit_of_measure"(name, type, market)
VALUES ('L', 'bulk', 'us');
INSERT INTO "unit_of_measure"(name, type, market)
VALUES ('gal', 'bulk', 'us');
INSERT INTO "unit_of_measure"(name, type, market)
VALUES ('lbs', 'bulk', 'us');

INSERT INTO "unit_of_measure"(name, type, market)
VALUES ('unit(s)', 'unit', 'us');
INSERT INTO "unit_of_measure"(name, type, market)
VALUES ('unit(s)', 'unit', 'gb');
