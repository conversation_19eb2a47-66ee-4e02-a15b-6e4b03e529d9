CREATE TABLE IF NOT EXISTS grn(
   id UUID PRIMARY KEY,
   po_number TEXT NOT NULL,
   reference TEXT,
   dc_code TEXT,
   delivery_start_time TIMESTAMP WITH TIME ZONE,
   delivery_end_time TIMESTAMP WITH TIME ZONE,
   state TEXT,
   wms_name TEXT,
   CONSTRAINT unique_po_number UNIQUE (po_number)
);

CREATE TABLE IF NOT EXISTS grn_purchase_order_delivery(
  id UUID PRIMARY KEY,
  po_delivery_id TEXT,
  delivery_time TIMESTAMP WITH TIME ZONE,
  expected_delivery_start_time TIMESTAMP WITH TIME ZONE,
  expected_delivery_end_time TIMESTAMP WITH TIME ZONE,
  state TEXT,
  grn_id UUID references grn(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS grn_purchase_order_delivery_line(
   id UUID PRIMARY KEY,
   po_delivery_line_id TEXT,
   sku_code TEXT,
   unloaded_quantity NUMERIC,
   received_quantity NUMERIC,
   expected_quantity NUMERIC,
   palletized_quantity NUMERIC,
   case_size NUMERIC,
   state TEXT,
   sku_uom TEXT,
   expiration_date TIMESTAMP WITH TIME ZONE,
   rejected_quantity NUMERIC,
   supplier_lot_number TEXT,
   grn_purchase_order_delivery_id UUID references grn_purchase_order_delivery(id) ON DELETE CASCADE
)
