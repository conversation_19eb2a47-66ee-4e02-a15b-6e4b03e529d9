INSERT INTO emergency_order_reason(uuid, market, name, disabled)
VALUES  ('3326676b-9396-43de-aba7-bc896a22570f', 'eu', 'Stock Movement', false),
        ('0cdbd2ca-bca0-4d72-9c04-39881b68866c', 'eu', 'Packaging', false),
        ('184f5cde-7989-4a48-8c21-cc1af0a2886a', 'eu', 'Internal Procurement Issue', false),
        ('18901491-97d1-4d18-b820-0b34cf8bfe8a', 'eu', 'Internal Production Issue', false),
        ('847b221c-ac76-4426-b889-19a86f631d62', 'eu', 'Internal Tech Limitation', false),
        ('92e1fed6-b8e8-44b8-8d20-aa58ee0d9583', 'eu', 'Internal Forecast Change', false),
        ('a80fc7bd-0f71-4655-96f4-dbfcf1e9864c', 'eu', 'Supplier Error', false),
        ('fe77d66d-8126-49ad-b51e-ef898109e6c4', 'eu', 'Multi-Week', false),
        ('*************-4354-ab21-2b48da4fb3b0', 'eu', 'Raw Ingredients (Bulk)', false),
        ('d0a994db-8cd9-4a93-8782-f87a43f3078d', 'eu', 'Non-Pick List Ingredient', false),
        ('6d1989a3-58c6-40c8-b133-78aa1cb1b39e', 'eu', 'Sample', false),
        ('d8d25c31-5d44-44e9-be8c-a75aadf7c9fa', 'eu', 'Marketing', false)
;

INSERT INTO unit_of_measure(name, type, market, enum_value) VALUES ('unit(s)', 'unit', 'eu', 'UNIT')
