CREATE TABLE IF NOT EXISTS po_void (
    id                  UUID PRIMARY KEY,
    user_email          TEXT NOT NULL,
    dc TEXT             NOT NULL,
    week TEXT           NOT NULL,
    brand TEXT          NOT NULL,
    po_number           TEXT NOT NULL,
    "source"            TEXT NOT NULL,
    "comment"           TEXT,
    create_at           TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sku_code TEXT       NOT NULL,
    supplier_name       TEXT,
    deleted_by          TEXT,
    deleted_at          TIMESTAMP,
    last_updated        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    market TEXT         NOT NULL
);
