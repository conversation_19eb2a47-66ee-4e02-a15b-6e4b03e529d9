INSERT INTO "unit_of_measure"(name, type, market, enum_value)
VALUES ('unit(s)', 'unit', 'dkse', 'UNIT'),
       ('unit(s)', 'unit', 'dach', 'UNIT');

UPDATE change_reason
SET allowed_markets = array_append(allowed_markets, 'dkse')
where reason_type = 'ORDER_ITEM_CHANGE'
  and 'SE' = ANY(allowed_markets);

UPDATE change_reason
SET allowed_markets = array_append(allowed_markets, 'dach')
where reason_type = 'ORDER_ITEM_CHANGE'
  and 'DE' = ANY(allowed_markets);

UPDATE emergency_order_reason
SET market = 'dach'
WHERE market = 'de';

UPDATE emergency_order_reason
SET market = 'dkse'
WHERE market = 'se';

