-- Adds a new column to unit_of_measure table called enum_value_new of type text
ALTER TABLE unit_of_measure ADD COLUMN enum_value_new VARCHAR;
-- Migrate values from enum_value to enum_value_new
UPDATE unit_of_measure SET enum_value_new = enum_value::text;
-- Drops the enum_value column
ALTER TABLE unit_of_measure DROP COLUMN enum_value;
-- Renames the enum_value_new column to enum_value
ALTER TABLE unit_of_measure RENAME COLUMN enum_value_new TO enum_value;
-- Drop the UOM type
DROP TYPE UOM;
