-- Communication Preferences by Market
INSERT INTO communication_preference (market, dc_code, supplier_id, preference)
VALUES ('au', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('ca', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('dach', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('es', NULL, NULL, 'E2OPEN'),
       ('eu', NULL, NULL, 'E2OPEN'),
       ('fr', NULL, NULL, 'E2OPEN'),
       ('gb', NULL, NULL, 'E2OPEN'),
       ('ie', NULL, NULL, 'EMAIL_AND_E2OPEN'),
       ('it', NULL, NULL, 'E2OPEN'),
       ('beneluxfr', NULL, NULL, 'E2OPEN'),
       ('nz', NULL, NULL, 'E2OPEN'),
       ('dkse', NULL, NULL, 'E2OP<PERSON>'),
       ('us', NULL, NULL, 'EMAIL_AND_E2OPEN');

-- Communication Preferences by Distribution Center
INSERT INTO communication_preference (market, dc_code, supplier_id, preference)
VALUES (NULL, 'QF', NULL, 'EMAIL_AND_E2OPEN'),
       (NULL, 'AV', NULL, 'EMAIL_AND_E2OPEN'),
       (NULL, 'OV', NULL, 'EMAIL_AND_E2OPEN'),
       (NULL, 'BM', NULL, 'EMAIL_AND_E2OPEN');
