ALTER TABLE ics_ticket DROP CONSTRAINT IF EXISTS ics_ticket_pkey;
ALTER TABLE ics_ticket ADD CONSTRAINT ics_ticket_pkey PRIMARY KEY (ticket_id);

CREATE INDEX IF NOT EXISTS idx_ics_ticket_po_number ON ics_ticket (po_number);

ALTER TABLE IF EXISTS ics_ticket ADD COLUMN IF NOT EXISTS po_reference TEXT;
ALTER TABLE IF EXISTS ics_ticket ADD COLUMN IF NOT EXISTS production_impact TEXT;
ALTER TABLE IF EXISTS ics_ticket ADD COLUMN IF NOT EXISTS created_at TIMESTAMP;

ALTER TABLE ics_ticket ALTER COLUMN bob_code DROP NOT NULL;
ALTER TABLE ics_ticket ALTER COLUMN week DROP NOT NULL;
ALTER TABLE ics_ticket ALTER COLUMN week DROP DEFAULT;

ALTER TABLE ics_ticket ALTER COLUMN po_number SET NOT NULL;
ALTER TABLE ics_ticket ALTER COLUMN po_reference SET NOT NULL;
