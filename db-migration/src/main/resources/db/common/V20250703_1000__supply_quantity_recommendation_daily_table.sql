CREATE TABLE IF NOT EXISTS supply_quantity_recommendation_daily (
        sku_id UUID NOT NULL,
        dc_code TEXT NOT NULL,
        supply_date DATE NOT NULL,
        forecasted_demanded NUMERIC(20,4) NOT NULL,
        production_year_week TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

        CONSTRAINT supply_quantity_recommendation_daily_pkey PRIMARY KEY (sku_id, dc_code, supply_date)
);
