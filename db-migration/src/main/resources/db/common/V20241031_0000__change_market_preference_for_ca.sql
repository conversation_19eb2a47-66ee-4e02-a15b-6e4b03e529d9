UPDATE communication_preference
SET preference = 'EMAIL_AND_E2OPEN'
where market = 'ca'
  and supplier_id is null
  and dc_code is null;

DELETE
FROM communication_preference
WHERE dc_code IN ('AV', 'OV', 'BM')
  AND preference = 'EMAIL_AND_E2OPEN';

DELETE FROM communication_preference
WHERE supplier_id IN (
    SELECT supplier_id
    FROM communication_preference cp
            JOIN supplier s ON s.id = cp.supplier_id
    WHERE s.market = 'ca'
      AND preference = 'EMAIL_AND_E2OPEN'
);
