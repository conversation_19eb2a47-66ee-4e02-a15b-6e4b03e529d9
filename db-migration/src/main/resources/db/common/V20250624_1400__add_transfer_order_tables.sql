-- Migration: V20250624_1400__add_transfer_order_tables.sql (Environment: common)
CREATE TABLE IF NOT EXISTS transfer_order (
    -- Required fields (no optional keyword in proto)
    id UUID PRIMARY KEY NOT NULL,
    source_dc_code TEXT NOT NULL,
    destination_dc_code TEXT NOT NULL,
    creator_email TEXT NOT NULL,
    reason_text TEXT NOT NULL,
    status TEXT NOT NULL,
    production_week_year INTEGER NOT NULL,
    production_week_week INTEGER NOT NULL,
    create_time TIMESTAMP WITH TIME ZONE NOT NULL,
    update_time TIMESTAMP WITH TIME ZONE NOT NULL,
    pickup_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    pickup_end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    delivery_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    delivery_end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    transfer_order_number TEXT NOT NULL UNIQUE,
    source_dc_name TEXT NOT NULL,
    shipping_method TEXT NOT NULL,
    market_code TEXT NOT NULL,
    sent_time TIMESTAMP WITH TIME ZONE NOT NULL,
    version INTEGER NOT NULL,

    -- Total price (google.type.Money) - required
    total_price_currency_code TEXT NOT NULL,
    total_price_units BIGINT NOT NULL,
    total_price_nanos INTEGER NOT NULL,

    -- Shipping information stored as JSONB
    shipping JSONB NOT NULL,

    -- Optional fields (marked as optional in proto)
    comments TEXT,
    delivery_change_reason_key TEXT,
    delivery_change_reason_value TEXT,
    order_items_change_reason_key TEXT,
    order_items_change_reason_value TEXT,
    region_code TEXT,
    shipping_notes TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS transfer_order_item (
    -- Required fields (no optional keyword in proto)
    id UUID PRIMARY KEY NOT NULL,
    transfer_order_id UUID NOT NULL REFERENCES transfer_order(id) ON DELETE CASCADE,
    csku_code TEXT NOT NULL,
    price_currency_code TEXT NOT NULL,
    price_units BIGINT NOT NULL,
    price_nanos INTEGER NOT NULL,
    total_price_currency_code TEXT NOT NULL,
    total_price_units BIGINT NOT NULL,
    total_price_nanos INTEGER NOT NULL,
    order_size INTEGER NOT NULL,
    supplier_id UUID NOT NULL,
    supplier_code TEXT NOT NULL,
    supplier_sku_id UUID NOT NULL,
    inventory_type TEXT NOT NULL,
    csku_name TEXT NOT NULL,
    quantity_value TEXT NOT NULL,

    -- Packaging information (oneof: case_packaging or unit_packaging)
    packaging_type TEXT NOT NULL, -- 'case' or 'unit'

    -- Case packaging fields (only populated when packaging_type = 'case')
    case_packaging_size_value TEXT,
    case_packaging_size_currency_code TEXT,
    case_packaging_size_units BIGINT,
    case_packaging_size_nanos INTEGER,
    case_packaging_unit TEXT,
    case_per_pallet INTEGER,

    -- Optional fields (marked as optional in proto)
    original_po_number TEXT,
    lot_number TEXT,
    lot_expiration_time TIMESTAMP WITH TIME ZONE,
    sku_id UUID,
    license_plate_number TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_transfer_order_item_transfer_order_id ON transfer_order_item (transfer_order_id);
CREATE INDEX idx_transfer_order_source_dc ON transfer_order (source_dc_code);
CREATE INDEX idx_transfer_order_destination_dc ON transfer_order (destination_dc_code);
CREATE INDEX idx_transfer_order_status ON transfer_order (status);
CREATE INDEX idx_transfer_order_number ON transfer_order (transfer_order_number);
CREATE INDEX idx_transfer_order_item_csku_code ON transfer_order_item (csku_code);
CREATE INDEX idx_transfer_order_item_supplier_id ON transfer_order_item (supplier_id);
CREATE INDEX idx_transfer_order_create_time ON transfer_order (create_time);
CREATE INDEX idx_transfer_order_production_week ON transfer_order (production_week_year, production_week_week);

-- JSONB indexes for shipping information
CREATE INDEX idx_transfer_order_shipping_method ON transfer_order USING GIN ((shipping->>'method'));
CREATE INDEX idx_transfer_order_shipping_address ON transfer_order USING GIN ((shipping->'address'));