alter table if exists po_void add column if not exists po_reference TEXT;
alter table if exists receipt_override add column if not exists po_reference TEXT;
alter table if exists export_receipt add column if not exists po_reference TEXT;

update po_void set po_reference = po_number where po_reference is null;;
update receipt_override set po_reference = po_number where po_reference is null;;
update export_receipt set po_reference = po_number where po_reference is null;;

update po_void set po_number = split_part(po_number, '_', 1) where po_number like '%\_%';
update receipt_override set po_number = split_part(po_number, '_', 1) where po_number like '%\_%';
update export_receipt set po_number = split_part(po_number, '_', 1) where po_number like '%\_%';

alter table if exists po_void alter column po_reference set not null;
alter table if exists receipt_override alter column po_reference set not null;
alter table if exists export_receipt alter column po_reference set not null;
