CREATE TYPE UOM AS ENUM ('UNIT','KG','L','LBS','GAL','OZ');
ALTER TABLE unit_of_measure ADD COLUMN enum_value UOM ;
DELETE FROM unit_of_measure;

INSERT INTO "unit_of_measure"(name, type, market, enum_value) VALUES ('kg', 'bulk', 'us', 'KG');
INSERT INTO "unit_of_measure"(name, type, market, enum_value) VALUES ('oz', 'bulk', 'us', 'OZ');
INSERT INTO "unit_of_measure"(name, type, market, enum_value) VALUES ('L', 'bulk', 'us', 'L');
INSERT INTO "unit_of_measure"(name, type, market, enum_value) VALUES ('gal', 'bulk', 'us', 'GAL');
INSERT INTO "unit_of_measure"(name, type, market, enum_value) VALUES ('lbs', 'bulk', 'us', 'LBS');
INSERT INTO "unit_of_measure"(name, type, market, enum_value) VALUES ('unit(s)', 'unit', 'us', 'UNIT');
INSERT INTO "unit_of_measure"(name, type, market, enum_value) VALUES ('unit(s)', 'unit', 'gb', 'UNIT');

ALTER TABLE unit_of_measure ALTER COLUMN enum_value SET NOT NULL;
