CREATE TABLE IF NOT EXISTS advance_shipping_notification
(
    id                          UUID PRIMARY KEY,
    asn_id                      VARCHAR   NOT NULL,
    purchase_order_id           UUID   NOT NULL,
    notes                       VARCHAR   DEFAULT NULL,
    supplier_id                 UUID      NOT NULL,
    distribution_center         VARCHAR   NOT NULL,
    standard_carrier_alpha_code VA<PERSON>HAR,
    shipping_method             VARCHAR   NOT NULL,
    create_time                 TIMESTAMP NOT NULL,
    shipment_time               TIMESTAMP NOT NULL,
    planned_delivery_time       TIMESTAMP NOT NULL,
    tracking_number             VARCHAR,
    number_pallet               VARCHAR   NOT NULL,

    shipping_region_code        VARCHAR,
    shipping_postal_code        VARCHAR,
    shipping_locality           VARCHAR,
    shipping_address_lines      VARCHAR,
    shipping_organization       VARCHAR,

    pickup_region_code          VARCHAR,
    pickup_postal_code          VA<PERSON>HAR,
    pickup_locality             VARCHAR,
    pickup_address_lines        VARCHAR,
    pickup_organization         VARCHAR,

    created_at                  TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at                  TIMESTAMP NOT NULL DEFAULT NOW()
    );

CREATE TABLE IF NOT EXISTS advance_shipping_notification_item(
    id                  UUID,
    sku_code            VARCHAR          NOT NULL,
    shipping_state      VARCHAR          NOT NULL,

    shipped_order_size  DOUBLE PRECISION NOT NULL,
    shipped_size        DOUBLE PRECISION NOT NULL,
    shipped_unit        VARCHAR          NOT NULL,
    shipped_pallet_id   VARCHAR          NOT NULL,
    shipped_crate_type  VARCHAR,

    promised_order_size DOUBLE PRECISION,
    promised_size       DOUBLE PRECISION,
    promised_pallet_id  VARCHAR,
    promised_unit       VARCHAR,
    promised_crate_type VARCHAR,

    packing_size        DOUBLE PRECISION,
    lot_number          VARCHAR,
    expiration_time     TIMESTAMP,

    asn_id UUID         NOT NULL,
    created_at          TIMESTAMP        NOT NULL,
    updated_at          TIMESTAMP        NOT NULL,

    PRIMARY KEY (id, sku_code),

    CONSTRAINT fk_asn_item FOREIGN KEY (asn_id)
    REFERENCES advance_shipping_notification (id)
    ON DELETE CASCADE
);


