ALTER TABLE purchase_order ADD COLUMN total_price_currency varchar;
ALTER TABLE purchase_order ADD COLUMN total_price_amount varchar;

ALTER TABLE purchase_order_sku DROP COLUMN price_currency;
ALTER TABLE purchase_order_sku ADD COLUMN price_currency varchar;
ALTER TABLE purchase_order_sku DROP COLUMN price_permyriad;
ALTER TABLE purchase_order_sku ADD COLUMN price_amount varchar;

ALTER TABLE purchase_order_sku DROP COLUMN total_price_currency;
ALTER TABLE purchase_order_sku ADD COLUMN total_price_currency varchar;
ALTER TABLE purchase_order_sku DROP COLUMN total_price_permyriad;
ALTER TABLE purchase_order_sku ADD COLUMN total_price_amount varchar;

ALTER TABLE purchase_order_sku DROP COLUMN case_size_permyriad;
ALTER TABLE purchase_order_sku ADD COLUMN case_size NUMERIC(20,4);
ALTER TABLE purchase_order_sku DROP COLUMN total_qty_permyriad;
ALTER TABLE purchase_order_sku ADD COLUMN total_qty NUMERIC(20,4);
ALTER TABLE purchase_order_sku DROP COLUMN raw_qty_permyriad;
ALTER TABLE purchase_order_sku ADD COLUMN raw_qty NUMERIC(20,4);

ALTER TABLE purchase_order DROP CONSTRAINT purchase_order_pkey;
ALTER TABLE purchase_order_sku DROP CONSTRAINT purchase_order_sku_pkey;

ALTER TABLE purchase_order ADD PRIMARY KEY (id);
ALTER TABLE purchase_order_sku RENAME TO order_item;
ALTER TABLE order_item ADD COLUMN id uuid DEFAULT gen_random_uuid() PRIMARY KEY;
ALTER TABLE order_item DROP COLUMN po_number;

