DROP TABLE IF EXISTS send_worker_action;
DROP TABLE IF EXISTS worker_action;

CREATE TABLE IF NOT EXISTS worker_action
(
    id                    uuid PRIMARY KEY,
    status                TEXT NOT NULL,
    user_email            TEXT NOT NULL,
    user_id               uuid NOT NULL,
    last_status_change_at TIMESTAMP WITH TIME ZONE,
    created_at            TIMESTAMP WITH TIME ZONE,
    action_type           TEXT NOT NULL,
    payload               jsonb,
    last_error            TEXT
);
