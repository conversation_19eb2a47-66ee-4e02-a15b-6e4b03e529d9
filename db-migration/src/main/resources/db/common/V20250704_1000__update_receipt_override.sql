DELETE FROM receipt_override;

ALTER TABLE receipt_override DROP CONSTRAINT receipt_override_pkey;
ALTER TABLE receipt_override ADD COLUMN id uuid DEFAULT gen_random_uuid() PRIMARY KEY;
ALTER TABLE receipt_override DROP COLUMN user_email;
ALTER TABLE receipt_override DROP COLUMN dc;
ALTER TABLE receipt_override DROP COLUMN brand;
ALTER TABLE receipt_override DROP COLUMN hf_week;
ALTER TABLE receipt_override DROP COLUMN po_reference;
ALTER TABLE receipt_override DROP COLUMN "source";
ALTER TABLE receipt_override DROP COLUMN deleted_by;
ALTER TABLE receipt_override DROP COLUMN deleted_at;
ALTER TABLE receipt_override DROP COLUMN market;
ALTER TABLE receipt_override RENAME COLUMN receiving_date TO delivery_date;
ALTER TABLE receipt_override ALTER COLUMN delivery_date TYPE TIMESTAMP;
ALTER TABLE receipt_override RENAME COLUMN comment TO details;
ALTER TABLE receipt_override ADD COLUMN updated_by TEXT;
