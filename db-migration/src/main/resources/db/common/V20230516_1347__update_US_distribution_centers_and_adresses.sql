INSERT INTO distribution_center(code, status, name, market)
VALUES ('KF', 'ACTIVE', 'Ice Safety Stock - Factor Kentucky', 'us'),
       ('KY', 'ACTIVE', 'Ice Safety Stock - Hellofresh Kentucky', 'us');

UPDATE dc_address
SET address    = 'Liberty St, 10th Floor',
    number     = '28',
    zip        = '10005',
    city       = 'New York',
    state      = 'NY',
    company    = 'HelloFresh',
    updated_at = NOW()
WHERE dc_code = 'RC'
  and type = 'DELIVERY';

INSERT INTO dc_address (dc_code, number, address, zip, city, state, company, type, country_code)
VALUES ('IB', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'DELIVERY', 'US'),
       ('IC', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'DELIVERY', 'US'),
       ('IJ', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'DELIVERY', 'US'),
       ('IN', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'DELIVERY', 'US'),
       ('IS', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'DELIVERY', 'US'),
       ('KY', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'DELIVERY', 'US'),
       ('RD', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'DELIVERY', 'US'),
       ('IW', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'DELIVERY', 'US'),
       ('IX', '5490', 'Conestoga Court', '80303', 'Boulder', 'CO', 'GREEN CHEF CORPORATION', 'DELIVERY', 'US'),
       ('IF', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'DELIVERY', 'US'),
       ('KF', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'DELIVERY', 'US');

INSERT INTO dc_address (dc_code, number, address, zip, city, state, company, type, country_code)
VALUES ('KY', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('RD', '28', 'Liberty St, 10th Floor', '10005', 'New York', 'NY', 'HelloFresh', 'BILLING', 'US'),
       ('KF', '170', 'S Water St', '60510', 'Batavia', 'Illinois', 'Factor 75 LLC', 'BILLING', 'US');
