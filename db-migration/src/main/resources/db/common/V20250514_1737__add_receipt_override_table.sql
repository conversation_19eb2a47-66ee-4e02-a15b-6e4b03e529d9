CREATE TABLE IF NOT EXISTS public.receipt_override
(
    user_email                  TEXT NOT NULL,
    dc                          TEXT NOT NULL,
    brand                       TEXT NOT NULL,
    hf_week                     TEXT NOT NULL,
    "source"                    TEXT NOT NULL,
    po_number                   TEXT NOT NULL,
    sku_code                    TEXT NOT NULL,
    quantity                    INT  NOT NULL,
    updated_at                  TIMESTAMP NOT NULL,
    created_at                  TIMESTAMP NOT NULL,
    deleted_by                  TEXT,
    deleted_at                  TIMESTAMP,
    market                      TEXT NOT NULL,
    cases                       INT,
    "comment"                   TEXT,
    receiving_date              DATE,
    CONSTRAINT receipt_override_pkey PRIMARY KEY (hf_week, brand, dc, po_number, sku_code)
    );
