CREATE TABLE IF NOT EXISTS transfer_order (
    id UUID PRIMARY KEY NOT NULL,
    source_dc_code TEXT NOT NULL,
    destination_dc_code TEXT NOT NULL,
    creator_email TEXT NOT NULL,
    reason_text TEXT NOT NULL,
    status TEXT NOT NULL,
    year_week TEXT NOT NULL,
    create_time TIMESTAMP WITH TIME ZONE NOT NULL,
    update_time TIMESTAMP WITH TIME ZONE NOT NULL,
    pickup_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    pickup_end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    delivery_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    delivery_end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    transfer_order_number TEXT NOT NULL,
    source_dc_name TEXT NOT NULL,
    market_code TEXT NOT NULL,
    sent_time TIMESTAMP WITH TIME ZONE NOT NULL,
    version INTEGER NOT NULL,
    total_price_currency_code TEXT NOT NULL,
    total_price_units BIGINT NOT NULL,
    total_price_nanos INTEGER NOT NULL,
    shipping JSONB NOT NULL,
    comments TEXT,
    delivery_change_reason_key TEXT,
    delivery_change_reason_value TEXT,
    order_items_change_reason_key TEXT,
    order_items_change_reason_value TEXT,
    region_code TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS transfer_order_item (
    -- Required fields (no optional keyword in proto)
    id UUID PRIMARY KEY NOT NULL,
    transfer_order_id UUID NOT NULL REFERENCES transfer_order(id) ON DELETE CASCADE,
    csku_code TEXT NOT NULL,
    price_currency_code TEXT NOT NULL,
    price_units BIGINT NOT NULL,
    price_nanos INTEGER NOT NULL,
    total_price_currency_code TEXT NOT NULL,
    total_price_units BIGINT NOT NULL,
    total_price_nanos INTEGER NOT NULL,
    order_size INTEGER NOT NULL,
    supplier_id UUID NOT NULL,
    supplier_code TEXT NOT NULL,
    inventory_type TEXT NOT NULL,
    csku_name TEXT NOT NULL,
    quantity_value TEXT NOT NULL,
    packaging_type TEXT NOT NULL,
    case_packaging_size_value TEXT,
    case_packaging_size_currency_code TEXT,
    case_packaging_size_units BIGINT,
    case_packaging_size_nanos INTEGER,
    case_packaging_unit TEXT,
    case_per_pallet INTEGER,
    original_po_number TEXT,
    lot_number TEXT,
    lot_expiration_time TIMESTAMP WITH TIME ZONE,
    sku_id UUID,
    license_plate_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
