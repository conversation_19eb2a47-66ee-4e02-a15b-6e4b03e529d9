CREATE TABLE IF NOT EXISTS ics_ticket (
        market TEXT NOT NULL,
        week INTEGER NOT NULL,
        bob_code TEXT NOT NULL,
        sku_code TEXT,
        po_number TEXT,
        subject TEXT,
        ticket_link TEXT,
        priority INTEGER,
        request_type TEXT,
        ticket_id INTEGER,
        status INTEGER,
        updated_at TIMESTAMP,

        CONSTRAINT ics_ticket_pkey PRIMARY KEY (market, week, bob_code, ticket_id)
);
