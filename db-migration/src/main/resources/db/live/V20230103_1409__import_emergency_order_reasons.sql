INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('5de0dbd6-8519-432d-94bc-f3ffb6b06f62', 'DC Director Approval', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ac2cf596-f75f-4126-a8df-302f8598bddb', 'Discard/Yield Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('079dfaf3-9a59-4580-b522-1a6701b6c966', 'Rejection Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('8dead596-1c72-4bd5-bad6-d8386ee37adc', 'Charity', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('77a3bbf8-89c6-4ae3-b66b-931398babdfa', 'Advance Weekly order', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('f7da5bef-840d-431f-9c70-617775926d9d', 'Repacking- Service', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('153c7dd1-69f4-431f-a42f-90cd0f8b5c49', 'Raw ingredients (Bulk)', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('d865b839-e08b-4d51-83a4-4d857aa8ed83', 'Packaging', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('b6ea490b-2afc-4554-b47a-044e818c78ce', 'Thanksgiving', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('599d4145-838b-4df3-ad65-304c8ce09b23', 'Pre-PO Shipment Error', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('68f5cb89-eda2-4e1e-8115-9bf88e028303', 'Supplemental Add-On', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('fbf45daf-9e2f-42d3-b0f6-1d1bc5fd374c', 'Bulk for Auto-bagger', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('b5848775-ae9a-43f9-a243-a517783d190f', '3PL - TX Southwest', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('8fc7372b-3f85-4980-8be9-17d486af8cc2', '3PL - NJ Uncommon Carriers', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('7e95154d-bb19-4cc4-8054-6dbed64d0196', 'Film for Auto-bagger', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('d7fb08ec-c80d-4dee-b486-dde2cf76d8e5', 'Test PO', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('321f98bb-4f04-4f66-82c2-426151f645e9', 'Delivery Date Change', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('333e028a-0627-45df-9f86-74a28cd7006e', 'Truck Capacity Split', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3d7fe742-fd80-489d-9a1b-d23e5bcdb522', 'Add-Ons', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('91ff2b8f-f583-46d3-bd7f-a5a5f2ec5eca', 'Database correction', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('0a11d5ef-1c4a-4fbc-9854-74a93e235023', 'Safety Stock', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('32082a09-21b3-4685-bba3-770647a013db', 'Backhaul', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('48941b1c-b678-492d-8aa9-60e369bc3955', 'Database correction', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3ccde9ad-ab92-4c9c-b80c-4f38ea9639b7', 'Merchandising', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('d2d42380-04e0-4b27-a8e8-b125f666bd45', 'Marketing', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('46517571-26eb-4e2d-a76c-290a0c06ea66', 'Packaging', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('84fffef2-3bc2-41b3-b474-fbac2d1f3f70', 'Supply issue', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2fda89a6-0db4-438c-86c1-a68a1d19254a', 'Short delivery', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('d9dcfa49-e050-4b88-bb16-acd4d818b849', 'Quality issue', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('44f3fbfd-9921-4eca-92e2-d4bca5d8ba5c', 'Late delivery', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('62e9ed42-f043-4909-bd2a-6415facde270', 'HelloFresh error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('72d54257-2de4-438e-b0f2-e4e650345733', 'Add-ons', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('1058b467-0d4c-4c17-bed4-e3e63a20ec62', 'Arranged', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('727f06f2-ec36-405d-b8b7-5cc8807cc1fa', 'Cross-docking', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('*************-45d2-abf4-6d52460c6ff7', 'Database Correction', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('c98610e0-2a3d-4c4a-afea-455de09a140a', 'Demand Change', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3ccbe5df-2f78-4e18-bd12-aac33d63730f', 'From Storage', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2652284b-27b8-40c4-9446-d34aed870931', 'HelloFresh error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('130c5ef6-0d69-486c-b201-7dd61e0937d2', 'Historical Upload', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('73d8a03a-8592-4552-b05b-370115d312ab', 'Packaging', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('435265ea-901c-4c56-8644-630d13be1c7d', 'Price Error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ba499a99-18e3-4ff7-af03-1c3bf586a796', 'Regular', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('4bf38165-ca82-4037-b010-882731395e4c', 'Replenishment', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('8c39b034-43ba-4d4c-8141-b34b0d16425e', 'Stock Error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('5224886e-b180-448b-b9e8-25806447c486', 'Supplier Error', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('243cf5dd-4a15-4a11-b664-e870c2c9f1e1', 'Supply Issue', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('cd05b544-7267-421d-ac7a-bc4c371696f9', 'Test PO', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3bc31565-1560-4fb2-ac71-ddeb6ef681e3', 'Repacking- All in', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2f596564-7340-4336-88c6-d36e5da4a94e', 'Internal Transfer', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('c84b7409-6d3b-4ae6-8495-3142d2c0d84c', 'Multi Week', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('e5876b2f-b484-4665-9f7f-9ca8bbd3080a', 'Bulk Order', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('63a551b2-8b87-4b31-ab09-4c2a1963992c', 'Long Lead time', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('263ddddb-ee5e-4b8f-ba8a-0e21da2422d7', 'Repackaging', 'gb');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('6e6c272f-032d-42d3-ac4c-67118bb8b6a1', 'Charity - SoNY', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('c0c03bad-25d2-4259-9d60-68623594d06f', 'Charity - TAFB', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('edbc3c93-2ac7-4b45-a1d3-8edb1bae6973', 'Unexpected Shortage - FedEx/UPS', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3b012b58-8c55-48f9-8a72-0a2f0ed6558e', 'Offsite Production - MD', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3230c284-b581-4d57-a1a6-cc6aa1db3b99', 'Offsite Production - PA', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('36c52f94-6a66-414b-9bb2-8ed60a983390', 'Offsite Production - FreshPoint', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('a2617648-6456-4f3c-ad83-65c91853cb22', 'Offsite Production - Standard Meats', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('59a960d0-c71e-4ecc-9e35-8e6d26f18f4b', 'Offsite Production - Deen''s Meats', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('339db74c-2495-4b7f-88bc-d6d05929a132', 'Offsite Production - GoCheetah', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('636ae3a7-da49-45f7-82b9-521903f7f72d', 'Offsite Production - NW', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2ab2ac46-66ff-4f62-ac4e-35de6875009c', 'Offsite Production - DL', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('a80aa9aa-d923-4bee-a65e-c5e120f8805f', 'Retail', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('aa4380b8-1e4d-41fb-81dc-53fb2664ce53', 'Retail - HK', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('195031a3-925b-4eb5-9d9d-201d052e721f', 'Retail - Indy', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('96ea5ef0-592b-4e06-9cc4-f65dd56ace17', 'Retail - GFK', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('226a70be-cbe8-4078-a36a-56447bd2ace4', 'Retail - UCC', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('e153298f-b908-442b-ae50-4c2183ad7bb9', 'Retail - Burlington', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('32d7829c-d445-4de5-bb1e-e38d6ee1dbde', 'Retail - Win', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('eaa73ede-b562-4947-9515-c937df3e93d0', 'Case Shortage Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('b26c0a1e-8703-4f40-8877-100428543692', 'HF-Managed Freezing', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('d1ff7a99-b839-47a1-bd2b-bc10260953a2', 'Holiday', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ec1ca1e1-8f6c-415e-934b-9a77ccee2ea6', 'Forecast Increase Order', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('11d64996-1056-4f9a-a887-e7553ce7dffe', 'Communicated Shortage Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('af3b9e24-f69e-4a9c-bbad-f9c978443a83', 'Unexpected Shortage Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2fe9ba9d-245b-4561-a154-802f4e60de4a', 'Future Week Product Pull Replacement', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('7a65379e-87b6-466d-ad26-1b9ccceafbe1', '3PW Inventory Pull', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ecdd4ac7-0afb-435f-b9ed-1ab0caba27a7', 'OrderPlanningService-US', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('00ad806f-0ca2-4a87-a286-a53a01264cc6', 'Marketing', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('209fa559-ca61-44fe-bbb2-640a97f9082d', 'Insourcing', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('f1ee49c1-30f8-45bc-b743-082276782975', 'Charity - T2T', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ca8b25b2-d926-43f6-a617-0f3168d3ad54', 'Charity - SHA', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('c0e6f4a8-289a-4e36-8b30-e2358651b4d5', 'Charity - SMFB', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('254cbd5a-feef-4478-9bb3-2ed01e6a26d2', 'Initial Ordering Non-Emergency', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('8478b54e-c624-4a53-b956-9eff9761357a', 'Sauce Remake', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('e863eb89-a86d-492c-a691-ef36e639e862', 'Bulk SKU Order', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('2bed6c5d-779d-4a58-92ec-bc617d5a9d3b', 'Emergency DC Transfer', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('266bb2f5-81f5-4860-a591-b81e370c44be', 'Carrier Delay Supplemental Add On', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('1a4548b4-541c-45dd-beea-32bb20298317', 'Vendor Delay Supplemental Add On', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3be40bce-4a4c-49d9-b1b5-4beac203a3fb', 'Autobagger PO', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3c3a9c14-b1d8-4637-9a8b-e9a46018097f', 'Specialty Box', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('3862d184-6f32-4582-b3ef-2a2ebc6aa0b6', 'Vendor Error Split Delivery', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('a5a9162b-7644-495f-831a-c5ea20af6ddc', 'Protein Offsite Cold Storage', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('ea3f5975-0c69-4b1e-954e-fc5c34a47bcf', 'Charity - WDW', 'us');
INSERT INTO "emergency_order_reason"(uuid, name, market)
VALUES ('4a05f404-3240-43fe-ab97-534b6a4ae37e', 'OrderPlanningService-GB', 'gb');
