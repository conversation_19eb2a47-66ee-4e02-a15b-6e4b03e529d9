UPDATE emergency_order_reason
SET name = '3PL Backhaul'
WHERE uuid = '32082a09-21b3-4685-bba3-770647a013db';

UPDATE emergency_order_reason
SET name = 'Standard Non-Emergency or Safety Stock order'
WHERE uuid = '254cbd5a-feef-4478-9bb3-2ed01e6a26d2';

UPDATE emergency_order_reason
SET name = 'Forecast Increase Supplemental Order'
WHERE uuid = 'ec1ca1e1-8f6c-415e-934b-9a77ccee2ea6';

UPDATE emergency_order_reason
SET name = 'Emergency UPS/FedEx'
WHERE uuid = 'edbc3c93-2ac7-4b45-a1d3-8edb1bae6973';

INSERT INTO emergency_order_reason(uuid, name, market)
VALUES ('6499c384-c285-4fd9-a7a4-0aea2128fc53', 'Unexpected Shortage Replacement- Procurement Driven', 'us'),
       ('61e19ffa-88dc-4a7d-a018-98d3320fbab3', 'DC Communicated Shortage Replacement', 'us'),
       ('8448a0cc-2d31-4cf7-ae80-36c8dc08aca5', 'Unexpected Shortage Replacement- Transportation Driven', 'us'),
       ('5c42a01b-4dc6-46d8-932b-bdca2495eeac', 'Unexpected Shortage Replacement- Vendor Driven', 'us');
