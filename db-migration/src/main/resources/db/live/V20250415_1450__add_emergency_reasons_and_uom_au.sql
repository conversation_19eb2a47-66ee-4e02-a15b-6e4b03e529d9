INSERT INTO emergency_order_reason(uuid, market, name, disabled)
VALUES ('d1cbea48-2724-4497-a713-77a2a5591e05', 'au', 'Stock Movement', false),
       ('05384819-d4ce-4fd8-8854-3596368d19c6', 'au', 'Packaging', false),
       ('9b657112-0fe9-42d6-a407-6165601760df', 'au', 'Internal Procurement Issue', false),
       ('893db735-58ec-49da-a8f5-bdc970a88761', 'au', 'Internal Production Issue', false),
       ('001442a9-fba6-402e-90fc-aea125d7f463', 'au', 'Internal Tech Limitation', false),
       ('9676e216-f955-4840-ba4b-310efe03bb71', 'au', 'Internal Forecast Change', false),
       ('b3e7d174-4cb7-4d0d-be2c-1aca6408e496', 'au', 'Supplier Error', false),
       ('e22e4c06-a594-4d0d-a4d2-ea6e4f65ab63', 'au', 'Multi-Week', false),
       ('8a1ace07-7d44-4b25-9388-9d2b5314f0cc', 'au', 'Raw Ingredients (Bulk)', false),
       ('05b1ee4b-1561-4351-885b-556825b57b2e', 'au', 'Non-Pick List Ingredient', false),
       ('7a59bfa0-af7b-4592-a4e7-85d6799e3aeb', 'au', 'Sample', false),
       ('2621e740-8b17-43cb-8bf8-ccdd23733c37', 'au', 'Marketing', false);

INSERT INTO unit_of_measure(name, type, market, enum_value)
VALUES ('unit(s)', 'unit', 'au', 'UNIT'),
       ('kg', 'bulk', 'au', 'KG');