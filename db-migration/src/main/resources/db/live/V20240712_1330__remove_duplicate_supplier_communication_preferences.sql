-- remove duplicates in dach
DELETE
FROM communication_preference
WHERE supplier_id in (
                      '03817495-56cd-4c74-aa89-47f4a4e44269', -- 10022
                      '8b1a84b9-d0cd-4f2a-8999-d6d24a05b6f4', -- 113202
                      'ad73415e-1d30-435e-81a9-d2c5911bdbef', -- 113447
                      'd7fd76e1-1377-4360-9fee-10680be0f5a3' -- 10052
    )
  AND preference = 'E2OPEN';

DELETE
FROM communication_preference
WHERE supplier_id in (
    '38c5a2ae-b926-4737-9036-503c3e80e4e3' -- 110007
    )
  AND preference = 'EMAIL_AND_E2OPEN';

-- remove duplicates in it
DELETE
FROM communication_preference
WHERE supplier_id in (
                      '12ca5f0e-ec61-446a-ab18-e1fa8e0aab07', -- 112223
                      '8106227f-d3c4-4640-92ae-e701abc23692' -- 300311
    )
  AND preference = 'EMAIL_AND_E2OPEN';
