DELETE
FROM communication_preference
WHERE supplier_id in (
                      '0199e541-d18f-4a3e-9e01-d7fd0ab9370c',
                      '55b67fb9-c433-4bc7-84dc-80af2a70b281',
                      'a918beb6-4d3e-4c82-b16d-faaf362cae8e'
    )
  AND preference = 'EMAIL_AND_E2OPEN';

INSERT INTO communication_preference (supplier_id, preference)
VALUES ('0199e541-d18f-4a3e-9e01-d7fd0ab9370c', 'E2OPEN'),           -- DE: 113379
       ('55b67fb9-c433-4bc7-84dc-80af2a70b281', 'E2OPEN'),           -- DE: 113401
       ('5a23c3cf-1dfe-45b3-8132-cb01d8796aca', 'E2OPEN'),           -- DE: 300438
       ('c2fccb90-8c89-438b-8d03-861fc1cd9ced', 'EMAIL_AND_E2OPEN'), -- IT: 112950
       ('a918beb6-4d3e-4c82-b16d-faaf362cae8e', 'E2OPEN'); -- DE: 10013
