-- dach suppliers (E2Open)
INSERT INTO communication_preference (supplier_id, preference)
VALUES ('03817495-56cd-4c74-aa89-47f4a4e44269', 'E2OPEN'), -- 10022
       ('d7fd76e1-1377-4360-9fee-10680be0f5a3', 'E2OP<PERSON>'), -- 10052
       ('8b1a84b9-d0cd-4f2a-8999-d6d24a05b6f4', 'E2OPEN'), -- 113202
       ('ad73415e-1d30-435e-81a9-d2c5911bdbef', 'E2OPEN'), -- 113447
       ('ab981711-fb44-4cdd-8b7d-68d4835dfc45', 'E2OPEN'), -- 300233
       ('2e7e57c5-91d3-4d44-9bc4-b507724c678b', 'E2OPEN'), -- 300235
       ('ef8c79a5-a60f-4502-abd0-85c7c1ef4645', 'E2OPEN'), -- 300236
       ('395f6f68-3134-4639-a356-b5d6b419d272', 'E2OPEN'), -- 300241
       ('f4458e0e-11c5-4e8f-968c-17742256415a', 'E2OPEN'), -- 300384
       ('e1bcb76c-c2fd-4509-a264-4fc2f2627216', 'E2OPEN'), -- 300385
       ('965ce34e-a51d-433d-bbbc-0897511dd2e0', 'E2OPEN'), -- 300387
       ('f0b9f3da-2d77-4de6-bbd0-5ef44544e4e4', 'E2OPEN'), -- 300388
       ('860f8407-9d01-4ab7-8162-1f1c532611bc', 'E2OPEN'), -- 300390
       ('8c735b5b-9d94-40bf-b1b5-dadbe035336e', 'E2OPEN'); -- 300391

-- dach suppliers (Email and E2Open)
INSERT INTO communication_preference (supplier_id, preference)
VALUES ('f4490e36-6289-46c2-b0c5-f6db1d4b7884', 'EMAIL_AND_E2OPEN'), -- 300212
       ('41dff2aa-79ab-478d-942f-f7e31e294a01', 'EMAIL_AND_E2OPEN'), -- 300213
       ('38f3bcc8-8389-4976-ac39-aa5c1658518d', 'EMAIL_AND_E2OPEN'), -- 300611
       ('34c26ec3-57fc-44a6-925f-d062aa696e13', 'EMAIL_AND_E2OPEN'); -- 300612

-- it suppliers (Email)
INSERT INTO communication_preference (supplier_id, preference)
VALUES ('8106227f-d3c4-4640-92ae-e701abc23692', 'EMAIL'), -- 300311
       ('98898a19-90bf-4f79-9a39-30215e19b78e', 'EMAIL'); -- 300319

