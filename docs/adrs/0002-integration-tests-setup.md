# 1. OMS INTEGRATION TESTS

Date: 2023-01-27

## Status

Accepted

## Context

This ADR is created to decide about a way to:

* unify the way we test integration between OMS and external systems like: kafka, postgres etc.
* introduce integration tests for kafka

## Current state

* unit tests with Mockito
* integration tests oms <--> database - embedded zonky db (kafka-processor), postgres testcontainer (order-management-http)
* no integration tests for oms <--> kafka

## Possible solutions

1. Docker containers.

  * Already implemented in `order-management-http`
    module - [link](https://github.com/hellofresh/order-management-service/blob/master/order-management-http/src/test/kotlin/com/hellofresh/oms/orderManagementHttp/supplier/SupplierRepositoryIntegrationTest.kt#L22).
  * We use [testcontainers](https://www.testcontainers.org/) dependency to spin up docker containers in a test runtime.
  * Containers run per module.

2. In memory services (ex. zonky, spring-kafka-test).

  * Already implemented in `kafka-processor` module to test database
    integration - [link](https://github.com/hellofresh/order-management-service/blob/master/kafka-processor/src/test/kotlin/com/hellofresh/oms/supplierSkuPrice/SupplierSkuPriceRepositoryTest.kt#L13)
  * Poc PR for Kafka - [link](https://github.com/hellofresh/order-management-service/pull/88/)
  * We use dedicated libraries to mock external
    services/applications: [Postgres](https://github.com/zonkyio/embedded-postgres), [Kafka](https://spring.io/projects/spring-kafka)

3. Combination of points 1 and 2.

  * Poc PR [link](https://github.com/hellofresh/order-management-service/pull/89)
  * Introduce new module `integration-test`
  * Test example:

```agsl
Given: Supplier avro message
When:  Test producer publishes the message
And:   HttpClient call GET /suppliers
Then:  Response should contain published supplier
```

Required containers:

- order-management-http
- kafka-processor
- schema-registry
- kafka-broker
- postgres ..

### Comparison table

|      | In memory services                                                                                                                                                                                                                                                                            | Docker containers (per-module)                                                                                                                                                                                                                                             | Combo                                                                                                                                                                                         |
|------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Pros | - Very fast<br/> - No data state management between tests / between modules needed<br/> - No dependencies on other applications like docker<br/> - Integration tests for each of the modules can be run in parallel without hassle<br/> - No need to build docker images<br/> - Easy to debug | - Test environment close to production one<br/> - Data state re-usability                                                                                                                                                                                                  | - Allow to have very fast integration tests per-module and also test with production like environment<br/> - Able to test data flow including multiple services/applications at the same time |
| Cons | - This test environment is further from the real one so we could experience some integration surprises ex. service can't connect to kafka-broker<br/> - Lack of postgres state re-usability                                                                                                   | - Relatively slow<br/> - Dependency on Docker (deamon needs to be up)<br/> - Additional effort to manage state between tests<br/> - Harder to design the tests to be run in parallel<br/> - Harder to debug<br/> - Necessity to duplicate/share test setup between modules | - Very slow<br/> - Build local images<br/> - Overwriting tests                                                                                                                                |

### Decision

We decided to go with solution nr 2. It turned out to be an option that allows us to proceed much faster with satisfying test coverage.
The test containers setup is there for us if we decide that additional test layer is needed.
