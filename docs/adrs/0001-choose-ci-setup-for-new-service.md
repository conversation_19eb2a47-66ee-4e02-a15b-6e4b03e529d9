# 1. CI Order Management

Date: 2022-10-28

## Status

Accepted

## Context

Before we start working on continues integration for our new service ([OMGMT-59](https://hellofresh.atlassian.net/browse/OMGMT-59)) we want to make a decision
between CI/CD or manual deployment setup.

## Possible solutions

1. Continuous Delivery (CD).

The pipeline contains all necessary steps to ship a change to production like:

- build
- test
- create release
  - migrate databases
  - deploy application to particular environments etc.

The last step in the pipeline is an automatic deployment to production environment.

[Order Planning Service example.](https://github.com/hellofresh/order-planning-service/actions/workflows/on-push-master-release-and-deploy.yml)

2. Manual Deployment.

The pipeline is the same as the one above except the last step. Deployment to production environment happens via manual trigger in GHA UI.

[Scm Front App example.](https://github.com/hellofresh/scm-front-apps/actions/runs/3344361104)

3. Hybrid of points 1 and 2 - skip live deployment based on pr-label.

### Comparison table

|      | CI/CD                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | Manual deployment                                                                                                                                                                                                                                                                                                          | Hybrid (use GH label)                                                                                                                                                                                                     |
|------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Pros | - Lack of inconsistencies between environments (Live, Staging)<br/> - Deploy one PR at the time<br/> - No hassle to do any manual operations <br/> - Simple revert<br/>- No need to keep a discipline for deploying changes to production (automatic deployment)<br/> - Makes possible to clone data between environments (Staging and Live)<br/> - Accordingly to the State of DevOps Report there is a strong correlation between the deployment frequency and overall company performance. From the 2021 report, the top-performing companies have 973x more frequent deployments, and 6570x faster lead time from commit to deploy than the average performers. | - We can test on staging<br/>- GitHub support for controlling live deployment ex. assign approves, setting rules for pending changes to be canceled (non blocking deployment.)<br/>- Github has quite good UI to manage all the deployment related rules.<br/>                                                             | - We test on staging before deploying live by setting PR label<br/>- We can stop live deployment for special PR.<br/>                                                                                                    |
| Cons | - Necessity to ship production ready chunks which can cause bigger pr's.<br/> - Increase amount of feature flags to be developed<br/>- Changes can not be tested on staging before deploying live                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | - Inconsistency of versions deployed to environments (Staging Live)<br/>- Misconfiguration of deployment rules (ex. excluding all reviewers ends up with CI/CD) made by admins.<br/>- Impossible to clone data between environments (Staging and Live)<br/>- Need to keep a discipline for deploying changes to production | - Need to keep a discipline for deploying changes for PR's being skipped.<br/> - Requires additional logic to be implemented and maintained in CI<br/>- Impossible to clone data between environments (Staging and Live) |

### Decision

According to few discussions took place we decide for Continues Delivery (option 1).
