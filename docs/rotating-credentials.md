# Rotating Credentials

## RDS

To rotate the password for <PERSON>, We can use the dynamic credentials. Even setting up this infrastructure still requires the master password.
Rotating the master password can be done via AWS management console. This would also require updating vault and restarting the pods.

1. Navigate to [AWS](https://hfsso.awsapps.com/start#/) login (or via [Azure Apps](https://myapplications.microsoft.com/)).
2. Choose the `main-it`, navigate to RDS → DB Instances.
3. Find the correct database and click `Modify` button on top right corner.
4. Provide a new master password with confirmation.
5. Update the password value in the corresponding vault namespace.
6. Re-start the pods by scaling them down, and then back up.
The application could alternatively be re-deployed, but due to how current CI/CD
process is based on changed files, this might fail to redeploy all pods.
    ```shell
    # login with the correct profile
    aws sso login --profile sso-eks-staging-admin
    # run ktx to switch to the correct context, for staging "sso-eks-staging-admin"
    ktx
    # run kns to switch to the "scm" namespace
    kns scm
    # scale the pods up/down
    kubectl scale --replicas=0 deployments oms-order-management-http-app
    kubectl scale --replicas=2 deployments oms-order-management-http-app
    # note how the number of replicas might be different per app
    # make sure to not bump up the number of replicas for the apps
    # that are designed to run a single instance
    kubectl scale --replicas=0 deploymentsoms-kafka-processor-app
    kubectl scale --replicas=1 deploymentsoms-kafka-processor-app
    kubectl scale --replicas=0 deployments oms-purchase-order-synchronizer-app
    kubectl scale --replicas=1 deployments oms-purchase-order-synchronizer-app
    ```
7. Verify that the pods are back up and running.
8. Additionally, it might be useful to drop the existing connections. To make sure potential attacker does not keep an open connection.
Be aware, it will also drop the connections that the running pods might be using.
```sql
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE
    pid <> pg_backend_pid()         -- don't kill my own connection!
    AND datname = 'database_name' ; -- don't kill the connections to other databases
```
