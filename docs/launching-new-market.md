# Launching a new market requires a couple of steps to be done.

The following steps are required to be done in order to launch a new market.
- [ ] Open a new ticket in order-management-service or Jira to track the completion of the following steps
- [ ] [Check emergency reasons are populated correctly](#check-emergency-reasons-are-populated-correctly)
- [ ] [Check that OPE emergency reasons are disabled in order-management-service](https://github.com/hellofresh/order-management-service/pull/530/files)
- [ ] [Check order item change reasons are populated correctly](#check-order-item-change-reasons-are-populated-correctly)
- [ ] [Check UOMs for a new market are populated correctly](#check-uoms-for-a-new-market-are-populated-correctly)
- [ ] [Allow roles for market in FE Fragment](#allow-roles-for-market-in-fe-fragment)
- [ ] [Enable features flag for a new Market in FE](#enable-features-flag-for-a-new-market-in-fe)
- [ ] [Check that supplier skus and supplier sku prices are published and consumed correctly](#check-that-supplier-skus-and-supplier-sku-prices-are-published-and-consumed-correctly)
- [ ] Rollout first on staging, engage in testing and then rollout to the live environment

## Check emergency reasons are populated correctly

- Run this query in OT to get all the enabled emergency reasons for the market
```sql
select oer.uuid, lower(c.iso2code) market, oer.name, oer.isdisabled as disabled
from order_emergency_reasons oer
join public.countries c on oer.country_id = c.id
where c.iso2code = <MARKET_CODE> and oer.isdisabled = false
```
- Insert all the results of a previous query into `emergency_order_reason` table.

## Check order item change reasons are populated correctly

- Run this query in OT to get all the order item change reasons for the market
```sql
select cr.uuid id, cr.name, 'ORDER_ITEM_CHANGE' reason_type, string_to_array(c.iso2code, '') allowed_markets
from correction_reasons cr
join public.countries c on cr.country_id = c.id
where c.iso2code = <MARKET_CODE>;
```
- Insert all the results of a previous query into `change_reason` table.

## Check UOMs for a new market are populated correctly

- Run this query in OT to get all the UOMs for the market
```sql
select distinct uom.type from units_of_measure uom join countries co
on uom.country_id = co.id
where co.iso2code = 'NZ';
```
- Check, if bulk is present?
  - if yes: [We have this documentation](https://hellofresh.atlassian.net/wiki/spaces/SCM/pages/3533078529/Bulk+Ordering+via+EPO).
- if existing types are `mass` and `unit`, then just check if `unit` is already present in OM db.
  - if not, then insert using a migration script.

## Check that supplier skus and supplier sku prices are published and consumed correctly
Supplier sku prices are only periodically published by Supplier Portal Service (SPS).

The following query can be used to check if the supplier sku prices were published and consumed correctly.
```sql
SELECT count(*) from supplier_sku_price where dc_codes && (SELECT array_agg(code::text) from distribution_center where market = '<MARKET_CODE>');
```
If there's an issue with the supplier sku prices, then contact SPS team and ask them to republish the affected markets. [An example.](https://hellofresh.slack.com/archives/CUDNHJ1PF/p1707304225120099)

## Allow roles for market in FE Fragment

Fragments `Pages` need to be passed the correct `purchasing.<country>.all.manager` for access to be allowed.

This is done in all entry points in the following path: [@hellofresh/scm-front-apps/packages/scm-order-management/pages](https://github.com/hellofresh/scm-front-apps/tree/master/packages/scm-order-management/pages)

[Example PR](https://github.com/hellofresh/scm-front-apps/pull/13028/files)

## Enable config for the new Market in FE

Finally a local configuration to be updated for the new market to allow the correct navigation from the Emergency Order page to the new tool. There are 3 keys currently in use:
- `newOrderCreate` - Manages redirection to the new Manual Order Create
- `newOrderEdit` - Manages redirection to the new Manual Order Edit
- `newOrderClone` - Manages redirection to the new Manual Order Clone

The keys can be found under the following path [@hellofresh/scm-front-apps/packages/scm-order-management/common/config/index.ts](https://github.com/hellofresh/scm-front-apps/blob/master/packages/scm-order-management/common/config/index.ts)
