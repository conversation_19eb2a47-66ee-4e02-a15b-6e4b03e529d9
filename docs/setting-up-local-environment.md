# Getting Started

## Required Tools

* JDK 11 or higher
* [Docker](https://docs.docker.com/docker-for-mac/install/)
* [Docker Compose](https://docs.docker.com/compose/install/)
* [Hasicorp Vault](https://vaultproject.io/downloads)
  * for retrieving the `ansible-vault` key for decrypting the `.env.dist` file.

## Required Access

* Make sure that you have `awscli` setup, refer
  to [AWS Single Sign-on](https://github.com/hellofresh/infrastructure/blob/master/aws/README.md)
* Login to the AWS ECR (the docker
  registry) [Using ECR from workstation](https://hellofresh.atlassian.net/wiki/spaces/PLAT/pages/1100185638/Migrating+from+Quay.io+to+ECR#MigratingfromQuay.iotoECR-UsingECRfromworkstation)
  * Shortcut command:
* Some of the setup steps require that the HelloFresh VPN is enabled.

```bash
aws sso login --profile sso-hf-it-developer
aws ecr get-login-password --region eu-west-1 --profile sso-hf-it-developer | docker login --username AWS --password-stdin 489198589229.dkr.ecr.eu-west-1.amazonaws.com
```

## Running with Docker

First you need to create a copy of the `.env.dist` file to `.env`.

Optional: update the values in the created `.env` file if needed.

## How to build the project

Simply execute the following command from the project root:

```shell
./gradlew build
```

## Run tests

```bash
./gradlew check
```

This task also generates a code coverage report available under `build/reports/jacoco/jacocoASggregatedReport` and also as a console output after the task's successful execution.

**Note:** Gradle does not execute tests subsequently if there is no modification in the built binary. To force Gradle to re-run the test use the following command:

```bash
./gradlew cleanTest test
```

## Verifying distroless image

To get the sha256 for the distroless image like [here](https://github.com/hellofresh/order-management-service/blob/89fabf75fe18501c0e960c1fa2e97e780842090f/gradle.properties#L9), please refer to this [link](https://github.com/GoogleContainerTools/distroless#how-do-i-verify-distroless-images).
