# Joining the On-Call rotation

The Integrated Ordering domain provides the On-Call support for the following services:

* [tapioca](https://github.com/hellofresh/tapioca)
* [sku-mapper](https://github.com/hellofresh/sku-mapper)
* [order-management-service](https://github.com/hellofresh/order-management-service)
* [order-planning-service](https://github.com/hellofresh/order-planning-service)
* [distribution-center-registry](https://github.com/hellofresh/distribution-center-registry)
* [supplier-portal-integration](https://github.com/hellofresh/supplier-portal-integration)

## Prerequisites

Participation in the On-Call rotation is voluntarily, but in order the qualify for the On-Call rotation, the candidate should:

* Have at least 6 months of experience with the HelloFresh organization.
* Familiarize themselves with the existing runbooks.
* Preferably join the Fire-Fighting rotation upfront and participate in any mitigation efforts.
* Receive approval from the line manager.

## Required Credentials

| Service      | Description                   | Link                          |
|--------------|-------------------------------|-------------------------------|
| AWS          | Access RDS/S3                 | [AWS](#aws)                   |
| K8s          | Inspect pods, Rollback        | [K8s](#K8s)                   |
| Grafana      | Observability & Logs          | [Grafana](#Grafana)           |
| Aiven        | Kafka authentication          | [Aiven](#Aiven)               |
| Statsig      | Feature Flag management       | [Statsig](#statsig) |
| Sendgrid     | Sending email communications  | [Sendgrid](#sendgrid)         |
| AppLanga     | Managing translations         | [AppLanga](#applanga)         |
| E2Open       | Supplier communication portal | [E2Open](#e2open)             |
| OpsGenie     | Automated alerting            | [OpsGenie](#opsgenie)         |

## Obtaining Credentials

### AWS

AWS accounts can be created by making a request to
the [Global IT Helpdesk - AWS Accounts](https://hellofresh.atlassian.net/servicedesk/customer/portal/4/group/252/create/1287)

You will need the following account/roles:

* k8s-live / `acl_aws_sso_eks_live_admin`
* k8s-staging / `acl_aws_sso_eks_staging_admin`
* main-it / `acl_main-it_developer`

### K8s

TBC

### Grafana

To get access to Grafana, please complete the below request for access.

You will need to choose 'Grafana' from the software dropdown. "On-call support" is typically a valid business Justification.

[Request for access (IT Helpdesk)](https://hellofresh.atlassian.net/servicedesk/customer/portal/4/group/252/create/2984)

### Aiven

TBC

### Statsig

> It will be replaced by [Statsig](https://statsig.com) for feature flag management.

Statsig is a tool for managing feature flags.

The following three people in planning-and-purchasing (Berlin) are Statsig admins and can create accounts:

* [Neil Tarrant](mailto:<EMAIL>)
* [Serghei Bleih](mailto:<EMAIL>)

In the event of both being unavailable, you can make a request in [#statsig-operations-feature-flags-users](https://hellofresh.slack.com/archives/C07F81ZP5SP), however
this may take some time as the other admins are in US/AU timezones.

You will need access to the following teams:

* `Purchase Order Lifecycle`
* `SCM Front Apps`

### Sendgrid

To get access to Sendgrid, please complete the below request for access.

You will need to choose 'other' and enter 'SendGrid'. "On-call support" is typically a valid business Justification.

[Request for access (IT Helpdesk)](https://hellofresh.atlassian.net/servicedesk/customer/portal/4/group/252/create/2984)

### Applanga

To get access to Applanga, please complete the below request for access.

You will want to request access to the 'HelloFresh Operations' team, with a 'Manager' role. "On-call support" is typically a valid business Justification.

[Request for access (IT Helpdesk)](https://hellofresh.atlassian.net/servicedesk/customer/portal/4/group/252/create/2984)

### E2Open

E2Open operates the Supplier Portal, a tool where suppliers can review and accept/reject purchase orders, as well as create ASNs (advance shipment
notifications). It is also used for inventory downloads, however this integration is owned by the Fulfilment alliance (useful contacts
are [Boris Blinov](<EMAIL>) and [Aleksander Petrovski](<EMAIL>))

To request an account with E2Open... [TBD]

### OpsGenie

To create a user in OpsGenie is a two-stage process. You must first follow the instruction in
[creating an Opsgenie user (HelloDev)](https://hellodev.hellofresh.io/project/kubernetes/docs/kubernetes/observability/05-Incident-Management/01-infrastructure-components/03-opsgenie-team-setup/#users)
to create the user in the OpsGenie automation repository, after which you can complete
the [Request for access (IT Helpdesk)](https://hellofresh.atlassian.net/servicedesk/customer/portal/4/group/252/create/2984) form.
