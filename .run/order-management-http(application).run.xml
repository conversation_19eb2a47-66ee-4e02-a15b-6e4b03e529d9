<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="order-management-http(application)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ALTERNATIVE_JRE_PATH" value="19" />
    <envs>
      <env name="API_ALLOWED_ISSUERS" value="r2a543nd-6o21-47m7-a979-5b3302b8e9d6" />
      <env name="PLANNED_ORIGIN_ISSUERS" value="r2a543nd-6o21-47m7-a979-5b3302b8e9d6" />
      <env name="AUTH_SERVICE_JWT_SECRET_KEY" value="some_long_secret_key_at_least_256_bits" />
      <env name="DB_PASSWORD" value="1234" />
      <env name="DB_URL" value="********************************************" />
      <env name="DB_USERNAME" value="manager" />
      <env name="OTLP_EXPORTER_HOST" value="localhost" />
      <env name="SPRING_PROFILES_ACTIVE" value="local" />
      <env name="TAPIOCA_BASE_URL" value="localhost" />
      <env name="AUTH_SERVICE_BASE_URL" value="random" />
      <env name="AUTH_SERVICE_CLIENT_ID" value="random" />
      <env name="AUTH_SERVICE_CLIENT_SECRET" value="random" />
      <env name="ICS_TICKETS_URL" value="localhost/api/v2/tickets/" />
      <env name="ICS_TICKETS_USERNAME" value="ics_tickets_username" />
    </envs>
    <module name="order-management-service.order-management-http.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.hellofresh.oms.orderManagementHttp.OrderManagementServiceApplication" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
      </ENTRIES>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
