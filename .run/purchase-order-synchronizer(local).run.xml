<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="purchase-order-synchronizer(local)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="local" />
    <option name="ALTERNATIVE_JRE_PATH" value="17" />
    <envs>
      <env name="DB_PASSWORD" value="1234" />
      <env name="DB_URL" value="********************************************" />
      <env name="DB_USERNAME" value="manager" />
    </envs>
    <module name="order-management-service.purchase-order-synchronizer.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.hellofresh.purchaseordersynchronizer.PurchaseOrderSynchronizerApplication" />
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>