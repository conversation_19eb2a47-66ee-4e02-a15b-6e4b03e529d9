package com.hellofresh.oms.model

import java.math.BigDecimal
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class PermyriadTest {

    @Test
    fun `should return correct percentage value`() {
        assertEquals(BigDecimal("17.42"), Permyriad(1742).toPercent())
    }

    @Test
    fun `should return correct percentage value without stripping the zeros`() {
        assertEquals(BigDecimal("17.00"), Permyriad(1700).toPercent())
    }

    @Test
    fun `should create correct permyriad from percent value`() {
        // given
        val percent = BigDecimal("17.42")

        // when
        val permyriad = Permyriad.fromPercent(percent)

        // then
        assertEquals(1742, permyriad.value)
        assertEquals(BigDecimal("17.42"), permyriad.toPercent())
    }

    @Test
    fun `should create correct permyriad from double percent value`() {
        // given
        val percent = 17.42

        // when
        val permyriad = Permyriad.fromPercent(percent)

        // then
        assertEquals(1742, permyriad.value)
        assertEquals(BigDecimal("17.42"), permyriad.toPercent())
    }

    @Test
    fun `should create correct permyriad from percent value with more than 2 decimals rounding down`() {
        // given
        val percent = BigDecimal("17.421")

        // when
        val permyriad = Permyriad.fromPercent(percent)

        // then
        assertEquals(1742, permyriad.value)
        assertEquals(BigDecimal("17.42"), permyriad.toPercent())
    }

    @Test
    fun `should create correct permyriad from double percent value with more than 2 decimals rounding down`() {
        // given
        val percent = 17.421

        // when
        val permyriad = Permyriad.fromPercent(percent)

        // then
        assertEquals(1742, permyriad.value)
        assertEquals(BigDecimal("17.42"), permyriad.toPercent())
    }

    @Test
    fun `should create correct permyriad from percent value with more than 2 decimals rounding up`() {
        // given
        val percent = BigDecimal("17.428")

        // when
        val permyriad = Permyriad.fromPercent(percent)

        // then
        assertEquals(1743, permyriad.value)
        assertEquals(BigDecimal("17.43"), permyriad.toPercent())
    }

    @Test
    fun `should create correct permyriad from double percent value with more than 2 decimals rounding up`() {
        // given
        val percent = 17.428

        // when
        val permyriad = Permyriad.fromPercent(percent)

        // then
        assertEquals(1743, permyriad.value)
        assertEquals(BigDecimal("17.43"), permyriad.toPercent())
    }

    @Test
    fun `companion constants should hold correct values`() {
        assertEquals(0, Permyriad.ZERO.value)
        assertEquals(1, Permyriad.ONE.value)
        assertEquals(100, Permyriad.ONE_PERCENT.value)
        assertEquals(10_000, Permyriad.HUNDRED_PERCENT.value)
    }

    @Test
    fun `should convert Permyriad to fraction`() {
        assertEquals("0.05", Permyriad(489).convertToBigDecimal().toString())
    }

    @Test
    fun `should convert Permitted to integer`() {
        assertEquals("1234", Permyriad(1234 * 10_000).convertToInteger().toString())
    }
}
