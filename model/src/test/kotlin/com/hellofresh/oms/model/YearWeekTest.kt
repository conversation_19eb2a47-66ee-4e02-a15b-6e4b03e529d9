package com.hellofresh.oms.model

import java.time.format.DateTimeParseException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class YearWeekTest {

    @Test
    fun `should instantiate YearWeek when provided valid year and week as integers`() {
        // given
        val year = 2023
        val week = 32

        // when
        val yearWeek = YearWeek(year, week)

        // then
        assertEquals("2023-W32", yearWeek.toString())
    }

    @Test
    fun `should instantiate YearWeek when provided valid year and week as strings`() {
        // given
        val year = "2023"
        val week = "32"

        // when
        val yearWeek = YearWeek(year, week)

        // then
        assertEquals("2023-W32", yearWeek.toString())
    }

    @Test
    fun `should throw an exception when provided invalid year and week as integers`() {
        // given
        val year = 2023
        val week = 300

        // then
        assertThrows<DateTimeParseException> { YearWeek(year, week) }
    }

    @Test
    fun `should throw an exception when provided negative year and week as integers`() {
        // given
        val year = -2023
        val week = -30

        // then
        assertThrows<DateTimeParseException> { YearWeek(year, week) }
    }

    @Test
    fun `should throw an exception when provided invalid year and week as strings`() {
        // given
        val year = "2023"
        val week = "300"

        // then
        assertThrows<DateTimeParseException> { YearWeek(year, week) }
    }
}
