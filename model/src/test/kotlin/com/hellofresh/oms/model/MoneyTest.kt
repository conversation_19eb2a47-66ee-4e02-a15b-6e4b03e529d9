package com.hellofresh.oms.model

import java.math.BigDecimal
import kotlin.test.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class MoneyTest {

    @ParameterizedTest
    @CsvSource(
        "1.10, 1.1",
        "0.001, 0.0009",
        "222.2, 222.20000",
        "0.0006, 0.001",
    )
    fun `money objects should be equal`(
        amount1: String,
        amount2: String
    ) {
        // given
        val money1 = Money(BigDecimal(amount1), "EUR")
        val money2 = Money(BigDecimal(amount2), "EUR")

        // then
        assertTrue(money1 == money2)
        assertTrue(money1.hashCode() == money2.hashCode())
    }

    @ParameterizedTest
    @CsvSource(
        "3.303, 3.3",
        "2, 3.000",
        "0.002, 0.0009",
        "222.2, 222.2006",
        "0.0005, 0.001",
        "0.0000001, 0.001",
    )
    fun `money objects should not be equal`(
        amount1: String,
        amount2: String
    ) {
        // given
        val money1 = Money(BigDecimal(amount1), "EUR")
        val money2 = Money(BigDecimal(amount2), "EUR")

        // then
        assertTrue(money1 != money2)
        assertTrue(money1.hashCode() != money2.hashCode())
    }

    @ParameterizedTest
    @CsvSource(
        "0, 0.0000000001",
        "0.0002, 0.0002",
        "0.0000000001, 0.0000000001",
    )
    fun `money object should map amount correctly with toTapioca function`(amount: String, expected: String) {
        // given
        val money = Money(BigDecimal(amount), "EUR")

        // then
        assertEquals(BigDecimal(expected), money.toTapioca())
    }

    @ParameterizedTest
    @CsvSource(
        "3, 3",
        "0.0001, 0",
        "0.00001, 0",
        "0.0002, 0.0002",
    )
    fun `money object should map amount correctly with fromTapioca function`(amount: String, expected: String) {
        // given
        val money = Money.fromTapioca(BigDecimal(amount), "EUR")

        // then
        assertEquals(BigDecimal(expected), money.amount)
    }
}
