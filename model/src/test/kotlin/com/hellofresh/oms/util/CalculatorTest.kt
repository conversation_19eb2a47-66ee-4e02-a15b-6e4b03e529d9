package com.hellofresh.oms.util

import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.UOM
import java.math.BigDecimal
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class CalculatorTest {

    @ParameterizedTest
    @CsvSource(
        "10, 10, 100.000",
        "10, 0, 0.000",
        "0, 10, 0.000",
        "1.1, 10, 11.000",
        "1.999, 10, 19.990",
        "1.99999, 10, 20.000",
        "1.99994, 10, 19.999",
        "1.99995, 10, 20.000",
    )
    fun `should calculate CASE RAW quantity`(
        caseSize: BigDecimal,
        numberOfCases: Int,
        expectedRawQty: BigDecimal
    ) {
        assertEquals(expectedRawQty, Calculator.calculateRawQuantity(caseSize, numberOfCases))
    }

    @ParameterizedTest
    @CsvSource(
        "100, 1, 100",
        "100, 10, 10",
        "0, 10, 0"
    )
    fun `should calculate total case quantity`(
        totalQuantity: BigDecimal,
        caseOrUnitSize: BigDecimal,
        expectedTotalCaseQty: BigDecimal
    ) {
        val packaging = Packaging(
            packagingType = PackagingType.CASE_TYPE,
            caseSize = caseOrUnitSize,
            unitOfMeasure = UOM.KG
        )
        assertEquals(expectedTotalCaseQty, Calculator.calculateTotalCaseQuantity(totalQuantity, packaging))
    }
}
