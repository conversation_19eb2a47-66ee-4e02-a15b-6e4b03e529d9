package com.hellofresh.oms.util

import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType.CASE_TYPE
import com.hellofresh.oms.model.PackagingType.PALLET_TYPE
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.RoundingMode.CEILING
import java.math.RoundingMode.HALF_EVEN

object Calculator {
    const val PRIME_NUMBER_FOR_HASH_CODE: Int = 31
    const val PRECISION = 3

    fun calculateRawQuantity(
        caseOrUnitSize: BigDecimal,
        orderSize: Int,
    ): BigDecimal =
        caseOrUnitSize.times(orderSize.toBigDecimal())
            .setScale(PRECISION, HALF_EVEN)

    fun calculateTotalQuantity(
        totalCaseQuantity: Int,
        caseOrUnitSize: BigDecimal,
    ): BigDecimal =
        BigDecimal(totalCaseQuantity)
            .times(caseOrUnitSize)
            .setScale(PRECISION, HALF_EVEN)

    fun calculateCaseOrUnitSize(packaging: Packaging): BigDecimal =
        when (packaging.packagingType) {
            CASE_TYPE, PALLET_TYPE -> packaging.caseSize!!
            UNIT_TYPE -> ONE
        }.setScale(PRECISION, HALF_EVEN)

    fun calculateTotalCaseQuantity(
        totalQuantity: BigDecimal,
        packaging: Packaging,
    ): BigDecimal = totalQuantity.divide(calculateCaseOrUnitSize(packaging), 0, HALF_EVEN)

    fun areEqual(
        a: BigDecimal?,
        b: BigDecimal?,
    ): Boolean = a?.setScale(PRECISION, HALF_EVEN) == b?.setScale(PRECISION, HALF_EVEN)

    fun calculateNumberCases(
        totalQuantity: BigDecimal,
        caseSize: BigDecimal,
    ) = totalQuantity.divide(caseSize, PRECISION, HALF_EVEN).toInt()

    fun calculateNumberOfPallets(
        totalQuantity: BigDecimal,
        caseSize: BigDecimal,
        casesPerPallet: BigDecimal
    ) = totalQuantity.divide(caseSize, PRECISION, HALF_EVEN)
        .divide(casesPerPallet, 0, CEILING).toInt()
}
