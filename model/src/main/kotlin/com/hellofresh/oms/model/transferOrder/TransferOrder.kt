package com.hellofresh.oms.model.transferOrder

import com.fasterxml.jackson.annotation.JsonProperty
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import org.hibernate.annotations.Type
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "transfer_order")
data class TransferOrder(
    @Id
    val id: UUID,

    // Required fields
    @Column(name = "source_dc_code")
    val sourceDcCode: String,

    @Column(name = "destination_dc_code")
    val destinationDcCode: String,

    @Column(name = "creator_email")
    val creatorEmail: String,

    @Column(name = "reason_text")
    val reasonText: String,

    @Enumerated(EnumType.STRING)
    val status: TransferOrderStatus,

    @Column(name = "production_week_year")
    val productionWeekYear: Int,

    @Column(name = "production_week_week")
    val productionWeekWeek: Int,

    @Column(name = "transfer_order_number")
    val transferOrderNumber: String,

    @Column(name = "source_dc_name")
    val sourceDcName: String,

    @Column(name = "shipping_method")
    val shippingMethod: String,

    @Column(name = "market_code")
    val marketCode: String,

    @Column(name = "version")
    val version: Int,

    @Embedded
    val totalPrice: TransferOrderPrice,

    // Shipping information stored as JSONB
    @Type(JsonType::class)
    @Column(name = "shipping", columnDefinition = "jsonb")
    val shipping: ShippingInfo,

    // Required timestamps
    @Column(name = "create_time")
    val createTime: LocalDateTime,

    @Column(name = "update_time")
    val updateTime: LocalDateTime,

    @Column(name = "pickup_start_time")
    val pickupStartTime: LocalDateTime,

    @Column(name = "pickup_end_time")
    val pickupEndTime: LocalDateTime,

    @Column(name = "delivery_start_time")
    val deliveryStartTime: LocalDateTime,

    @Column(name = "delivery_end_time")
    val deliveryEndTime: LocalDateTime,

    @Column(name = "sent_time")
    val sentTime: LocalDateTime,

    // Optional fields
    val comments: String?,

    @Embedded
    val deliveryChangeReason: TransferOrderChangeReason?,

    @Embedded
    val orderItemsChangeReason: TransferOrderItemsChangeReason?,

    @Column(name = "region_code")
    val regionCode: String?,

    @Column(name = "created_at")
    val createdAt: LocalDateTime,

    @Column(name = "updated_at")
    val updatedAt: LocalDateTime,

    @OneToMany(
        cascade = [CascadeType.ALL],
        fetch = FetchType.LAZY,
        mappedBy = "transferOrderId"
    )
    val items: List<TransferOrderItem> = emptyList()
)

// Data class for shipping information stored as JSONB
data class ShippingInfo(
    val address: AddressInfo,
    val method: String,
    val notes: String? = null
)

data class AddressInfo(
    @JsonProperty("regionCode")
    val regionCode: String,

    @JsonProperty("postalCode")
    val postalCode: String,

    @JsonProperty("administrativeArea")
    val administrativeArea: String,

    val locality: String,

    @JsonProperty("addressLines")
    val addressLines: List<String>,

    val organization: String,

    val recipients: List<String>? = null,

    val revision: Int? = null
)

@jakarta.persistence.Embeddable
data class TransferOrderChangeReason(
    @Column(name = "delivery_change_reason_key")
    val key: String?,

    @Column(name = "delivery_change_reason_value")
    val value: String?
)

@jakarta.persistence.Embeddable
data class TransferOrderItemsChangeReason(
    @Column(name = "order_items_change_reason_key")
    val key: String?,

    @Column(name = "order_items_change_reason_value")
    val value: String?
)

@jakarta.persistence.Embeddable
data class TransferOrderPrice(
    @Column(name = "total_price_currency_code")
    val currencyCode: String,

    @Column(name = "total_price_units")
    val units: Long,

    @Column(name = "total_price_nanos")
    val nanos: Int
)

enum class TransferOrderStatus {
    STATE_UNSPECIFIED,
    STATE_OPEN,
    STATE_DELETED,
    STATE_CANCELLED,
    STATE_ORDERED,
    STATE_ACCEPTED,
    STATE_REJECTED,
    STATE_RESERVED,
    STATE_IN_TRANSIT,
    STATE_DELIVERED
}
