package com.hellofresh.oms.model

import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.util.UUID

@Entity
data class SupplierSku(
    @Id
    val uuid: UUID,
    val parentSupplierId: UUID,
    val skuId: UUID,
    val market: String,
    @Enumerated(STRING)
    val status: SupplierSkuStatus,
    val supplierCode: String
)

enum class SupplierSkuStatus {
    ACTIVE, ARCHIVED, ONBOARDING, INACTIVE, OFFBOARDING
}
