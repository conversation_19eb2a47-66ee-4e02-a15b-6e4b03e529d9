package com.hellofresh.oms.model.purchaseOrderShipment

import jakarta.persistence.Entity
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity
data class PurchaseOrderShipment(
    @Id
    val poNumber: String,
    val loadNumber: String?,
    val palletCount: Long?,
    val carrierName: String?,
    val regionCode: String?,
    val postalCode: String?,
    val administrativeArea: String?,
    val locality: String?,
    val organization: String?,
    val appointmentTime: LocalDateTime?,
    val executionEvent: String?
)
