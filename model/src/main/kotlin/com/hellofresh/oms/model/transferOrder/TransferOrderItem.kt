package com.hellofresh.oms.model.transferOrder

import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "transfer_order_item")
data class TransferOrderItem(
    @Id
    val id: UUID,

    @Column(name = "transfer_order_id")
    val transferOrderId: UUID,

    // Required fields
    @Column(name = "csku_code")
    val cskuCode: String,

    @Column(name = "csku_name")
    val cskuName: String,

    @Column(name = "supplier_id")
    val supplierId: UUID,

    @Column(name = "supplier_code")
    val supplierCode: String,

    @Column(name = "supplier_sku_id")
    val supplierSkuId: UUID,

    @Column(name = "order_size")
    val orderSize: Int,

    @Column(name = "inventory_type")
    val inventoryType: String,

    @Column(name = "packaging_type")
    val packagingType: String, // 'case' or 'unit'

    @Embedded
    val price: TransferOrderItemPrice,

    @Embedded
    val totalPrice: TransferOrderItemTotalPrice,

    @Embedded
    val quantity: TransferOrderItemQuantity,

    // Optional fields
    @Column(name = "original_po_number")
    val originalPoNumber: String?,

    @Column(name = "lot_number")
    val lotNumber: String?,

    @Column(name = "lot_expiration_time")
    val lotExpirationTime: LocalDateTime?,

    @Column(name = "sku_id")
    val skuId: UUID?,

    @Column(name = "license_plate_number")
    val licensePlateNumber: String?,

    @Embedded
    val casePackaging: TransferOrderItemCasePackaging?,

    @Column(name = "created_at")
    val createdAt: LocalDateTime,

    @Column(name = "updated_at")
    val updatedAt: LocalDateTime
)

@jakarta.persistence.Embeddable
data class TransferOrderItemPrice(
    @Column(name = "price_currency_code")
    val currencyCode: String,

    @Column(name = "price_units")
    val units: Long,

    @Column(name = "price_nanos")
    val nanos: Int
)

@jakarta.persistence.Embeddable
data class TransferOrderItemTotalPrice(
    @Column(name = "total_price_currency_code")
    val currencyCode: String,

    @Column(name = "total_price_units")
    val units: Long,

    @Column(name = "total_price_nanos")
    val nanos: Int
)

@jakarta.persistence.Embeddable
data class TransferOrderItemCasePackaging(
    @Column(name = "case_packaging_size_value")
    val sizeValue: String?,

    @Column(name = "case_packaging_size_currency_code")
    val sizeCurrencyCode: String?,

    @Column(name = "case_packaging_size_units")
    val sizeUnits: Long?,

    @Column(name = "case_packaging_size_nanos")
    val sizeNanos: Int?,

    @Column(name = "case_packaging_unit")
    val unit: String?,

    @Column(name = "case_per_pallet")
    val casePerPallet: Int?
)

@jakarta.persistence.Embeddable
data class TransferOrderItemQuantity(
    @Column(name = "quantity_value")
    val value: String
)
