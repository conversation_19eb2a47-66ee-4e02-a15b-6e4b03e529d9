package com.hellofresh.oms.model

import com.hellofresh.oms.util.Calculator
import com.hellofresh.oms.util.Calculator.PRECISION
import jakarta.persistence.Embeddable
import java.math.BigDecimal
import java.math.RoundingMode.HALF_EVEN

@Embeddable
data class Money(
    val amount: BigDecimal,
    val currency: String,
) {

    fun toTapioca(): BigDecimal =
        if (amount == BigDecimal.ZERO) MINIMUM_TAPIOCA_PRICE else amount

    override fun equals(other: Any?): <PERSON>olean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Money

        if (amount.setScale(PRECISION, HALF_EVEN) != other.amount.setScale(PRECISION, HALF_EVEN)) return false

        return currency == other.currency
    }

    override fun hashCode(): Int {
        var result = amount.setScale(PRECISION, HALF_EVEN).hashCode()
        result = Calculator.PRIME_NUMBER_FOR_HASH_CODE * result + currency.hashCode()
        return result
    }

    companion object {
        private val MINIMUM_TAPIOCA_PRICE = BigDecimal("0.0000000001")
        private val MINIMUM_ORDER_MANAGEMENT_PRICE = BigDecimal("0.0001") // special values which we map to 0

        fun fromTapioca(amount: BigDecimal, currencyCode: String): Money =
            if (amount <= MINIMUM_ORDER_MANAGEMENT_PRICE) {
                Money(BigDecimal.ZERO, currencyCode)
            } else {
                Money(amount, currencyCode)
            }
    }
}
