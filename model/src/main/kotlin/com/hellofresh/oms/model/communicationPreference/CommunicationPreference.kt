package com.hellofresh.oms.model.communicationPreference

import jakarta.persistence.ColumnResult
import jakarta.persistence.ConstructorResult
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import jakarta.persistence.SqlResultSetMapping
import java.util.UUID

@Entity
@SqlResultSetMapping(
    name = "CommunicationPreferenceDetailedMapping",
    classes = [
        ConstructorResult(
            targetClass = CommunicationPreferenceDetailed::class,
            columns = arrayOf(
                ColumnResult(name = "id", type = UUID::class),
                ColumnResult(name = "market", type = String::class),
                ColumnResult(name = "dc_code", type = String::class),
                ColumnResult(name = "dc_name", type = String::class),
                ColumnResult(name = "supplier_id", type = UUID::class),
                ColumnResult(name = "supplier_code", type = Int::class),
                ColumnResult(name = "supplier_name", type = String::class),
                ColumnResult(name = "preference", type = CommunicationPreferenceEnum::class),
            ),
        ),
    ],
)
data class CommunicationPreference(
    @Id
    val id: UUID,
    val market: String? = null,
    val supplierId: UUID? = null,
    val dcCode: String? = null,
    @Enumerated(EnumType.STRING)
    val preference: CommunicationPreferenceEnum
)

data class CommunicationPreferenceDetailed(
    val id: UUID,
    val market: String?,
    val dcCode: String?,
    val dcName: String?,
    val supplierId: UUID?,
    val supplierCode: Int?,
    val supplierName: String?,
    val preference: CommunicationPreferenceEnum,
)

enum class CommunicationPreferenceEnum {
    EMAIL,
    E2OPEN,
    EMAIL_AND_E2OPEN
}
