package com.hellofresh.oms.model

import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.time.LocalDateTime
import java.util.UUID

@Entity
data class OutboxItem(
    @Id
    val id: UUID,
    val poId: UUID,
    val poNumber: String,
    @Enumerated(STRING)
    val status: OutboxItemStatus,
    val userEmail: String,
    val userId: UUID,
    val lastStatusChangeAt: LocalDateTime,
    val createdAt: LocalDateTime,
    val version: Int
) {
    companion object {
        @Suppress("LongParameterList")
        fun createItem(
            poId: UUID,
            poNumber: String,
            now: LocalDateTime,
            userEmail: String,
            userId: UUID,
            version: Int,
            status: OutboxItemStatus
        ): OutboxItem =
            OutboxItem(
                id = UUID.randomUUID(),
                poId = poId,
                poNumber = poNumber,
                status = status,
                userEmail = userEmail,
                userId = userId,
                lastStatusChangeAt = now,
                createdAt = now,
                version = version,
            )
    }
}

enum class OutboxItemStatus {
    PENDING, // = 0
    SENT, // = 1
    FAILED // = 2
}
