package com.hellofresh.oms.model

import jakarta.persistence.Embeddable
import jakarta.persistence.Transient
import java.time.format.DateTimeFormatterBuilder
import java.time.temporal.WeekFields

/**
 * YearWeek abstraction that parses  a yyyyu-Www formatted year-week string and internally takes Monday
 * as start of the week. This class only accepts valid combinations of year and week.
 * Example: 2021-W08
 */
@Embeddable
class YearWeek(val value: String) {

    @Transient
    private var year: Int = 0

    @Transient
    private var week: Int = 0

    init {
        /**
         * This is purely used for checking that a year-week combination is valid.
         * This is part of the initialization of this object and there is no start of the week that can be set yet, therefore we use
         * a Monday-based week. The LocalDate that could be derived from here is not useful since we always need a DC start of the week for
         * any computed date to be correct in a context of a DC YearWeek.
         * As well, a year and week that could be obtained after this parsing might be incorrect since after parsing, a different year-week combination
         * might be yielded due to Java's week alignment.
         * In case of invalid year-week combination this throws an exception.
         */
        mondayBasedYearWeekFormatter.parse(value)
    }

    constructor(year: String, week: String) : this("$year-W$week")

    constructor(year: Int, week: Int) : this("$year-W${"%02d".format(week)}")

    @Transient
    fun getYear(): Int {
        if (year <= 0) {
            initYearAndWeek()
        }
        return year
    }

    @Transient
    fun getWeek(): Int {
        if (week <= 0) {
            initYearAndWeek()
        }
        return week
    }

    private fun initYearAndWeek() = value.split("-W").let {
        year = it[0].toInt()
        week = it[1].toInt()
    }

    override fun toString() = value

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as YearWeek

        return value == other.value
    }

    override fun hashCode(): Int = value.hashCode()

    companion object {
        const val YEAR_WEEK_PATTERN = "YYYY-'W'ww"

        private val mondayBasedYearWeekFormatter = DateTimeFormatterBuilder()
            .appendPattern(YEAR_WEEK_PATTERN)
            .parseDefaulting(WeekFields.ISO.dayOfWeek(), 1)
            .toFormatter()
    }
}
