@file:Suppress("MagicNumber")

package com.hellofresh.oms.model.icsTicket

import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.time.LocalDateTime

@Entity
data class IcsTicket(
    @Id
    val ticketId: Int,
    val market: String,
    val week: String?,
    val bobCode: String?,
    val skuCode: String?,
    val poNumber: String,
    val poReference: String,
    val subject: String?,
    val ticketLink: String?,
    @Enumerated(EnumType.STRING)
    val priority: TicketPriority?,
    val requestType: String?,
    val type: String?,
    val productionImpact: String?,
    @Enumerated(EnumType.STRING)
    val status: TicketStatus?,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)

enum class TicketStatus(val value: Int) {
    OPEN(2),
    PENDING(3),
    RESOLVED(4),
    CLOSED(5);

    companion object {
        fun fromValue(value: Int?) = entries.find { it.value == value }
    }
}

enum class TicketPriority(val value: Int) {
    LOW(1),
    MEDIUM(2),
    HIGH(3),
    URGENT(4);

    companion object {
        fun fromValue(value: Int?) = entries.find { it.value == value }
    }
}
