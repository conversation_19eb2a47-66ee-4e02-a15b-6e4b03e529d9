package com.hellofresh.oms.model.acknowledgement

import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import java.time.LocalDateTime
import java.util.UUID

@Entity
data class Acknowledgement(
    @Id
    val id: UUID,
    val poId: UUID,
    val poNumber: String,
    val createTime: LocalDateTime?,

    val pickupTime: LocalDateTime?,
    val pickupRegion: String?,
    val pickupPostalCode: String?,
    val pickupAdministrativeArea: String?,
    val pickupLocality: String?,
    @Suppress("JpaAttributeTypeInspection")
    val pickupAddress: List<String>?,
    val pickupOrganization: String?,

    @OneToMany(
        targetEntity = AcknowledgementLineItem::class,
        cascade = [CascadeType.ALL],
        fetch = FetchType.LAZY,
        mappedBy = "acknowledgementId",
    )
    val lines: List<AcknowledgementLineItem>,

    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
)
