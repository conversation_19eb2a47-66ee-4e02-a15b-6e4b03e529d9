package com.hellofresh.oms.model.importHistory

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.importHistory.ImportHistoryStatus.INITIATED
import com.hellofresh.oms.model.importHistory.ImportHistorySummary.BatchPoCreation
import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.time.LocalDateTime
import java.util.UUID
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes

@Entity
data class ImportHistory(
    @Id
    val id: UUID,
    val filename: String,
    @Enumerated(STRING)
    val type: ImportHistoryType,
    @Enumerated(STRING)
    val status: ImportHistoryStatus,
    val userId: UUID,
    val userEmail: String,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    @JdbcTypeCode(SqlTypes.JSON)
    val summary: JsonNode,
) {
    fun getSummary() = when (type) {
        ImportHistoryType.BATCH_PO_CREATION -> jacksonObjectMapper().treeToValue(
            summary,
            BatchPoCreation::class.java,
        )
    }

    companion object {
        @Suppress("UseIfInsteadOfWhen")
        fun <T : ImportHistorySummary> createImportHistory(
            summary: T?,
            userEmail: String,
            userId: UUID,
            filename: String,
            createdAt: LocalDateTime,
        ) = ImportHistory(
            id = UUID.randomUUID(),
            filename = filename,
            status = INITIATED,
            userEmail = userEmail,
            userId = userId,
            createdAt = createdAt,
            updatedAt = createdAt,
            type = when (summary) {
                is BatchPoCreation -> ImportHistoryType.BATCH_PO_CREATION
                else -> throw IllegalArgumentException("Unknown import history type")
            },
            summary = jacksonObjectMapper().valueToTree(summary),
        )
    }
}

@Entity(name = "purchase_order")
data class PurchaseOrderHistory(
    @Id
    val id: UUID,
    val poNumber: String,
    val dcCode: String,

    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "year_week")))
    val yearWeek: YearWeek
)

sealed class ImportHistorySummary {
    data class BatchPoCreation(
        val purchaseOrders: List<String>,
        val markets: List<String>,
    ) : ImportHistorySummary()
}

enum class ImportHistoryType {
    BATCH_PO_CREATION
}

enum class ImportHistoryStatus {
    INITIATED, // when an import history is created
    SUCCEEDED, // when all rows of an import history are processed successfully
    PARTIALLY_SUCCEEDED, // when some rows of an import history are processed successfully
    FAILED // when all rows of an import history are not processed successfully
}
