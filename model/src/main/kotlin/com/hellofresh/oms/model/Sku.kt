package com.hellofresh.oms.model

import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.util.UUID

@Entity
data class Sku(
    @Id
    val uuid: UUID,
    val market: String,
    val name: String,
    val code: String,

    @Enumerated(STRING)
    val status: SkuStatus,
    @Suppress("JpaAttributeTypeInspection")
    val brands: List<String>,
    val category: String,
    @Enumerated(STRING)
    val uom: UOM?
)

enum class SkuStatus {
    LAUNCH,
    LIMITED,
    ONBOARDING,
    TEMPORARY,
    DEPLETION_TRACK,
    ACTIVE,
    ARCHIVED,
    INACTIVE,
    OFFBOARDING
}
