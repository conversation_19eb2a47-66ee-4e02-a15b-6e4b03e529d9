package com.hellofresh.oms.model

import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime
import java.util.UUID
import java.util.UUID.randomUUID

@Entity
@Table(name = "comment")
data class Comment(
    @Id
    val id: UUID = randomUUID(),
    val sourceId: String?,
    val resourceType: String,
    val resourceId: String,
    val domain: String,
    val dc: String,
    val brand: String,
    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "year_week")))
    val yearWeek: YearWeek,
    val comment: String,
    val createdBy: String,
    val createdAt: LocalDateTime,
    val updatedBy: String,
    val updatedAt: LocalDateTime,
)
