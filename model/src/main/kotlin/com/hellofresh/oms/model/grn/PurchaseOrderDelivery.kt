package com.hellofresh.oms.model.grn

import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import java.time.LocalDateTime
import java.util.UUID

@Entity(name = "grn_purchase_order_delivery")
data class PurchaseOrderDelivery(
    @Id
    val id: UUID,
    val grnId: UUID,

    /**
     * The ID for the PO delivery
     * e.g. "2215BV309618_O1-001"
     * or "ASN-0001"
     */
    val poDeliveryId: String,
    val deliveryTime: LocalDateTime?,
    val expectedDeliveryStartTime: LocalDateTime?,
    val expectedDeliveryEndTime: LocalDateTime?,

    @Enumerated(EnumType.STRING)
    val state: DeliveryStateEnum,

    @OneToMany(
        targetEntity = PurchaseOrderDeliveryLine::class,
        cascade = [CascadeType.ALL],
        fetch = FetchType.LAZY,
        mappedBy = "grnPurchaseOrderDeliveryId"
    )
    val lines: List<PurchaseOrderDeliveryLine>,
)

enum class DeliveryStateEnum {
    DELIVERY_STATE_OPEN,
    DELIVERY_STATE_RECEIVED,
    DELIVERY_STATE_CLOSED,

    // Unknown delivery state. Normally should not be used.
    DELIVERY_STATE_UNSPECIFIED;

    companion object
}
