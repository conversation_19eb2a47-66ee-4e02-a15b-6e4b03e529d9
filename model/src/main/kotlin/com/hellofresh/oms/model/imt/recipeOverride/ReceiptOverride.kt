package com.hellofresh.oms.model.imt.recipeOverride

import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "receipt_override")
data class ReceiptOverride(
    @Id
    val id: UUID,
    val poNumber: String,
    val skuCode: String,
    val quantity: Int,
    val cases: Int?,
    val deliveryDate: LocalDateTime?,
    val details: String? = null,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?,
    val updatedBy: String? = null,
)
