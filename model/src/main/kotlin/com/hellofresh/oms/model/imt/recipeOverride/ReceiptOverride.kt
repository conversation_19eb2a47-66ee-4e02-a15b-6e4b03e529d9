package com.hellofresh.oms.model.imt.recipeOverride

import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import jakarta.persistence.Table
import java.io.Serializable
import java.time.LocalDateTime

@Entity
@Table(name = "receipt_override")
@IdClass(ReceiptOverrideId::class)
data class ReceiptOverride(
    @Id
    val hfWeek: String,
    @Id
    val brand: String,
    @Id
    val dc: String,
    @Id
    val poNumber: String,
    @Id
    val skuCode: String,
    val poReference: String,
    val userEmail: String,
    val source: String,
    val quantity: Int,
    val updatedAt: LocalDateTime?,
    val createdAt: LocalDateTime?,
    val deletedBy: String? = null,
    val deletedAt: LocalDateTime?,
    val market: String,
    val cases: Int? = null,
    val comment: String? = null,
    val receivingDate: LocalDateTime? = null
)

data class ReceiptOverrideId(
    val hfWeek: String = "",
    val brand: String = "",
    val dc: String = "",
    val poNumber: String = "",
    val skuCode: String = ""
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
