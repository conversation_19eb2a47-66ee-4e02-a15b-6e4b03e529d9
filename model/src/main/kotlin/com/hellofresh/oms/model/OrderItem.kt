package com.hellofresh.oms.model

import com.hellofresh.oms.util.Calculator
import com.hellofresh.oms.util.Calculator.PRECISION
import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.math.BigDecimal
import java.math.RoundingMode.HALF_EVEN
import java.time.LocalDateTime
import java.util.UUID
import java.util.UUID.randomUUID

@Entity
data class OrderItem(
    @Id
    val id: UUID = randomUUID(),
    @Column(name = "po_id")
    val poId: UUID,
    val totalQty: BigDecimal,
    val skuId: UUID,

    @AttributeOverrides(
        AttributeOverride(name = "currency", column = Column(name = "price_currency")),
        AttributeOverride(name = "amount", column = Column(name = "price_amount")),
    )
    val price: Money,

    @AttributeOverrides(
        AttributeOverride(name = "currency", column = Column(name = "total_price_currency")),
        AttributeOverride(name = "amount", column = Column(name = "total_price_amount")),
    )
    val totalPrice: Money,

    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "buffer_permyriad")))
    val buffer: Permyriad,
    val correctionReason: String?,
    val packaging: Packaging,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?,
    val rawQty: BigDecimal?,
    val changeReasonId: UUID?,
    val casesPerPallet: Int?
) {
    companion object {
        @Suppress("LongParameterList")
        fun createOrderItem(
            poId: UUID,
            skuId: UUID,
            price: Money,
            buffer: Permyriad,
            changeReasonId: UUID?,
            packaging: Packaging,
            totalCaseQuantity: Int,
            currentTime: LocalDateTime,
            totalPrice: Money,
            casesPerPallet: Int?
        ): OrderItem {
            val caseOrUnitSize: BigDecimal = Calculator.calculateCaseOrUnitSize(packaging)

            return OrderItem(
                id = randomUUID(),
                poId = poId,
                totalQty = Calculator.calculateTotalQuantity(
                    totalCaseQuantity = totalCaseQuantity,
                    caseOrUnitSize = caseOrUnitSize,
                ),
                skuId = skuId,
                price = price,
                totalPrice = totalPrice,
                buffer = buffer,
                correctionReason = null,
                packaging = packaging,
                createdAt = currentTime,
                updatedAt = currentTime,
                rawQty = null,
                changeReasonId = changeReasonId,
                casesPerPallet = if (packaging.packagingType == PackagingType.PALLET_TYPE) casesPerPallet else null
            )
        }

        @Suppress("LongParameterList")
        fun createOrderItem(
            poId: UUID,
            skuId: UUID,
            price: Money,
            buffer: Permyriad,
            changeReasonId: UUID?,
            packaging: Packaging,
            totalNumberOfUnits: BigDecimal,
            currentTime: LocalDateTime,
            totalPrice: Money,
            casesPerPallet: Int?
        ): OrderItem = OrderItem(
            id = randomUUID(),
            poId = poId,
            totalQty = totalNumberOfUnits,
            skuId = skuId,
            price = price,
            totalPrice = totalPrice,
            buffer = buffer,
            correctionReason = null,
            packaging = packaging,
            createdAt = currentTime,
            updatedAt = currentTime,
            rawQty = null,
            changeReasonId = changeReasonId,
            casesPerPallet = if (packaging.packagingType == PackagingType.PALLET_TYPE) casesPerPallet else null
        )

        fun createNewOrderItemFrom(orderItem: OrderItem, currentTime: LocalDateTime, poId: UUID): OrderItem = OrderItem(
            id = randomUUID(),
            createdAt = currentTime,
            updatedAt = currentTime,
            correctionReason = orderItem.correctionReason,
            poId = poId,
            totalQty = orderItem.totalQty,
            skuId = orderItem.skuId,
            price = orderItem.price,
            totalPrice = orderItem.totalPrice,
            buffer = orderItem.buffer,
            packaging = orderItem.packaging,
            rawQty = orderItem.rawQty,
            changeReasonId = orderItem.changeReasonId,
            casesPerPallet = orderItem.casesPerPallet
        )
    }
}

@Embeddable
data class Packaging(
    @Enumerated(STRING)
    val packagingType: PackagingType,
    val caseSize: BigDecimal? = null,

    @Enumerated(STRING)
    val unitOfMeasure: UOM
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Packaging

        if (caseSize?.setScale(PRECISION, HALF_EVEN) != other.caseSize?.setScale(PRECISION, HALF_EVEN)) return false

        if (packagingType != other.packagingType) return false
        return unitOfMeasure == other.unitOfMeasure
    }

    override fun hashCode(): Int {
        var result = packagingType.hashCode()
        result = Calculator.PRIME_NUMBER_FOR_HASH_CODE * result + caseSize?.setScale(PRECISION, HALF_EVEN).hashCode()
        result = Calculator.PRIME_NUMBER_FOR_HASH_CODE * result + unitOfMeasure.hashCode()
        return result
    }
}
