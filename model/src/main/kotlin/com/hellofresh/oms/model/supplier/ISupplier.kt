package com.hellofresh.oms.model.supplier

import jakarta.persistence.Embeddable
import java.time.LocalDateTime
import java.util.UUID

interface ISupplier {
    val id: UUID
    val parentId: UUID
    val market: String
    val code: Int
    val name: String
    val status: SupplierStatus
    val currency: String
    val type: String
    val createdAt: LocalDateTime?
    val updatedAt: LocalDateTime?
    val supplierAddress: SupplierAddress
    val dcCodes: List<String>
}

@Embeddable
data class SupplierAddress(
    val city: String,
    val country: String,
    val state: String,
    val address: String,
    val number: String,
    val postCode: String,
)

enum class SupplierStatus {
    ONBOARDING, ACTIVE, INACTIVE, ARCHIVED, OFFBOARDING
}
