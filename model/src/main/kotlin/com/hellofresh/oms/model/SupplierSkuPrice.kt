package com.hellofresh.oms.model

import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

@Entity
data class SupplierSkuPrice(
    @Id
    val uuid: UUID,
    val supplierSkuId: UUID,
    @Suppress("JpaAttributeTypeInspection")
    val dcCodes: List<String>,
    val startDate: LocalDateTime,
    val endDate: LocalDateTime,
    val enabled: Boolean,
    val priceType: String,
    val market: String,
    val currency: String,

    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "price_permyriad")))
    val pricePermyriad: Permyriad,

    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "buffer_permyriad")))
    val bufferPermyriad: Permyriad,
    val caseSize: Int?,
    val unitsPerCase: BigDecimal?,

    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "case_price_permyriad")))
    val casePrice: Permyriad?,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
)
