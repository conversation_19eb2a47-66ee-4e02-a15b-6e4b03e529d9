package com.hellofresh.oms.model.grn

import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import java.time.LocalDateTime
import java.util.UUID

/**
 * The GoodsReceipt is created after upstream has all the necessary information about deliveries,
 * and it will be published every time upstream receives any updates.
 */
@Entity
data class Grn(
    /**
     * This is obtained by the "reference" field of the protobuf message by trying to parse a PO number.
     * It is expected to be unique, and any non PO numbers are explicitly ignored.
     */
    @Id
    val id: UUID,
    val poNumber: String,
    val dcCode: String,
    val deliveryStartTime: LocalDateTime?,
    val deliveryEndTime: LocalDateTime?,
    val wmsName: String,

    /** Either a reference to the purchase order number, or to an internal number created in the wms system
     * e.g. "2215BV309618_O1"
     * This can also be an internal reference, eg: "Intern", if so, we can't match it to a PO.
     * Only the ones that contain a valid PO number are consumed.
     */
    val reference: String,

    @OneToMany(
        targetEntity = PurchaseOrderDelivery::class,
        cascade = [CascadeType.ALL],
        fetch = FetchType.EAGER,
        mappedBy = "grnId",
    )
    val deliveries: List<PurchaseOrderDelivery>,

    @Enumerated(EnumType.STRING)
    val state: GrnStateEnum,

    // This is a snapshot of Purchase Order's Order items, hence unconsumed.
    // val purchaseOrderLines: List<PurchaseOrderLine>
)

enum class GrnStateEnum {
    // Unknown state. Normally should not be used.
    STATE_UNSPECIFIED,
    STATE_OPEN,
    STATE_CLOSE, ;

    companion object
}
