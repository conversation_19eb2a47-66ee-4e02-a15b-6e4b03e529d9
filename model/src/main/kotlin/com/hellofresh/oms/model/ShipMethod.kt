package com.hellofresh.oms.model

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.util.UUID

@Entity
data class ShipMethod(
    @Id
    val uuid: UUID,
    val dcCode: String,
    @Column(name = "supplier_id")
    val supplierId: UUID,
    val market: String,
    @Enumerated(value = STRING)
    val method: ShipMethodEnum
)

enum class ShipMethodEnum {
    VENDOR,
    CROSSDOCK,
    FREIGHT_ON_BOARD,
    OTHER
}
