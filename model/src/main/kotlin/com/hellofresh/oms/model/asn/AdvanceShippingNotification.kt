package com.hellofresh.oms.model.asn

import jakarta.persistence.CascadeType
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import java.time.LocalDateTime
import java.util.UUID

@Entity
data class AdvanceShippingNotification(

    @Id
    val id: UUID,
    val asnId: String,
    val purchaseOrderId: UUID,
    val notes: String?,
    val supplierId: UUID,

    @Embedded
    val shipment: Shipment,

    @OneToMany(
        targetEntity = AdvanceShippingNotificationItem::class,
        cascade = [CascadeType.ALL],
        fetch = FetchType.LAZY,
        mappedBy = "asnId",
    )
    val items: List<AdvanceShippingNotificationItem>,

    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
)
