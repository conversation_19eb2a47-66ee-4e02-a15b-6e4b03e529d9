package com.hellofresh.oms.model

import jakarta.persistence.CascadeType.ALL
import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import java.time.LocalDateTime

@Entity
data class DistributionCenter(
    @Id
    val code: String,
    @Enumerated(STRING)
    val status: DistributionCenterStatus,
    val name: String,
    val market: String,
    @OneToMany(mappedBy = "dcCode", cascade = [ALL], fetch = FetchType.EAGER)
    val addresses: List<DistributionCenterAddress>,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val isVisible: Boolean = true
)

enum class DistributionCenterStatus {
    ACTIVE,
    INACTIVE;

    companion object {
        fun fromBoolean(value: Boolean) = if (value) ACTIVE else INACTIVE
    }
}

fun DistributionCenter.getDeliveryAddress() = addresses.firstOrNull { it.isDelivery() }

fun DistributionCenter.getBillingAddress() = addresses.firstOrNull { it.isBilling() }
