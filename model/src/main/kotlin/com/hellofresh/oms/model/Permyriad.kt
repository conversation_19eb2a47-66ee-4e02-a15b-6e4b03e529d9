package com.hellofresh.oms.model

import jakarta.persistence.Embeddable
import java.io.Serializable
import java.math.BigDecimal
import java.math.RoundingMode.HALF_EVEN

@Embeddable
data class Permyriad(val value: Int) : Serializable {
    constructor(value: String) : this(value.toInt())

    fun toPercent(): BigDecimal = BigDecimal(value).divide(percentFactor, 2, HALF_EVEN)
    fun convertToBigDecimal(scale: Int = 2): BigDecimal = BigDecimal(value).divide(
        BigDecimal(decimalFactor),
        scale,
        HALF_EVEN
    )
    fun convertToInteger(): BigDecimal = BigDecimal(value).divide(BigDecimal(decimalFactor), 0, HALF_EVEN)

    companion object {
        private const val serialVersionUID: Long = 1
        private val percentFactor = BigDecimal(100)
        private val permilleFactor = BigDecimal(10)
        private const val decimalFactor = 10_000

        val ZERO = Permyriad(0)
        val ONE = Permyriad(1)
        val ONE_PERCENT = Permyriad(100)
        val HUNDRED_PERCENT = Permyriad(10_000)

        fun fromPercent(percent: BigDecimal) =
            Permyriad(percent.multiply(percentFactor).setScale(0, HALF_EVEN).toInt())

        fun fromPercent(percent: Double) =
            Permyriad(percent.toBigDecimal().multiply(percentFactor).setScale(0, HALF_EVEN).toInt())

        fun fromPermille(permille: Int) =
            Permyriad(permille.toBigDecimal().multiply(permilleFactor).setScale(0, HALF_EVEN).toInt())
    }
}
