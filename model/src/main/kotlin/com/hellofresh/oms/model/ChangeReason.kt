package com.hellofresh.oms.model

import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.util.UUID

@Entity
data class ChangeReason(
    @Id
    val id: UUID,
    val name: String,
    @Suppress("JpaAttributeTypeInspection")
    val allowedMarkets: List<String>,
    @Enumerated(STRING)
    val reasonType: ChangeReasonType
)

enum class ChangeReasonType(val value: String) {
    DELIVERY_DATE("delivery_date"),
    ORDER_ITEM_CHANGE("order_item_change");

    companion object {
        fun convert(value: String) = values().first { it.value == value }
    }
}
