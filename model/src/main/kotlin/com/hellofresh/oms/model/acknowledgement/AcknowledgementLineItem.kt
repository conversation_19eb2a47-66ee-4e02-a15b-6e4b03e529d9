package com.hellofresh.oms.model.acknowledgement

import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.time.LocalDateTime
import java.util.UUID

@Entity
data class AcknowledgementLineItem(
    @Id
    val id: UUID,
    val acknowledgementId: UUID,
    val skuCode: String?,
    val skuId: UUID,
    @Enumerated(EnumType.STRING)
    val state: AcknowledgementLineStateEnum,
    val numberOfPallets: Int?,
    val promisedTime: LocalDateTime?,
    val numberOfUnits: Double?,
    val unitsPerCase: Double?,

    @Enumerated(EnumType.STRING)
    val uom: UnitOfMeasureAcknowledgementLineEnum,

    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
)

enum class AcknowledgementLineStateEnum {
    // Unknown state. Normally should not be used.
    STATE_UNSPECIFIED,
    STATE_OPEN,
    STATE_ACCEPTED,
    STATE_ACCEPTED_WITH_CHANGES,
    STATE_REJECTED ;

    companion object
}

enum class UnitOfMeasureAcknowledgementLineEnum {
    // Invalid, do not use!
    UNIT_OF_MEASURE_UNSPECIFIED,
    UNIT_OF_MEASURE_OTHER,

    // Indicating the shipped item quantity value is in Unit.
    // This means that quantity value represent number of items individually.
    UNIT_OF_MEASURE_UNIT,

    // Indicating the shipped item quantity value is in Case unit.
    // This means that quantity value represent number of batches of items.
    UNIT_OF_MEASURE_CASE,

    UNIT_OF_MEASURE_KG,
    UNIT_OF_MEASURE_LBS,
    UNIT_OF_MEASURE_OZ,
    UNIT_OF_MEASURE_LITRE,
    UNIT_OF_MEASURE_GAL;

    companion object
}
