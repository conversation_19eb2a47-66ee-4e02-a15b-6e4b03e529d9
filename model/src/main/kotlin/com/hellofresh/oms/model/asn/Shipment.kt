package com.hellofresh.oms.model.asn

import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import java.time.LocalDateTime

@Embeddable
data class Shipment(
    val distributionCenter: String,
    val standardCarrierAlphaCode: String?,
    val shippingMethod: String,
    val createTime: LocalDateTime,
    val shipmentTime: LocalDateTime,
    val plannedDeliveryTime: LocalDateTime,
    val trackingNumber: String?,
    val numberPallet: String,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "regionCode", column = Column(name = "shipping_region_code")),
        AttributeOverride(name = "postalCode", column = Column(name = "shipping_postal_code")),
        AttributeOverride(name = "locality", column = Column(name = "shipping_locality")),
        AttributeOverride(name = "addressLines", column = Column(name = "shipping_address_lines")),
        AttributeOverride(name = "organization", column = Column(name = "shipping_organization")),
    )
    val shippingAddress: Address,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "regionCode", column = Column(name = "pickup_region_code")),
        AttributeOverride(name = "postalCode", column = Column(name = "pickup_postal_code")),
        AttributeOverride(name = "locality", column = Column(name = "pickup_locality")),
        AttributeOverride(name = "addressLines", column = Column(name = "pickup_address_lines")),
        AttributeOverride(name = "organization", column = Column(name = "pickup_organization")),
    )
    val pickupAddress: Address
)

@Embeddable
data class Address(
    val regionCode: String?,
    val postalCode: String?,
    val locality: String?,
    val addressLines: String?,
    val organization: String?
)
