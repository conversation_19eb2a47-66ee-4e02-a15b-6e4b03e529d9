package com.hellofresh.oms.model.imt.poVoid

import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "po_void")
data class PoVoid(

    @Id
    val id: UUID,
    val userEmail: String,
    val dc: String,
    val week: String,
    val brand: String,
    val poNumber: String,
    val poReference: String,
    val source: String,
    val comment: String? = null,
    val createAt: LocalDateTime?,
    val skuCode: String,
    val supplierName: String?,
    val deletedBy: String? = null,
    val deletedAt: LocalDateTime? = null,
    val lastUpdated: LocalDateTime?,
    val market: String
)
