package com.hellofresh.oms.model.imt.exportReceipt

import com.hellofresh.oms.model.YearWeek
import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.util.UUID

@Entity
@Table(name = "export_receipt")
data class ExportReceipt(
    @Id
    val id: UUID,
    val warehouseId: String,
    val poNumber: String,
    val poReference: String,
    val skuCode: String,
    val supplierName: String,
    val casesReceived: Int,
    val quantityReceived: Double,
    val skuName: String,
    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "year_week")))
    val yearWeek: YearWeek,
    val status: String,
    val unit: String,
    val market: String,
    val supplierCode: String
)
