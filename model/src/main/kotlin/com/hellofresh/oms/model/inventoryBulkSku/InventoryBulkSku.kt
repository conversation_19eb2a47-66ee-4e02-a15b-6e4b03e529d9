package com.hellofresh.oms.model.inventoryBulkSku

import com.vladmihalcea.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import java.io.Serializable
import org.hibernate.annotations.Type

@Entity
@IdClass(InventoryBulkSkuId::class)
data class InventoryBulkSku(
    @Id
    val bulkSkuCode: String,
    @Id
    val packagedSkuCode: String,
    @Type(JsonType::class)
    @Column(columnDefinition = "json")
    val brands: List<String>,
    val pickConversion: Int,
)

data class InventoryBulkSkuId(
    val bulkSkuCode: String = "",
    val packagedSkuCode: String = ""
) : Serializable {

    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
