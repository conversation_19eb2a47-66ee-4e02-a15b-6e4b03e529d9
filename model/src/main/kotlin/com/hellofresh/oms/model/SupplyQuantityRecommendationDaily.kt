package com.hellofresh.oms.model

import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import java.io.Serializable
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@Entity
@IdClass(SupplyQuantityRecommendationDailyId::class)
data class SupplyQuantityRecommendationDaily(
    @Id
    val skuId: UUID,
    @Id
    val dcCode: String,
    @Id
    val supplyDate: LocalDate,
    val forecastedDemanded: BigDecimal,
    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "production_year_week")))
    val productionWeek: YearWeek,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)

data class SupplyQuantityRecommendationDailyId(
    val skuId: UUID? = UUID(0, 0),
    val dcCode: String = "",
    val supplyDate: LocalDate = LocalDate.MIN,
) : Serializable {

    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
