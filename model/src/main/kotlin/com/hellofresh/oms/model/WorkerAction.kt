package com.hellofresh.oms.model

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.oms.model.WorkerAction.Companion.createWorkerAction
import com.hellofresh.oms.model.WorkerActionData.CreateAndSendPurchaseOrder
import com.hellofresh.oms.model.WorkerActionData.CreatePurchaseOrder
import com.hellofresh.oms.model.WorkerActionData.SendPurchaseOrder
import com.hellofresh.oms.model.WorkerActionData.SyncBatchOrder
import com.hellofresh.oms.model.WorkerActionType.CREATE_AND_SEND_ORDER
import com.hellofresh.oms.model.WorkerActionType.CREATE_ORDER
import com.hellofresh.oms.model.WorkerActionType.SEND_ORDER
import com.hellofresh.oms.model.WorkerActionType.SYNC_BATCH_ORDER
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.time.LocalDateTime
import java.util.UUID
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes

@Entity
data class WorkerAction(
    @Id
    val id: UUID = UUID.randomUUID(),
    @Enumerated(EnumType.STRING)
    val status: WorkerActionStatus = WorkerActionStatus.PENDING,
    val userEmail: String,
    val userId: UUID,
    val lastStatusChangeAt: LocalDateTime = LocalDateTime.now(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val lastError: String? = null,
    @JdbcTypeCode(SqlTypes.JSON)
    val payload: JsonNode,
    @Enumerated(EnumType.STRING)
    val actionType: WorkerActionType,
) {
    fun getPayload(): WorkerActionData = when (actionType) {
        SEND_ORDER -> jacksonObjectMapper().treeToValue(
            payload,
            SendPurchaseOrder::class.java,
        )

        SYNC_BATCH_ORDER -> jacksonObjectMapper().treeToValue(
            payload,
            SyncBatchOrder::class.java,
        )

        CREATE_ORDER -> jacksonObjectMapper().treeToValue(
            payload,
            CreatePurchaseOrder::class.java,
        )

        CREATE_AND_SEND_ORDER -> jacksonObjectMapper().treeToValue(
            payload,
            CreateAndSendPurchaseOrder::class.java,
        )
    }

    companion object {
        @Suppress("UseIfInsteadOfWhen")
        fun <T : WorkerActionData> createWorkerAction(
            workerActionData: T?,
            userEmail: String,
            userId: UUID,
        ): WorkerAction {
            val now = LocalDateTime.now()
            return WorkerAction(
                id = UUID.randomUUID(),
                status = WorkerActionStatus.PENDING,
                userEmail = userEmail,
                userId = userId,
                lastStatusChangeAt = now,
                createdAt = now,
                lastError = null,
                actionType = when (workerActionData) {
                    is SendPurchaseOrder -> SEND_ORDER
                    is SyncBatchOrder -> SYNC_BATCH_ORDER
                    is CreatePurchaseOrder -> CREATE_ORDER
                    is CreateAndSendPurchaseOrder -> CREATE_AND_SEND_ORDER
                    else -> throw IllegalArgumentException("Unknown worker action type")
                },
                payload = jacksonObjectMapper().valueToTree(workerActionData),
            )
        }
    }
}

sealed class WorkerActionData {
    fun toWorkerAction(userEmail: String, userId: UUID): WorkerAction = when (this) {
        is SendPurchaseOrder -> createWorkerAction(this, userEmail, userId)
        is SyncBatchOrder -> createWorkerAction(this, userEmail, userId)
        is CreatePurchaseOrder -> createWorkerAction(this, userEmail, userId)
        is CreateAndSendPurchaseOrder -> createWorkerAction(this, userEmail, userId)
    }

    data class SendPurchaseOrder(
        val purchaseOrderNumber: String,
        val bulkId: UUID,
        val outboxItemId: UUID
    ) : WorkerActionData()

    data class SyncBatchOrder(
        val purchaseOrderNumber: String,
        val importHistoryId: UUID,
    ) : WorkerActionData()

    data class CreatePurchaseOrder(
        val purchaseOrderId: UUID,
    ) : WorkerActionData()

    data class CreateAndSendPurchaseOrder(
        val purchaseOrderId: UUID,
    ) : WorkerActionData()
}

enum class WorkerActionStatus {
    PROCESSED, PENDING, FAILED, PARTIALLY_PROCESSED
}

enum class WorkerActionType {
    SEND_ORDER, SYNC_BATCH_ORDER, CREATE_ORDER, CREATE_AND_SEND_ORDER
}
