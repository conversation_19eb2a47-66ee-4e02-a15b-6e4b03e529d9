package com.hellofresh.oms.model.supplier

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.SupplierContact
import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.CascadeType.ALL
import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType.LAZY
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToMany
import java.time.LocalDateTime
import java.util.UUID

@Entity(name = "supplier")
data class SupplierExtended(
    @Id
    override val id: UUID,
    override val parentId: UUID,
    override val market: String,
    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "code")))
    override val code: Int,
    override val name: String,
    @Enumerated(STRING)
    override val status: SupplierStatus,
    override val currency: String,
    override val type: String,
    override val createdAt: LocalDateTime? = null,
    override val updatedAt: LocalDateTime? = null,
    @Embedded
    override val supplierAddress: SupplierAddress,
    @Suppress("JpaAttributeTypeInspection")
    override val dcCodes: List<String>,
    @OneToMany(
        cascade = [ALL],
        orphanRemoval = true,
        fetch = LAZY,
    )
    @JoinColumn(name = "supplier_id")
    val contacts: Set<SupplierContact>,
    @OneToMany(
        cascade = [ALL],
        orphanRemoval = true,
        fetch = LAZY,
    )
    @JoinColumn(name = "supplier_id")
    val shipMethods: List<ShipMethod>,
) : ISupplier
