package com.hellofresh.oms.model

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.util.UUID

@Entity
data class UnitOfMeasure(
    @Id
    val uuid: UUID,
    val name: String,
    val market: String,
    @Column(name = "enum_value")
    @Enumerated(STRING)
    val enumValue: UOM,
    val type: String
)
