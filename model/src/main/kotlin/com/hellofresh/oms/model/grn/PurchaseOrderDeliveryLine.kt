package com.hellofresh.oms.model.grn

import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.time.LocalDateTime
import java.util.UUID

// The Purchase order delivery line
@Entity(name = "grn_purchase_order_delivery_line")
data class PurchaseOrderDeliveryLine(
    @Id
    val id: UUID,
    val grnPurchaseOrderDeliveryId: UUID,
    val poDeliveryLineId: String?, // The ID for the PO delivery line. Might not be unique across different GRNs
    val skuCode: String?,
    val unloadedQuantity: Double?,
    val receivedQuantity: Double?,
    val expectedQuantity: Double?,
    val rejectedQuantity: Double?,
    val palletizedQuantity: Double?,
    val expirationDate: LocalDateTime?, // Expiration date or use by date for this sku
    val supplierLotNumber: String?,

    // Represents the size of the case when UOM is case.
    // This field would be omitted when UOM is not case.
    val caseSize: Double?,

    @Enumerated(EnumType.STRING)
    val state: DeliveryLineStateEnum,

    @Enumerated(EnumType.STRING)
    val skuUom: UnitOfMeasureGrnEnum,
)

enum class DeliveryLineStateEnum {
    DELIVERY_LINE_STATE_UNSPECIFIED, // Unknown delivery line state. Normally should not be used.
    DELIVERY_LINE_STATE_EXPECTED,
    DELIVERY_LINE_STATE_OPEN,
    DELIVERY_LINE_STATE_RECEIVED,
    DELIVERY_LINE_STATE_REJECTED,
    DELIVERY_LINE_STATE_CLOSED,
    DELIVERY_LINE_STATE_CANCELLED;

    companion object
}

enum class UnitOfMeasureGrnEnum {
    UNIT_OF_MEASURE_OTHER,
    UNIT_OF_MEASURE_KG,
    UNIT_OF_MEASURE_LBS,
    UNIT_OF_MEASURE_OZ,
    UNIT_OF_MEASURE_LITRE,
    UNIT_OF_MEASURE_GAL,

    // Indicating the shipped item quantity value is in Unit.
    // This means that quantity value represent number of items individually.
    UNIT_OF_MEASURE_UNIT,

    // Indicating the shipped item quantity value is in Case unit.
    // The quantity is number of items (eaches).
    UNIT_OF_MEASURE_CASE,

    UNIT_OF_MEASURE_UNSPECIFIED; // Reserved for protobuf default value, do not use!

    companion object
}
