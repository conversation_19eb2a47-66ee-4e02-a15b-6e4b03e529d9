package com.hellofresh.oms.model.asn

import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.Id
import java.time.LocalDateTime
import java.util.UUID

@Entity
data class AdvanceShippingNotificationItem(
    @Id
    val id: UUID,
    val asnId: UUID,
    val skuCode: String,
    val shippingState: String,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "orderSize", column = Column(name = "shipped_orderSize")),
        AttributeOverride(name = "size", column = Column(name = "shipped_size")),
        AttributeOverride(name = "unit", column = Column(name = "shipped_unit")),
        AttributeOverride(name = "palletId", column = Column(name = "shipped_palletId")),
        AttributeOverride(name = "crateType", column = Column(name = "shipped_crateType")),
    )
    val shippedQuantity: ItemQuantity,

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "orderSize", column = Column(name = "promised_orderSize")),
        AttributeOverride(name = "size", column = Column(name = "promised_size")),
        AttributeOverride(name = "unit", column = Column(name = "promised_unit")),
        AttributeOverride(name = "palletId", column = Column(name = "promised_palletId")),
        AttributeOverride(name = "crateType", column = Column(name = "promised_crateType")),
    )
    val promisedQuantity: ItemQuantity?,
    val packingSize: Double,
    val lotNumber: String,
    val expirationTime: LocalDateTime?,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)

@Embeddable
data class ItemQuantity(
    val orderSize: Int?,
    val size: Double?,
    val unit: String?,
    val palletId: String?,
    val crateType: String?
)
