package com.hellofresh.oms.model.supplier

import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import java.time.LocalDateTime
import java.util.UUID

@Entity
data class Supplier(
    @Id
    override val id: UUID,
    override val parentId: UUID,
    override val market: String,
    @AttributeOverrides(AttributeOverride(name = "value", column = Column(name = "code")))
    override val code: Int,
    override val name: String,
    @Enumerated(STRING)
    override val status: SupplierStatus,
    override val currency: String,
    override val type: String,
    override val createdAt: LocalDateTime? = null,
    override val updatedAt: LocalDateTime? = null,
    @Embedded
    override val supplierAddress: SupplierAddress,
    @Deprecated("This field is deprecated and can cause some issues. Do not use it until further investigation")
    @Suppress("JpaAttributeTypeInspection")
    override val dcCodes: List<String>,
) : ISupplier
