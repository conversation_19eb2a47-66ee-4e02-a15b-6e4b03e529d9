package com.hellofresh.oms.model

import jakarta.persistence.Entity
import jakarta.persistence.EnumType.STRING
import jakarta.persistence.Enumerated
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime
import java.util.UUID

@Entity
@Table(name = "dc_address")
data class DistributionCenterAddress(
    @Id
    val id: UUID,
    val dcCode: String,
    val number: String,
    val address: String,
    val zip: String,
    val city: String,
    val state: String,
    val company: String?,
    @Enumerated(STRING)
    val type: DistributionCenterAddressType,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val countryCode: String,
)

enum class DistributionCenterAddressType {
    DELIVERY,
    BILLING,
    LEGAL,
}

fun DistributionCenterAddress.isDelivery() = type == DistributionCenterAddressType.DELIVERY

fun DistributionCenterAddress.isBilling() = type == DistributionCenterAddressType.BILLING || type == DistributionCenterAddressType.LEGAL
