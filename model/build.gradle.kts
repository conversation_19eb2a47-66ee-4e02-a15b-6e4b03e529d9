import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    id("java-base")
    id("jacoco")
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.jpa)
}

group = "$group.models"

dependencies {
    implementation(libs.hibernate.core)
    implementation(libs.spring.data.commons)
    implementation(libs.jackson.databind)
    implementation(libs.jackson.module.kotlin)
    implementation(libs.vladmihalcea.hibernate.types)
    testImplementation(kotlin("test-junit5"))
    testImplementation(libs.junit.jupiter.params)
}

tasks {
    compileKotlin {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_21
        }
    }

    withType<Test>().configureEach {
        useJUnitPlatform()
    }
}
