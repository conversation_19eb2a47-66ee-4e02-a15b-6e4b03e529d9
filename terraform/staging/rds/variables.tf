# Environment specific
variable "env_suffix" {
  type        = string
  default     = "staging"
  description = "Environment suffix: staging/live"
}

variable "db_settings" {
  type = object({
      instance_class = string
      backup_retention_period = string
      engine_version = string
      option_group_name = string
      parameter_group_name = string
      apply_immediately = bool
  })
  description = "AWS DB instance settings"
}

variable "aws_settings" {
  type = object({
      region = string
  })
  default = { region = "eu-west-1" }
  description = "AWS settings"
}

# Variables replaced automatically by: https://github.com/hellofresh/jetstream-ci-scripts/tree/master/actions/terraform#useful-tips
variable "hf_squad" {
  description = "This is automatically replaced by jetstream-ci-scripts/actions/terraform"
}

variable "hf_tribe" {
  description = "This is automatically replaced by jetstream-ci-scripts/actions/terraform"
}

variable "hf_repo" {
  description = "This is automatically replaced by jetstream-ci-scripts/actions/terraform"
}
