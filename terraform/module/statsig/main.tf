resource "statsig_gate" "gate" {
    for_each    = {for i in var.gate_settings : i.name => i}
    name        = each.value.name
    description = each.value.description
    is_enabled  = true
    id_type     = "userID"
    rules {
        name            = "Staging Rule"
        pass_percentage = 0
        conditions {
            type         = "environment_tier"
            target_value = ["staging"]
            operator     = "any"
        }
    }
    rules {
        name            = "Production Rule"
        pass_percentage = 0
        conditions {
            type         = "environment_tier"
            target_value = ["production"]
            operator     = "any"
        }
    }
}
