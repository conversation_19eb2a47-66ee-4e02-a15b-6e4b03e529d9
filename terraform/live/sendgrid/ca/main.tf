resource "aws_route53_record" "record_user_authentication" {
  zone_id = data.aws_route53_zone.canada.zone_id
  name    = "em9340"
  type    = "CNAME"
  ttl     = "300"
  records = ["u53987168.wl169.sendgrid.net"]
}

resource "aws_route53_record" "record_user_ca_oms" {
  zone_id = data.aws_route53_zone.canada.zone_id
  name    = "oms._domainkey"
  type    = "CNAME"
  ttl     = "300"
  records = ["oms.domainkey.u53987168.wl169.sendgrid.net"]
}

resource "aws_route53_record" "record_user_ca_oms2" {
  zone_id = data.aws_route53_zone.canada.zone_id
  name    = "oms2._domainkey"
  type    = "CNAME"
  ttl     = "300"
  records = ["oms2.domainkey.u53987168.wl169.sendgrid.net"]
}

resource "aws_route53_record" "record_user_ca_oms_txt" {
  zone_id = data.aws_route53_zone.canada.zone_id
  name    = "_dmarc"
  type    = "TXT"
  ttl     = "300"
  records = ["v=DMARC1; p=quarantine; sp=quarantine; rua=mailto:<EMAIL>,mailto:<EMAIL>; ruf=mailto:<EMAIL>,mailto:<EMAIL>; adkim=r; aspf=r; ri=43200; fo=1"]
}

resource "aws_route53_record" "record_user_ca_branded_link_1" {
  zone_id = data.aws_route53_zone.canada.zone_id
  name    = "url7952"
  type    = "CNAME"
  ttl     = "300"
  records = ["sendgrid.net"]
}

resource "aws_route53_record" "record_user_ca_branded_link_2" {
  zone_id = data.aws_route53_zone.canada.zone_id
  name    = "53987168"
  type    = "CNAME"
  ttl     = "300"
  records = ["sendgrid.net"]
}
