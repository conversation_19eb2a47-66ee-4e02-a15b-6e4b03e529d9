provider "vault" {
    namespace = "services/order-management-service"
}

data "vault_generic_secret" "vault_secrets" {
    path = format("common/key-value/secrets")
}

module "gates" {
    source        = "../../module/statsig"
    api_key       = data.vault_generic_secret.vault_secrets.data.STATSIG_API_KEY
    gate_settings = [
        {
            name        = "Gate 1"
            description = "A feature gate named 1 for POC"
        }
    ]
}
