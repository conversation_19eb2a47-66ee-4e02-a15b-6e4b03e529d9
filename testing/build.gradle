plugins {
    id 'org.jetbrains.kotlin.jvm'
}

group = 'com.hellofresh.oms'
version = 'unspecified'

repositories {
    mavenCentral()
}

dependencies {
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3") // Replace with the latest version if needed
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.7.3") // For better compatibility with Java 8+

    implementation("org.postgresql:postgresql:42.7.2")

    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.opencsv:opencsv:5.8")
}

test {
    useJUnitPlatform()
}
