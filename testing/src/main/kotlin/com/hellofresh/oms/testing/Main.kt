package com.hellofresh.oms.testing

import java.io.File
import kotlinx.coroutines.runBlocking

fun main() = runBlocking {
    // Builds the configuration from environment variables
    EnvConfiguration().let { runConfiguration ->
        val omsClient = OmsClient(runConfiguration)
        val testOutput = TestOutput(runConfiguration)
        val databaseManager = DatabaseManager(runConfiguration)

        val generationFiles = File("testing/src/main/resources/Generate").listFiles()?.toList()?.sorted()
            ?: throw IllegalArgumentException("No test-cases found")

        generationFiles.forEach { sqlFile: File ->
            databaseManager.connect().use { connection ->
                val outputCsvFile =
                    File("testing/src/main/resources/Generated/" + sqlFile.nameWithoutExtension + ".csv")

                val results = databaseManager.runSqlScript(connection, sqlFile)
                databaseManager.exportToCsv(results, outputCsvFile)
            }
        }

        val testCases = listFilesInDirectoryOrThrow(
            "testing/src/main/resources/generated",
            "No generated test-cases found",
        ) + listFilesInDirectoryOrThrow(
            "testing/src/main/resources/static",
            "No static test-cases found",
        )

        val results = testCases
            .flatMap { file ->
                // Repeat each file for maxRequestsPerTestCase times
                List(runConfiguration.maxRequestsPerTestCase) {
                    file
                }
            }.map { file ->
                TestWorker(runConfiguration, file, omsClient)
            }.map {
                // Execute each test case in a sequential manner
                // If needed, this can be parallelized using coroutines
                // But for now I would keep it simple
                it.run()
            }

        testOutput
            .consolidateTestResults(results)
    }
}

private fun listFilesInDirectoryOrThrow(path: String, error: String) =
    File(path).listFiles()?.toList() ?: throw IllegalArgumentException(error)

@Suppress("MagicNumber")
class EnvConfiguration {
    val debugMode: Boolean = System.getenv("DEBUG_MODE")?.toBoolean() ?: false
    val maxRequestsPerTestCase: Int = System.getenv("MAX_REQUEST_PER_TEST_CASE")?.toInt() ?: 1
    val timeoutSeconds: Long = System.getenv("TIMEOUT_SECONDS")?.toLong() ?: 60
    val percentile: Double = System.getenv("PERCENTILE")?.toDouble() ?: 0.95
    val hostUrl: String = System.getenv("HOST_URL") ?: throw IllegalArgumentException("HOST_URL not provided")
    val token: String = System.getenv("TOKEN") ?: throw IllegalArgumentException("TOKEN not provided")

    // DB Connection
    val dbUrl: String = System.getenv("DB_URL") ?: throw IllegalArgumentException("DB_URL not provided")
    val dbUser: String = System.getenv("DB_USER") ?: throw IllegalArgumentException("DB_USER not provided")
    val dbPassword: String = System.getenv("DB_PASSWORD") ?: throw IllegalArgumentException("DB_PASSWORD not provided")

    init {
        if (debugMode) {
            println("Debug mode enabled")
            println("Using following configuration:")
            println("  - MAX_REQUEST_PER_TEST_CASE: $maxRequestsPerTestCase")
            println("  - TIMEOUT_SECONDS: $timeoutSeconds")
            println("  - PERCENTILE: $percentile")
        }
    }
}
