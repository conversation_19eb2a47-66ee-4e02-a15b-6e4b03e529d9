package com.hellofresh.oms.testing

import java.io.File
import java.time.LocalDateTime
import kotlin.system.measureTimeMillis
import okhttp3.Response

class TestWorker(
    private val envConfiguration: EnvConfiguration,
    private val file: File,
    private val omsClient: OmsClient,
) {
    fun run(): TestResult {
        val filename = file.name
        val expectedCode = FILENAME_TO_RESPONSE_MAP[filename]
            ?: throw IllegalArgumentException("No test case found for file: $filename")
        val startTime = LocalDateTime.now()
        println("Starting test for $filename")
        val executionTimeInMillis = measureTimeMillis {
            try {
                omsClient.postBatchImportFile(file).use { response ->
                    handleResponseCode(response, expectedCode, file)
                }
            } catch (e: UnexpectedResponseStatusException) {
                println("Error during request: ${e.message}")
            }
        }

        return TestResult(
            testCaseFile = filename,
            startTime = startTime,
            executionTimeInMillis = executionTimeInMillis,
        )
    }

    private fun handleResponseCode(response: Response, expectedCode: Int, file: File) {
        if (response.code == HTTP_UNAUTHORIZED) {
            println("Unauthorized (401) response received. Stopping tests")

            throw TokenExpiredException(
                "Token expired or unauthorized, stopping further tests.",
            )
        }

        if (response.code != expectedCode) {
            println("Error - unexpected response code: ${response.code}")
            println("Response Body: ${response.body?.string()}")

            throw UnexpectedResponseStatusException(
                "Unexpected response for ${file.name}. Expected: $expectedCode, Got: ${response.code}",
            )
        } else {
            if (envConfiguration.debugMode) {
                println(
                    "Request for ${file.name} succeeded with status: ${response.code}",
                )
            }
        }

        if (envConfiguration.debugMode) {
            println("Response Body: ${response.body?.string()}")
        }
    }

    companion object {
        private const val HTTP_SUCCESS = 200
        private const val HTTP_CLIENT_ERROR = 400
        private const val HTTP_UNAUTHORIZED = 401
        private const val HTTP_CONTENT_TOO_LARGE = 413

        private val FILENAME_TO_RESPONSE_MAP = mapOf(
            "1 - 1 PO, 1 line total.csv" to HTTP_SUCCESS,
            "2 - 1 POs, 600 lines total.csv" to HTTP_SUCCESS,
            "3 - 600 POs, 600 lines total.csv" to HTTP_SUCCESS,
            "4 - 3 POs, 40 lines total.csv" to HTTP_SUCCESS,
            "e0 - 1 PO, 600 errors.csv" to HTTP_CLIENT_ERROR,
            "e1 - 1 PO, 3 errors.csv" to HTTP_CLIENT_ERROR,
            "e2 - Every missing field.csv" to HTTP_CLIENT_ERROR,
            "e3 - Every Invalid Field.csv" to HTTP_CLIENT_ERROR,
            "f0 - completely empty file.csv" to HTTP_CLIENT_ERROR,
            "f1 - not a csv file.csv" to HTTP_CLIENT_ERROR,
            "f2 - too many lines.csv" to HTTP_CONTENT_TOO_LARGE,
            "f3 - too large file.csv" to HTTP_CONTENT_TOO_LARGE,
        )
    }

    data class TestResult(
        val testCaseFile: String,
        val startTime: LocalDateTime,
        val executionTimeInMillis: Long,
    )

    class TokenExpiredException(message: String) : Exception(message)
    class UnexpectedResponseStatusException(message: String) : RuntimeException(message)
}
