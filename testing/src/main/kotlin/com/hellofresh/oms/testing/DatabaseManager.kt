package com.hellofresh.oms.testing

import com.opencsv.CSVWriter
import java.io.File
import java.io.FileWriter
import java.sql.Connection
import java.sql.DriverManager
import java.sql.ResultSet

class DatabaseManager(private val config: EnvConfiguration) {

    fun connect(): Connection {
        val url = config.dbUrl
        val user = config.dbUser
        val password = config.dbPassword
        return DriverManager.getConnection(url, user, password)
    }

    fun runSqlScript(connection: Connection, sqlFile: File): ResultSet {
        val sql = sqlFile.readText()
        val statement = connection.createStatement()

        println("Executing SQL: $sqlFile")

        return statement.executeQuery(sql)
    }

    fun exportToCsv(resultSet: ResultSet, csvFile: File) {
        getCsvWriter(csvFile).use { writer ->
            val columnCount = resultSet.metaData.columnCount
            val header = (1..columnCount).map { resultSet.metaData.getColumnName(it) }.toTypedArray()
            writer.writeNext(header)

            while (resultSet.next()) {
                val row = (1..columnCount).map { resultSet.getString(it) ?: "" }.toTypedArray()
                writer.writeNext(row)
            }
        }
    }

    fun getCsvWriter(csvFile: File) = CSVWriter(
        FileWriter(csvFile),
        CSVWriter.DEFAULT_SEPARATOR,
        CSVWriter.NO_QUOTE_CHARACTER,
        CSVWriter.DEFAULT_ESCAPE_CHARACTER,
        CSVWriter.DEFAULT_LINE_END,
    )
}
