package com.hellofresh.oms.testing

import java.io.File
import java.util.concurrent.TimeUnit.SECONDS
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.asRequestBody

class OmsClient(private val envConfiguration: EnvConfiguration) {
    private val client = OkHttpClient.Builder()
        .connectTimeout(envConfiguration.timeoutSeconds, SECONDS)
        .readTimeout(envConfiguration.timeoutSeconds, SECONDS)
        .writeTimeout(envConfiguration.timeoutSeconds, SECONDS)
        .build()

    fun postBatchImportFile(csvFile: File) = client
        .newCall(buildRequest(csvFile))
        .execute()

    private fun buildRequest(csvFile: File): Request = Request.Builder()
        .url(
            "${envConfiguration.hostUrl}/imports/orders?dry_run=true",
        )
        .post(
            MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", csvFile.name, csvFile.asRequestBody())
                .build(),
        )
        .header("Authorization", "Bearer ${envConfiguration.token}")
        .build()
}
