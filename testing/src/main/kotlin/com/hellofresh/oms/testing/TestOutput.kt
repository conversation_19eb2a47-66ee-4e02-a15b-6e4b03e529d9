package com.hellofresh.oms.testing

import com.hellofresh.oms.testing.TestWorker.TestResult

class TestOutput(private val envConfiguration: EnvConfiguration) {
    fun consolidateTestResults(results: List<TestResult>) {
        println()
        println("Test Results:")
        println("=====================================")
        println(
            listOf(
                "Filename",
                "Min Time",
                "Median Time",
                "95% Time",
                "Max Time",
                "Mean Time",
            ).joinToString(","),
        )
        results.groupBy { it.testCaseFile }
            .map { groupedResults ->
                val times = groupedResults.value.map { it.executionTimeInMillis }
                Result(
                    filename = groupedResults.key,
                    minTime = times.minOrNull() ?: 0,
                    medianTime = calculateMedian(times),
                    ninetyFifthPercentile = calculatePercentile(times, envConfiguration.percentile),
                    maxTime = times.maxOrNull() ?: 0,
                    meanTime = times.average(),
                )
            }
            .sortedBy { it.filename }
            .forEach {
                println(it)
            }
    }

    private fun calculateMedian(times: List<Long>): Double {
        if (times.isEmpty()) return 0.0
        val sorted = times.sorted()
        return if (sorted.size % 2 == 0) {
            (sorted[sorted.size / 2 - 1] + sorted[sorted.size / 2]) / 2.0
        } else {
            sorted[sorted.size / 2].toDouble()
        }
    }

    private fun calculatePercentile(times: List<Long>, percentile: Double): Long {
        if (times.isEmpty()) return 0
        val sorted = times.sorted()
        return sorted[(percentile * sorted.size).toInt()]
    }

    data class Result(
        val filename: String,
        val minTime: Long,
        val medianTime: Double,
        val ninetyFifthPercentile: Long,
        val maxTime: Long,
        val meanTime: Double
    ) {
        override fun toString(): String = "$filename,$minTime,$medianTime,$ninetyFifthPercentile,$maxTime,$meanTime"
    }
}
