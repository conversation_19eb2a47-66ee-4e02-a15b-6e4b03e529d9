WITH constants AS (
    SELECT
        (SELECT name FROM public.emergency_order_reason WHERE market = 'gb' ORDER BY random() LIMIT 1) AS reason,
        (SELECT code FROM public.supplier WHERE market = 'gb' ORDER BY random() LIMIT 1) AS supplier_code,
        (SELECT unnest(ARRAY ['Others', 'Freight On Board', 'Crossdock', 'Vendor Delivered']) ORDER BY random() LIMIT 1) AS ship_method
),
     sku_list AS (
         SELECT code, ROW_NUMBER() OVER () AS rn
         FROM (
                  SELECT DISTINCT ON (code) code
                  FROM public.sku
                  WHERE market = 'gb'
                  ORDER BY code, random()  -- Ensure uniqueness & randomness
                  LIMIT 600
              ) AS unique_skus
     )
SELECT
    TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'IYYY-"W"IW') AS "week.value",
    'NT' AS "distributionCenter.value",
    NULL AS "customOrderNumberCode",
    constants.reason AS "reason",
    constants.supplier_code AS "supplierCode.value",
    TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'YYYY-MM-DD') AS "deliveryDate",
    '07:00' AS "startTime",
    '09:00' AS "endTime",
    sku_list.code AS "sku.value",
    10 AS "orderSize",
    order_unit.value AS "orderUnit.value",
    10 AS "buffer.value",
    10 AS "case.size",
    case_uom.value AS "case.uom",
    10 AS "price",
    constants.ship_method AS "shipMethod"
FROM generate_series(1, 600) AS g
         CROSS JOIN constants
         JOIN sku_list ON g = sku_list.rn
         JOIN LATERAL (
    SELECT unnest(ARRAY ['UNIT_TYPE', 'CASE_TYPE']) AS value
    ORDER BY random()
    LIMIT 1
    ) AS order_unit ON true
         JOIN LATERAL (
    SELECT unnest(ARRAY ['oz', 'kg', 'lbs', 'L', 'gal', 'unit', 'units']) AS value
    ORDER BY random()
    LIMIT 1
    ) AS case_uom ON true;
