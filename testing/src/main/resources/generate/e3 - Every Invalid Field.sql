SELECT
    CASE WHEN g = 1 THEN 'Invalid Week' ELSE TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'IYYY-"W"IW') END AS "week.value",
    CASE WHEN g = 2 THEN '??' ELSE 'NT' END AS "distributionCenter.value",
    NULL AS "customOrderNumberCode",
    CASE WHEN g = 3 THEN 'Invalid Reason' ELSE constants.reason END AS "reason",
    CASE WHEN g = 4 THEN 'Invalid Supplier Code' ELSE constants.supplier END AS "supplierCode.value",
    CASE WHEN g = 5 THEN TO_CHAR(CURRENT_DATE + INTERVAL '10000 years', 'YYYY-MM-DD') ELSE TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'YYYY-MM-DD') END AS "deliveryDate",
    CASE WHEN g = 6 THEN '25:00' ELSE '07:00' END AS "startTime",
    CASE WHEN g = 7 THEN '25:00' ELSE '09:00' END AS "endTime",
    CASE WHEN g = 8 THEN 'Invalid SKU Code' ELSE constants.sku END AS "sku.value",
    CASE WHEN g = 9 THEN -1 ELSE 10 END AS "orderSize",
    CASE WHEN g = 10 THEN 'Invalid' ELSE constants.order_unit END AS "orderUnit.value",
    CASE WHEN g = 11 THEN -1 ELSE 10 END AS "buffer.value",
    CASE WHEN g = 12 THEN NULL ELSE 10 END AS "case.size",
    CASE WHEN g = 13 THEN -1 ELSE 10 END AS "case.size",
    CASE WHEN g = 14 THEN 0 ELSE 10 END AS "case.size",
    CASE WHEN g = 15 THEN 'Invalid' ELSE constants.case_uom END AS "case.uom",
    CASE WHEN g = 16 THEN -1.0 ELSE 10 END AS "price",
    CASE WHEN g = 17 THEN 'Invalid Ship Method' ELSE constants.ship_method END AS "shipMethod"
FROM generate_series(1, 17) AS g
         CROSS JOIN (
    SELECT
        (SELECT name FROM public.emergency_order_reason WHERE market = 'gb' ORDER BY random() LIMIT 1) AS reason,
        (SELECT code FROM public.supplier WHERE market = 'gb' ORDER BY random() LIMIT 1) AS supplier,
        (SELECT code FROM public.sku WHERE market = 'gb' ORDER BY random() LIMIT 1) AS sku,
        (SELECT unnest(ARRAY ['UNIT_TYPE', 'CASE_TYPE']) ORDER BY random() LIMIT 1) AS order_unit,
        (SELECT unnest(ARRAY ['oz', 'kg', 'lbs', 'L', 'gal', 'unit', 'units']) ORDER BY random() LIMIT 1) AS case_uom,
        (SELECT unnest(ARRAY ['Others', 'Freight On Board', 'Crossdock', 'Vendor Delivered']) ORDER BY random() LIMIT 1) AS ship_method
) AS constants;
