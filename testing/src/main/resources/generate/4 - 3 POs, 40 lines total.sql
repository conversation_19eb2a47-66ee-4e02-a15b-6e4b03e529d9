WITH constants AS (
    SELECT
        (SELECT name FROM public.emergency_order_reason WHERE market = 'gb' ORDER BY random() LIMIT 1) AS reason,
        (SELECT unnest(ARRAY ['Others', 'Freight On Board', 'Crossdock', 'Vendor Delivered']) ORDER BY random() LIMIT 1) AS ship_method
),
     suppliers AS (
         SELECT code FROM public.supplier WHERE market = 'gb' ORDER BY random() LIMIT 3
     )
SELECT
    TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'IYYY-"W"IW') AS "week.value",
    'NT' AS "distributionCenter.value",
    '' AS "customOrderNumberCode",
    constants.reason AS "reason",
    supplier.code AS "supplierCode.value",
    TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'YYYY-MM-DD') AS "deliveryDate",
    '07:00' AS "startTime",
    '09:00' AS "endTime",
    sku.code AS "sku.value",
    10 AS "orderSize",
    order_unit.value AS "orderUnit.value",
    10 AS "buffer.value",
    10 AS "case.size",
    case_uom.value AS "case.uom",
    10 AS "price",
    constants.ship_method AS "shipMethod"
FROM generate_series(1,40) AS g
         CROSS JOIN constants
         JOIN LATERAL (
    SELECT code
    FROM suppliers
    ORDER BY random() + g*1e-9  -- Randomize suppliers per row
    LIMIT 1
    ) AS supplier ON true
         JOIN LATERAL (
    SELECT code
    FROM public.sku
    WHERE market = 'gb'
    ORDER BY random() + g*1e-9
    LIMIT 1
    ) AS sku ON true
         JOIN LATERAL (
    SELECT unnest(ARRAY ['UNIT_TYPE', 'CASE_TYPE']) AS value
    ORDER BY random() + g*1e-9
    LIMIT 1
    ) AS order_unit ON true
         JOIN LATERAL (
    SELECT unnest(ARRAY ['oz', 'kg', 'lbs', 'L', 'gal', 'unit', 'units']) AS value
    ORDER BY random() + g*1e-9
    LIMIT 1
    ) AS case_uom ON true;
