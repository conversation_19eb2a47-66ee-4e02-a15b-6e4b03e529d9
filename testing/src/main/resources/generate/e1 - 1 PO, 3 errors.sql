WITH constants AS (
    SELECT
        (SELECT name FROM public.emergency_order_reason WHERE market = 'gb' ORDER BY random() LIMIT 1) AS reason,
        (SELECT unnest(ARRAY ['Others', 'Freight On Board', 'Crossdock', 'Vendor Delivered']) ORDER BY random() LIMIT 1) AS ship_method
),
     suppliers AS (
         SELECT code FROM public.supplier WHERE market = 'gb' ORDER BY random() LIMIT 1  -- Choose one supplier for all rows
     )
SELECT
    CASE
        WHEN g = 1 THEN '2025-W00'  -- For the first row, set week.value
        ELSE TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'IYYY-"W"IW')
        END AS "week.value",
    'NT' AS "distributionCenter.value",
    '' AS "customOrderNumberCode",
    constants.reason AS "reason",
    supplier.code AS "supplierCode.value",
    TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'YYYY-MM-DD') AS "deliveryDate",
    '07:00' AS "startTime",
    '09:00' AS "endTime",
    CASE
        WHEN g = 12 THEN 'PHF-10-9999902'  -- For the 12th row, set sku.value
        ELSE sku.code
        END AS "sku.value",
    10 AS "orderSize",
    order_unit.value AS "orderUnit.value",
    10 AS "buffer.value",
    10 AS "case.size",
    CASE
        WHEN g = 6 THEN 'units'  -- For the 6th row, set case.uom to 'units'
        ELSE case_uom.value
        END AS "case.uom",
    10 AS "price",
    constants.ship_method AS "shipMethod"
FROM generate_series(1,40) AS g
         CROSS JOIN constants
         JOIN LATERAL (
    SELECT code
    FROM suppliers  -- This will return the same supplier for all rows
    LIMIT 1
    ) AS supplier ON true
         JOIN LATERAL (
    SELECT code
    FROM public.sku
    WHERE market = 'gb'
    ORDER BY random() + g*1e-9
    LIMIT 1
    ) AS sku ON true
         JOIN LATERAL (
    SELECT unnest(ARRAY ['UNIT_TYPE', 'CASE_TYPE']) AS value
    ORDER BY random() + g*1e-9
    LIMIT 1
    ) AS order_unit ON true
         JOIN LATERAL (
    SELECT unnest(ARRAY ['oz', 'kg', 'lbs', 'L', 'gal', 'unit', 'units']) AS value
    ORDER BY random() + g*1e-9
    LIMIT 1
    ) AS case_uom ON true;
