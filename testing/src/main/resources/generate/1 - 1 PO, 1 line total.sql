SELECT TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'IYYY-"W"IW') AS "week.value",
       'NT'                                                     as "distributionCenter.value",
       ''                                                       as "customOrderNumberCode",
       (SELECT public.emergency_order_reason.name
        FROM public.emergency_order_reason
        WHERE market = 'gb'
        ORDER BY random()
        LIMIT 1)                                                as "reason",
       (SELECT code
        FROM public.supplier
        WHERE market = 'gb'
        ORDER BY random()
        LIMIT 1)                                                as "supplierCode.value",
       TO_CHAR(CURRENT_DATE + INTERVAL '4 weeks', 'YYYY-MM-DD') as "deliveryDate",
       '07:00'                                                  as "startTime",
       '09:00'                                                  as "endTime",
       (SELECT code
        FROM public.sku
        WHERE market = 'gb'
        ORDER BY random()
        LIMIT 1)                                                as "sku.value",
       10                                                       as "orderSize",
       (SELECT unnest(ARRAY ['UNIT_TYPE', 'CASE_TYPE'])
        ORDER BY random()
        LIMIT 1)                                                as "orderUnit.value",
       10                                                       as "buffer.value",
       10                                                       as "case.size",
       (SELECT unnest(ARRAY ['oz', 'kg', 'lbs', 'L', 'gal', 'unit', 'units'])
        ORDER BY random()
        LIMIT 1)                                                as "case.uom",
       10                                                       as "price",
       (SELECT unnest(ARRAY ['Others', 'Freight On Board', 'Crossdock', 'Vendor Delivered'])
        ORDER BY random()
        LIMIT 1)                                                as "shipMethod"
