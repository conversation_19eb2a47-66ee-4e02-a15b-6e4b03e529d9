# Testing module

This module consists of a standalone tool for running latency/response time tests against the batch EPO endpoint.

All test files are currently set up for the `NT` DC, a test-DC which exists *only* in the staging environment, so running these tests against live **will**
result in errors (intentionally).

In order to run the test you will need to do the following:

1. Have access to the GB market in staging,
2. Log-in to the staging operations portal [https://staging-operations.hellofresh.com/](https://staging-operations.hellofresh.com/)
3. Copy your authorisation token (currently found in the `operation.azure.token
   ` Key in Local Storage) into the token field in `/src/main/kotlin/Main.kt`
4. Run `Main.kt` (e.g. through IntelliJ IDEA)

## How testing works

The file will upload a number of test CSV a configurable number (typically 100) of times, and produce some statistics to the console.

Currently, no verification is made of the responses received beyond the HTTP status code.

## Test files

Happy Paths:

- `1 - Single Line PO.csv` - the most simple example of a batch EPO, a single line PO from a single supplier,
- `2 - 200 individual POs.csv` - a file with the (current) maximum number of lines, each of which will create a single-line PO from a distinct supplier.
- `3 - 200 Line PO.csv` - a file with the (current) maximum number of lines, each from a single supplier.
- `4 - Typical Load.csv` - a file which looks like a typical EPO upload, with 45 lines creating 3 POs.

Unhappy Paths:

- `e0 - completely empty file` - an empty file
- `e1 - not a csv file.csv` - a file consisting of a brief plain-text instead of CSV formatted data.
- `e2 - too many lines.csv` - a file consisting of 201 lines
- `e3 - too large file.csv` - a file which exceeds our file-size limit
- `e4 - maximum errors.csv` - a file with the maximum number of lines, in which every field of every line has an error
- `e5 - Typical errors.csv` - a file which looks like a typical EPO upload, with 45 lines creating 3 POs, but with 5 errors introduced

The following two files are not tested as a part of the latency testing, but may be useful for testing the endpoint in general.

- `e6 - Each missing field.csv` - a file in which line is missing a single required field, or has text in a field required to be empty. Useful for testing
  errors on the endpoint
- `e7 - Each invalid field.csv` - a file which tries to exercise every validation by presenting invalid data of every kind in every field

## Output

Console output will show an any errors, give a description of progress, and present a summary of the result as follows:

```
Filename,Min Time,Median Time,95% Time,Max Time,Mean Time
1 - Single Line PO.csv,83,99.5,168.0,670,118.54
2 - 200 POs - 1 supplier.csv,5364,6488.5,7969.0,11158,6635.58
3 - 200 Line PO.csv,5446,6601.5,7542.0,7942,6593.82
4 - Typical Load.csv,1228,1636.5,2339.0,2625,1699.54
e0 - completely empty file.csv,52,56.5,98.0,155,63.14
e1 - not a csv file.csv,53,59.0,87.0,103,63.14
e2 - too many lines.csv,57,67.0,95.0,97 ,69.82
e3 - too large file.csv,67,441.5,516.0,551,439.82
e4 - maximum errors.csv,125,6256.5,8088.0,10479,6366.24
e5 - Typical errors.csv,1187,1528.5,1870.0,2273,1523.2
```

## Notes on Usage / Trouble-shooting

For the uploads to work, the master-data in staging much be correct. This is likely to change over time as suppliers/SKUs are updated/archived, so the CSV will
need to be updated.

At some point it may be worth writing scripts to regenerate the CSV files with up-to-date date, an example of how this could look can be found in the
`src/main/resources/Generate` folder.
