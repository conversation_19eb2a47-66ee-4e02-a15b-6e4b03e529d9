#!/bin/bash

# Prerequisite
#    Make sure you have +x permissions (e.g. chmod +x ./vault.sh)
#    Vault (refer to the install page https://developer.hashicorp.com/vault/install)
#
# Usage
#   It defaults to order-management-service vault namespace and staging environment.
#   Works for order-planning-service and tapioca since they follow the same path in vault data ${ENVIRONMENT}/key-value/secrets.
#
# Example: vault.sh order-management-service live
#
# Reference: https://github.com/hellofresh/order-management-service/pull/1098

export VAULT_ADDR='https://vault.secrets.hellofresh.io'
DEFAULT_NAMESPACE='order-management-service'
DEFAULT_ENVIRONMENT='staging'

NAMESPACE=${1:-${DEFAULT_NAMESPACE}}
ENVIRONMENT=${2:-${DEFAULT_ENVIRONMENT}}

print_secrets () {
	echo "$(VAULT_NAMESPACE=services/${NAMESPACE} vault kv get -format json -field data ${ENVIRONMENT}/key-value/secrets)"
}

token_is_for_namespace () {
	token_namespace=$(vault read -field namespace_path auth/token/lookup-self)

	[[ "${token_namespace}" =~ .*"${NAMESPACE}".* ]]
}

login_to_namespace () {
	$(VAULT_NAMESPACE=services/${NAMESPACE} vault login -method=oidc -no-print)
}

main() {
	start_date=$(date +%s.%N)

	if token_is_for_namespace; then
		echo ""
		echo token is for namespace:${NAMESPACE}
		echo ""
	else
		login_to_namespace
	fi

  print_secrets

	end_date=$(date +%s.%N)
	echo "$end_date-$start_date" |bc
}


main ${NAMESPACE} ${ENVIRONMENT}
