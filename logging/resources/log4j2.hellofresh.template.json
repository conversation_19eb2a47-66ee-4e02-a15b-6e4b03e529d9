{"@timestamp": {"$resolver": "timestamp", "pattern": {"format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", "timeZone": "UTC"}}, "level": {"$resolver": "level", "field": "severity", "severity": {"field": "code"}}, "message": {"$resolver": "message", "stringified": true}, "exception_class": {"$resolver": "exception", "field": "className"}, "exception_message": {"$resolver": "exception", "field": "message"}, "stacktrace": {"$resolver": "exception", "field": "stackTrace", "stringified": true}, "handler": {"$resolver": "logger", "field": "name"}, "thread_name": {"$resolver": "thread", "field": "name"}, "thread_context": {"$resolver": "mdc"}, "location.class": {"$resolver": "source", "field": "className"}, "location.method": {"$resolver": "source", "field": "methodName"}, "location.file": {"$resolver": "source", "field": "fileName"}, "location.line": {"$resolver": "source", "field": "lineNumber"}}