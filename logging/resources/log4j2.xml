<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="error" strict="true">
    <Properties>
        <Property name="LOG_EXCEPTION_CONVERSION_WORD">%xwEx</Property>
        <Property name="LOG_LEVEL_PATTERN">%5p</Property>
        <Property name="LOG_DATEFORMAT_PATTERN">yyyy-MM-dd'T'HH:mm:ss.SSSXXX</Property>
        <Property name="CONSOLE_LOG_PATTERN">%clr{%d{${sys:LOG_DATEFORMAT_PATTERN}}}{faint} %clr{${sys:LOG_LEVEL_PATTERN}} %clr{%pid}{magenta} %clr{---}{faint} %clr{[%15.15t]}{faint} %clr{%-40.40c{1.}}{cyan} %clr{:}{faint} %m%n${sys:LOG_EXCEPTION_CONVERSION_WORD}</Property>
    </Properties>
    <Appenders>
        <Console name="HelloFresh">
            <JsonTemplateLayout
                eventDelimiter="&#xA;"
                eventTemplateUri="classpath:log4j2.hellofresh.template.json"
                locationInfoEnabled="${sys:logging.location-info:-false}"
                stackTraceEnabled="${sys:logging.stack-trace.enabled:-true}"
            />
        </Console>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${sys:CONSOLE_LOG_PATTERN}" charset="${sys:CONSOLE_LOG_CHARSET}"/>
        </Console>
    </Appenders>
    <Loggers>
        <Root level="${sys:logging.level.root:-info}" includeLocation="${sys:logging.location-info:-false}">
            <AppenderRef ref="${sys:logging.layout:-Console}"/>
            <!-- https://issues.apache.org/jira/browse/KAFKA-7509 -->
            <RegexFilter regex="^The configuration '[^']+' was supplied but isn't a known config\.$" onMatch="DENY" onMismatch="NEUTRAL"/>
        </Root>
    </Loggers>
</Configuration>
