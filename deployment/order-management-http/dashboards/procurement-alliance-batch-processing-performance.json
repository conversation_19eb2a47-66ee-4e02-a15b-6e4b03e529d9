{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Dashboard for procurement-wide batch processing performance monitoring.\n", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1767, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "Tribe: Planning and Purchasing", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 143, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-79828", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "max (delivery_date_generation_messages_longest_wait_seconds)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Order Planning DD Processing", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["-"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": false}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 142, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-79828", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "max by (application) (sku_demand_forecast_job_duration_seconds_max{application=~\".*-job\"})", "instant": false, "legendFormat": "{{application}}", "range": true, "refId": "A"}], "title": "IP Import Time Taken", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 144, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-79828", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "histogram_quantile(0.99, sum by(import_name, le) (procurement_import_duration_seconds_bucket{service_name=\"tapioca\"}))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "OT Import Processing", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 145, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["max"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.4.0-79828", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "max by (message_type) (procurement_stream_processing_duration_seconds{service_name=~\"order-planning-service\", message_type=\"pqr-generation\"})", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Max PQR Generation Time in OPE", "type": "stat"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 146, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-79828", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "histogram_quantile(0.99, sum by(import_name, le) (procurement_import_duration_seconds_bucket{service_name=\"supplier-split-service\"}))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Supplier Split Service Import Processing", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 5000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 147, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-79828", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by(processType) (batch_purchase_orders_processing_time_max{container=\"oms-order-management-http-app\"})", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "OM Batch Import Processing", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 240}, {"color": "light-red", "value": 300}, {"color": "red", "value": 600}]}, "unit": "dthms"}, "overrides": []}, "gridPos": {"h": 21, "w": 24, "x": 0, "y": 25}, "id": 2, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 19, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "limit": 10, "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-79828", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": false, "expr": "tapioca_import_end_time{hf_import_step=\"main\"}-tapioca_import_start_time{hf_import_step=\"main\"}", "hide": false, "instant": false, "legendFormat": "#{{hf_import}} ({{hf_dc}} {{hf_week}}) / {{hf_import_type}}", "range": true, "refId": "B"}], "title": "Import run times", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 46}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.3.0-76537", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "(tapioca_forecast_import_end_time{hf_import_step=\"main\"} - tapioca_forecast_import_start_time{hf_import_step=\"main\"} < 0) ", "legendFormat": "{{hf_import}}. {{hf_import_type}}-{{hf_dc}}-{{hf_week}}", "range": true, "refId": "A"}], "title": "Failed imports", "type": "stat"}], "schemaVersion": 39, "tags": ["scm", "procurement"], "templating": {"list": [{"hide": 0, "includeAll": false, "label": "Metrics Datasource", "multi": false, "name": "metrics_ds", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "/^metrics_/", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Procurement Alliance Batch Processing Performance", "version": 1, "weekStart": ""}