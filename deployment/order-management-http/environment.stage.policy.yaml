apiVersion: core.oam.dev/v1alpha1
kind: Policy
type: override
metadata:
  annotations:
    notifications.argoproj.io/subscribe.on-health-degraded.slack: squad-purchase-order-lifecycle-alerts-staging
    notifications.argoproj.io/subscribe.on-sync-failed.slack: squad-purchase-order-lifecycle-alerts-staging
  appLabels:
    slack: squad-purchase-order-lifecycle-alerts-staging
properties:
  components:
    - name: app
      type: hf-webservice
      properties:
        env:
          SPRING_PROFILES_ACTIVE: staging
          STATSIG_ENVIRONMENT: staging
