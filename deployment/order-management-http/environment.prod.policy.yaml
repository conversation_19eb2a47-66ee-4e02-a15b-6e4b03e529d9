apiVersion: core.oam.dev/v1alpha1
kind: Policy
type: override
metadata:
  annotations:
    notifications.argoproj.io/subscribe.on-health-degraded.slack: squad-purchase-order-lifecycle-alerts-live
    notifications.argoproj.io/subscribe.on-sync-failed.slack: squad-purchase-order-lifecycle-alerts-live
  appLabels:
    slack: squad-purchase-order-lifecycle-deployments
properties:
  components:
    - name: app
      type: hf-webservice
      properties:
        cpu: 2.0
        memory: 2Gi
        memoryLimits: 2Gi
        env:
          SPRING_PROFILES_ACTIVE: live
          # Statsig only display diagnostics and statistics for the `production` environment, not `live`
          STATSIG_ENVIRONMENT: production
      traits:
        - type: dependencies
          properties:
            meshInternals:
              - name: tapioca-http
                namespace: scm
                partition: legacy
            meshExternals:
              - host: order-management-service-db.live.hellofresh.io
                port: 5432
        - type: alerting-rules
          properties:
            rules:
              - alertName: 'OrderManagementHttpFrequentPodRestarts'
                expression: 'sum(increase(kube_pod_container_status_restarts_total{container=~"oms-order-management-http-app"}[10m])) by (pod) > 3'
                evaluationDuration: 2m
                severity: P4
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                summary: '{{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container }}) is restarting {{ printf "%.2f" $value }} times / 10 minutes'
                description: 'OrderManagementHttp pod is frequently restarting.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#modulenamefrequentpodrestarts--module_name--is-restarting-n-times--10-minutes
              - alertName: 'CommunicationPreferenceMarketDefaultMissing'
                expression: 'max by(market) (communication_preference_market_default_not_found_total{container=~"oms-order-management-http-app"}) > 0'
                evaluationDuration: 1m
                severity: P4
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                summary: '[Order Management] The given market ({{ $labels.market }}) does not have a default communication preference.'
                description: '[Order Management] The given market ({{ $labels.market }}) does not have a default communication preference.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#communicationpreferencemarketdefaultmissing-a-communication-preference-was-not-found-for-the-given-market

        - type: http-slo
          properties:
            objective: 99.9
            highBurnRate:
              severity: "P4"
              slack: 'squad-purchase-order-lifecycle-alerts-temp'
            lowBurnRate:
              severity: "P5"
              slack: 'squad-purchase-order-lifecycle-alerts-temp'
            spec:
              badStatusCodesRegex: "5.*"
              latencyMS: "2500"
              serviceFilter: "oms-order-management-http-app"

