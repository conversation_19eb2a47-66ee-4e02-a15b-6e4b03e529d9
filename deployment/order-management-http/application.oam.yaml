---
apiVersion: core.oam.dev/v1beta1
kind: Application
metadata:
  labels:
    partition: food-systems
    region: eu-west-1
    squad: purchase-order-lifecycle
    tribe: planning-and-purchasing
    slack: squad-purchase-order-lifecycle-deployments
  name: oms-order-management-http

spec:
  components:
    - name: app
      type: hf-webservice
      properties:
        image: '489198589229.dkr.ecr.eu-west-1.amazonaws.com/order-management-service'
        version: 'order-management-http-@version@'
        memory: 1Gi
        memoryLimits: 2Gi
        cpu: 1.0
        ports:
          - containerPort: 8080
            port: 8080
            protocol: http
          - containerPort: 8081
            port: 8081
            protocol: http
        probes:
          liveness:
            path: /actuator/health/liveness
            port: 8081
            initialDelaySeconds: 30
            timeoutSeconds: 1
          readiness:
            path: /actuator/health/readiness
            port: 8081
            timeoutSeconds: 30
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
        minReplicas: 2
        maxReplicas: 2

      traits:
        - type: intranet-ingress
          properties: { }
        - type: secrets
          properties:
            secrets:
              - name: secrets
        - type: dependencies
          properties:
            meshInternals:
              - name: tapioca-http
                namespace: scm
                partition: legacy
            meshExternals:
              - host: order-management-service-db.staging.hellofresh.io
                port: 5432
        - type: grafana-dashboards
          properties:
            dashboards:
              - path: ./deployment/order-management-http/dashboards/order-management-service.json
              - path: ./deployment/order-management-http/dashboards/procurement-alliance-batch-processing-performance.json
        - type: service-monitor
          properties:
            port: 8081
            path: "/actuator/prometheus"
        - type: alerting-rules
          properties:
            rules:
            - alertName: 'OrderManagementHttpFrequentPodRestarts'
              expression: 'sum(increase(kube_pod_container_status_restarts_total{container=~"oms-order-management-http-app"}[10m])) by (pod) > 3'
              evaluationDuration: 2m
              severity: P4
              slackChannel: 'squad-purchase-order-lifecycle-alerts-staging'
              summary: '{{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container }}) is restarting {{ printf "%.2f" $value }} times / 10 minutes'
              description: 'OrderManagementHttp pod is frequently restarting.'
              runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#modulenamefrequentpodrestarts--module_name--is-restarting-n-times--10-minutes
        - type: http-slo
          properties:
            objective: 99.9
            highBurnRate:
              severity: "P4"
              slack: 'squad-purchase-order-lifecycle-alerts-staging'
            lowBurnRate:
              severity: "P5"
              slack: 'squad-purchase-order-lifecycle-alerts-staging'
            spec:
              badStatusCodesRegex: "5.*"
              latencyMS: "2500"
              serviceFilter: "oms-order-management-http-app"

  policies:
    - properties:
        namespace: services/order-management-service
      type: secrets
      name: secrets
    - properties: {}
      name: dependencies
      type: dependencies
