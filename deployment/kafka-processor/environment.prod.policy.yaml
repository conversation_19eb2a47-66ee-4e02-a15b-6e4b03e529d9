apiVersion: core.oam.dev/v1alpha1
kind: Policy
type: override
metadata:
  annotations:
    notifications.argoproj.io/subscribe.on-health-degraded.slack: squad-purchase-order-lifecycle-alerts-live
    notifications.argoproj.io/subscribe.on-sync-failed.slack: squad-purchase-order-lifecycle-alerts-live
  appLabels:
    slack: squad-purchase-order-lifecycle-alerts-live
properties:
  components:
    - name: app
      type: hf-webservice
      properties:
        memory: 2Gi
        cpu: 2.0
        env:
          SPRING_PROFILES_ACTIVE: live
          # Statsig only display diagnostics and statistics for the `production` environment, not `live`
          STATSIG_ENVIRONMENT: production
      traits:
        - type: dependencies
          properties:
            meshInternals: []
            meshExternals:
              - host: order-management-service-db.live.hellofresh.io
                port: 5432
        - type: alerting-rules
          properties:
            rules:
              - alertName: 'KafkaProcessorFrequentPodRestarts'
                expression: 'sum(increase(kube_pod_container_status_restarts_total{container=~"oms-kafka-processor-app"}[10m])) by (pod) > 3'
                evaluationDuration: 2m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: '{{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container }}) is restarting {{ printf "%.2f" $value }} times / 10 minutes'
                description: 'oms-kafka-processor-app pod is frequently restarting.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#modulenamefrequentpodrestarts--module_name--is-restarting-n-times--10-minutes

              - alertName: 'Kafka consumer lag - Distribution centers'
                expression: 'kafka_consumergroup_group_lag_seconds{group="oms-kafka-processor-distributionCenter.v1"} > 300'
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-distributionCenter Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka consumer lag - supplier'
                expression: kafka_consumergroup_group_lag_seconds{group=~"oms-kafka-processor-supplier.*",topic=~"public.planning.facility.v1"} > 300
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-supplier Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka consumer lag - ship method'
                expression: kafka_consumergroup_group_lag_seconds{group="oms-kafka-processor-ship-method.v2"} > 300
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-ship-method Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka consumer lag - sku'
                expression: kafka_consumergroup_group_lag_seconds{group=~"oms-kafka-processor-sku.*",topic=~"public.planning.culinarysku.v1"} > 300
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-sku Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka consumer lag - supplierSku'
                expression: kafka_consumergroup_group_lag_seconds{group=~"oms-kafka-processor-supplierSku.*",topic=~"public.planning.suppliersku.v1"} > 300
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-supplierSku Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka consumer lag - supplierSkuPricing'
                expression: kafka_consumergroup_group_lag_seconds{group=~"oms-kafka-processor-supplierSkuPrice.*",topic="public.planning.suppliersku.pricing.v1"} > 300
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-supplierSkuPricing Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka consumer lag - purchaseOrder'
                expression: kafka_consumergroup_group_lag_seconds{group="oms-kafka-processor-purchaseOrder.v2"} > 300
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-purchaseOrder Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka consumer lag - supplierSkuPackaging'
                expression: kafka_consumergroup_group_lag_seconds{group="oms-kafka-processor-supplierSkuPackaging.v1"} > 300
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-supplierSkuPackaging Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka consumer lag - goodsReceivedNote'
                expression: kafka_consumergroup_group_lag_seconds{group="oms-kafka-processor-goodsReceivedNote.v1"} > 300
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'kafka-processor-goodsReceivedNote Kafka Topic Lag Time'
                description: 'KafkaProcessor is experiencing lag for over 5 minutes on fetching records for topic: "{{ $labels.topic }}".'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-consumer-lag---consumer_name--kafka-processor-consumer_name-kafka-topic-lag-time

              - alertName: 'Kafka message processing exception - distribution center'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~".+public.scm.registry.dc.v1beta1.+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'Distribution Center Consumer'
                description: 'Exception has been thrown while processing Distribution Center message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - ship method'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~".+public.planning.supplier.default-ship-methods.v1.+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'Ship Method Consumer'
                description: 'Exception has been thrown while processing SHIP METHOD message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - supplier'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~".+(public.planning.facility.v1).+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'Supplier Consumer'
                description: 'Exception has been thrown while processing FACILITY SUPPLIER message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - sku'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~".+(public.planning.culinarysku.v1).+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'Sku Consumer'
                description: 'Exception has been thrown while processing CULINARY SKU message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - supplierSku'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~"public.planning.suppliersku.+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'SupplierSku Consumer'
                description: 'Exception has been thrown while processing SUPPLIER SKU message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - supplierSkuPricing'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~"public.planning.suppliersku.pricing.+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'SupplierSkuPricing Consumer'
                description: 'Exception has been thrown while processing SUPPLIER SKU PRICING message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - purchaseOrder'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~".+public.supply.procurement.purchase-order.v1.+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'PurchaseOrder Consumer'
                description: 'Exception has been thrown while processing PURCHASE ORDER message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - supplierSkuPackaging'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~".+public.planning.suppliersku.packaging.v1.+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'SupplierSkuPackaging Consumer'
                description: 'Exception has been thrown while processing SUPPLIER SKU PACKAGING message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - supplyQuantityRecommendationDaily'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~".+public.ordering.supply-quantity-recommendation-daily.v1.+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'SupplyQuantityRecommendationDaily Consumer'
                description: 'Exception has been thrown while processing SUPPLY QUANTITY RECOMMENDATION DAILY message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name

              - alertName: 'Kafka message processing exception - goodsReceivedNote'
                expression: sum by (name) (increase(spring_kafka_listener_seconds_count{result!="success", container="oms-kafka-processor-app", name=~".+public.distribution-center.inbound.goods-received-note.v1.+"}[5m])) > 0
                evaluationDuration: 5m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'GoodsReceivedNote Consumer'
                description: 'Exception has been thrown while processing GOODS RECEIVED NOTE message. Check kafka-processor logs.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#kafka-message-processing-exception-consumer_name
