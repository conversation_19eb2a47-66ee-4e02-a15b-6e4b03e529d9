---
apiVersion: core.oam.dev/v1beta1
kind: Application
metadata:
  labels:
    partition: food-systems
    region: eu-west-1
    squad: purchase-order-lifecycle
    tribe: planning-and-purchasing
    slack: squad-purchase-order-lifecycle-deployments
  name: oms-purchase-order-synchronizer

spec:
  components:
    - name: app
      type: hf-webservice
      properties:
        image: '489198589229.dkr.ecr.eu-west-1.amazonaws.com/order-management-service'
        version: 'purchase-order-synchronizer-@version@'
        memory: 1Gi
        memoryLimits: 1Gi
        cpu: 1.0
        ports:
          - containerPort: 8080
            port: 8080
            protocol: http
          - containerPort: 8081
            port: 8081
            protocol: http
        probes:
          liveness:
            path: /actuator/health/liveness
            port: 8081
            initialDelaySeconds: 30
            timeoutSeconds: 1
          readiness:
            path: /actuator/health/readiness
            port: 8081
            timeoutSeconds: 30
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
        minReplicas: 1
        maxReplicas: 1

      traits:
        - type: intranet-ingress
          properties: { }
        - type: secrets
          properties:
            secrets:
              - name: secrets
        - type: dependencies
          properties:
            meshInternals:
              - name: tapioca-http
                namespace: scm
                partition: legacy
            meshExternals:
              - host: order-management-service-db.staging.hellofresh.io
                port: 5432
        - type: service-monitor
          properties:
            port: 8081
            path: "/actuator/prometheus"
        - type: alerting-rules
          properties:
            rules:
            - alertName: 'PurchaseOrderSynchronizerFrequentPodRestarts'
              expression: 'sum(increase(kube_pod_container_status_restarts_total{container=~"oms-purchase-order-synchronizer-app"}[10m])) by (pod) > 3'
              evaluationDuration: 2m
              slackChannel: 'squad-purchase-order-lifecycle-alerts-staging'
              severity: 'P4'
              summary: '{{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container }}) is restarting {{ printf "%.2f" $value }} times / 10 minutes'
              description: 'Purchase Order Synchronizer pod is frequently restarting.'
              runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#modulenamefrequentpodrestarts--module_name--is-restarting-n-times--10-minutes

            - alertName: 'WorkerActionTimeInQueueTooLong'
              expression: 'max by (type, status) (worker_action_time_in_queue_max{container="oms-purchase-order-synchronizer-app"}) > 900000'
              evaluationDuration: 1m
              slackChannel: 'squad-purchase-order-lifecycle-alerts-staging'
              severity: 'P4'
              summary: '[Order Management] {{ $labels.type }} action with status={{ $labels.status }} is more than 15 minutes in the queue'
              description: 'Worker action is taking too long in the queue.'
              runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#workeractiontimeinqueuetoolong-given-worker-action-is-hanging-in-queue-for-too-long

            - alertName: 'SendPurchaseOrderTaskFailed'
              expression: 'increase(worker_action_failed_task_total{type="SEND_ORDER", container=~"oms-purchase-order-synchronizer-app"}[5m]) > 0'
              evaluationDuration: 1m
              slackChannel: 'squad-purchase-order-lifecycle-alerts-staging'
              severity: 'P4'
              summary: 'A task to Bulk Send a Purchase Order to Tapioca has FAILED'
              description: 'A task to Bulk Send a Purchase Order to Tapioca has FAILED'
              runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#sendpurchaseordertaskfailed

            - alertName: 'CreatePurchaseOrderTaskFailed'
              expression: 'increase(worker_action_failed_task_total{type="CREATE_ORDER", container=~"oms-purchase-order-synchronizer-app"}[5m]) > 0'
              evaluationDuration: 1m
              slackChannel: 'squad-purchase-order-lifecycle-alerts-staging'
              severity: 'P4'
              summary: 'A task to Create a Purchase Order to Tapioca has FAILED'
              description: 'A task to Create a Purchase Order to Tapioca has FAILED'
              runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#createpurchaseordertaskfailed

            - alertName: 'CreateBatchPurchaseOrderTaskFailed'
              expression: 'increase(worker_action_failed_task_total{type="SYNC_BATCH_ORDER", container=~"oms-purchase-order-synchronizer-app"}[5m]) > 0'
              evaluationDuration: 1m
              slackChannel: 'squad-purchase-order-lifecycle-alerts-staging'
              severity: 'P4'
              summary: 'A task to Batch Create Purchase Orders to Tapioca has FAILED'
              description: 'A task to Batch Create Purchase Orders to Tapioca has FAILED'
              runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#createbatchpurchaseordertaskfailed

  policies:
    - properties:
        namespace: services/order-management-service
      type: secrets
      name: secrets
    - properties: {}
      name: dependencies
      type: dependencies
