apiVersion: core.oam.dev/v1alpha1
kind: Policy
type: override
metadata:
  annotations:
    notifications.argoproj.io/subscribe.on-health-degraded.slack: squad-purchase-order-lifecycle-alerts-live
    notifications.argoproj.io/subscribe.on-sync-failed.slack: squad-purchase-order-lifecycle-alerts-live
  appLabels:
    slack: squad-purchase-order-lifecycle-alerts-live
properties:
  components:
    - name: app
      type: hf-webservice
      properties:
        cpu: 2.0
      traits:
        - type: intranet-ingress
          properties: { }
        - type: secrets
          properties:
            secrets:
              - name: secrets
        - type: dependencies
          properties:
            meshInternals:
              - name: tapioca-http
                namespace: scm
                partition: legacy
            meshExternals:
              - host: order-management-service-db.live.hellofresh.io
                port: 5432
        - type: alerting-rules
          properties:
            rules:
              - alertName: 'PurchaseOrderSynchronizerFrequentPodRestarts'
                expression: 'sum(increase(kube_pod_container_status_restarts_total{container=~"oms-purchase-order-synchronizer-app"}[10m])) by (pod) > 3'
                evaluationDuration: 2m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: '{{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container }}) is restarting {{ printf "%.2f" $value }} times / 10 minutes'
                description: 'Purchase Order Synchronizer pod is frequently restarting.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#modulenamefrequentpodrestarts--module_name--is-restarting-n-times--10-minutes

              - alertName: 'WorkerActionTimeInQueueTooLong'
                expression: 'max by (type, status) (worker_action_time_in_queue_max{container="oms-purchase-order-synchronizer-app"}) > 900000'
                evaluationDuration: 1m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: '[Order Management] {{ $labels.type }} action with status={{ $labels.status }} is more than 15 minutes in the queue'
                description: 'Worker action is taking too long in the queue.'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#workeractiontimeinqueuetoolong-given-worker-action-is-hanging-in-queue-for-too-long

              - alertName: 'SendPurchaseOrderTaskFailed'
                expression: 'increase(worker_action_failed_task_total{type="SEND_ORDER", container=~"oms-purchase-order-synchronizer-app"}[5m]) > 0'
                evaluationDuration: 1m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'A task to Bulk Send a Purchase Order to Tapioca has FAILED'
                description: 'A task to Bulk Send a Purchase Order to Tapioca has FAILED'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#sendpurchaseordertaskfailed

              - alertName: 'CreatePurchaseOrderTaskFailed'
                expression: 'increase(worker_action_failed_task_total{type="CREATE_ORDER", container=~"oms-purchase-order-synchronizer-app"}[5m]) > 0'
                evaluationDuration: 1m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'A task to Create a Purchase Order to Tapioca has FAILED'
                description: 'A task to Create a Purchase Order to Tapioca has FAILED'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#createpurchaseordertaskfailed

              - alertName: 'CreateBatchPurchaseOrderTaskFailed'
                expression: 'increase(worker_action_failed_task_total{type="SYNC_BATCH_ORDER", container=~"oms-purchase-order-synchronizer-app"}[5m]) > 0'
                evaluationDuration: 1m
                slackChannel: 'squad-purchase-order-lifecycle-alerts-live'
                severity: 'P4'
                summary: 'A task to Batch Create Purchase Orders to Tapioca has FAILED'
                description: 'A task to Batch Create Purchase Orders to Tapioca has FAILED'
                runbook: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/scm/ordering/order-management-service/order-management-service.md#createbatchpurchaseordertaskfailed
