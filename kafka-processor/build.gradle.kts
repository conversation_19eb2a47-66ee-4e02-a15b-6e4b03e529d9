import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.spring.dependency.management)
    id("com.github.davidmc24.gradle.plugin.avro") version "1.9.1"
    id("jacoco")
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.kotlin.jpa)
    alias(libs.plugins.kotlin.jvm)
    id("com.google.cloud.tools.jib")
}

group = "$group.kafka-processor"

extra["springCloudVersion"] = libs.versions.springCloud.get()
extra["testcontainersVersion"] = libs.versions.testcontainers.get()

dependencyManagement {
    imports {
        mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
        mavenBom("org.testcontainers:testcontainers-bom:${property("testcontainersVersion")}")
    }
}

dependencies {
    implementation(project(":model"))
    // spring
    implementation("org.springframework.boot:spring-boot-properties-migrator")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.cloud:spring-cloud-stream")
    implementation("org.springframework.cloud:spring-cloud-stream-binder-kafka")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.security:spring-security-oauth2-client")
    implementation("org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j")

    // avro
    implementation(libs.avro)
    implementation(libs.kafka.streams.avro.serde)

    // Protobuf and avro (pulling the generated AVRO files).
    implementation(libs.hf.schema.registry)
    implementation("com.google.api.grpc", "proto-google-common-protos").version {
        strictly(libs.versions.proto.google.common.get())
    }

    // tracing
    implementation("io.micrometer:micrometer-tracing-bridge-otel")
    implementation("io.micrometer:micrometer-registry-prometheus")
    implementation("io.opentelemetry:opentelemetry-exporter-zipkin")
    implementation(libs.datasource.micrometer.spring.boot)

    // Logging
    implementation("org.springframework.boot:spring-boot-starter-log4j2")
    implementation("org.apache.logging.log4j:log4j-layout-template-json")

    implementation(libs.apache.commons.lang3)

    // Feature flags
    implementation(libs.statsig.serversdk)

    // database
    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("org.jetbrains.kotlin:kotlin-reflect")
    runtimeOnly("org.flywaydb:flyway-database-postgresql")

    // test
    testImplementation(kotlin("test"))
    testImplementation(project(":db-migration"))
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.cloud:spring-cloud-starter-contract-stub-runner")
    testImplementation(libs.zonky.embedded.database)

    testImplementation(project(":model"))
    testImplementation("org.springframework.kafka:spring-kafka-test")
    testImplementation("org.awaitility:awaitility")
    testImplementation(libs.mockito.kotlin)
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:kafka")

    testImplementation(libs.squareup.okhttp3.mockwebserver)
    testImplementation(libs.squareup.okhttp3.okhttp)

    testImplementation("io.projectreactor:reactor-test")

    testRuntimeOnly(libs.zonky.embedded.postgres)
    testRuntimeOnly("org.flywaydb:flyway-core")
}

configurations {
    implementation {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
    }
    testImplementation {
        exclude(group = "ch.qos.logback", module = "logback-classic")
    }
}

avro {
    isCreateSetters.set(true)
    fieldVisibility.set("PUBLIC")
}

sourceSets.getByName("main").resources {
    srcDir("../logging/resources")
}

tasks {
    compileKotlin {
        dependsOn("generateAvroJava")
        compilerOptions {
            jvmTarget = JvmTarget.JVM_21
        }
    }

    test {
        useJUnitPlatform {
            excludeTags("integration")
        }
    }
    test.configure {
        systemProperty("spring.profiles.active", "test")
    }
}

task<Test>("integrationTest") {
    description = "Runs integration tests."
    group = "verification"
    shouldRunAfter(tasks.test)
    useJUnitPlatform {
        includeTags("integration")
    }
}

jib {
    from {
        image = project.property("jib.from.image").toString()
    }
    to {
        image = "kafka-processor:latest"
    }
    container {
        project.findProperty("jib.container.jvmFlags")?.toString()?.split(' ')?.let {
            jvmFlags = it
        }
    }
}
