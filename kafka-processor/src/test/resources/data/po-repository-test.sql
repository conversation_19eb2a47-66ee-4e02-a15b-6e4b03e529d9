insert into purchase_order (po_number, id, version, type, year_week, user_id, user_email, status, send_time, supplier_id, supplier_code, dc_code,
                            shipping_method, location_name, street_address, city, region, postal_code, country_code, expected_start_time, expected_end_time, emergency_reason_uuid, created_at, updated_at, total_price_currency, total_price_amount)
values ('123NJ5000', 'a3610310-0bd3-456c-8078-d60e3b7cece2', 1, 'PREORDER', null, null, null, 'APPROVED', null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null);
insert into purchase_order (po_number, id, version, type, year_week, user_id, user_email, status, send_time, supplier_id, supplier_code, dc_code,
                            shipping_method, location_name, street_address, city, region, postal_code, country_code, expected_start_time, expected_end_time, emergency_reason_uuid, created_at, updated_at, total_price_currency, total_price_amount)
values ('123NJ5000', 'a3610310-0bd3-456c-8078-d60e3b7cece3', 3, 'PREORDER', null, null, null, 'APPROVED', null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null);
insert into purchase_order (po_number, id, version, type, year_week, user_id, user_email, status, send_time, supplier_id, supplier_code, dc_code,
                            shipping_method, location_name, street_address, city, region, postal_code, country_code, expected_start_time, expected_end_time, emergency_reason_uuid, created_at, updated_at, total_price_currency, total_price_amount)
values ('123NJ5000', 'a3610310-0bd3-456c-8078-d60e3b7cece4', 2, 'PREORDER', null, null, null, 'APPROVED', null, null, null, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null);
