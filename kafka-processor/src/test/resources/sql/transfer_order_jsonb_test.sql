INSERT INTO transfer_order (
    id,
    source_dc_code,
    destination_dc_code,
    creator_email,
    reason_text,
    status,
    year_week,
    transfer_order_number,
    source_dc_name,
    market_code,
    version,
    total_price_currency_code,
    total_price_units,
    total_price_nanos,
    shipping,
    create_time,
    update_time,
    pickup_start_time,
    pickup_end_time,
    delivery_start_time,
    delivery_end_time,
    sent_time,
    created_at,
    updated_at
) VALUES (
    '11111111-1111-1111-1111-111111111111',
    'AV',
    'AE',
    '<EMAIL>',
    'Test transfer',
    'STATE_ORDERED',
    '2025-01',
    'TEST001',
    'Test DC',
    'US',
    1,
    'USD',
    100,
    0,
    '{
        "address": {
            "regionCode": "US",
            "postalCode": "85043",
            "administrativeArea": "AZ",
            "locality": "Phoenix",
            "addressLines": ["1850", "S 71st Avenue"],
            "organization": "United States - AZ"
        },
        "method": "Freight on board"
    }'::jsonb,
    '2025-01-01 10:00:00',
    '2025-01-01 10:00:00',
    '2025-01-02 10:00:00',
    '2025-01-02 11:00:00',
    '2025-01-03 12:00:00',
    '2025-01-03 14:00:00',
    '2025-01-01 09:00:00',
    '2025-01-01 08:00:00',
    '2025-01-01 08:00:00'
);
