package com.hellofresh.oms.purchaseOrderShipment

import com.hellofresh.oms.purchaseOrderShipment.Fixture.getProtoPurchaseOrderShipment
import com.hellofresh.oms.purchaseOrderShipment.consumer.toPurchaseOrderShipment
import com.hellofresh.oms.purchaseOrderShipment.repository.PurchaseOrderShipmentRepository
import com.hellofresh.oms.purchaseOrderShipment.service.PurchaseOrderShipmentService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class PurchaseOrderShipmentServiceTest {
    @Mock
    lateinit var purchaseOrderShipmentRepositoryMock: PurchaseOrderShipmentRepository

    @InjectMocks
    lateinit var service: PurchaseOrderShipmentService

    @Test
    fun `should save new purchase order shipment`() {
        val protoShipment = getProtoPurchaseOrderShipment()
        val incomingShipmentEntity = protoShipment.toPurchaseOrderShipment()
        whenever(
            purchaseOrderShipmentRepositoryMock.save(incomingShipmentEntity)
        ).thenReturn(incomingShipmentEntity)

        service.process(incomingShipmentEntity)

        verify(purchaseOrderShipmentRepositoryMock).save(incomingShipmentEntity)
    }
}
