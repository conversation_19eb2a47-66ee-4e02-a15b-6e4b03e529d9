package com.hellofresh.oms.purchaseOrderShipment

import com.google.type.DateTime
import com.google.type.PostalAddress
import com.hellofresh.proto.service.ordering.purchaseOrder.v1beta1.PurchaseOrderNumber
import com.hellofresh.proto.service.ordering.purchaseOrder.v1beta1.PurchaseOrderRevision
import com.hellofresh.proto.stream.distributionCenter.thirdParty.blujay.inbound.purchaseOrderShipment.v1beta1.PurchaseOrderShipment as ProtoPurchaseOrderShipment

object Fixture {

    fun getProtoPurchaseOrderShipment(
        poNumber: String = "2115NJ309625"
    ): ProtoPurchaseOrderShipment = ProtoPurchaseOrderShipment
        .newBuilder()
        .setPurchaseOrderRevision(
            PurchaseOrderRevision.newBuilder()
                .setNumber(
                    PurchaseOrderNumber.newBuilder()
                        .setFormatted(poNumber)
                        .build(),
                ),
        )
        .setLoadNumber("LOAD123")
        .setPalletCount(5)
        .setCarrierName("Test Carrier")
        .setOriginLocation(
            PostalAddress.newBuilder()
                .setRegionCode("GB")
                .setPostalCode("CW9 7WA")
                .setAdministrativeArea("Cheshire")
                .setLocality("Northwich")
                .setOrganization("Test Organization")
                .build(),
        )
        .setAppointmentTime(
            DateTime
                .newBuilder()
                .setYear(2024)
                .setMonth(4)
                .setDay(12)
                .setHours(10)
                .setMinutes(0)
                .setSeconds(0)
                .build(),
        )
        .setExecutionEvent("SHIPMENT_CREATED")
        .build()
}
