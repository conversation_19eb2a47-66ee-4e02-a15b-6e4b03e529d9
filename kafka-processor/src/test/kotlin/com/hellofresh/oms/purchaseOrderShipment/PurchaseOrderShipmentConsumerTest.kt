package com.hellofresh.oms.purchaseOrderShipment

import com.hellofresh.oms.purchaseOrderShipment.Fixture.getProtoPurchaseOrderShipment
import com.hellofresh.oms.purchaseOrderShipment.consumer.PurchaseOrderShipmentConsumer
import com.hellofresh.oms.purchaseOrderShipment.service.PurchaseOrderShipmentService
import com.hellofresh.proto.stream.distributionCenter.thirdParty.blujay.inbound.purchaseOrderShipment.v1beta1.PurchaseOrderShipment
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class PurchaseOrderShipmentConsumerTest {
    @Mock
    private lateinit var purchaseOrderShipmentServiceMock: PurchaseOrderShipmentService

    @Test
    fun `should call PurchaseOrderShipment service method when consuming message`() {
        val subject = PurchaseOrderShipmentConsumer()

        subject.processPurchaseOrderShipment(purchaseOrderShipmentServiceMock).accept(
            object : Message<PurchaseOrderShipment> {
                override fun getPayload(): PurchaseOrderShipment = getProtoPurchaseOrderShipment()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        verify(purchaseOrderShipmentServiceMock).process(any())
    }
}
