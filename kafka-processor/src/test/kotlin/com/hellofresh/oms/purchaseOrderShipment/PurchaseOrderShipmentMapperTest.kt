package com.hellofresh.oms.purchaseOrderShipment

import com.google.type.DateTime
import com.hellofresh.oms.purchaseOrderShipment.Fixture.getProtoPurchaseOrderShipment
import com.hellofresh.oms.purchaseOrderShipment.consumer.toLocalDateTime
import com.hellofresh.oms.purchaseOrderShipment.consumer.toPurchaseOrderShipment
import java.time.LocalDateTime
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PurchaseOrderShipmentMapperTest {

    @Test
    fun `should map ProtoPurchaseOrderShipment to PurchaseOrderShipment entity`() {
        val poNumber = "2215NJ300005"
        val protoShipment = getProtoPurchaseOrderShipment(poNumber = poNumber)

        val entity = protoShipment.toPurchaseOrderShipment()

        assertEquals(poNumber, entity.poNumber)
        assertEquals("LOAD123", entity.loadNumber)
        assertEquals(5, entity.palletCount)
        assertEquals("Test Carrier", entity.carrierName)
        assertEquals("GB", entity.regionCode)
        assertEquals("CW9 7WA", entity.postalCode)
        assertEquals("Cheshire", entity.administrativeArea)
        assertEquals("Northwich", entity.locality)
        assertEquals("Test Organization", entity.organization)
        assertEquals(LocalDateTime.of(2024, 4, 12, 10, 0, 0), entity.appointmentTime)
        assertEquals("SHIPMENT_CREATED", entity.executionEvent)
    }

    @Test
    fun `should map DateTime to LocalDateTime`() {
        val dateTime = DateTime.newBuilder()
            .setYear(2024)
            .setMonth(4)
            .setDay(12)
            .setHours(10)
            .setMinutes(30)
            .setSeconds(45)
            .setNanos(123456789)
            .build()

        val localDateTime = dateTime.toLocalDateTime()

        assertEquals(2024, localDateTime?.year)
        assertEquals(4, localDateTime?.monthValue)
        assertEquals(12, localDateTime?.dayOfMonth)
        assertEquals(10, localDateTime?.hour)
        assertEquals(30, localDateTime?.minute)
        assertEquals(45, localDateTime?.second)
        assertEquals(123456789, localDateTime?.nano)
    }
}
