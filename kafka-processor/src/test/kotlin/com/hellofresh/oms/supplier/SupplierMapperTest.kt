package com.hellofresh.oms.supplier

import com.hellofresh.oms.supplier.consumer.SupplierConstants.US_DISTRIBUTION_CENTERS
import com.hellofresh.oms.supplier.consumer.mapToSupplier
import java.util.UUID.randomUUID
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class SupplierMapperTest {

    @Test
    fun `should map supplier avro message supplier entity`() {
        // given
        val supplierId = randomUUID()
        val avroPayload = getAvroSupplier(supplierId = supplierId)

        // when
        val supplierEntity = avroPayload.mapToSupplier(supplierId)

        // then
        assertEquals(avroPayload.id, supplierEntity.id)
        assertEquals(avroPayload.code, supplierEntity.code)
        assertEquals(avroPayload.name, supplierEntity.name)
        assertEquals(avroPayload.status.name.uppercase(), supplierEntity.status.toString())
        assertEquals(avroPayload.market, supplierEntity.market)
        assertEquals(avroPayload.currency, supplierEntity.currency)
        assertEquals(avroPayload.type, supplierEntity.type)
        assertEquals(avroPayload.createdAt, supplierEntity.createdAt.toString())
        assertEquals(avroPayload.updatedAt, supplierEntity.updatedAt.toString())
        assertEquals(avroPayload.id, supplierEntity.contacts.first().supplierId)
        assertEquals(
            getAvroSupplier(supplierId = supplierId).contactPersons.first().emails.first(),
            supplierEntity.contacts.first().email,
        )
    }

    @Test
    fun `should map US distribution centers when market is US`() {
        // given
        val supplierId = randomUUID()
        val avroPayload = getAvroSupplier(supplierId = supplierId, distributionCenters = listOf("NJ"), market = "us")

        // when
        val supplierEntity = avroPayload.mapToSupplier(supplierId)

        // then
        assertArrayEquals(US_DISTRIBUTION_CENTERS.toTypedArray(), supplierEntity.dcCodes.toTypedArray())
    }

    @Test
    fun `should not map US distribution centers when market is not US`() {
        // given
        val supplierId = randomUUID()
        val avroPayload = getAvroSupplier(supplierId = supplierId, distributionCenters = listOf("VE"), market = "dach")

        // when
        val supplierEntity = avroPayload.mapToSupplier(supplierId)

        // then
        assertArrayEquals(avroPayload.distributionCenters.toTypedArray(), supplierEntity.dcCodes.toTypedArray())
    }

    @Test
    fun `should store supplier contact details only for order related contact person`() {
        // when
        val supplierId = randomUUID()
        val supplierEntity = getAvroSupplier(
            listOf(
                getContactPerson("emergency-contact" to "<EMAIL>"),
                getContactPerson("purchase-order-contact" to "<EMAIL>"),
            ),
            supplierId = supplierId,
        ).mapToSupplier(supplierId)

        // then
        assertEquals("<EMAIL>", supplierEntity.contacts.first().email)
        assertEquals(1, supplierEntity.contacts.size)
    }
}
