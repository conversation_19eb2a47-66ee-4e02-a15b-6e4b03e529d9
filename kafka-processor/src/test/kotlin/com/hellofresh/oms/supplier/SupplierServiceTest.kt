package com.hellofresh.oms.supplier

import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.shippingMethod.repository.ShipMethodRepository
import com.hellofresh.oms.supplier.repository.SupplierRepository
import com.hellofresh.oms.supplier.service.SupplierService
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.any
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.whenever

class SupplierServiceTest {
    private val supplierRepositoryMock: SupplierRepository = mock(SupplierRepository::class.java)
    private val shipMethodRepositoryMock: ShipMethodRepository = mock(ShipMethodRepository::class.java)
    val subject: SupplierService = SupplierService(supplierRepositoryMock, shipMethodRepositoryMock)

    @Test
    fun `should use stored ship method when save supplier`() {
        // given
        val shipMethod = getShipMethod()
        val supplier = getSupplierEntity()
        whenever(shipMethodRepositoryMock.findAllBySupplierId(supplier.id))
            .thenReturn(listOf(shipMethod))
        whenever(supplierRepositoryMock.save(any()))
            .thenReturn(supplier)

        // when
        subject.upsertSupplier(supplier)

        // then
        val argumentCaptor = argumentCaptor<SupplierExtended>()
        verify(supplierRepositoryMock).save(argumentCaptor.capture())
        assertEquals(listOf(shipMethod), argumentCaptor.firstValue.shipMethods)
    }

    @Test
    fun `should save supplier with empty ship method list if no ship method found`() {
        // given
        val supplier = getSupplierEntity()
        whenever(shipMethodRepositoryMock.findAllBySupplierId(supplier.id))
            .thenReturn(emptyList())
        whenever(supplierRepositoryMock.save(any()))
            .thenReturn(supplier)

        // when
        subject.upsertSupplier(supplier)

        // then
        val argumentCaptor = argumentCaptor<SupplierExtended>()
        verify(supplierRepositoryMock).save(argumentCaptor.capture())
        assertEquals(emptyList(), argumentCaptor.firstValue.shipMethods)
    }
}
