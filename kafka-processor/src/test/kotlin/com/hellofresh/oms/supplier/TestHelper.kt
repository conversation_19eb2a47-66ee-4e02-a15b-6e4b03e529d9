package com.hellofresh.oms.supplier

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.SupplierContact
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.model.supplier.SupplierStatus.ACTIVE
import com.hellofresh.planning.remps.facility.contact_person as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>erson
import com.hellofresh.planning.remps.facility.event_types as EventTypes
import com.hellofresh.planning.remps.facility.facility as AvroSupplier
import com.hellofresh.planning.remps.facility.statuses.Active as Active
import com.hellofresh.planning.remps.facility.supplier_address as AvroSupplierAddress
import java.time.LocalDateTime
import java.util.UUID
import java.util.UUID.randomUUID

fun getSupplierEntity(
    supplierId: UUID = randomUUID(),
    name: String = "Supplier Name",
    contacts: Set<SupplierContact> = setOf(
        SupplierContact(
            supplierId = supplierId,
            email = "<EMAIL>",
        ),
    ),
) = SupplierExtended(
    id = supplierId,
    parentId = randomUUID(),
    market = "dach",
    code = 1234,
    name = name,
    status = ACTIVE,
    currency = "EUR",
    type = "Manufacturer",
    createdAt = LocalDateTime.now(),
    updatedAt = LocalDateTime.now(),
    dcCodes = listOf("NJ"),
    supplierAddress = SupplierAddress(
        "Berlin",
        "Germany",
        state = "Berlin",
        address = "HelloFresh Str. 1",
        number = "1234",
        postCode = "1234",
    ),
    contacts = contacts,
    shipMethods = emptyList(),
)

fun getAvroSupplier(
    contactPersons: List<AvroContactPerson> = listOf(
        AvroContactPerson.newBuilder()
            .setName("ContName")
            .setEmails(listOf("<EMAIL>"))
            .setRoles(listOf("purchase-order-contact"))
            .build(),
    ),
    supplierId: UUID,
    supplierName: String = "Supplier Name",
    market: String = "us",
    distributionCenters: List<String> = listOf("NJ"),
): AvroSupplier = AvroSupplier.newBuilder()
    .setId(supplierId)
    .setParentId(randomUUID())
    .setCode(1000)
    .setName(supplierName)
    .setStatus(Active)
    .setMarket(market)
    .setCurrency("USD")
    .setType("Manufacturer")
    .setContactPersons(contactPersons)
    .setDistributionCenters(distributionCenters)
    .setAddress(
        AvroSupplierAddress.newBuilder()
            .setAddress("address")
            .setCity("city")
            .setCountry("country")
            .setNumber("1234")
            .setPostCode("1239")
            .build(),
    )
    .setEventType(EventTypes.supplier_created)
    .setLegalName("legal name")
    .setCreatedAt(LocalDateTime.now().toString())
    .setUpdatedAt(LocalDateTime.now().toString())
    .build()

fun getContactPerson(roleEmail: Pair<String, String>): AvroContactPerson = AvroContactPerson.newBuilder()
    .setName("ContName")
    .setEmails(listOf(roleEmail.second))
    .setRoles(listOf(roleEmail.first))
    .build()

fun getShipMethod() = ShipMethod(
    uuid = randomUUID(),
    dcCode = "NJ",
    supplierId = randomUUID(),
    market = "us",
    method = VENDOR,
)
