package com.hellofresh.oms.supplier.integration

import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.model.supplier.SupplierStatus.ACTIVE
import com.hellofresh.oms.shippingMethod.repository.ShipMethodRepository
import com.hellofresh.oms.supplier.getAvroSupplier
import com.hellofresh.oms.supplier.repository.SupplierRepository
import com.hellofresh.planning.remps.facility.facility as AvroSupplier
import java.time.LocalDateTime
import java.util.UUID
import org.apache.avro.specific.SpecificRecordBase
import org.apache.kafka.clients.producer.ProducerRecord
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

class SupplierTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var supplierRepository: SupplierRepository

    @Autowired
    private lateinit var shipMethodRepository: ShipMethodRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, SpecificRecordBase>

    @Value("\${topics.supplier}")
    private lateinit var topics: String

    @BeforeEach
    fun setUp() {
        supplierRepository.deleteAll()
        shipMethodRepository.deleteAll()
    }

    @Test
    fun `should consume supplier`() {
        // given
        val supplierId = UUID.randomUUID()
        val supplier = getAvroSupplier(supplierId = supplierId)

        // when
        publishMessage(supplier, supplierId)

        // then
        assertAwaiting {
            val persistedSuppliers = supplierRepository.findAll().map { it.id }
            assertThat(persistedSuppliers, equalTo(listOf(supplierId)))
        }
    }

    @Test
    fun `should not delete existing ship methods when updating supplier`() {
        // given
        val givenSupplierId = UUID.randomUUID()
        val givenSupplierName = "Field Fresh Foods, Inc"
        val givenShipMethodId = UUID.randomUUID()
        supplierRepository.save(
            getSupplierEntity(
                givenSupplierId,
                givenSupplierName,
                ShipMethod(
                    uuid = givenShipMethodId,
                    dcCode = "FE",
                    supplierId = givenSupplierId,
                    market = "us",
                    method = VENDOR,
                )
            )
        )
        val newSupplierName = "Different name"
        val givenSupplierProto = getAvroSupplier(supplierId = givenSupplierId, supplierName = newSupplierName)

        // when
        publishMessage(givenSupplierProto, givenSupplierId)

        // then
        assertAwaiting {
            val persistedSuppliers = supplierRepository.findAll()
            assertThat(persistedSuppliers.map { it.name }, equalTo(listOf(newSupplierName)))
            assertThat(shipMethodRepository.findAll().map { it.uuid }, equalTo(listOf(givenShipMethodId)))
        }
    }

    private fun getSupplierEntity(
        givenSupplierId: UUID,
        givenSupplierName: String,
        shipMethod: ShipMethod
    ) = SupplierExtended(
        id = givenSupplierId,
        parentId = UUID.randomUUID(),
        market = "us",
        code = 10595,
        name = givenSupplierName,
        status = ACTIVE,
        currency = "USD",
        type = "Manufacturer",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        supplierAddress = SupplierAddress(
            city = "Gardena",
            country = "US",
            state = "CA",
            address = "South San Pedro Street",
            number = "14805",
            postCode = "90248",
        ),
        dcCodes = emptyList(),
        contacts = emptySet(),
        shipMethods = listOf(
            shipMethod,
        ),
    )

    private fun publishMessage(message: AvroSupplier, messageUuid: UUID? = null) {
        val record = ProducerRecord<String, SpecificRecordBase>(topics.split(",").first(), message)
        record.headers().add("UUID", messageUuid.toString().toByteArray())
        kafkaTemplate.send(record)
        kafkaTemplate.flush()
    }
}
