package com.hellofresh.oms.supplier

import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.supplier.consumer.SupplierConsumer
import com.hellofresh.oms.supplier.service.SupplierService
import com.hellofresh.planning.remps.facility.facility as AvroSupplier
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.DynamicTest.dynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import org.mockito.Mockito.mock
import org.mockito.Mockito.reset
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

class SupplierConsumerTest {
    private val supplierServiceMock: SupplierService = mock(SupplierService::class.java)

    @Test
    fun `should call supplier service method when consuming message`() {
        // given
        val subject = SupplierConsumer(supplierServiceMock)

        // when
        subject.processSupplier().accept(
            object : Message<AvroSupplier> {
                override fun getPayload() = getAvroSupplier(supplierId = UUID.randomUUID())
                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, ByteArray>("UUID" to UUID.randomUUID().toString().toByteArray()),
                )
            },
        )

        // then
        verify(supplierServiceMock, times(1)).upsertSupplier(any())
    }

    @TestFactory
    fun `should map supplier names in CA market`() = listOf(
        Pair("Test without suffix", "Test without suffix"),
        Pair("Test Suppler OA", "Test Suppler ON"),
        Pair("Test Supplier AA", "Test Supplier AB"),
        Pair("Test Supplier BA", "Test Supplier BC"),
        Pair("OA AA BA Test Supplier", "OA AA BA Test Supplier"),
        Pair("OA AA BA Test Supplier BA", "OA AA BA Test Supplier BC"),
    ).map { (givenName, modifiedName) ->
        dynamicTest("""should map "$givenName" to "$modifiedName" in CA market""") {
            reset(supplierServiceMock)

            // given
            val subject = SupplierConsumer(supplierServiceMock)

            // when
            subject.processSupplier().accept(
                object : Message<AvroSupplier> {
                    override fun getPayload() = getAvroSupplier(
                        supplierId = UUID.randomUUID(),
                        market = "ca",
                        supplierName = givenName
                    )
                    override fun getHeaders(): MessageHeaders = MessageHeaders(
                        mapOf<String, ByteArray>("UUID" to UUID.randomUUID().toString().toByteArray()),
                    )
                },
            )

            // then
            val supplier = argumentCaptor<SupplierExtended>()
            verify(supplierServiceMock, times(1)).upsertSupplier(supplier.capture())

            assertEquals(modifiedName, supplier.lastValue.name)
        }
    }
}
