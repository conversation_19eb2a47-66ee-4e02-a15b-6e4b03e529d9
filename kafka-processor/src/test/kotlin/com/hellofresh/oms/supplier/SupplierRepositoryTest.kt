package com.hellofresh.oms.supplier

import com.hellofresh.oms.model.SupplierContact
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.supplier.repository.SupplierRepository
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest

@DataJpaTest
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
class SupplierRepositoryTest(
    @Autowired val subject: SupplierRepository
) {

    @Test
    fun `should persist supplier`() {
        // given
        val supplierId = UUID.randomUUID()
        val expectedSupplier = getSupplierEntity(supplierId)

        // when
        val actualSupplier: SupplierExtended = subject.save(expectedSupplier)

        // then
        assertEquals(expectedSupplier, actualSupplier)
    }

    @Test
    fun `should upsert supplier`() {
        // given
        val supplierId = UUID.randomUUID()
        val supplier = getSupplierEntity(
            supplierId = supplierId,
            contacts = setOf(
                SupplierContact(
                    supplierId = supplierId,
                    email = "<EMAIL>",
                ),
                SupplierContact(
                    supplierId = supplierId,
                    email = "<EMAIL>",
                ),
            ),
        )

        val supplierUpdate = supplier.copy(
            contacts = setOf(
                SupplierContact(
                    supplierId = supplierId,
                    email = "updated",
                ),
            ),
        )

        // when
        subject.save(supplier)
        subject.save(supplierUpdate)

        // then
        val suppliers = subject.findAll()
        assertEquals(1, suppliers.size)
        assertEquals(1, suppliers.first().contacts.size)
        assertEquals("updated", suppliers.first().contacts.first().email)
    }
}
