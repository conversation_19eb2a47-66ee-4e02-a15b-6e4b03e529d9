package com.hellofresh.oms.supplierSkuPackaging.integtation

import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.supplierSkuPackaging.Fixture.getAvroSupplierSkuPackaging
import com.hellofresh.oms.supplierSkuPackaging.repository.SupplierSkuPackagingRepository
import com.hellofresh.planning.suppliersku.packaging.packaging
import java.util.UUID
import java.util.UUID.randomUUID
import org.apache.avro.specific.SpecificRecordBase
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.empty
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

class SupplierSkuPackagingTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var repository: SupplierSkuPackagingRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, SpecificRecordBase>

    @Value("\${topics.supplier-sku-packaging}")
    private lateinit var topics: String

    @BeforeEach
    fun setUp() {
        repository.deleteAll()
    }

    @Test
    fun `should consume Packaging and save a new supplier sku Packaging`() {
        // given
        val supplierSkuId = randomUUID()
        val avroPackaging = getAvroSupplierSkuPackaging(supplierSkuId = supplierSkuId)

        // when
        publishMessage(message = avroPackaging, messageUuid = supplierSkuId)

        // then
        assertAwaiting {
            val skus = repository.findAll().map { it.supplierSkuId }
            assertThat(skus, Matchers.equalTo(listOf(supplierSkuId)))
        }
    }

    @Test
    fun `should not save events when kafka_receivedMessageKey is null`() {
        // given
        val supplierSkuId = randomUUID()
        val avroPackaging = getAvroSupplierSkuPackaging(supplierSkuId = supplierSkuId)

        // when
        publishMessage(message = avroPackaging, messageUuid = null)

        // then
        // wait for a given time so that we are sure that no message is consumed
        Thread.sleep(1000)
        assertThat(repository.findAll(), empty())
    }

    private fun publishMessage(message: packaging, messageUuid: UUID? = null) {
        kafkaTemplate.send(topics.split(",").first(), messageUuid.toString(), message)
        kafkaTemplate.flush()
    }
}
