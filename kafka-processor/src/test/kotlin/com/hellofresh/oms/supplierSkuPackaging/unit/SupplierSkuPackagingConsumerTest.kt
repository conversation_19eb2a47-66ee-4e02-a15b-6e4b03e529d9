package com.hellofresh.oms.supplierSkuPackaging.unit

import com.hellofresh.oms.supplierSkuPackaging.Fixture.getAvroSupplierSkuPackaging
import com.hellofresh.oms.supplierSkuPackaging.consumer.SupplierSkuPackagingConsumer
import com.hellofresh.oms.supplierSkuPackaging.service.SupplierSkuPackagingService
import com.hellofresh.planning.suppliersku.packaging.packaging as AvroSupplierSkuPackaging
import java.util.UUID
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

class SupplierSkuPackagingConsumerTest {
    private val supplierSkuPackagingService = mock(SupplierSkuPackagingService::class.java)

    @Test
    fun `should call supplierSkuPackaging service method when consuming message`() {
        // given
        val subject = SupplierSkuPackagingConsumer(supplierSkuPackagingService)

        // when
        subject.processSupplierSkuPackaging().accept(
            object : Message<AvroSupplierSkuPackaging> {
                override fun getPayload() = getAvroSupplierSkuPackaging()
                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        Mockito.verify(supplierSkuPackagingService, Mockito.times(1)).upsertSupplierSkuPackaging(any())
    }
}
