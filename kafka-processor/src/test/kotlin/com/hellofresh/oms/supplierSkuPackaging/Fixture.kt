package com.hellofresh.oms.supplierSkuPackaging

import com.hellofresh.oms.model.SupplierSkuPackaging
import com.hellofresh.planning.suppliersku.packaging.event_types
import com.hellofresh.planning.suppliersku.packaging.packaging as AvroSupplierSkuPackaging
import java.time.LocalDateTime
import java.util.UUID
import java.util.UUID.randomUUID
import kotlin.random.Random

object Fixture {
    fun getAvroSupplierSkuPackaging(
        supplierSkuId: UUID = randomUUID(),
        casesPerPallet: Int = Random.nextInt()
    ): AvroSupplierSkuPackaging =
        AvroSupplierSkuPackaging.newBuilder()
            .setEventType(event_types.entity_created)
            .setSupplierSkuId(supplierSkuId)
            .setCasesPerPallet(casesPerPallet)
            .setCreatedAt(LocalDateTime.now().toString())
            .setUpdatedAt(LocalDateTime.now().toString())
            .setMarket("market")
            .setDisposalCategory("disposal category")
            .setLabelType("label type")
            .setWeight(10.4)
            .setWeightToleranceOver(10.4)
            .setWeightToleranceUnder(10.4)
            .setDepth(10.4)
            .setLength(10.3)
            .setWidth(10.3)
            .setVolume(10.3)
            .setUnitsPerPallet(10)
            .setPalletType("pallet type")
            .setSupplierPackaging("supplier packaging")
            .setCaseCount(10)
            .setExtras("extras")
            .setShape("shape")
            .setShippingMode("shipping mode")
            .setUnitsPerSecondaryPackage(10)
            .setShippingStrategy("shipping strategy")
            .setCasesPerLayer(10)
            .setLayersPerPallet(10)
            .setPalletHeight(10.5)
            .build()

    fun getSupplierSkuPackagingEntity(
        supplierSkuId: UUID = randomUUID(),
        casesPerPallet: Int = Random.nextInt()
    ): SupplierSkuPackaging =
        SupplierSkuPackaging(
            supplierSkuId = supplierSkuId,
            casesPerPallet = casesPerPallet,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
}
