package com.hellofresh.oms.supplierSkuPackaging.unit

import com.hellofresh.oms.supplierSkuPackaging.Fixture.getSupplierSkuPackagingEntity
import com.hellofresh.oms.supplierSkuPackaging.repository.SupplierSkuPackagingRepository
import com.hellofresh.oms.supplierSkuPackaging.service.SupplierSkuPackagingService
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class SupplierSkuPackagingServiceTest {

    @Mock
    private lateinit var supplierSkuPackagingRepositoryMock: SupplierSkuPackagingRepository

    @InjectMocks
    private lateinit var supplierSkuPackagingService: SupplierSkuPackagingService

    @Test
    fun `should save new supplierSkuPackaging when it doesn't exist in the database`() {
        // given
        val incomingEntity = getSupplierSkuPackagingEntity()
        whenever(supplierSkuPackagingRepositoryMock.save(incomingEntity))
            .thenReturn(incomingEntity)

        // when
        val actualEntity = supplierSkuPackagingService.upsertSupplierSkuPackaging(incomingEntity)

        // then
        verify(supplierSkuPackagingRepositoryMock).save(incomingEntity)
        assertNotNull(actualEntity)
    }
}
