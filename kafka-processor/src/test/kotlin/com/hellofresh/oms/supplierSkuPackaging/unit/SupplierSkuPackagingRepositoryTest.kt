package com.hellofresh.oms.supplierSkuPackaging.unit

import com.hellofresh.oms.supplierSkuPackaging.Fixture
import com.hellofresh.oms.supplierSkuPackaging.repository.SupplierSkuPackagingRepository
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest

@DataJpaTest
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
class SupplierSkuPackagingRepositoryTest(
    @Autowired private val supplierSkuPackagingRepository: SupplierSkuPackagingRepository
) {

    @Test
    fun `should save new entity`() {
        // given
        val supplierSkuPackagingEntity = Fixture.getSupplierSkuPackagingEntity()

        // when
        supplierSkuPackagingRepository.save(supplierSkuPackagingEntity)

        // then
        assertEquals(1, supplierSkuPackagingRepository.findAll().size)
    }

    @Test
    fun `should save new entity and then update it`() {
        // given
        val supplierSkuPackagingEntity = Fixture.getSupplierSkuPackagingEntity(casesPerPallet = 1)
        val supplierSkuPackagingEntityUpdated = supplierSkuPackagingEntity.copy(casesPerPallet = 3)

        // when
        supplierSkuPackagingRepository.save(supplierSkuPackagingEntity)
        supplierSkuPackagingRepository.save(supplierSkuPackagingEntityUpdated)

        // then
        val actualResult = supplierSkuPackagingRepository.findAll()
        assertEquals(1, actualResult.size)
        assertEquals(supplierSkuPackagingEntityUpdated.casesPerPallet, actualResult.first().casesPerPallet)
    }
}
