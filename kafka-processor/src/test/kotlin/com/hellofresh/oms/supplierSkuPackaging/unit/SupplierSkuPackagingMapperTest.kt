package com.hellofresh.oms.supplierSkuPackaging.unit

import com.hellofresh.oms.supplierSkuPackaging.Fixture.getAvroSupplierSkuPackaging
import com.hellofresh.oms.supplierSkuPackaging.consumer.mapToSupplierSkuPackaging
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS
import org.junit.jupiter.api.assertAll

@TestInstance(PER_CLASS)
class SupplierSkuPackagingMapperTest {

    @Test
    fun `should map avro SupplierSkuPackaging to supplierSkuPackaging entity`() {
        // given
        val avroSupplierSkuPackaging = getAvroSupplierSkuPackaging()

        // when
        val mappedSupplierSkuPackaging = avroSupplierSkuPackaging.mapToSupplierSkuPackaging()

        // then
        assertAll(
            {
                assertEquals(
                    avroSupplierSkuPackaging.supplierSkuId,
                    mappedSupplierSkuPackaging.supplierSkuId,
                )
            },
            { assertEquals(avroSupplierSkuPackaging.casesPerPallet, mappedSupplierSkuPackaging.casesPerPallet) },
        )
    }
}
