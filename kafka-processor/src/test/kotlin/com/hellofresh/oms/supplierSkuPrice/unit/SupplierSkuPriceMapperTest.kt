package com.hellofresh.oms.supplierSkuPrice.unit

import com.hellofresh.oms.supplierSkuPrice.Fixture.getAvroSupplierSkuPrice
import com.hellofresh.oms.supplierSkuPrice.consumer.mapToSupplierSkuPrice
import java.time.format.DateTimeFormatter.ISO_DATE_TIME
import java.util.UUID
import kotlin.test.assertContains
import kotlin.test.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll

class SupplierSkuPriceMapperTest {

    @Test
    fun `should map avro SupplierSkuPrice to entity`() {
        // given
        val avroSupplierSkuPrice = getAvroSupplierSkuPrice(uuid = UUID.randomUUID())

        // when
        val resultEntity = avroSupplierSkuPrice.mapToSupplierSkuPrice()

        // then
        assertAll(
            { assertEquals(avroSupplierSkuPrice.id, resultEntity.uuid) },
            { assertEquals(avroSupplierSkuPrice.supplierSkuId, resultEntity.supplierSkuId) },
            { assertEquals(avroSupplierSkuPrice.distributionCenter, resultEntity.dcCodes) },
            { assertTrue(avroSupplierSkuPrice.startDate.contains(resultEntity.startDate.toString())) },
            { assertTrue(avroSupplierSkuPrice.endDate.contains(resultEntity.endDate.toString())) },
            { assertEquals(avroSupplierSkuPrice.enabled, resultEntity.enabled) },
            { assertEquals(avroSupplierSkuPrice.priceType, resultEntity.priceType) },
            { assertEquals(avroSupplierSkuPrice.market, resultEntity.market) },
            { assertEquals(avroSupplierSkuPrice.currency, resultEntity.currency) },
            { assertEquals(avroSupplierSkuPrice.price, resultEntity.pricePermyriad.value) },
            { assertEquals(avroSupplierSkuPrice.unitsPerCase, resultEntity.caseSize) },
            { assertEquals(avroSupplierSkuPrice.casePrice, resultEntity.casePrice!!.value) },
            {
                assertContains(
                    avroSupplierSkuPrice.createdAt,
                    resultEntity.createdAt.format(ISO_DATE_TIME),
                )
            },
            {
                assertContains(
                    avroSupplierSkuPrice.updatedAt,
                    resultEntity.updatedAt.format(ISO_DATE_TIME),
                )
            },
        )
    }

    @Test
    fun `should map avro SupplierSkuPrice unitsPerCase to null when it is 0`() {
        // given
        val avroSupplierSkuPrice = getAvroSupplierSkuPrice(unitsPerCase = 0, uuid = UUID.randomUUID())

        // when
        val resultEntity = avroSupplierSkuPrice.mapToSupplierSkuPrice()

        // then
        assertNull(resultEntity.caseSize)
    }

    @Test
    fun `should map incoming buffer permyriad`() {
        // given
        // 20.99%
        val bufferPercentSetInSpi = 2099
        val expectedValuePersisted = 2099

        val avroSupplierSkuPrice = getAvroSupplierSkuPrice(
            bufferPermyriad = bufferPercentSetInSpi,
            uuid = UUID.randomUUID()
        )

        // when
        val resultEntity = avroSupplierSkuPrice.mapToSupplierSkuPrice()

        // then
        assertEquals(expectedValuePersisted, resultEntity.bufferPermyriad.value)
    }
}
