package com.hellofresh.oms.supplierSkuPrice.unit

import com.hellofresh.oms.supplierSkuPrice.Fixture.getAvroSupplierSkuPrice
import com.hellofresh.oms.supplierSkuPrice.consumer.SupplierSkuPriceConsumer
import com.hellofresh.oms.supplierSkuPrice.service.SupplierSkuPriceService
import com.hellofresh.planning.suppliersku.pricing.pricing as AvroSupplierSkuPrice
import java.util.UUID
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

class SupplierSkuPriceConsumerTest {
    private val supplierSkuPriceService = mock(SupplierSkuPriceService::class.java)

    @Test
    fun `should call supplierSkuPrice service method when consuming message`() {
        // given
        val subject = SupplierSkuPriceConsumer(supplierSkuPriceService)

        // when
        subject.processSupplierSkuPrice().accept(
            object : Message<AvroSupplierSkuPrice> {
                override fun getPayload() = getAvroSupplierSkuPrice(uuid = UUID.randomUUID())
                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        Mockito.verify(supplierSkuPriceService, Mockito.times(1)).upsertSupplierSkuPrice(any())
    }
}
