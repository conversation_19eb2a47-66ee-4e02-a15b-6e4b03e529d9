package com.hellofresh.oms.supplierSkuPrice.unit

import com.hellofresh.oms.supplierSkuPrice.Fixture
import com.hellofresh.oms.supplierSkuPrice.repository.SupplierSkuPriceRepository
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest

@DataJpaTest
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
class SupplierSkuPriceRepositoryTest(
    @Autowired private val supplierSkuPriceRepository: SupplierSkuPriceRepository
) {

    @Test
    fun `should save new entity`() {
        // given
        val supplierSkuPriceEntity = Fixture.getSupplierSkuPriceEntity()

        // when
        supplierSkuPriceRepository.save(supplierSkuPriceEntity)

        // then
        assertEquals(1, supplierSkuPriceRepository.findAll().size)
    }

    @Test
    fun `should save new entity and then update it`() {
        // given
        val supplierSkuPriceEntity = Fixture.getSupplierSkuPriceEntity()
        val supplierSkuPriceEntityUpdated = supplierSkuPriceEntity.copy(dcCodes = listOf("EZ", "RC"))

        // when
        supplierSkuPriceRepository.save(supplierSkuPriceEntity)
        supplierSkuPriceRepository.save(supplierSkuPriceEntityUpdated)

        // then
        val actualResult = supplierSkuPriceRepository.findAll()
        assertEquals(1, actualResult.size)
        assertEquals(supplierSkuPriceEntityUpdated.dcCodes, actualResult.first().dcCodes)
    }
}
