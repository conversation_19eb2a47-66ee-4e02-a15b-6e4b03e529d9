package com.hellofresh.oms.supplierSkuPrice.unit

import com.hellofresh.oms.supplierSkuPrice.Fixture.getSupplierSkuPriceEntity
import com.hellofresh.oms.supplierSkuPrice.repository.SupplierSkuPriceRepository
import com.hellofresh.oms.supplierSkuPrice.service.SupplierSkuPriceService
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class SupplierSkuPriceServiceTest {

    @Mock
    private lateinit var supplerSkuPriceRepositoryMock: SupplierSkuPriceRepository

    @InjectMocks
    private lateinit var supplierSkuPriceService: SupplierSkuPriceService

    @Test
    fun `should save new supplierSkuPrice when it doesn't exist in the database`() {
        // given
        val incomingSupplierSkuPriceEntity = getSupplierSkuPriceEntity()
        whenever(supplerSkuPriceRepositoryMock.save(incomingSupplierSkuPriceEntity))
            .thenReturn(incomingSupplierSkuPriceEntity)

        // when
        val actualSupplierSkuPriceEntity =
            supplierSkuPriceService.upsertSupplierSkuPrice(incomingSupplierSkuPriceEntity)

        // then
        verify(supplerSkuPriceRepositoryMock).save(incomingSupplierSkuPriceEntity)
        assertNotNull(actualSupplierSkuPriceEntity)
    }
}
