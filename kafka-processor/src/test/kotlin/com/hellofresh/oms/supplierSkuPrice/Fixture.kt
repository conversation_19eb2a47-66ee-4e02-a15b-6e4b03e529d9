package com.hellofresh.oms.supplierSkuPrice

import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.SupplierSkuPrice
import com.hellofresh.planning.suppliersku.pricing.event_types.entity_created
import com.hellofresh.planning.suppliersku.pricing.pricing as AvroSupplierSkuPrice
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import java.util.UUID.randomUUID

object Fixture {
    fun getAvroSupplierSkuPrice(
        uuid: UUID = randomUUID(),
        unitsPerCase: Int = 1,
        bufferPermyriad: Int = 1,
        supplierSkuId: UUID = randomUUID(),
        casesPerPallet: Int = 1,
    ): AvroSupplierSkuPrice =
        AvroSupplierSkuPrice.newBuilder()
            .setId(uuid)
            .setSupplierSkuId(supplierSkuId)
            .setDistributionCenter(listOf("NJ"))
            .setStartDate("2021-06-04T01:01:01Z")
            .setEndDate("2023-06-04T00:00:00Z")
            .setEnabled(true)
            .setCasesPerPallet(casesPerPallet)
            .setPriceType("unit_type")
            .setCurrency("EUR")
            .setPrice(3500)
            .setBufferPercent(bufferPermyriad)
            .setUnitsPerCase(unitsPerCase)
            .setMarket("us")
            .setEventType(entity_created)
            .setMinOrderQuantity(0)
            .setPalletType("")
            .setSupplierPackaging("")
            .setExtraCostRepacking(0)
            .setExtraCostPackaging(0)
            .setExtraCostLabelling(0)
            .setExtraCostStorage(0)
            .setExtraCostFreight(0)
            .setConversionRate(10000)
            .setCreatedAt("2021-06-09T16:37:56.869764Z")
            .setUpdatedAt("2023-01-22T19:44:33.64022Z")
            .build()

    fun getSupplierSkuPriceEntity(): SupplierSkuPrice =
        SupplierSkuPrice(
            uuid = randomUUID(),
            supplierSkuId = randomUUID(),
            dcCodes = listOf("NJ"),
            startDate = LocalDateTime.now(),
            endDate = LocalDateTime.now().plusYears(2),
            enabled = true,
            priceType = "unit_type",
            market = "us",
            currency = "EUR",
            pricePermyriad = Permyriad(3500),
            bufferPermyriad = Permyriad(0),
            caseSize = 1,
            unitsPerCase = BigDecimal.valueOf(1),
            casePrice = null,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )
}
