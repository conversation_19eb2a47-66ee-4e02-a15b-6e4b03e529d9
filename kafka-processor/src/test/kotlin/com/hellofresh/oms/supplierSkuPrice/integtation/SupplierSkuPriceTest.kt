package com.hellofresh.oms.supplierSkuPrice.integtation

import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.supplierSkuPrice.Fixture.getAvroSupplierSkuPrice
import com.hellofresh.oms.supplierSkuPrice.repository.SupplierSkuPriceRepository
import com.hellofresh.planning.suppliersku.pricing.pricing
import java.util.UUID
import java.util.UUID.randomUUID
import org.apache.avro.specific.SpecificRecordBase
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.empty
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

class SupplierSkuPriceTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var repository: SupplierSkuPriceRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, SpecificRecordBase>

    @Value("\${topics.supplier-sku-price}")
    private lateinit var topics: String

    @BeforeEach
    fun setUp() {
        repository.deleteAll()
    }

    @Test
    fun `should consume price and save new supplier sku price`() {
        // given
        val priceId = randomUUID()
        val avroPrice = getAvroSupplierSkuPrice(uuid = priceId)

        // when
        publishMessage(avroPrice, priceId)

        // then
        assertAwaiting {
            val skus = repository.findAll().map { it.uuid }
            assertThat(skus, equalTo(listOf(priceId)))
        }
    }

    @Test
    fun `should not save events when kafka_receivedMessageKey is null`() {
        // given
        val priceId = randomUUID()
        val avroPrice = getAvroSupplierSkuPrice(supplierSkuId = priceId, uuid = randomUUID())

        // when
        publishMessage(avroPrice)

        // then
        // wait for a given time so that we are sure that no message is consumed
        Thread.sleep(1000)
        assertThat(repository.findAll(), empty())
    }

    private fun publishMessage(message: pricing, messageUuid: UUID? = null) {
        kafkaTemplate.send(topics.split(",").first(), messageUuid.toString(), message)
        kafkaTemplate.flush()
    }
}
