package com.hellofresh.oms.acknowledgement

import com.hellofresh.oms.acknowledgement.Fixture.getProtoAcknowledgement
import com.hellofresh.oms.acknowledgement.consumer.AcknowledgementConsumer
import com.hellofresh.oms.acknowledgement.service.AcknowledgementService
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class AcknowledgementConsumerTest {
    @Mock
    private lateinit var acknowledgementServiceMock: AcknowledgementService

    @Test
    fun `should call Acknowledgement service method when consuming message`() {
        // given
        val subject = AcknowledgementConsumer(acknowledgementServiceMock)

        // when
        subject.processAcknowledgement().accept(
            object : Message<PurchaseOrderAcknowledgement> {
                override fun getPayload(): PurchaseOrderAcknowledgement = getProtoAcknowledgement()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        verify(acknowledgementServiceMock).process(any())
    }
}
