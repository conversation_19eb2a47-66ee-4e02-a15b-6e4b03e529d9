package com.hellofresh.oms.acknowledgement

import com.hellofresh.oms.acknowledgement.Fixture.getProtoAcknowledgement
import com.hellofresh.oms.acknowledgement.consumer.tryFrom
import com.hellofresh.oms.acknowledgement.repository.AcknowledgementRepository
import com.hellofresh.oms.acknowledgement.service.AcknowledgementService
import com.hellofresh.oms.client.featureflag.FeatureFlagClient
import com.hellofresh.oms.client.tapioca.TapiocaClient
import com.hellofresh.oms.model.acknowledgement.Acknowledgement
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.AcknowledgementItem.State.STATE_REJECTED
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.Mockito.mockStatic
import org.mockito.Mockito.never
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doThrow
import org.mockito.kotlin.reset
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class AcknowledgementServiceTest {
    @Mock
    lateinit var acknowledgementRepositoryMock: AcknowledgementRepository

    @Mock
    lateinit var featureFlagClientMock: FeatureFlagClient

    @Mock
    lateinit var tapiocaClientMock: TapiocaClient

    @Mock
    lateinit var meterRegistry: MeterRegistry

    @InjectMocks
    lateinit var subject: AcknowledgementService

    @BeforeEach
    fun setup() {
        `when`(featureFlagClientMock.shouldUseNewApproveRejectFeatureFlag()).thenReturn(true)
        reset(tapiocaClientMock)
    }

    @Test
    fun `should save new po acknowledgment when it doesn't exist in the database`() {
        // given
        val incomingAcknowledgementEntity = Acknowledgement::class.tryFrom(getProtoAcknowledgement())
        whenever(
            acknowledgementRepositoryMock.save(incomingAcknowledgementEntity)
        ).thenReturn(incomingAcknowledgementEntity)

        // when
        subject.process(incomingAcknowledgementEntity)

        // then
        verify(acknowledgementRepositoryMock).save(incomingAcknowledgementEntity)
    }

    @Test
    fun `should call approvePurchaseOrder when all lines are accepted`() {
        // given
        val incomingAcknowledgementEntity = Acknowledgement::class.tryFrom(getProtoAcknowledgement())

        `when`(featureFlagClientMock.shouldUseNewApproveRejectFeatureFlag()).thenReturn(true)
        `when`(
            acknowledgementRepositoryMock.findAllByPoIdWithLines(incomingAcknowledgementEntity.poId)
        ).thenReturn(listOf(incomingAcknowledgementEntity))

        // when
        subject.process(incomingAcknowledgementEntity)

        // then
        verify(tapiocaClientMock).approvePurchaseOrder(any())
        verify(tapiocaClientMock, never()).rejectPurchaseOrder(any())
    }

    @Test
    fun `should increment metric when approve request fails`() {
        // given
        val incomingAcknowledgementEntity = Acknowledgement::class.tryFrom(getProtoAcknowledgement())
        val poId = incomingAcknowledgementEntity.poId

        val counterMock = mock(Counter::class.java)
        val builderMock = mock(Counter.Builder::class.java)

        mockStatic(Counter::class.java).use { counterStatic ->
            `when`(Counter.builder("failing_approving_po_attempt")).thenReturn(builderMock)
            `when`(builderMock.tag("action", "APPROVE")).thenReturn(builderMock)
            `when`(builderMock.register(meterRegistry)).thenReturn(counterMock)
            `when`(featureFlagClientMock.shouldUseNewApproveRejectFeatureFlag()).thenReturn(true)
            `when`(
                acknowledgementRepositoryMock.findAllByPoIdWithLines(poId)
            ).thenReturn(listOf(incomingAcknowledgementEntity))
            doThrow(RuntimeException("boom")).`when`(tapiocaClientMock).approvePurchaseOrder(any())

            // when
            subject.process(incomingAcknowledgementEntity)

            // then
            verify(counterMock).increment()
        }
    }

    @Test
    fun `should call rejectPurchaseOrder when all lines are rejected`() {
        // given
        val givenPoId = UUID.randomUUID()
        val givenPoNumber = "2115NJ309625"
        val givenState = STATE_REJECTED

        val incomingAcknowledgementEntity = Acknowledgement::class.tryFrom(
            getProtoAcknowledgement(poId = givenPoId, poNumber = givenPoNumber, state = givenState)
        )

        `when`(featureFlagClientMock.shouldUseNewApproveRejectFeatureFlag()).thenReturn(true)
        `when`(
            acknowledgementRepositoryMock.findAllByPoIdWithLines(incomingAcknowledgementEntity.poId)
        ).thenReturn(listOf(incomingAcknowledgementEntity))

        // when
        subject.process(incomingAcknowledgementEntity)

        // then
        verify(tapiocaClientMock).rejectPurchaseOrder(any())
        verify(tapiocaClientMock, never()).approvePurchaseOrder(any())
    }

    @Test
    fun `should increment metric when reject request fails`() {
        // given
        val givenPoId = UUID.randomUUID()
        val givenPoNumber = "2115NJ309625"
        val givenState = STATE_REJECTED

        val incomingAcknowledgementEntity = Acknowledgement::class.tryFrom(
            getProtoAcknowledgement(poId = givenPoId, poNumber = givenPoNumber, state = givenState)
        )
        val poId = incomingAcknowledgementEntity.poId

        val counterMock = mock(Counter::class.java)
        val builderMock = mock(Counter.Builder::class.java)

        mockStatic(Counter::class.java).use { counterStatic ->
            `when`(Counter.builder("failing_rejecting_po_attempt")).thenReturn(builderMock)
            `when`(builderMock.tag("action", "REJECT")).thenReturn(builderMock)
            `when`(builderMock.register(meterRegistry)).thenReturn(counterMock)
            `when`(featureFlagClientMock.shouldUseNewApproveRejectFeatureFlag()).thenReturn(true)
            `when`(
                acknowledgementRepositoryMock.findAllByPoIdWithLines(poId)
            ).thenReturn(listOf(incomingAcknowledgementEntity))
            doThrow(RuntimeException("boom")).`when`(tapiocaClientMock).rejectPurchaseOrder(any())

            // when
            subject.process(incomingAcknowledgementEntity)

            // then
            verify(counterMock).increment()
        }
    }
}
