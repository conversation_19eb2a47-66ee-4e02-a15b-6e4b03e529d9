package com.hellofresh.oms.acknowledgement.integration

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.put
import com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching
import com.hellofresh.oms.acknowledgement.Fixture.getProtoAcknowledgement
import com.hellofresh.oms.acknowledgement.repository.AcknowledgementRepository
import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.AcknowledgementItem.State.STATE_REJECTED
import com.statsig.sdk.Statsig
import io.micrometer.core.instrument.MeterRegistry
import java.util.UUID
import java.util.concurrent.TimeUnit
import org.awaitility.Awaitility.await
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.kafka.core.KafkaTemplate

@AutoConfigureWireMock
class AcknowledgementTest : AbstractIntegrationTest() {
    @Autowired
    private lateinit var acknowledgementRepository: AcknowledgementRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, PurchaseOrderAcknowledgement>

    @Value("\${topics.purchase-order-acknowledgement:}")
    private lateinit var topics: String

    @Value("\${statsig.gates.enable-new-approve-reject}")
    private lateinit var enableMevApproveRejectStatsigGate: String

    @Autowired
    lateinit var wireMock: WireMockServer

    @Autowired
    private lateinit var meterRegistry: MeterRegistry

    @BeforeEach
    fun setUp() {
        wireMock.resetAll()
        stubSuccessAuthRequest()

        acknowledgementRepository.deleteAll()
        Statsig.overrideGate(enableMevApproveRejectStatsigGate, false)
    }

    @AfterEach
    fun tearDown() {
        meterRegistry.clear()
    }

    @Test
    fun `should create new Acknowledgement`() {
        // given
        val givenPoId = UUID.randomUUID()
        val givenPoNumber = "2215NJ300005"
        val givenProtoAcknowledgement = getProtoAcknowledgement(poId = givenPoId, poNumber = givenPoNumber)

        // when
        publishMessage(givenProtoAcknowledgement)

        // then
        assertAwaiting {
            val acknowledgements = acknowledgementRepository.findAll().map { it.poId to it.poNumber }
            assertThat(acknowledgements, equalTo(listOf(givenPoId to givenPoNumber)))
        }
    }

    @Test
    fun `should make an approve request if Acknowledgement lines are ACCEPTED`() {
        // given
        val givenPoId = UUID.randomUUID()
        val givenPoNumber = "2215NJ300065"
        val givenProtoAcknowledgement = getProtoAcknowledgement(poId = givenPoId, poNumber = givenPoNumber)

        Statsig.overrideGate(enableMevApproveRejectStatsigGate, true)

        // when
        publishMessage(givenProtoAcknowledgement)

        // then
        assertAwaiting {
            val acknowledgements = acknowledgementRepository.findAll().map { it.poId to it.poNumber }
            assertThat(acknowledgements, equalTo(listOf(givenPoId to givenPoNumber)))
        }
    }

    @Test
    fun `should increment failing metric if approving request to Tapioca is failed`() {
        // given
        val givenPoId = UUID.randomUUID()
        val givenPoNumber = "2215NJ300090"
        val givenProtoAcknowledgement = getProtoAcknowledgement(poId = givenPoId, poNumber = givenPoNumber)

        Statsig.overrideGate(enableMevApproveRejectStatsigGate, true)

        stubFailingRequest()

        // when
        publishMessage(givenProtoAcknowledgement)

        // then
        assertMetricIncreased("failing_approving_po_attempt")
    }

    @Test
    fun `should increment failing metric if rejecting request to Tapioca is failed`() {
        // given
        val givenPoId = UUID.randomUUID()
        val givenPoNumber = "2215NJ300090"
        val givenProtoAcknowledgement = getProtoAcknowledgement(
            poId = givenPoId,
            poNumber = givenPoNumber,
            state = STATE_REJECTED
        )

        Statsig.overrideGate(enableMevApproveRejectStatsigGate, true)

        stubFailingRequest()

        // when
        publishMessage(givenProtoAcknowledgement)

        // then
        assertMetricIncreased("failing_rejecting_po_attempt")
    }

    private fun publishMessage(message: PurchaseOrderAcknowledgement) {
        kafkaTemplate.send(topics.split(",").first(), message.purchaseOrderId, message)
        kafkaTemplate.flush()
    }

    private fun stubSuccessAuthRequest() {
        wireMock.stubFor(
            post("/token")
                .willReturn(
                    aResponse()
                        .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .withBody(
                            """
                                {
                                    "token_type": "Bearer",
                                    "access_token": "eyJleHAiOjE2Mzg5MDg4NTMsImlhdCI6MTYzODg4NzI1MywiaXNzIjoiMWI1ZWVkMWYtMDNkZC00ZjlkLThkMzAtMmEwOGY5Mzk3NjU1IiwianRpIjoiMWZiYjBmMmEtMmI4ZS00NjJlLTljYTctMWZhNDUzZWYxOTM1In0",
                                    "expires_in": 21600
                                }
                            """.trimIndent(),
                        ),
                ),
        )
    }

    private fun stubFailingRequest() {
        wireMock.stubFor(
            put(urlPathMatching("/purchase-orders/(.*)/(.*)"))
                .willReturn(
                    aResponse()
                        .withStatus(500)
                        .withBody(
                            """{
                            |"status" : 500,
                            |"message" : "Internal Server Error",
                            |}
                            """.trimMargin(),
                        )
                        .withHeader("Content-Type", "application/json"),
                ),
        )
    }

    private fun assertMetricIncreased(metricName: String) {
        val actionTagValue = when (metricName) {
            "failing_approving_po_attempt" -> "APPROVE"
            "failing_rejecting_po_attempt" -> "REJECT"
            else -> "UNKNOWN"
        }

        await().atMost(5, TimeUnit.SECONDS).untilAsserted {
            val counter = meterRegistry
                .find(metricName)
                .tag("action", actionTagValue)
                .counter()

            assertThat(counter?.count(), equalTo(1.0))
        }
    }
}
