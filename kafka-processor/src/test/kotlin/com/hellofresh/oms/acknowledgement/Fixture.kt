package com.hellofresh.oms.acknowledgement

import com.google.type.DateTime
import com.google.type.Decimal
import com.google.type.PostalAddress
import com.google.type.TimeZone
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement as ProtoPurchaseOrderAcknowledgement
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.AcknowledgementItem as ProtoPurchaseOrderAcknowledgementItem
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.AcknowledgementItem.State
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.AcknowledgementItem.State.STATE_ACCEPTED
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.Quantity
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.Quantity.CasePackaging.UnitOfMeasure.UNIT_OF_MEASURE_CASE
import java.util.UUID

object Fixture {

    fun getProtoAcknowledgement(
        poId: UUID = UUID.randomUUID(),
        poNumber: String = "2115NJ309625",
        state: State = STATE_ACCEPTED,
    ): ProtoPurchaseOrderAcknowledgement = ProtoPurchaseOrderAcknowledgement
        .newBuilder()
        .setPurchaseOrderId(poId.toString())
        .setPurchaseOrderNumber(poNumber)
        .setCreateTime(getDateTime(2024, 4, 8, 12, 27, 10))
        .setPickupTime(getDateTime(2025, 5, 9, 10, 0, 0))
        .setPickupAddress(getProtoPickupAddress())
        .addAllItems(listOf(getProtoAcknowledgementItem(state)))
        .build()

    private fun getProtoAcknowledgementItem(state: State): ProtoPurchaseOrderAcknowledgementItem = ProtoPurchaseOrderAcknowledgementItem
        .newBuilder()
        .setId(UUID.randomUUID().toString())
        .setPromisedTime(getDateTime(2026, 4, 23, 10, 0, 30))
        .setSkuCode("PHF-10-10208-4")
        .setState(state)
        .setPalletsQuantity(0)
        .setPromisedQuantity(getProtoPromisedQuantity())
        .build()

    private fun getProtoPromisedQuantity(): Quantity = Quantity
        .newBuilder()
        .setOrderSize(10)
        .setCasePackaging(
            Quantity.CasePackaging
                .newBuilder()
                .setSize(Decimal.newBuilder().setValue("0").build())
                .setUnit(UNIT_OF_MEASURE_CASE)
                .build()
        )
        .build()

    private fun getProtoPickupAddress(
        regionCode: String = "GB",
        postalCode: String = "CW9 7WA",
        administrativeArea: String = "Cheshire",
        locality: String = "Northwich",
        organization: String = "HelloFresh",
    ): PostalAddress = PostalAddress
        .newBuilder()
        .setRegionCode(regionCode)
        .setPostalCode(postalCode)
        .setAdministrativeArea(administrativeArea)
        .setLocality(locality)
        .setOrganization(organization)
        .addAllAddressLines(listOf("Munich", "Berlin"))
        .build()

    @Suppress("LongParameterList")
    private fun getDateTime(
        year: Int,
        month: Int,
        day: Int,
        hours: Int,
        minutes: Int,
        seconds: Int
    ): DateTime = DateTime.newBuilder()
        .setTimeZone(TimeZone.newBuilder().setId("Europe/Berlin").build())
        .setYear(year)
        .setMonth(month)
        .setDay(day)
        .setHours(hours)
        .setMinutes(minutes)
        .setSeconds(seconds)
        .build()
}
