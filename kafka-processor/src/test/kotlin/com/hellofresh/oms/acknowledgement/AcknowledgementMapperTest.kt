package com.hellofresh.oms.acknowledgement

import com.google.type.DateTime
import com.hellofresh.oms.acknowledgement.Fixture.getProtoAcknowledgement
import com.hellofresh.oms.acknowledgement.consumer.tryFrom
import com.hellofresh.oms.model.acknowledgement.Acknowledgement
import com.hellofresh.oms.model.acknowledgement.AcknowledgementLineItem
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.AcknowledgementItem
import java.time.LocalDateTime
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll

class AcknowledgementMapperTest {

    @Test
    fun `should map proto acknowledgement to acknowledgement entity`() {
        val protoAcknowledgement = getProtoAcknowledgement()
        val acknowledgement = Acknowledgement::class.tryFrom(protoAcknowledgement)

        assertAll(
            { assertEquals(protoAcknowledgement.purchaseOrderId, acknowledgement.poId.toString()) },
            { assertEquals(protoAcknowledgement.purchaseOrderNumber, acknowledgement.poNumber) },
            { assertDate(protoAcknowledgement.createTime, acknowledgement.createTime!!) },
            { assertDate(protoAcknowledgement.pickupTime, acknowledgement.pickupTime!!) },

            { assertEquals(protoAcknowledgement.pickupAddress.regionCode, acknowledgement.pickupRegion) },
            { assertEquals(protoAcknowledgement.pickupAddress.postalCode, acknowledgement.pickupPostalCode) },
            {
                assertEquals(
                    protoAcknowledgement.pickupAddress.administrativeArea,
                    acknowledgement.pickupAdministrativeArea
                )
            },
            { assertEquals(protoAcknowledgement.pickupAddress.locality, acknowledgement.pickupLocality) },
            { assertEquals(protoAcknowledgement.pickupAddress.organization, acknowledgement.pickupOrganization) },
            { assertEquals(protoAcknowledgement.pickupAddress.addressLinesList, acknowledgement.pickupAddress) },

            { assertLines(protoAcknowledgement.itemsList, acknowledgement.lines) },
        )
    }

    private fun assertLines(protoItemsList: List<AcknowledgementItem>, lines: List<AcknowledgementLineItem>) {
        protoItemsList.forEach { protoItem ->
            val entity = lines.find { it.skuId.toString() == protoItem.id }

            assertEquals(protoItem.id, entity!!.skuId.toString())
            assertEquals(protoItem.skuCode, entity.skuCode)
            assertEquals(protoItem.state.name, entity.state.name)
            assertEquals(protoItem.palletsQuantity, entity.numberOfPallets)
            assertDate(protoItem.promisedTime, entity.promisedTime!!)

            assertEquals(protoItem.promisedQuantity.orderSize.toBigDecimal().toDouble(), entity.numberOfUnits)
            assertEquals(
                protoItem.promisedQuantity.casePackaging.size.value.toBigDecimal().toDouble(),
                entity.unitsPerCase
            )
        }
    }

    private fun assertDate(protoDateTime: DateTime, mappedDateTime: LocalDateTime) {
        assertEquals(protoDateTime.year, mappedDateTime.year)
        assertEquals(protoDateTime.month, mappedDateTime.monthValue)
        assertEquals(protoDateTime.day, mappedDateTime.dayOfMonth)
        assertEquals(protoDateTime.hours, mappedDateTime.hour)
        assertEquals(protoDateTime.minutes, mappedDateTime.minute)
        assertEquals(protoDateTime.seconds, mappedDateTime.second)
        assertEquals(protoDateTime.nanos, mappedDateTime.nano)
    }
}
