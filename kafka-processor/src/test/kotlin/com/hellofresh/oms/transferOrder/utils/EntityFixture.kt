@file:Suppress("ExpressionBodySyntax")

package com.hellofresh.oms.transferOrder.utils

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.transferOrder.AddressInfo
import com.hellofresh.oms.model.transferOrder.ShippingInfo
import com.hellofresh.oms.model.transferOrder.TransferOrder
import com.hellofresh.oms.model.transferOrder.TransferOrderChangeReason
import com.hellofresh.oms.model.transferOrder.TransferOrderItem
import com.hellofresh.oms.model.transferOrder.TransferOrderItemCasePackaging
import com.hellofresh.oms.model.transferOrder.TransferOrderItemPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderItemQuantity
import com.hellofresh.oms.model.transferOrder.TransferOrderItemTotalPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderItemsChangeReason
import com.hellofresh.oms.model.transferOrder.TransferOrderPrice
import com.hellofresh.oms.model.transferOrder.TransferOrderStatus
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

object EntityFixture {

    fun getTransferOrderEntity(
        id: UUID = UUID.randomUUID(),
        transferOrderNumber: String = "2552AET01638",
        sourceDcCode: String = "BL",
        destinationDcCode: String = "AE",
        status: TransferOrderStatus = TransferOrderStatus.STATE_CANCELLED
    ): TransferOrder {
        return TransferOrder(
            id = id,
            sourceDcCode = sourceDcCode,
            destinationDcCode = destinationDcCode,
            creatorEmail = "<EMAIL>",
            reasonText = "Replenishment",
            status = status,
            yearWeek = YearWeek(2025, 52),
            transferOrderNumber = transferOrderNumber,
            sourceDcName = "CA - Valley",
            marketCode = "CA",
            version = 1,
            totalPrice = TransferOrderPrice(
                currencyCode = "CAD",
                units = 98L,
                nanos = 10000000
            ),
            shipping = ShippingInfo(
                address = AddressInfo(
                    regionCode = "CA",
                    postalCode = "T9E 1C6",
                    administrativeArea = "Alberta",
                    locality = "Nisku",
                    addressLines = listOf("920", "36 Avenue"),
                    organization = "CA - EDM2"
                ),
                method = "Vendor delivered"
            ),
            createTime = LocalDateTime.now(),
            updateTime = LocalDateTime.now(),
            pickupStartTime = LocalDateTime.now().plusDays(1),
            pickupEndTime = LocalDateTime.now().plusDays(1).plusHours(1),
            deliveryStartTime = LocalDateTime.now().plusDays(2),
            deliveryEndTime = LocalDateTime.now().plusDays(2).plusHours(2),
            sentTime = LocalDateTime.now(),
            comments = null,
            deliveryChangeReason = null,
            orderItemsChangeReason = null,
            regionCode = "CA",
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    fun getTransferOrderEntityWithShipping(
        id: UUID = UUID.randomUUID()
    ): TransferOrder {
        return getTransferOrderEntity(id = id).copy(
            shipping = ShippingInfo(
                address = AddressInfo(
                    regionCode = "CA",
                    postalCode = "T9E 1C6",
                    administrativeArea = "Alberta",
                    locality = "Nisku",
                    addressLines = listOf("920", "36 Avenue"),
                    organization = "CA - EDM2",
                    recipients = listOf("John Doe", "Jane Smith"),
                    revision = 1
                ),
                method = "Vendor delivered",
                notes = "Handle with care"
            )
        )
    }

    fun getTransferOrderEntityWithItems(
        id: UUID = UUID.randomUUID()
    ): TransferOrder {
        val transferOrder = getTransferOrderEntity(id = id)
        val items = listOf(
            getTransferOrderItemEntity(transferOrderId = id),
            getTransferOrderItemEntity(
                id = UUID.randomUUID(),
                transferOrderId = id,
                cskuCode = "PHF-10-88381-4"
            )
        )
        return transferOrder.copy(items = items)
    }

    fun getTransferOrderEntityWithChangeReasons(
        id: UUID = UUID.randomUUID()
    ): TransferOrder {
        return getTransferOrderEntity(id = id).copy(
            deliveryChangeReason = TransferOrderChangeReason(
                key = "WEATHER",
                value = "Bad weather conditions"
            ),
            orderItemsChangeReason = TransferOrderItemsChangeReason(
                key = "SHORTAGE",
                value = "Inventory shortage"
            )
        )
    }

    fun getTransferOrderItemEntity(
        id: UUID = UUID.randomUUID(),
        transferOrderId: UUID = UUID.randomUUID(),
        cskuCode: String = "PHF-10-88380-4"
    ): TransferOrderItem {
        return TransferOrderItem(
            id = id,
            transferOrderId = transferOrderId,
            cskuCode = cskuCode,
            cskuName = "X3B- Garlic, Unpeeled Clove",
            supplierId = UUID.fromString("64ba864f-d7b1-4216-8c98-90fc32f77335"),
            supplierCode = "301330",
            orderSize = 99,
            inventoryType = "INVENTORY_INPUT_TYPE_MANUAL",
            packagingType = "case",
            price = TransferOrderItemPrice(
                currencyCode = "CAD",
                units = 0L,
                nanos = 990000000
            ),
            totalPrice = TransferOrderItemTotalPrice(
                currencyCode = "CAD",
                units = 98L,
                nanos = 10000000
            ),
            quantity = TransferOrderItemQuantity(
                value = BigDecimal("99")
            ),
            originalPoNumber = null,
            lotNumber = null,
            lotExpirationTime = null,
            skuId = UUID.fromString("77946983-7dc1-41fc-9f85-655131fce4a7"),
            licensePlateNumber = null,
            casePackaging = TransferOrderItemCasePackaging(
                sizeValue = "1",
                unit = "UOM_UNIT",
                casePerPallet = null
            ),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
}
