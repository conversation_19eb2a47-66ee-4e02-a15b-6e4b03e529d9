package com.hellofresh.oms.transferOrder.unit

import com.hellofresh.oms.model.transferOrder.TransferOrderStatus
import com.hellofresh.oms.transferOrder.consumer.toTransferOrder
import com.hellofresh.oms.transferOrder.utils.Fixture
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class TransferOrderMapperTest {

    @Test
    fun `should map avro transfer order to entity correctly`() {
        val transferOrderId = UUID.randomUUID()
        val protoTransferOrder = Fixture.getProtoTransferOrder(
            id = transferOrderId,
            transferOrderNumber = "2552AET01638",
            sourceDcCode = "BL",
            destinationDcCode = "AE",
            status = "STATE_CANCELLED"
        )

        val result = protoTransferOrder.toTransferOrder()

        assertEquals(transferOrderId, result.id)
        assertEquals("2552AET01638", result.transferOrderNumber)
        assertEquals("BL", result.sourceDcCode)
        assertEquals("AE", result.destinationDcCode)
        assertEquals("<EMAIL>", result.creatorEmail)
        assertEquals("Replenishment", result.reasonText)
        assertEquals(TransferOrderStatus.STATE_CANCELLED, result.status)
        assertEquals(2025, result.yearWeek.getYear())
        assertEquals(52, result.yearWeek.getWeek())
        assertEquals("CA - Valley", result.sourceDcName)
        assertEquals("CA", result.marketCode)
        assertEquals(1, result.version)
    }

    @Test
    fun `should map total price correctly`() {
        val protoTransferOrder = Fixture.getProtoTransferOrder()

        val result = protoTransferOrder.toTransferOrder()

        assertNotNull(result.totalPrice)
        assertEquals("CAD", result.totalPrice.currencyCode)
        assertEquals(98L, result.totalPrice.units)
        assertEquals(10000000, result.totalPrice.nanos)
    }

    @Test
    fun `should map shipping information correctly`() {
        val protoTransferOrder = Fixture.getProtoTransferOrder()

        val result = protoTransferOrder.toTransferOrder()

        assertNotNull(result.shipping)
        assertEquals("Vendor delivered", result.shipping.method)

        val address = result.shipping.address
        assertEquals("CA", address.regionCode)
        assertEquals("T9E 1C6", address.postalCode)
        assertEquals("Alberta", address.administrativeArea)
        assertEquals("Nisku", address.locality)
        assertEquals(listOf("920", "36 Avenue"), address.addressLines)
        assertEquals("CA - EDM2", address.organization)
    }

    @Test
    fun `should map transfer order items correctly`() {
        val itemId1 = UUID.randomUUID()
        val itemId2 = UUID.randomUUID()
        val protoTransferOrder = Fixture.getProtoTransferOrderWithItems(
            itemIds = listOf(itemId1, itemId2)
        )

        val result = protoTransferOrder.toTransferOrder()

        assertEquals(2, result.items.size)

        val firstItem = result.items.first()
        assertEquals("PHF-10-88380-4", firstItem.cskuCode)
        assertEquals("X3B- Garlic, Unpeeled Clove", firstItem.cskuName)
        assertEquals("301330", firstItem.supplierCode)
        assertEquals(99, firstItem.orderSize)
        assertEquals("INVENTORY_INPUT_TYPE_MANUAL", firstItem.inventoryType)
        assertEquals("case", firstItem.packagingType)

        assertEquals("CAD", firstItem.price.currencyCode)
        assertEquals(0L, firstItem.price.units)
        assertEquals(990000000, firstItem.price.nanos)

        assertEquals("CAD", firstItem.totalPrice.currencyCode)
        assertEquals(98L, firstItem.totalPrice.units)
        assertEquals(10000000, firstItem.totalPrice.nanos)

        assertEquals("99", firstItem.quantity.value.toString())

        assertNotNull(firstItem.casePackaging)
        assertEquals("1", firstItem.casePackaging?.sizeValue)
        assertEquals("UOM_UNIT", firstItem.casePackaging?.unit)
    }

    @Test
    fun `should map optional fields correctly when present`() {
        val protoTransferOrder = Fixture.getProtoTransferOrderWithOptionalFields(
            comments = "Test comments",
            regionCode = "CA"
        )

        val result = protoTransferOrder.toTransferOrder()

        assertEquals("Test comments", result.comments)
        assertEquals("CA", result.regionCode)
    }

    @Test
    fun `should handle optional fields when not present`() {
        val protoTransferOrder = Fixture.getProtoTransferOrder()

        val result = protoTransferOrder.toTransferOrder()

        assertNull(result.comments)
        assertNull(result.deliveryChangeReason)
        assertNull(result.orderItemsChangeReason)
    }

    @Test
    fun `should map delivery change reason correctly`() {
        val protoTransferOrder = Fixture.getProtoTransferOrderWithDeliveryChangeReason(
            deliveryChangeReasonKey = "WEATHER",
            deliveryChangeReasonValue = "Bad weather conditions"
        )

        val result = protoTransferOrder.toTransferOrder()

        assertNotNull(result.deliveryChangeReason)
        assertEquals("WEATHER", result.deliveryChangeReason?.key)
        assertEquals("Bad weather conditions", result.deliveryChangeReason?.value)
    }

    @Test
    fun `should map order items change reason correctly`() {
        val protoTransferOrder = Fixture.getProtoTransferOrderWithOrderItemsChangeReason(
            orderItemsChangeReasonKey = "SHORTAGE",
            orderItemsChangeReasonValue = "Inventory shortage"
        )

        val result = protoTransferOrder.toTransferOrder()

        assertNotNull(result.orderItemsChangeReason)
        assertEquals("SHORTAGE", result.orderItemsChangeReason?.key)
        assertEquals("Inventory shortage", result.orderItemsChangeReason?.value)
    }

    @Test
    fun `should map packaging type to case when hasCasePackaging is true`() {
        val itemId = UUID.randomUUID()
        val protoTransferOrder = Fixture.getProtoTransferOrderWithItems(itemIds = listOf(itemId))

        val result = protoTransferOrder.toTransferOrder()

        val item = result.items.first()
        assertEquals("case", item.packagingType)
        assertNotNull(item.casePackaging)
    }

    @Test
    fun `should map packaging type to unit when hasUnitPackaging is true`() {
        val itemId = UUID.randomUUID()
        val transferOrderId = UUID.randomUUID()

        val itemWithUnitPackaging = Fixture.getProtoTransferOrderItem(id = itemId)
            .toBuilder()
            .clearCasePackaging()
            .setUnitPackaging(
                com.hellofresh.proto.stream.transferOrder.v1.UnitPackaging.newBuilder()
                    .build()
            )
            .build()

        val protoTransferOrder = Fixture.getProtoTransferOrder(id = transferOrderId)
            .toBuilder()
            .clearItems()
            .addItems(itemWithUnitPackaging)
            .build()

        val result = protoTransferOrder.toTransferOrder()

        val item = result.items.first()
        assertEquals("unit", item.packagingType)
        assertNull(item.casePackaging)
    }

    @Test
    fun `should map packaging type to unit as default when no packaging is set`() {
        val itemId = UUID.randomUUID()
        val transferOrderId = UUID.randomUUID()

        val itemWithNoPackaging = Fixture.getProtoTransferOrderItem(id = itemId)
            .toBuilder()
            .clearCasePackaging()
            .build()

        val protoTransferOrder = Fixture.getProtoTransferOrder(id = transferOrderId)
            .toBuilder()
            .clearItems()
            .addItems(itemWithNoPackaging)
            .build()

        val result = protoTransferOrder.toTransferOrder()

        val item = result.items.first()
        assertEquals("unit", item.packagingType)
        assertNull(item.casePackaging)
    }
}
