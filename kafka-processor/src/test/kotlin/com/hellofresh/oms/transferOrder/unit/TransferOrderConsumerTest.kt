package com.hellofresh.oms.transferOrder.unit

import com.hellofresh.oms.transferOrder.consumer.TransferOrderConsumer
import com.hellofresh.oms.transferOrder.service.TransferOrderService
import com.hellofresh.oms.transferOrder.utils.EntityFixture
import com.hellofresh.oms.transferOrder.utils.Fixture
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder as ProtoTransferOrder
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.support.MessageBuilder

@ExtendWith(MockitoExtension::class)
class TransferOrderConsumerTest {

    @Mock
    private lateinit var transferOrderService: TransferOrderService

    @InjectMocks
    private lateinit var transferOrderConsumer: TransferOrderConsumer

    @Test
    fun `should process transfer order message successfully`() {
        val transferOrderId = UUID.randomUUID()
        val protoTransferOrder = Fixture.getProtoTransferOrder(id = transferOrderId)
        val message: Message<ProtoTransferOrder> = MessageBuilder.withPayload(protoTransferOrder).build()
        val transferOrderEntity = EntityFixture.getTransferOrderEntity(id = transferOrderId)

        `when`(transferOrderService.upsertTransferOrder(any())).thenReturn(transferOrderEntity)

        transferOrderConsumer.processTransferOrder().accept(message)

        verify(transferOrderService).upsertTransferOrder(any())
    }

    @Test
    fun `should handle service exception and rethrow`() {
        val protoTransferOrder = Fixture.getProtoTransferOrder()
        val message: Message<ProtoTransferOrder> = MessageBuilder.withPayload(protoTransferOrder).build()
        val exception = RuntimeException("Database error")

        `when`(transferOrderService.upsertTransferOrder(any())).thenThrow(exception)

        assertThrows(RuntimeException::class.java) {
            transferOrderConsumer.processTransferOrder().accept(message)
        }

        verify(transferOrderService).upsertTransferOrder(any())
    }

    @Test
    fun `should process transfer order with multiple items`() {
        val transferOrderId = UUID.randomUUID()
        val itemIds = listOf(UUID.randomUUID(), UUID.randomUUID())
        val protoTransferOrder = Fixture.getProtoTransferOrderWithItems(
            id = transferOrderId,
            itemIds = itemIds
        )
        val message: Message<ProtoTransferOrder> = MessageBuilder.withPayload(protoTransferOrder).build()
        val transferOrderEntity = EntityFixture.getTransferOrderEntityWithItems(id = transferOrderId)

        `when`(transferOrderService.upsertTransferOrder(any())).thenReturn(transferOrderEntity)

        transferOrderConsumer.processTransferOrder().accept(message)

        verify(transferOrderService).upsertTransferOrder(any())
    }

    @Test
    fun `should process transfer order with optional fields`() {
        val protoTransferOrder = Fixture.getProtoTransferOrderWithOptionalFields(
            comments = "Test comments",
            regionCode = "CA"
        )
        val message: Message<ProtoTransferOrder> = MessageBuilder.withPayload(protoTransferOrder).build()
        val transferOrderEntity = EntityFixture.getTransferOrderEntity()

        `when`(transferOrderService.upsertTransferOrder(any())).thenReturn(transferOrderEntity)

        transferOrderConsumer.processTransferOrder().accept(message)

        verify(transferOrderService).upsertTransferOrder(any())
    }

    @Test
    fun `should process transfer order with change reasons`() {
        val protoTransferOrder = Fixture.getProtoTransferOrderWithDeliveryChangeReason(
            deliveryChangeReasonKey = "WEATHER",
            deliveryChangeReasonValue = "Bad weather conditions"
        )
        val message: Message<ProtoTransferOrder> = MessageBuilder.withPayload(protoTransferOrder).build()
        val transferOrderEntity = EntityFixture.getTransferOrderEntityWithChangeReasons()

        `when`(transferOrderService.upsertTransferOrder(any())).thenReturn(transferOrderEntity)

        transferOrderConsumer.processTransferOrder().accept(message)

        verify(transferOrderService).upsertTransferOrder(any())
    }
}
