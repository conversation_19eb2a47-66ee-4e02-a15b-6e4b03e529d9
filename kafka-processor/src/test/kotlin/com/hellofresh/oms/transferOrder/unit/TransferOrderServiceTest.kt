package com.hellofresh.oms.transferOrder.unit

import com.hellofresh.oms.transferOrder.repository.TransferOrderRepository
import com.hellofresh.oms.transferOrder.service.TransferOrderService
import com.hellofresh.oms.transferOrder.utils.EntityFixture
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class TransferOrderServiceTest {

    @Mock
    private lateinit var transferOrderRepository: TransferOrderRepository

    @InjectMocks
    private lateinit var transferOrderService: TransferOrderService

    @Test
    fun `should upsert transfer order successfully`() {
        val transferOrderEntity = EntityFixture.getTransferOrderEntity()
        `when`(transferOrderRepository.save(transferOrderEntity)).thenReturn(transferOrderEntity)

        val result = transferOrderService.upsertTransferOrder(transferOrderEntity)

        assertEquals(transferOrderEntity, result)
        verify(transferOrderRepository).save(transferOrderEntity)
    }
}
