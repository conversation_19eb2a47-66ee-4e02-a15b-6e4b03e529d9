package com.hellofresh.oms.advanceShippingNotification

import com.google.type.DateTime
import com.google.type.Decimal
import com.google.type.PostalAddress
import com.google.type.TimeZone
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem.Quantity
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem.ShippingState.SHIPPING_STATE_SHIPPED
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.Shipment
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.Shipment.ShippingMethod.SHIPPING_METHOD_VENDOR
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.Supplier
import java.util.UUID

object Fixture {

    fun getProtoAdvanceShippingNotice(
        asnId: String = UUID.randomUUID().toString(),
        poId: UUID = UUID.randomUUID(),
        poNumber: String = "2115NJ309625"
    ): PurchaseOrderAsn = PurchaseOrderAsn
        .newBuilder()
        .setAsnId(asnId)
        .setPurchaseOrderId(poId.toString())
        .setPurchaseOrderNumber(poNumber)
        .setNotes("Test ASN")
        .setSupplier(getProtoSupplier())
        .setShipment(getProtoShipment())
        .addAllItems(listOf(getProtoAsnItem()))
        .build()

    private fun getProtoSupplier(): Supplier = Supplier
        .newBuilder()
        .setId(UUID.randomUUID().toString())
        .setName("Test Supplier")
        .build()

    private fun getProtoShipment(): Shipment = Shipment
        .newBuilder()
        .setDistributionCenter("NJ")
        .setStandardCarrierAlphaCode("SCAC123")
        .setShippingMethod(SHIPPING_METHOD_VENDOR)
        .setCreateTime(getDateTime(2024, 4, 8, 12, 27, 10))
        .setShipmentTime(getDateTime(2024, 4, 10, 10, 0, 0))
        .setPlannedDeliveryTime(getDateTime(2024, 4, 12, 10, 0, 0))
        .setTrackingNumber("TRACK123456")
        .setNumberPallet("5")
        .setShippingAddress(getProtoAddress("Shipping"))
        .setPickupAddress(getProtoAddress("Pickup"))
        .build()

    private fun getProtoAsnItem(): AsnItem = AsnItem
        .newBuilder()
        .setId(UUID.randomUUID().toString())
        .setSkuCode("PHF-10-10208-4")
        .setShippingState(SHIPPING_STATE_SHIPPED)
        .setShippedQuantity(getProtoQuantity())
        .setPromisedQuantity(getProtoQuantity())
        .setPackingSize(Decimal.newBuilder().setValue("10").build())
        .setLot(
            AsnItem.Lot
                .newBuilder()
                .setNumber("LOT123")
                .setExpirationTime(
                    getDateTime(
                        2024,
                        4,
                        12,
                        10,
                        0,
                        0
                    )
                ).build()
        )
        .build()

    private fun getProtoQuantity(): Quantity = Quantity
        .newBuilder()
        .setOrderSize(10)
        .setCasePackaging(
            Quantity.CasePackaging
                .newBuilder()
                .setSize(Decimal.newBuilder().setValue("10").build())
                .setUnit(AsnItem.Quantity.CasePackaging.UnitOfMeasure.UNIT_OF_MEASURE_CASE)
                .build()
        )
        .setPalletId("PALLET123")
        .setCrateType("CRATE_TYPE_A")
        .build()

    private fun getProtoAddress(organization: String): PostalAddress = PostalAddress
        .newBuilder()
        .setRegionCode("GB")
        .setPostalCode("CW9 7WA")
        .setAdministrativeArea("Cheshire")
        .setLocality("Northwich")
        .setOrganization(organization)
        .addAllAddressLines(listOf("123 Main St", "Suite 100"))
        .build()

    @Suppress("LongParameterList")
    private fun getDateTime(
        year: Int,
        month: Int,
        day: Int,
        hours: Int,
        minutes: Int,
        seconds: Int
    ): DateTime = DateTime.newBuilder()
        .setTimeZone(TimeZone.newBuilder().setId("Europe/Berlin").build())
        .setYear(year)
        .setMonth(month)
        .setDay(day)
        .setHours(hours)
        .setMinutes(minutes)
        .setSeconds(seconds)
        .build()
}
