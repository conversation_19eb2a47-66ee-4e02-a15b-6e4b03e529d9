package com.hellofresh.oms.advanceShippingNotification

import com.hellofresh.oms.advanceShippingNotification.Fixture.getProtoAdvanceShippingNotice
import com.hellofresh.oms.advanceShippingNotification.consumer.toAdvanceShippingNotification
import com.hellofresh.oms.advanceShippingNotification.repository.AdvanceShippingNotificationRepository
import com.hellofresh.oms.advanceShippingNotification.service.AdvanceShippingNotificationService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class AdvanceShippingNotificationServiceTest {
    @Mock
    lateinit var advanceShippingNotificationRepositoryMock: AdvanceShippingNotificationRepository

    @InjectMocks
    lateinit var service: AdvanceShippingNotificationService

    @Test
    fun `should save new advance shipping notification`() {
        val protoAsn = getProtoAdvanceShippingNotice()
        val incomingAsnEntity = protoAsn.toAdvanceShippingNotification()
        whenever(
            advanceShippingNotificationRepositoryMock.save(incomingAsnEntity)
        ).thenReturn(incomingAsnEntity)

        service.process(incomingAsnEntity)

        verify(advanceShippingNotificationRepositoryMock).save(incomingAsnEntity)
    }
}
