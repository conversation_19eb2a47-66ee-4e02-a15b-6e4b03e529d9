package com.hellofresh.oms.advanceShippingNotification

import com.google.type.DateTime
import com.google.type.TimeZone
import com.hellofresh.oms.advanceShippingNotification.Fixture.getProtoAdvanceShippingNotice
import com.hellofresh.oms.advanceShippingNotification.consumer.toAdvanceShippingNotification
import com.hellofresh.oms.advanceShippingNotification.consumer.toAdvanceShippingNotificationItem
import com.hellofresh.oms.advanceShippingNotification.consumer.toLocalDateTime
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class AdvanceShippingNotificationMapperTest {

    @Test
    fun `should map PurchaseOrderAsn to AdvanceShippingNotification entity`() {
        val asnId = UUID.randomUUID().toString()
        val poId = UUID.randomUUID()
        val poNumber = "2215NJ300005"
        val protoAsn = getProtoAdvanceShippingNotice(asnId = asnId, poId = poId, poNumber = poNumber)

        val entity = protoAsn.toAdvanceShippingNotification()

        assertEquals(poId, entity.purchaseOrderId)
        assertEquals(poNumber, protoAsn.purchaseOrderNumber)
        assertEquals("Test ASN", entity.notes)

        assertEquals("NJ", entity.shipment.distributionCenter)
        assertEquals("SCAC123", entity.shipment.standardCarrierAlphaCode)
        assertEquals("SHIPPING_METHOD_VENDOR", entity.shipment.shippingMethod)
        assertEquals("TRACK123456", entity.shipment.trackingNumber)
        assertEquals("5", entity.shipment.numberPallet)

        assertEquals("GB", entity.shipment.shippingAddress.regionCode)
        assertEquals("CW9 7WA", entity.shipment.shippingAddress.postalCode)
        assertEquals("Northwich", entity.shipment.shippingAddress.locality)
        assertEquals("Shipping", entity.shipment.shippingAddress.organization)

        assertEquals("GB", entity.shipment.pickupAddress.regionCode)
        assertEquals("CW9 7WA", entity.shipment.pickupAddress.postalCode)
        assertEquals("Northwich", entity.shipment.pickupAddress.locality)
        assertEquals("Pickup", entity.shipment.pickupAddress.organization)

        assertEquals(1, entity.items.size)
        val item = entity.items.first()
        assertEquals("PHF-10-10208-4", item.skuCode)
        assertEquals("SHIPPING_STATE_SHIPPED", item.shippingState)
        assertEquals(10, item.shippedQuantity.orderSize)
        assertEquals(10.0, item.shippedQuantity.size)
        assertEquals("UNIT_OF_MEASURE_CASE", item.shippedQuantity.unit)
        assertEquals("PALLET123", item.shippedQuantity.palletId)
        assertEquals("CRATE_TYPE_A", item.shippedQuantity.crateType)

        assertEquals(10, item.promisedQuantity?.orderSize)
        assertEquals(10.0, item.promisedQuantity?.size)
        assertEquals("UNIT_OF_MEASURE_CASE", item.promisedQuantity?.unit)
        assertEquals("PALLET123", item.promisedQuantity?.palletId)
        assertEquals("CRATE_TYPE_A", item.promisedQuantity?.crateType)

        assertEquals(10.0, item.packingSize)
        assertEquals("LOT123", item.lotNumber)
        assertNotNull(item.expirationTime)
    }

    @Test
    fun `should map AsnItem to AdvanceShippingNotificationItem entity`() {
        val asnId = UUID.randomUUID().toString()
        val itemId = UUID.randomUUID().toString()
        val protoAsn = getProtoAdvanceShippingNotice(asnId = asnId)
        val protoItem = protoAsn.itemsList.first().toBuilder()
            .setId(itemId)
            .build()

        val entity = protoItem.toAdvanceShippingNotificationItem(UUID.fromString(asnId))

        assertEquals(UUID.fromString(itemId), entity.id)
        assertEquals(UUID.fromString(asnId), entity.asnId)
        assertEquals("PHF-10-10208-4", entity.skuCode)
        assertEquals("SHIPPING_STATE_SHIPPED", entity.shippingState)

        assertEquals(10, entity.shippedQuantity.orderSize)
        assertEquals(10.0, entity.shippedQuantity.size)
        assertEquals("UNIT_OF_MEASURE_CASE", entity.shippedQuantity.unit)
        assertEquals("PALLET123", entity.shippedQuantity.palletId)
        assertEquals("CRATE_TYPE_A", entity.shippedQuantity.crateType)

        assertEquals(10, entity.promisedQuantity?.orderSize)
        assertEquals(10.0, entity.promisedQuantity?.size)
        assertEquals("UNIT_OF_MEASURE_CASE", entity.promisedQuantity?.unit)
        assertEquals("PALLET123", entity.promisedQuantity?.palletId)
        assertEquals("CRATE_TYPE_A", entity.promisedQuantity?.crateType)

        assertEquals(10.0, entity.packingSize)
        assertEquals("LOT123", entity.lotNumber)
        assertNotNull(entity.expirationTime)
    }

    @Test
    fun `should convert DateTime to localDateTime`() {
        val dateTime = DateTime.newBuilder()
            .setYear(2024)
            .setMonth(4)
            .setDay(12)
            .setHours(10)
            .setMinutes(30)
            .setSeconds(45)
            .setNanos(123000000)
            .setTimeZone(TimeZone.newBuilder().setId("Europe/Berlin").build())
            .build()

        val offsetDateTime = dateTime.toLocalDateTime()

        assertEquals(2024, offsetDateTime.year)
        assertEquals(4, offsetDateTime.monthValue)
        assertEquals(12, offsetDateTime.dayOfMonth)
        assertEquals(10, offsetDateTime.hour)
        assertEquals(30, offsetDateTime.minute)
        assertEquals(45, offsetDateTime.second)
        assertEquals(123000000, offsetDateTime.nano)
    }
}
