package com.hellofresh.oms.advanceShippingNotification

import com.hellofresh.oms.advanceShippingNotification.Fixture.getProtoAdvanceShippingNotice
import com.hellofresh.oms.advanceShippingNotification.consumer.AdvanceShippingNotificationConsumer
import com.hellofresh.oms.advanceShippingNotification.service.AdvanceShippingNotificationService
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class AdvanceShippingNotificationConsumerTest {
    @Mock
    private lateinit var advanceShippingNotificationServiceMock: AdvanceShippingNotificationService

    @Test
    fun `should call AdvanceShippingNotification service method when consuming message`() {
        // given
        val subject = AdvanceShippingNotificationConsumer(advanceShippingNotificationServiceMock)

        // when
        subject.processAdvanceShippingNotification().accept(
            object : Message<PurchaseOrderAsn> {
                override fun getPayload(): PurchaseOrderAsn = getProtoAdvanceShippingNotice()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        verify(advanceShippingNotificationServiceMock).process(any())
    }
}
