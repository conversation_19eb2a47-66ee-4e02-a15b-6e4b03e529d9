package com.hellofresh.oms.shippingMethod.integration

import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.shippingMethod.Fixture.SHIP_METHOD_UUID
import com.hellofresh.oms.shippingMethod.Fixture.getAvroShipMethod
import com.hellofresh.oms.shippingMethod.repository.ShipMethodRepository
import com.hellofresh.planning.supplier.shipmethods.event_types.entity_created
import com.hellofresh.planning.supplier.shipmethods.event_types.entity_updated
import com.hellofresh.planning.supplier.shipmethods.ship_methods as AvroShipMethod
import java.util.UUID
import java.util.UUID.fromString
import java.util.UUID.randomUUID
import kotlin.test.assertEquals
import org.apache.avro.specific.SpecificRecordBase
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

class ShipMethodTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var shipMethodRepository: ShipMethodRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, SpecificRecordBase>

    @Value("\${topics.ship-method}")
    private lateinit var topics: String

    @BeforeEach
    fun setUp() {
        shipMethodRepository.deleteAll()
    }

    @Test
    fun `should consume ship method - happy path`() {
        // given
        val avroShipMethod = getAvroShipMethod(
            method = "Vendor Delivered",
            dcCode = "NJ",
            eventTypes = entity_created,
            market = "us",
        )

        // when
        publishMessage(avroShipMethod)

        // then
        assertAwaiting {
            val shipMethodEntity = shipMethodRepository.findAll().first()
            assertAll(
                { assertEquals("VENDOR", shipMethodEntity.method.toString()) },
                { assertEquals("NJ", shipMethodEntity.dcCode) },
                { assertEquals("us", shipMethodEntity.market) },
            )
        }
    }

    @Test
    fun `should consume and update ship method`() {
        // given
        val uuid = fromString(SHIP_METHOD_UUID)
        val avroShipMethodUpdated = getAvroShipMethod(
            shipMethodId = uuid,
            eventTypes = entity_updated,
            method = "Other",
            market = "us",
            dcCode = "NJ",
        )
        shipMethodRepository.save(
            ShipMethod(
                uuid = uuid,
                dcCode = "NJ",
                supplierId = randomUUID(),
                market = "us",
                method = ShipMethodEnum.VENDOR,
            ),
        )

        // when
        publishMessage(avroShipMethodUpdated, uuid)

        // then
        assertAwaiting {
            val shipMethods = shipMethodRepository.findAll()
            assertAll(
                { assertEquals("OTHER", shipMethods.first().method.toString()) },
                { assertEquals(uuid, shipMethods.first().uuid) },
                { assertEquals(1, shipMethods.size) },
            )
        }
    }

    private fun publishMessage(message: AvroShipMethod, messageUuid: UUID = randomUUID()) {
        kafkaTemplate.send(topics.split(",").first(), messageUuid.toString(), message)
        kafkaTemplate.flush()
    }
}
