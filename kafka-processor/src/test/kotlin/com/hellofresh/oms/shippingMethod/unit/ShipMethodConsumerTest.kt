package com.hellofresh.oms.shippingMethod.unit

import com.hellofresh.oms.shippingMethod.Fixture.DC_CODE_NJ
import com.hellofresh.oms.shippingMethod.Fixture.MARKET_US
import com.hellofresh.oms.shippingMethod.Fixture.SHIP_METHOD_VENDOR_DELIVERED
import com.hellofresh.oms.shippingMethod.Fixture.getAvroShipMethod
import com.hellofresh.oms.shippingMethod.consumer.ShipMethodConsumer
import com.hellofresh.oms.shippingMethod.service.ShipMethodService
import com.hellofresh.planning.supplier.shipmethods.event_types.entity_created
import com.hellofresh.planning.supplier.shipmethods.ship_methods as AvroShipMethod
import java.util.UUID
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

class ShipMethodConsumerTest {
    private val shipMethodServiceMock: ShipMethodService = Mockito.mock(ShipMethodService::class.java)

    @Test
    fun `should call ship method service method when consuming message`() {
        // given
        val subject = ShipMethodConsumer(shipMethodServiceMock)

        // when
        subject.processShipMethod().accept(
            object : Message<AvroShipMethod> {
                override fun getPayload(): AvroShipMethod = getAvroShipMethod(
                    dcCode = DC_CODE_NJ,
                    market = MARKET_US,
                    eventTypes = entity_created,
                    method = SHIP_METHOD_VENDOR_DELIVERED,
                )

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )
        // then
        Mockito.verify(shipMethodServiceMock).upsertShipMethod(any())
    }
}
