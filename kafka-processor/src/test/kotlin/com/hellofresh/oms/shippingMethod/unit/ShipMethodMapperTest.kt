package com.hellofresh.oms.shippingMethod.unit

import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.shippingMethod.Fixture.DC_CODE_NJ
import com.hellofresh.oms.shippingMethod.Fixture.MARKET_US
import com.hellofresh.oms.shippingMethod.Fixture.SHIP_METHOD_UUID
import com.hellofresh.oms.shippingMethod.Fixture.SHIP_METHOD_VENDOR_DELIVERED
import com.hellofresh.oms.shippingMethod.Fixture.SUPPLIER_ID
import com.hellofresh.oms.shippingMethod.Fixture.getAvroShipMethod
import com.hellofresh.oms.shippingMethod.consumer.mapToShipMethod
import com.hellofresh.planning.supplier.shipmethods.event_types
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class ShipMethodMapperTest {

    @Test
    fun `should map to ship method entity`() {
        // given
        val shipMethods = getAvroShipMethod(
            shipMethodId = UUID.fromString(SHIP_METHOD_UUID),
            dcCode = DC_CODE_NJ,
            supplierId = UUID.fromString(SUPPLIER_ID),
            market = MARKET_US,
            method = SHIP_METHOD_VENDOR_DELIVERED,
            eventTypes = event_types.entity_created,
        )

        // when
        val mappedShipMethods = shipMethods.mapToShipMethod(UUID.fromString(SHIP_METHOD_UUID))

        // then
        assertEquals(SHIP_METHOD_UUID, mappedShipMethods.uuid.toString())
        assertEquals(DC_CODE_NJ, mappedShipMethods.dcCode)
        assertEquals(SUPPLIER_ID, mappedShipMethods.supplierId.toString())
        assertEquals(MARKET_US, mappedShipMethods.market)
        assertEquals(VENDOR.name, mappedShipMethods.method.name)
    }

    @Test
    fun `should throw IllegalArgumentException when mapping ship method entity with unknown ship method`() {
        // given
        val shipMethods = getAvroShipMethod(
            shipMethodId = UUID.fromString(SHIP_METHOD_UUID),
            dcCode = DC_CODE_NJ,
            supplierId = UUID.fromString(SUPPLIER_ID),
            market = MARKET_US,
            method = "UnknownShipMethod",
            eventTypes = event_types.entity_created,
        )

        // then
        val exception = assertThrows<IllegalArgumentException> {
            shipMethods.mapToShipMethod(UUID.fromString(SHIP_METHOD_UUID))
        }
        assertEquals("Unknown ship method: UnknownShipMethod", exception.message)
    }
}
