package com.hellofresh.oms.shippingMethod.unit

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.shippingMethod.Fixture.DC_CODE_NJ
import com.hellofresh.oms.shippingMethod.Fixture.MARKET_US
import com.hellofresh.oms.shippingMethod.Fixture.SHIP_METHOD_UUID
import com.hellofresh.oms.shippingMethod.Fixture.SUPPLIER_ID
import com.hellofresh.oms.shippingMethod.repository.ShipMethodRepository
import com.hellofresh.oms.shippingMethod.service.ShipMethodService
import java.util.UUID
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class ShipMethodServiceTest {
    @Mock
    lateinit var shipMethodRepositoryMock: ShipMethodRepository

    @InjectMocks
    lateinit var subject: ShipMethodService

    @Test
    fun `should save new ship method`() {
        // given
        val shipMethodEntity = getShipMethodEntity()
        whenever(shipMethodRepositoryMock.save(shipMethodEntity)).thenReturn(shipMethodEntity)

        // when
        val actualEntity = subject.upsertShipMethod(shipMethodEntity)

        // then
        Mockito.verify(shipMethodRepositoryMock).save(shipMethodEntity)
        assertNotNull(actualEntity)
    }

    private fun getShipMethodEntity() = ShipMethod(
        uuid = UUID.fromString(SHIP_METHOD_UUID),
        dcCode = DC_CODE_NJ,
        supplierId = UUID.fromString(SUPPLIER_ID),
        market = MARKET_US,
        method = VENDOR,
    )
}
