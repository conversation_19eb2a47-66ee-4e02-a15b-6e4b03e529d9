package com.hellofresh.oms.shippingMethod

import com.hellofresh.planning.supplier.shipmethods.event_types
import com.hellofresh.planning.supplier.shipmethods.ship_methods
import java.time.LocalDateTime
import java.util.UUID

object Fixture {
    const val SHIP_METHOD_UUID = "fd8da06b-23ae-4002-8f6c-b797b9973b38"
    const val SUPPLIER_ID = "21888c07-ed63-4677-9652-817a8deb2f7d"
    const val DC_CODE_NJ = "NJ"
    const val MARKET_US = "us"
    const val SHIP_METHOD_VENDOR_DELIVERED = "Vendor Delivered"

    @Suppress("LongParameterList")
    fun getAvroShipMethod(
        shipMethodId: UUID = UUID.fromString(this.SHIP_METHOD_UUID),
        dcCode: String = "NJ",
        supplierId: UUID = UUID.fromString(this.SUPPLIER_ID),
        market: String = "uf",
        method: String = "Vendor Delivered",
        eventTypes: event_types = event_types.entity_created,
    ): ship_methods = ship_methods.newBuilder()
        .setId(shipMethodId)
        .setDistributionCenter(dcCode)
        .setSupplierId(supplierId)
        .setMarket(market)
        .setShipMethod(method)
        .setEventType(eventTypes)
        .setUpdatedAt(LocalDateTime.now().toString())
        .build()
}
