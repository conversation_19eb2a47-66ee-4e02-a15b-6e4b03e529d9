package com.hellofresh.oms.sku

import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.SkuStatus
import com.hellofresh.oms.model.SkuStatus.INACTIVE
import com.hellofresh.oms.model.UOM
import com.hellofresh.planning.culinarysku.cooling_type
import com.hellofresh.planning.culinarysku.culinarysku
import com.hellofresh.planning.culinarysku.event_types
import com.hellofresh.planning.culinarysku.packaging
import com.hellofresh.planning.culinarysku.quantity
import com.hellofresh.planning.culinarysku.temperature
import java.time.LocalDateTime
import java.util.UUID
import java.util.UUID.randomUUID

object Fixture {

    @Suppress("LongParameterList")
    fun getSkuEntity(
        id: UUID = randomUUID(),
        market: String = "us",
        name: String = "Test Sku",
        code: String = "PRO-10-10790-1",
        status: SkuStatus = INACTIVE,
        brands: List<String> = emptyList(),
        category: String = "PRO",
    ) = Sku(
        uuid = id,
        market = market,
        name = name,
        code = code,
        status = status,
        brands = brands,
        category = category,
        uom = UOM.KG
    )

    @Suppress("LongParameterList", "MaxLineLength")
    fun getAvroSku(
        skuId: String = randomUUID().toString(),
        skuName: String = "Test Sku (fl oz)",
        skuCode: String = "PRO-10-10790-1",
        skuStatus: String = "Inactive",
        skuBrands: List<String> = listOf("HelloFresh"),
        skuMarket: String = "us",
        extras: String = "{\"zolltarif_de\":\"\",\"zolltarif_ch\":\"\",\"single_line\":{\"destructive_class\":0,\"location_type\":\"\",\"compartment\":\"\",\"pack_instructions\":\"\"},\"extras\":\"{\\\"wms_uom\\\": \\\"ea\\\", \\\"drop_item\\\": true, \\\"high_ctfr\\\": false, \\\"obscure_sku\\\": false, \\\"box_assembly\\\": \\\"\\\", \\\"recipe_assembly\\\": \\\"Protein Chamber\\\", \\\"rework_required\\\": \\\"None\\\", \\\"product_overview\\\": \\\"\\\", \\\"time_to_max_temp\\\": 6, \\\"long_lead_time_sku\\\": false, \\\"size_specification\\\": \\\"5.5\\\\\\\" x 8.8\\\\\\\"\\\", \\\"purchasing_category\\\": \\\"Protein\\\", \\\"external_ingredient_name\\\": \\\"Sweet Italian Chicken Sausage\\\", \\\"wms_expiration_controlled\\\": true, \\\"brand_partnership_ingredient_name\\\": \\\"\\\"}\"}"
    ): culinarysku = culinarysku.newBuilder()
        .setId(skuId)
        .setName(skuName)
        .setCode(skuCode)
        .setStatus(skuStatus)
        .setBrands(skuBrands)
        .setMarket(skuMarket)
        .setEventType(event_types.entity_created)
        .setCategory("PRO")
        .setSubcategory("US-BRAND PA")
        .setSubcategory("US-BRAND PARTNERSHIPS-PRO")
        .setLocale("10")
        .setType("UNIT")
        .setCodeCheckReq(false)
        .setAcceptableCodeLife(1)
        .setCrushClass(1)
        .setIngredientId("")
        .setRecipeCardUnit("")
        .setRecipeCardTranslation("")
        .setRecipeCardQuantity(0f)
        .setQuantity(quantity(0.33f, "fluid ounce (fl oz)"))
        .setTemperature(temperature(-100, 100, "F"))
        .setPackaging(packaging(1.0f, "Light Produce/Ambient", "Unit's", 0.33f))
        .setCoolingType(cooling_type("Ambient", 1))
        .setThirdPwParentId("")
        .setProductTypes(emptyList())
        .setCreatedAt(LocalDateTime.now().toString())
        .setUpdatedAt(LocalDateTime.now().toString())
        .setExtras(extras)
        .build()
}
