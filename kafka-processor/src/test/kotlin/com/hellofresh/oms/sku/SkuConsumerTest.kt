package com.hellofresh.oms.sku

import com.hellofresh.oms.sku.Fixture.getAvroSku
import com.hellofresh.oms.sku.consumer.SkuConsumer
import com.hellofresh.oms.sku.service.SkuService
import com.hellofresh.planning.culinarysku.culinarysku as AvroSku
import java.util.UUID
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

class SkuConsumerTest {
    private val skuServiceMock: SkuService = Mockito.mock(SkuService::class.java)

    @Test
    fun `should call sku service method when consuming message`() {
        // given
        val subject = SkuConsumer(skuServiceMock)

        // when
        subject.processSku().accept(
            object : Message<AvroSku> {
                override fun getPayload(): AvroSku = getAvroSku()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        verify(skuServiceMock).upsertSku(any())
    }
}
