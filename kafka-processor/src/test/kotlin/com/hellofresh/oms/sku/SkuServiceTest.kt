package com.hellofresh.oms.sku

import com.hellofresh.oms.sku.Fixture.getSkuEntity
import com.hellofresh.oms.sku.repository.SkuRepository
import com.hellofresh.oms.sku.service.SkuService
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class SkuServiceTest {
    @Mock
    lateinit var skuRepositoryMock: SkuRepository

    @InjectMocks
    lateinit var subject: SkuService

    @Test
    fun `should save new sku when it doesn't exist in the database`() {
        // given
        val incomingSkuEntity = getSkuEntity()
        whenever(skuRepositoryMock.save(incomingSkuEntity)).thenReturn(incomingSkuEntity)

        // when
        val actualSkuEntity = subject.upsertSku(incomingSkuEntity)

        // then
        verify(skuRepositoryMock).save(incomingSkuEntity)
        assertNotNull(actualSkuEntity)
    }
}
