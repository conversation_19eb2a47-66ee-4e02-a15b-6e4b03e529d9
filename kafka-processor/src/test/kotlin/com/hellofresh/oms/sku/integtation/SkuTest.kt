package com.hellofresh.oms.sku.integtation

import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.sku.Fixture.getAvroSku
import com.hellofresh.oms.sku.repository.SkuRepository
import com.hellofresh.planning.culinarysku.culinarysku
import java.util.UUID
import java.util.UUID.randomUUID
import org.apache.avro.specific.SpecificRecordBase
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

class SkuTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var skuRepository: SkuRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, SpecificRecordBase>

    @Value("\${topics.sku}")
    private lateinit var topics: String

    @BeforeEach
    fun setUp() {
        skuRepository.deleteAll()
    }

    @Test
    fun `should consume sku`() {
        // given
        val skuId = randomUUID()
        val sku = getAvroSku(skuId = skuId.toString())

        // when
        publishMessage(sku, skuId)

        // then
        assertAwaiting {
            val persistedSkus = skuRepository.findAll().map { it.uuid to it.uom }
            assertThat(persistedSkus, equalTo(listOf(skuId to UNIT)))
        }
    }

    @Test
    fun `should consume sku and store uom as null when there is no extras`() {
        // given
        val skuId = randomUUID()
        val sku = getAvroSku(skuId = skuId.toString(), extras = "")

        // when
        publishMessage(sku, randomUUID())

        // then
        assertAwaiting {
            val persistedSkus = skuRepository.findAll().map { it.uuid to it.uom }
            assertThat(persistedSkus, equalTo(listOf(skuId to null)))
        }
    }

    private fun publishMessage(message: culinarysku, messageUuid: UUID? = null) {
        kafkaTemplate.send(topics.split(",").first(), messageUuid.toString(), message)
        kafkaTemplate.flush()
    }
}
