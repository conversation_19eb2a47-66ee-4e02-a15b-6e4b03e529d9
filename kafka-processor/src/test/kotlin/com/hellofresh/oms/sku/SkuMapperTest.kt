package com.hellofresh.oms.sku

import com.hellofresh.oms.model.SkuStatus.ACTIVE
import com.hellofresh.oms.model.SkuStatus.ARCHIVED
import com.hellofresh.oms.model.SkuStatus.DEPLETION_TRACK
import com.hellofresh.oms.model.SkuStatus.INACTIVE
import com.hellofresh.oms.model.SkuStatus.LAUNCH
import com.hellofresh.oms.model.SkuStatus.LIMITED
import com.hellofresh.oms.model.SkuStatus.OFFBOARDING
import com.hellofresh.oms.model.SkuStatus.ONBOARDING
import com.hellofresh.oms.model.SkuStatus.TEMPORARY
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.sku.Fixture.getAvroSku
import com.hellofresh.oms.sku.consumer.mapToSku
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.DynamicTest.dynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.assertThrows

@TestInstance(PER_CLASS)
class SkuMapperTest {

    @Test
    fun `should map AvroSku to sku entity`() {
        // given
        val avroSku = getAvroSku(
            extras = "{\"extras\": \" {\\\"wms_uom\\\": \\\"lbs\\\"} \"}",
        )
        // when
        val sku = avroSku.mapToSku()

        // then
        assertAll(
            { assertEquals(avroSku.name, sku.name) },
            { assertEquals(avroSku.code, sku.code) },
            { assertEquals(avroSku.getId(), sku.uuid.toString()) },
            { assertEquals(INACTIVE, sku.status) },
            { assertEquals(listOf("HelloFresh"), sku.brands) },
            { assertEquals(avroSku.market, sku.market) },
            { assertEquals(avroSku.category, sku.category) },
            { assertEquals(UOM.LBS, sku.uom) },
        )
    }

    @TestFactory
    fun `should map status of AvroSku to sku entity`() = listOf(
        Pair("Inactive", INACTIVE),
        Pair("Launch", LAUNCH),
        Pair("Limited", LIMITED),
        Pair("Onboarding", ONBOARDING),
        Pair("Temporary", TEMPORARY),
        Pair("Depletion Track", DEPLETION_TRACK),
        Pair("Active", ACTIVE),
        Pair("Archived", ARCHIVED),
        Pair("Inactive", INACTIVE),
        Pair("Offboarding", OFFBOARDING),
    ).map { (givenStatus, expectedStatus) ->
        dynamicTest("""should map "$givenStatus" to "$expectedStatus"""") {
            // given
            val avroSku = getAvroSku(skuStatus = givenStatus)
            // when
            val sku = avroSku.mapToSku()
            // then
            assertEquals(expectedStatus, sku.status)
        }
    }

    @TestFactory
    fun `should map parsed uom to UOM entity`() = listOf(
        Pair("kg", UOM.KG),
        Pair("liter", UOM.L),
        Pair("ea", UOM.UNIT),
        Pair("oz", UOM.OZ),
        Pair("gal", UOM.GAL),
        Pair("lbs", UOM.LBS),
    ).map { (givenUom, expectedUom) ->
        dynamicTest("""should map "$givenUom" to "$expectedUom"""") {
            // given
            val avroSku = getAvroSku(
                extras = "{\"extras\":\"{\\\"wms_uom\\\": \\\"$givenUom\\\"}\"}",
            )
            // when
            val sku = avroSku.mapToSku()
            // then
            assertEquals(expectedUom, sku.uom)
        }
    }

    @Test
    fun `should throw IllegalArgumentException when mapping AvroSku to sku entity`() {
        // given
        val avroSku = getAvroSku(skuStatus = "UnknownStatus")

        // then
        val exception = assertThrows<IllegalArgumentException> {
            avroSku.mapToSku()
        }
        assertEquals("Unknown Sku Status: UnknownStatus", exception.message)
    }

    @Test
    fun `should map uom to null when parsed input is null`() {
        // given
        val avroSku = getAvroSku(extras = "")

        // when
        val result = avroSku.mapToSku()

        // then
        assertNull(result.uom)
    }

    @Test
    fun `when wms_uom is empty then unit of measurement is null`() {
        // given
        val avroSku = getAvroSku(extras = "{\"extras\": \" {\\\"wms_uom\\\": \\\"\\\"} \"}")

        // when
        val result = avroSku.mapToSku()

        // then
        assertNull(result.uom)
    }

    @Test
    fun `should map AvroSku to sku entity with special character in extra field`() {
        // given
        val avroSku = getAvroSku(
            extras = "{\"extras\": \" {\\\"wms_uom\\\": \\\"lbs\\\", \\\"external_ingredient_name\\\": \\\"Cauliflower \\\\\\\"rice\\\\\\\"\\\"} \"}",
        )
        // when
        val sku = avroSku.mapToSku()

        // then
        assertEquals(UOM.LBS, sku.uom)
    }
}
