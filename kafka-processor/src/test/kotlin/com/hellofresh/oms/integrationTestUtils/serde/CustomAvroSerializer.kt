package com.hellofresh.oms.integrationTestUtils.serde

import com.hellofresh.planning.culinarysku.culinarysku
import com.hellofresh.planning.remps.facility.facility
import com.hellofresh.planning.supplier.shipmethods.ship_methods
import com.hellofresh.planning.suppliersku.packaging.packaging
import com.hellofresh.planning.suppliersku.pricing.pricing
import io.confluent.kafka.schemaregistry.avro.AvroSchema
import io.confluent.kafka.schemaregistry.testutil.MockSchemaRegistry
import io.confluent.kafka.serializers.KafkaAvroSerializer

class CustomAvroSerializer : KafkaAvroSerializer() {
    init {
        val client = MockSchemaRegistry.getClientForScope("dummy-registry")
        client.register("default-ship-methods", AvroSchema(ship_methods.`SCHEMA$`))
        client.register("suppliersku.pricing", AvroSchema(pricing.`SCHEMA$`))
        client.register("public.planning.culinarysku.v1", AvroSchema(culinarysku.`SCHEMA$`))
        client.register("public.planning.facility.v1", AvroSchema(facility.`SCHEMA$`))
        client.register("public.planning.suppliersku.packaging.v1", AvroSchema(packaging.`SCHEMA$`))
        schemaRegistry = client

        super.schemaRegistry = client
    }
}
