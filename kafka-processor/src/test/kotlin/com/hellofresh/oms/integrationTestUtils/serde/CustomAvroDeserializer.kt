package com.hellofresh.oms.integrationTestUtils.serde

import com.hellofresh.planning.culinarysku.culinarysku as AvroSku
import com.hellofresh.planning.remps.facility.facility as AvroSupplier
import com.hellofresh.planning.supplier.shipmethods.ship_methods as AvroShipMethods
import com.hellofresh.planning.suppliersku.packaging.packaging as AvroSupplierSkuPackaging
import com.hellofresh.planning.suppliersku.pricing.pricing as AvroSupplierSkuPrice
import io.confluent.kafka.schemaregistry.avro.AvroSchema
import io.confluent.kafka.schemaregistry.testutil.MockSchemaRegistry
import io.confluent.kafka.serializers.KafkaAvroDeserializer

class CustomAvroDeserializer : KafkaAvroDeserializer() {
    init {
        val client = MockSchemaRegistry.getClientForScope("dummy-registry")
        client.register("default-ship-methods", AvroSchema(AvroShipMethods.`SCHEMA$`))
        client.register("suppliersku.pricing", AvroSchema(AvroSupplierSkuPrice.`SCHEMA$`))
        client.register("public.planning.culinarysku.v1", AvroSchema(AvroSku.`SCHEMA$`))
        client.register("public.planning.facility.v1", AvroSchema(AvroSupplier.`SCHEMA$`))
        client.register("public.planning.suppliersku.packaging.v1", AvroSchema(AvroSupplierSkuPackaging.`SCHEMA$`))
        schemaRegistry = client

        super.schemaRegistry = client
    }
}
