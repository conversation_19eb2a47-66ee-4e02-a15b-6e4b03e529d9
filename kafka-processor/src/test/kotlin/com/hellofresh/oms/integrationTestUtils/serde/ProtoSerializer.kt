package com.hellofresh.oms.integrationTestUtils.serde

import com.google.protobuf.Message
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue as ProtoGoodsReceivedNoteValue
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal as ProtoSupplyQuantityRecommendationDaily
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter as ProtoDistributionCenter
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder as ProtoPurchaseOrder
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement
import org.apache.kafka.common.serialization.Serializer

open class ProtoSerializer<T : Message> : Serializer<T> {
    override fun serialize(topic: String, data: T): ByteArray = data.toByteArray()
}

class PurchaseOrderProtoSerializer : ProtoSerializer<ProtoPurchaseOrder>()

class DistributionCenterProtoSerializer : ProtoSerializer<ProtoDistributionCenter>()

class GoodsReceivedNoteProtoSerializer : ProtoSerializer<ProtoGoodsReceivedNoteValue>()

class SupplyQuantityRecommendationDailyProtoSerializer : ProtoSerializer<ProtoSupplyQuantityRecommendationDaily>()

class PurchaseOrderAcknowledgementProtoSerializer : ProtoSerializer<PurchaseOrderAcknowledgement>()
