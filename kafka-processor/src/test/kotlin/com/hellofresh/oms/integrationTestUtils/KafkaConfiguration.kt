package com.hellofresh.oms.integrationTestUtils

import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest.Companion.getKafkaBootstrapServers
import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest.Companion.getSchemaRegistryUrl
import com.hellofresh.oms.integrationTestUtils.serde.CustomAvroSerializer
import com.hellofresh.oms.integrationTestUtils.serde.DistributionCenterProtoSerializer
import com.hellofresh.oms.integrationTestUtils.serde.GoodsReceivedNoteProtoSerializer
import com.hellofresh.oms.integrationTestUtils.serde.PurchaseOrderAcknowledgementProtoSerializer
import com.hellofresh.oms.integrationTestUtils.serde.PurchaseOrderProtoSerializer
import com.hellofresh.oms.integrationTestUtils.serde.SupplyQuantityRecommendationDailyProtoSerializer
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement
import io.confluent.kafka.serializers.AbstractKafkaSchemaSerDeConfig
import org.apache.avro.generic.GenericRecord
import org.apache.avro.specific.SpecificRecordBase
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.common.serialization.StringSerializer
import org.springframework.boot.autoconfigure.kafka.KafkaProperties
import org.springframework.boot.ssl.SslBundles
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.kafka.core.DefaultKafkaProducerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.core.ProducerFactory

@TestConfiguration
class KafkaConfiguration {

    @Bean
    fun avroProducerFactory(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): ProducerFactory<String, SpecificRecordBase> {
        val props = properties.buildProducerProperties(sslBundles)
        props[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java
        props[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] = CustomAvroSerializer::class.java
        // For AVRO serialization we need to set the schema registry URL
        props[AbstractKafkaSchemaSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG] = getSchemaRegistryUrl()
        props[ProducerConfig.BOOTSTRAP_SERVERS_CONFIG] = getKafkaBootstrapServers()
        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun avroKafkaTemplate(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): KafkaTemplate<String, SpecificRecordBase> =
        KafkaTemplate(avroProducerFactory(properties, sslBundles))

    @Bean
    fun genericRecordProducerFactory(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): ProducerFactory<String, GenericRecord> {
        val props = properties.buildProducerProperties(sslBundles)
        props[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java
        props[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] = CustomAvroSerializer::class.java
        // For AVRO serialization we need to set the schema registry URL
        props[AbstractKafkaSchemaSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG] = getSchemaRegistryUrl()
        props[ProducerConfig.BOOTSTRAP_SERVERS_CONFIG] = getKafkaBootstrapServers()
        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun genericRecordKafkaTemplate(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): KafkaTemplate<String, GenericRecord> =
        KafkaTemplate(genericRecordProducerFactory(properties, sslBundles))

    @Bean
    fun purchaseOrderProducerFactory(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): ProducerFactory<String, PurchaseOrder> {
        val props = properties.buildProducerProperties(sslBundles)
        props[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java
        props[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] = PurchaseOrderProtoSerializer::class.java
        props[ProducerConfig.BOOTSTRAP_SERVERS_CONFIG] = getKafkaBootstrapServers()
        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun purchaseOrderKafkaTemplate(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): KafkaTemplate<String, PurchaseOrder> =
        KafkaTemplate(purchaseOrderProducerFactory(properties, sslBundles))

    @Bean
    fun distributionCenterProducerFactory(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): ProducerFactory<String, DistributionCenter> {
        val props = properties.buildProducerProperties(sslBundles)
        props[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java
        props[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] = DistributionCenterProtoSerializer::class.java
        props[ProducerConfig.BOOTSTRAP_SERVERS_CONFIG] = getKafkaBootstrapServers()
        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun distributionCenterKafkaTemplate(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): KafkaTemplate<String, DistributionCenter> =
        KafkaTemplate(distributionCenterProducerFactory(properties, sslBundles))

    @Bean
    fun goodsReceivedNoteProducerFactory(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): ProducerFactory<String, GoodsReceivedNoteValue> {
        val props = properties.buildProducerProperties(sslBundles)
        props[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java
        props[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] = GoodsReceivedNoteProtoSerializer::class.java
        props[ProducerConfig.BOOTSTRAP_SERVERS_CONFIG] = getKafkaBootstrapServers()
        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun goodsReceivedNoteKafkaTemplate(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): KafkaTemplate<String, GoodsReceivedNoteValue> =
        KafkaTemplate(goodsReceivedNoteProducerFactory(properties, sslBundles))

    @Bean
    fun supplyQuantityRecommendationDailyProducerFactory(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): ProducerFactory<String, SupplyQuantityRecommendationDailyVal> {
        val props = properties.buildProducerProperties(sslBundles)
        props[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java
        props[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] = SupplyQuantityRecommendationDailyProtoSerializer::class.java
        props[ProducerConfig.BOOTSTRAP_SERVERS_CONFIG] = getKafkaBootstrapServers()
        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun supplyQuantityRecommendationDailyKafkaTemplate(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): KafkaTemplate<String, SupplyQuantityRecommendationDailyVal> =
        KafkaTemplate(supplyQuantityRecommendationDailyProducerFactory(properties, sslBundles))

    @Bean
    fun purchaseOrderAcknowledgementProducerFactory(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): ProducerFactory<String, PurchaseOrderAcknowledgement> {
        val props = properties.buildProducerProperties(sslBundles)
        props[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java
        props[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] = PurchaseOrderAcknowledgementProtoSerializer::class.java
        props[ProducerConfig.BOOTSTRAP_SERVERS_CONFIG] = getKafkaBootstrapServers()
        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun purchaseOrderAcknowledgementKafkaTemplate(
        properties: KafkaProperties,
        sslBundles: SslBundles
    ): KafkaTemplate<String, PurchaseOrderAcknowledgement> =
        KafkaTemplate(purchaseOrderAcknowledgementProducerFactory(properties, sslBundles))
}
