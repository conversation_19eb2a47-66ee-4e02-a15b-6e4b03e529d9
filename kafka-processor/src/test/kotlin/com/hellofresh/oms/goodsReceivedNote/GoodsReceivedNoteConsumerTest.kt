package com.hellofresh.oms.goodsReceivedNote

import com.hellofresh.oms.goodsReceivedNote.Fixture.getProtoGrn
import com.hellofresh.oms.goodsReceivedNote.consumer.GoodsReceivedNoteConsumer
import com.hellofresh.oms.goodsReceivedNote.service.GoodsReceivedNoteService
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue as ProtoGoodsReceivedNoteValue
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class GoodsReceivedNoteConsumerTest {

    @Mock
    private lateinit var goodsReceivedNoteServiceMock: GoodsReceivedNoteService

    @Test
    fun `should call GoodsReceivedNote service method when consuming message`() {
        // given
        val subject = GoodsReceivedNoteConsumer(goodsReceivedNoteServiceMock)

        // when
        subject.processGoodsReceivedNote().accept(
            object : Message<ProtoGoodsReceivedNoteValue> {
                override fun getPayload(): ProtoGoodsReceivedNoteValue = getProtoGrn()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        verify(goodsReceivedNoteServiceMock).process(any())
    }
}
