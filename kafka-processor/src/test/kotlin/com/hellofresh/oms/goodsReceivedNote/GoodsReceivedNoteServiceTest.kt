package com.hellofresh.oms.goodsReceivedNote

import com.hellofresh.oms.goodsReceivedNote.Fixture.getProtoGrn
import com.hellofresh.oms.goodsReceivedNote.consumer.tryFrom
import com.hellofresh.oms.goodsReceivedNote.repository.GoodsReceivedNoteRepository
import com.hellofresh.oms.goodsReceivedNote.service.GoodsReceivedNoteService
import com.hellofresh.oms.model.grn.Grn
import java.time.LocalDateTime
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class GoodsReceivedNoteServiceTest {
    @Mock
    lateinit var goodsReceivedNoteRepositoryMock: GoodsReceivedNoteRepository

    @InjectMocks
    lateinit var subject: GoodsReceivedNoteService

    @Test
    fun `should save new goods received note when it doesn't exist in the database`() {
        // given
        val incomingGoodsReceivedNoteEntity = Grn::class.tryFrom(getProtoGrn())
        whenever(
            goodsReceivedNoteRepositoryMock.save(incomingGoodsReceivedNoteEntity)
        ).thenReturn(incomingGoodsReceivedNoteEntity)

        // when
        subject.process(incomingGoodsReceivedNoteEntity)

        // then
        verify(goodsReceivedNoteRepositoryMock).save(incomingGoodsReceivedNoteEntity)
    }

    @Test
    fun `should delete existing goods received note and save updated one`() {
        // given
        val incomingGoodsReceivedNoteEntity = Grn::class.tryFrom(getProtoGrn())
        val existingGoodsReceivedNoteEntity = Grn::class.tryFrom(getProtoGrn(deliveryStartDate = LocalDateTime.now()))

        whenever(
            goodsReceivedNoteRepositoryMock.findByPoNumber(incomingGoodsReceivedNoteEntity.poNumber)
        ).thenReturn(existingGoodsReceivedNoteEntity)
        whenever(
            goodsReceivedNoteRepositoryMock.save(incomingGoodsReceivedNoteEntity)
        ).thenReturn(incomingGoodsReceivedNoteEntity)

        // when
        subject.process(incomingGoodsReceivedNoteEntity)

        // then
        verify(goodsReceivedNoteRepositoryMock).delete(existingGoodsReceivedNoteEntity)
        verify(goodsReceivedNoteRepositoryMock).save(incomingGoodsReceivedNoteEntity)
    }
}
