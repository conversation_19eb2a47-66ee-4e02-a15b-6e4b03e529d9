package com.hellofresh.oms.goodsReceivedNote.integration

import com.hellofresh.oms.goodsReceivedNote.Fixture.getProtoGrn
import com.hellofresh.oms.goodsReceivedNote.Fixture.getProtoPurchaseOrderDelivery
import com.hellofresh.oms.goodsReceivedNote.repository.GoodsReceivedNoteRepository
import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.model.grn.DeliveryStateEnum
import com.hellofresh.oms.model.grn.Grn
import com.hellofresh.oms.model.grn.GrnStateEnum
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState.DELIVERY_STATE_RECEIVED
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.State.STATE_CLOSE
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.State.STATE_OPEN
import java.time.LocalDateTime
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

class GoodsReceivedNoteTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var goodsReceivedNoteRepository: GoodsReceivedNoteRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, GoodsReceivedNoteValue>

    @Value("\${topics.goods-received-note:}")
    private lateinit var topics: String

    @BeforeEach
    fun setUp() {
        goodsReceivedNoteRepository.deleteAll()
    }

    @Test
    fun `should create new GRN`() {
        // given
        val givenPoNumber = "2215NJ300001"
        val givenProtoGrn = getProtoGrn(poNumber = givenPoNumber, poState = STATE_OPEN)

        // when
        publishMessage(givenProtoGrn)

        // then
        assertAwaiting {
            val grns = goodsReceivedNoteRepository.findAll().map { it.poNumber to it.state }
            assertThat(grns, equalTo(listOf(givenPoNumber to GrnStateEnum.STATE_OPEN)))
        }
    }

    @Test
    fun `should store new GRN then remove it together with deliveries and save the update`() {
        // given
        val givenPoNumber = "2215NJ300002"
        val givenDcCode = "NJ"
        goodsReceivedNoteRepository.save(
            getGrnEntity(givenPoNumber, givenDcCode),
        )
        val givenGrnProto = getProtoGrn(
            poNumber = givenPoNumber,
            dcCode = givenDcCode,
            poState = STATE_CLOSE,
            deliveries = listOf(
                getProtoPurchaseOrderDelivery(DELIVERY_STATE_RECEIVED),
            ),
        )

        // when
        publishMessage(givenGrnProto)

        // then
        assertAwaiting {
            val grns = goodsReceivedNoteRepository.findAll().map { it.state to it.deliveries.first().state }
            assertThat(grns, equalTo(listOf(GrnStateEnum.STATE_CLOSE to DeliveryStateEnum.DELIVERY_STATE_RECEIVED)))
        }
    }

    private fun getGrnEntity(givenPoNumber: String, givenDcCode: String) = Grn(
        id = UUID.randomUUID(),
        poNumber = givenPoNumber,
        dcCode = givenDcCode,
        deliveryStartTime = LocalDateTime.now(),
        deliveryEndTime = LocalDateTime.now(),
        wmsName = "",
        reference = "",
        deliveries = emptyList(),
        state = GrnStateEnum.STATE_OPEN,
    )

    private fun publishMessage(message: GoodsReceivedNoteValue) {
        kafkaTemplate.send(topics.split(",").first(), message.reference, message)
        kafkaTemplate.flush()
    }
}
