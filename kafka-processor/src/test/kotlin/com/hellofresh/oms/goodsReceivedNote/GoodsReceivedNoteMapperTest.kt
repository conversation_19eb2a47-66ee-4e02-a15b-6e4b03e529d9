package com.hellofresh.oms.goodsReceivedNote

import com.google.type.DateTime
import com.hellofresh.oms.goodsReceivedNote.Fixture.getProtoGrn
import com.hellofresh.oms.goodsReceivedNote.consumer.PurchaseOrderNumberMappingError
import com.hellofresh.oms.goodsReceivedNote.consumer.tryFrom
import com.hellofresh.oms.model.grn.Grn
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDelivery
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDeliveryLine
import java.time.LocalDateTime
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.assertThrows

class GoodsReceivedNoteMapperTest {

    @Test
    fun `should map proto grn to grn entity`() {
        val protoGrn = getProtoGrn()
        val grn = Grn::class.tryFrom(protoGrn)

        assertAll(
            { assertEquals(protoGrn.dcCode, grn.dcCode) },
            { assertEquals(protoGrn.reference.split('_').first(), grn.poNumber) },
            { assertEquals(protoGrn.reference, grn.reference) },
            { assertDate(protoGrn.deliveryStartTime, grn.deliveryStartTime!!) },
            { assertDate(protoGrn.deliveryEndTime, grn.deliveryEndTime!!) },
            { assertEquals(protoGrn.state.name, grn.state.name) },
            { assertEquals(protoGrn.deliveriesCount, grn.deliveries.size) },
            { assertDeliveries(protoGrn.deliveriesList, grn.deliveries) },
            { assertEquals(protoGrn.wmsName.name, grn.wmsName) },
        )
    }

    @Test
    fun `should throw PurchaseOrderNumberMappingError when mapping proto grn to grn entity`() {
        val protoGrn = getProtoGrn(poNumber = "INTERNAL_123")
        assertThrows<PurchaseOrderNumberMappingError> {
            Grn::class.tryFrom(protoGrn)
        }
    }

    private fun assertDeliveries(protoDeliveries: List<PurchaseOrderDelivery>, entityDeliveries: List<com.hellofresh.oms.model.grn.PurchaseOrderDelivery>) {
        protoDeliveries.forEach { protoDelivery ->
            val entityDelivery = entityDeliveries.find { it.poDeliveryId == protoDelivery.id }

            assertEquals(protoDelivery.id, entityDelivery!!.poDeliveryId)
            assertDate(protoDelivery.expectedDeliveryStartTime, entityDelivery.expectedDeliveryStartTime!!)
            assertDate(protoDelivery.expectedDeliveryEndTime, entityDelivery.expectedDeliveryEndTime!!)
            assertDate(protoDelivery.deliveryTime, entityDelivery.deliveryTime!!)
            assertEquals(protoDelivery.state.name, entityDelivery.state.name)
            assertEquals(protoDelivery.linesCount, entityDelivery.lines.size)
            assertDeliveryLines(protoDelivery.linesList, entityDelivery.lines)
        }
    }

    private fun assertDeliveryLines(protoLines: List<PurchaseOrderDeliveryLine>, entityLines: List<com.hellofresh.oms.model.grn.PurchaseOrderDeliveryLine>) {
        protoLines.forEach { protoLine ->
            val entityLine = entityLines.find { it.poDeliveryLineId == protoLine.id }

            assertEquals(protoLine.id, entityLine!!.poDeliveryLineId)
            assertEquals(protoLine.skuCode, entityLine.skuCode)
            assertEquals(protoLine.unloadedQuantity.value.toBigDecimal().toDouble(), entityLine.unloadedQuantity)
            assertEquals(protoLine.receivedQuantity.value.toBigDecimal().toDouble(), entityLine.receivedQuantity)
            assertEquals(protoLine.expectedQuantity.value.toBigDecimal().toDouble(), entityLine.expectedQuantity)
            assertEquals(protoLine.rejectedQuantity.value.toBigDecimal().toDouble(), entityLine.rejectedQuantity)
            assertEquals(protoLine.palletizedQuantity.value.toBigDecimal().toDouble(), entityLine.palletizedQuantity)
            assertDate(protoLine.expirationDate, entityLine.expirationDate!!)
            assertEquals(protoLine.lotNumber, entityLine.supplierLotNumber)
        }
    }

    private fun assertDate(protoDateTime: DateTime, mappedDateTime: LocalDateTime) {
        assertEquals(protoDateTime.year, mappedDateTime.year)
        assertEquals(protoDateTime.month, mappedDateTime.monthValue)
        assertEquals(protoDateTime.day, mappedDateTime.dayOfMonth)
        assertEquals(protoDateTime.hours, mappedDateTime.hour)
        assertEquals(protoDateTime.minutes, mappedDateTime.minute)
        assertEquals(protoDateTime.seconds, mappedDateTime.second)
        assertEquals(protoDateTime.nanos, mappedDateTime.nano)
    }
}
