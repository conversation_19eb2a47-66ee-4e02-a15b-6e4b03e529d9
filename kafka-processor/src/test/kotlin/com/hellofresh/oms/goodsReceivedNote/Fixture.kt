package com.hellofresh.oms.goodsReceivedNote

import com.google.type.DateTime
import com.google.type.Decimal
import com.google.type.TimeZone
import com.hellofresh.proto.shared.distributionCenter.inbound.v1.WmsName.WMS_NAME_WMS2_0
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue as ProtoGoodsReceivedNoteValue
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState.DELIVERY_LINE_STATE_OPEN
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState.DELIVERY_STATE_OPEN
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDelivery
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDelivery as ProtoPurchaseOrderDelivery
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDeliveryLine
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDeliveryLine as ProtoPurchaseOrderDeliveryLine
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderLine
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderLine as ProtoPurchaseOrderLine
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.UnitOfMeasure.UNIT_OF_MEASURE_KG
import java.time.LocalDateTime

@Suppress("LongParameterList")
object Fixture {
    fun getProtoGrn(
        dcCode: String = "NJ",
        poNumber: String = "2215NJ309618",
        deliveryStartDate: LocalDateTime = LocalDateTime.of(2024, 1, 1, 10, 0, 0),
        poState: ProtoGoodsReceivedNoteValue.State = ProtoGoodsReceivedNoteValue.State.STATE_OPEN,
        lines: List<ProtoPurchaseOrderLine> = listOf(getProtoPurchaseOrderLine()),
        deliveries: List<ProtoPurchaseOrderDelivery> = listOf(getProtoPurchaseOrderDelivery()),
    ): ProtoGoodsReceivedNoteValue = ProtoGoodsReceivedNoteValue
        .newBuilder()
        .setDcCode(dcCode)
        .setReference(poNumber.plus("_O1"))
        .setDeliveryStartTime(
            getDateTime(
                year = deliveryStartDate.year,
                month = deliveryStartDate.monthValue,
                day = deliveryStartDate.dayOfMonth,
                hours = deliveryStartDate.hour,
                minutes = deliveryStartDate.minute,
                seconds = deliveryStartDate.second,
            ),
        )
        .setDeliveryEndTime(
            getDateTime(
                year = deliveryStartDate.year,
                month = deliveryStartDate.monthValue,
                day = deliveryStartDate.dayOfMonth,
                hours = deliveryStartDate.hour + 1,
                minutes = deliveryStartDate.minute,
                seconds = deliveryStartDate.second,
            ),
        )
        .setState(poState)
        .addAllLines(lines)
        .addAllDeliveries(deliveries)
        .setWmsName(WMS_NAME_WMS2_0)
        .build()

    fun getProtoPurchaseOrderDelivery(
        state: ProtoGoodsReceivedNoteValue.DeliveryState = DELIVERY_STATE_OPEN,
    ): PurchaseOrderDelivery =
        ProtoPurchaseOrderDelivery
            .newBuilder()
            .setId("2215NJ309618_O1-001")
            .setDeliveryTime(getDateTime(year = 2024, month = 1, day = 1, hours = 10, minutes = 0, seconds = 0))
            .setExpectedDeliveryStartTime(
                getDateTime(year = 2024, month = 1, day = 1, hours = 10, minutes = 0, seconds = 0),
            )
            .setExpectedDeliveryEndTime(
                getDateTime(year = 2024, month = 1, day = 1, hours = 11, minutes = 0, seconds = 0),
            )
            .setState(state)
            .addAllLines(listOf(getProtoPurchaseOrderDeliveryLine()))
            .build()

    private fun getProtoPurchaseOrderDeliveryLine(): PurchaseOrderDeliveryLine? =
        ProtoPurchaseOrderDeliveryLine
            .newBuilder()
            .setId("2215BV309618_O1-001-001")
            .setSkuCode("PHF-10-31688-2")
            .setUnloadedQuantity(Decimal.newBuilder().setValue("1").build())
            .setReceivedQuantity(Decimal.newBuilder().setValue("1").build())
            .setExpectedQuantity(Decimal.newBuilder().setValue("1").build())
            .setPalletizedQuantity(Decimal.newBuilder().setValue("1").build())
            .setCaseSize(Decimal.newBuilder().setValue("1").build())
            .setState(DELIVERY_LINE_STATE_OPEN)
            .setSkuUom(UNIT_OF_MEASURE_KG)
            .setExpirationDate(getDateTime(2025, 1, 1, 10, 0, 0))
            .setRejectedQuantity(Decimal.newBuilder().setValue("0").build())
            .setLotNumber("AB12345")
            .build()

    private fun getProtoPurchaseOrderLine(): PurchaseOrderLine =
        ProtoPurchaseOrderLine
            .newBuilder()
            .setSkuCode("sku")
            .setQuantity(Decimal.newBuilder().setValue("1").build())
            .setSkuUom(UNIT_OF_MEASURE_KG)
            .build()

    @Suppress("LongParameterList")
    private fun getDateTime(
        year: Int,
        month: Int,
        day: Int,
        hours: Int,
        minutes: Int,
        seconds: Int
    ): DateTime = DateTime.newBuilder()
        .setTimeZone(TimeZone.newBuilder().setId("Europe/Berlin").build())
        .setYear(year)
        .setMonth(month)
        .setDay(day)
        .setHours(hours)
        .setMinutes(minutes)
        .setSeconds(seconds)
        .build()
}
