package com.hellofresh.oms.imt.poVoid

import com.hellofresh.oms.imt.poVoid.Fixture.getGenericRecordPoVoid
import com.hellofresh.oms.imt.poVoid.consumer.toPoVoid
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class PoVoidMapperTest {

    @Test
    fun `should map GenericRecord to PoVoid entity`() {
        // given
        val poNumber = "2215NJ300005"
        val week = "2024-W20"
        val brand = "hellofresh"
        val dc = "NJ"
        val skuCode = "PHF-10-10208-4"
        val supplierName = "Test Supplier"
        val user = "testuser"
        val source = "IMT"
        val comment = "Test comment"
        val market = "us"
        val now = System.currentTimeMillis()
        val createTimestamp = now
        val lastUpdated = now

        val genericRecord = getGenericRecordPoVoid(
            poNumber = poNumber,
            week = week,
            brand = brand,
            dc = dc,
            skuCode = skuCode,
            supplierName = supplierName,
            user = user,
            source = source,
            comment = comment,
            market = market,
            createTimestamp = createTimestamp,
            lastUpdated = lastUpdated
        )

        // when
        val entity = genericRecord.toPoVoid()

        // then
        assertEquals(poNumber, entity.poNumber)
        assertEquals(week, entity.week)
        assertEquals(brand, entity.brand)
        assertEquals(dc, entity.dc)
        assertEquals(skuCode, entity.skuCode)
        assertEquals(supplierName, entity.supplierName)
        assertEquals(user, entity.userEmail)
        assertEquals(source, entity.source)
        assertEquals(comment, entity.comment)
        assertEquals(market, entity.market)
        assertNotNull(entity.createAt)
        assertNotNull(entity.lastUpdated)
        assertNull(entity.deletedBy)
    }

    @Test
    fun `should map GenericRecord with null values to PoVoid entity`() {
        // given
        val poNumber = "2215NJ300005"
        val week = "2024-W20"
        val brand = "hellofresh"
        val dc = "NJ"
        val skuCode = "PHF-10-10208-4"
        val supplierName = "Test Supplier"
        val user = "testuser"
        val source = "IMT"
        val market = "us"
        val now = System.currentTimeMillis()
        val createTimestamp = now

        val genericRecord = getGenericRecordPoVoid(
            poNumber = poNumber,
            week = week,
            brand = brand,
            dc = dc,
            skuCode = skuCode,
            supplierName = supplierName,
            user = user,
            source = source,
            comment = null,
            market = market,
            createTimestamp = createTimestamp,
            lastUpdated = null,
        )

        // when
        val entity = genericRecord.toPoVoid()

        // then
        assertEquals(poNumber, entity.poNumber)
        assertEquals(week, entity.week)
        assertEquals(brand, entity.brand)
        assertEquals(dc, entity.dc)
        assertEquals(skuCode, entity.skuCode)
        assertEquals(supplierName, entity.supplierName)
        assertEquals(user, entity.userEmail)
        assertEquals(source, entity.source)
        assertNull(entity.comment)
        assertEquals(market, entity.market)
        assertNotNull(entity.createAt)
        assertNull(entity.lastUpdated)
        assertNull(entity.deletedBy)
    }
}
