package com.hellofresh.oms.imt.comment

import com.hellofresh.oms.model.Comment
import com.hellofresh.oms.model.YearWeek
import java.time.LocalDateTime
import java.util.UUID
import org.apache.avro.Schema
import org.apache.avro.generic.GenericData
import org.apache.avro.generic.GenericRecord

@Suppress("LongParameterList")
object Fixture {

    fun getGenericRecordComment(
        id: String = UUID.randomUUID().toString(),
        sourceId: String? = null,
        resourceType: String = "sku",
        resourceId: String = "PHF-10-10208-4",
        domain: String = "imt",
        dc: String = "NJ",
        brand: String = "hellofresh",
        week: String = "202420",
        comment: String = "Test comment",
        createdBy: String = "<EMAIL>",
        updatedBy: String = "<EMAIL>",
        updatedAt: Long = System.currentTimeMillis()
    ): GenericRecord {
        val afterSchema = Schema.createRecord(
            "after",
            "",
            "com.hellofresh.oms.imt.comment",
            false
        )
        val afterFields = listOf(
            Schema.Field("id", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("source_id", Schema.create(Schema.Type.STRING), "", null),
            Schema.Field("resource_type", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("resource_id", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("domain", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("site", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("brand", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("week", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("comment", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("created_by", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("last_updated", Schema.create(Schema.Type.LONG), "", 0L),
            Schema.Field("updated_by", Schema.create(Schema.Type.STRING), "", ""),
        )
        afterSchema.setFields(afterFields)

        val after = GenericData.Record(afterSchema)
        after.put("id", id)
        after.put("source_id", sourceId)
        after.put("resource_type", resourceType)
        after.put("resource_id", resourceId)
        after.put("domain", domain)
        after.put("site", dc)
        after.put("brand", brand)
        after.put("week", week)
        after.put("comment", comment)
        after.put("created_by", createdBy)
        after.put("updated_by", updatedBy)
        after.put("last_updated", updatedAt)

        val schema = Schema.createRecord(
            "Comment",
            "",
            "com.hellofresh.oms.imt.comment",
            false
        )
        val fields = listOf(
            Schema.Field("after", afterSchema, "", null)
        )
        schema.setFields(fields)

        val record = GenericData.Record(schema)
        record.put("after", after)

        return record
    }

    fun getCommentEntity(
        id: UUID = UUID.randomUUID(),
        sourceId: String? = null,
        resourceType: String = "sku",
        resourceId: String = "PHF-10-10208-4",
        domain: String = "imt",
        dc: String = "NJ",
        brand: String = "hellofresh",
        yearWeek: YearWeek = YearWeek("2024-W20"),
        comment: String = "Test comment",
        createdBy: String = "<EMAIL>",
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedBy: String = "<EMAIL>",
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Comment = Comment(
        id = id,
        sourceId = sourceId,
        resourceType = resourceType,
        resourceId = resourceId,
        domain = domain,
        dc = dc,
        brand = brand,
        yearWeek = yearWeek,
        comment = comment,
        createdBy = createdBy,
        createdAt = createdAt,
        updatedBy = updatedBy,
        updatedAt = updatedAt
    )
}
