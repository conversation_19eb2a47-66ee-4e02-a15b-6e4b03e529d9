package com.hellofresh.oms.imt.poVoid

import com.hellofresh.oms.imt.poVoid.Fixture.getPoVoidEntity
import com.hellofresh.oms.imt.poVoid.repository.PoVoidRepository
import com.hellofresh.oms.imt.poVoid.service.PoVoidService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class PoVoidServiceTest {
    @Mock
    lateinit var poVoidRepositoryMock: PoVoidRepository

    @InjectMocks
    lateinit var service: PoVoidService

    @Test
    fun `should save new po void`() {
        // given
        val poVoidEntity = getPoVoidEntity()
        whenever(
            poVoidRepositoryMock.save(poVoidEntity)
        ).thenReturn(poVoidEntity)

        // when
        service.process(poVoidEntity)

        // then
        verify(poVoidRepositoryMock).save(poVoidEntity)
    }
}
