package com.hellofresh.oms.imt.exportReceipt

import com.hellofresh.oms.imt.exportReceipt.consumer.calculateYearWeek
import com.hellofresh.oms.model.imt.exportReceipt.ExportReceipt
import java.util.UUID
import org.apache.avro.Schema
import org.apache.avro.generic.GenericData
import org.apache.avro.generic.GenericRecord

@Suppress("LongParameterList")
object Fixture {

    fun getGenericRecordExportReceipt(
        warehouseId: String = "HF08",
        supplierName: String = "Igors Seasonal Harvest",
        poNumber: String = "2513IM432778_O1",
        casesReceived: Int = 0,
        quantityReceived: Double = 0.0,
        skuCode: String = "PHF-10-10378-4",
        skuName: String = "Spinach, Baby - 5 Ounce (oz)",
        scmWeekRaw: String = "202519",
        status: String = "Rejected",
        unit: String = "ea",
        market: String = "US",
        supplierCode: String = "40416",
        op: String = "c",
        tsMs: Long = System.currentTimeMillis(),
    ): List<GenericRecord> {
        val afterSchema = Schema.createRecord(
            "after",
            "",
            "com.hellofresh.oms.imt.exportReceipt",
            false
        )
        val afterFields = listOf(
            Schema.Field("wh_id", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("supplier_name", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("po_number", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("cases_received", Schema.create(Schema.Type.INT), "", 0),
            Schema.Field("quantity_received", Schema.create(Schema.Type.DOUBLE), "", 0.0),
            Schema.Field("sku_code", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("sku_name", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("scm_week_raw", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("status", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("unit", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("market", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("supplier_code", Schema.create(Schema.Type.STRING), "", "")
        )
        afterSchema.setFields(afterFields)

        val after = GenericData.Record(afterSchema)
        after.put("wh_id", warehouseId)
        after.put("supplier_name", supplierName)
        after.put("po_number", poNumber)
        after.put("cases_received", casesReceived)
        after.put("quantity_received", quantityReceived)
        after.put("sku_code", skuCode)
        after.put("sku_name", skuName)
        after.put("scm_week_raw", scmWeekRaw)
        after.put("status", status)
        after.put("unit", unit)
        after.put("market", market)
        after.put("supplier_code", supplierCode)

        val schema = Schema.createRecord(
            "ExportReceipt",
            "",
            "com.hellofresh.oms.imt.exportReceipt",
            false
        )
        val fields = listOf(
            Schema.Field("after", Schema.createUnion(Schema.create(Schema.Type.NULL), afterSchema), "", null),
            Schema.Field("before", Schema.createUnion(Schema.create(Schema.Type.NULL), afterSchema), "", null),
            Schema.Field("op", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("ts_ms", Schema.create(Schema.Type.LONG), "", 0L),
        )
        schema.setFields(fields)

        val record = GenericData.Record(schema)
        record.put("after", after)
        record.put("before", null)
        record.put("op", op)
        record.put("ts_ms", tsMs)

        return listOf(record)
    }

    fun getGenericRecordExportReceiptDelete(
        warehouseId: String = "HF08",
        supplierName: String = "Igors Seasonal Harvest",
        poNumber: String = "2513IM432778_O1",
        casesReceived: Int = 0,
        quantityReceived: Double = 0.0,
        skuCode: String = "PHF-10-10378-4",
        skuName: String = "Spinach, Baby - 5 Ounce (oz)",
        scmWeekRaw: String = "202519",
        status: String = "Rejected",
        unit: String = "ea",
        market: String = "US",
        supplierCode: String = "40416",
        tsMs: Long = System.currentTimeMillis(),
    ): List<GenericRecord> {
        val beforeSchema = Schema.createRecord(
            "before",
            "",
            "com.hellofresh.oms.imt.exportReceipt",
            false
        )
        val beforeFields = listOf(
            Schema.Field("wh_id", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("supplier_name", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("po_number", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("cases_received", Schema.create(Schema.Type.INT), "", 0),
            Schema.Field("quantity_received", Schema.create(Schema.Type.DOUBLE), "", 0.0),
            Schema.Field("sku_code", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("sku_name", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("scm_week_raw", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("status", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("unit", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("market", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("supplier_code", Schema.create(Schema.Type.STRING), "", "")
        )
        beforeSchema.setFields(beforeFields)

        val before = GenericData.Record(beforeSchema)
        before.put("wh_id", warehouseId)
        before.put("supplier_name", supplierName)
        before.put("po_number", poNumber)
        before.put("cases_received", casesReceived)
        before.put("quantity_received", quantityReceived)
        before.put("sku_code", skuCode)
        before.put("sku_name", skuName)
        before.put("scm_week_raw", scmWeekRaw)
        before.put("status", status)
        before.put("unit", unit)
        before.put("market", market)
        before.put("supplier_code", supplierCode)

        val schema = Schema.createRecord(
            "ExportReceipt",
            "",
            "com.hellofresh.oms.imt.exportReceipt",
            false
        )
        val fields = listOf(
            Schema.Field("after", Schema.createUnion(Schema.create(Schema.Type.NULL), beforeSchema), "", null),
            Schema.Field("before", Schema.createUnion(Schema.create(Schema.Type.NULL), beforeSchema), "", null),
            Schema.Field("op", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("ts_ms", Schema.create(Schema.Type.LONG), "", 0L),
        )
        schema.setFields(fields)

        val record = GenericData.Record(schema)
        record.put("after", null)
        record.put("before", before)
        record.put("op", "d")
        record.put("ts_ms", tsMs)

        return listOf(record)
    }

    fun getExportReceiptEntity(
        warehouseId: String = "HF08",
        supplierName: String = "Igors Seasonal Harvest",
        poNumber: String = "2513IM432778_O1",
        casesReceived: Int = 0,
        quantityReceived: Double = 0.0,
        skuCode: String = "PHF-10-10378-4",
        skuName: String = "Spinach, Baby - 5 Ounce (oz)",
        scmWeekRaw: String = "202519",
        status: String = "Rejected",
        unit: String = "ea",
        market: String = "US",
        supplierCode: String = "40416"
    ): ExportReceipt = ExportReceipt(
        id = UUID.randomUUID(),
        warehouseId = warehouseId,
        supplierName = supplierName,
        poNumber = poNumber,
        poReference = poNumber,
        casesReceived = casesReceived,
        quantityReceived = quantityReceived,
        skuCode = skuCode,
        skuName = skuName,
        yearWeek = calculateYearWeek(scmWeekRaw),
        status = status,
        unit = unit,
        market = market,
        supplierCode = supplierCode
    )
}
