package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer

import com.google.type.Date
import com.google.type.Decimal
import com.hellofresh.oms.model.SupplyQuantityRecommendationDaily
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@Suppress("LongParameterList")
object Fixture {

    fun getSupplyQuantityRecommendationDailyVal(
        skuId: String = UUID.randomUUID().toString(),
        forecastedDemanded: String = "10",
    ): SupplyQuantityRecommendationDailyVal =
        SupplyQuantityRecommendationDailyVal.newBuilder()
            .setSkuId(skuId)
            .setDcCode("GR")
            .setDate(Date.newBuilder().setYear(2023).setMonth(5).setDay(4).build())
            .setForecastedDemanded(
                SupplyQuantityRecommendationDailyVal.Quantity.newBuilder()
                    .setQty(Decimal.newBuilder().setValue(forecastedDemanded)),
            )
            .setProductionWeek(
                SupplyQuantityRecommendationDailyVal.ProductionWeek.newBuilder()
                    .setWeek(18)
                    .setYear(2023),
            )
            .build()

    fun getSupplyQuantityRecommendationDaily(
        skuId: UUID = UUID.randomUUID(),
        forecastedDemanded: Long = 10L,
    ) =
        SupplyQuantityRecommendationDaily(
            skuId = skuId,
            dcCode = "GR",
            supplyDate = LocalDate.of(2023, 5, 4),
            forecastedDemanded = BigDecimal.valueOf(forecastedDemanded),
            productionWeek = YearWeek(2023, 18),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )
}
