package com.hellofresh.oms.imt.comment

import com.hellofresh.oms.imt.comment.Fixture.getGenericRecordComment
import com.hellofresh.oms.imt.comment.consumer.CommentConsumer
import com.hellofresh.oms.imt.comment.service.CommentService
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class CommentConsumerTest {
    @Mock
    private lateinit var commentServiceMock: CommentService

    @Test
    fun `should call Comment service method when consuming message`() {
        val subject = CommentConsumer(commentServiceMock)

        subject.processImtComment().accept(
            object : Message<GenericRecord> {
                override fun getPayload(): GenericRecord = getGenericRecordComment()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        verify(commentServiceMock).process(any())
    }
}
