package com.hellofresh.oms.imt.receptOverride

import com.hellofresh.oms.imt.receptOverride.Fixture.getReceiptOverrideEntity
import com.hellofresh.oms.imt.receptOverride.repository.ReceiptOverrideRepository
import com.hellofresh.oms.imt.receptOverride.service.ReceiptOverrideService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class ReceiptOverrideServiceTest {
    @Mock
    lateinit var receiptOverrideRepositoryMock: ReceiptOverrideRepository

    @InjectMocks
    lateinit var service: ReceiptOverrideService

    @Test
    fun `should save new receipt override`() {
        // given
        val receiptOverrideEntity = getReceiptOverrideEntity()
        whenever(
            receiptOverrideRepositoryMock.save(receiptOverrideEntity)
        ).thenReturn(receiptOverrideEntity)

        // when
        service.process(receiptOverrideEntity)

        // then
        verify(receiptOverrideRepositoryMock).save(receiptOverrideEntity)
    }
}
