package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer

import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer.Fixture.getSupplyQuantityRecommendationDailyVal
import com.hellofresh.oms.model.YearWeek
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class SupplyQuantityRecommendationDailyMapperTest {

    @Test
    fun `should map SupplyQuantityRecommendationDailyVal to SupplyQuantityRecommendationDaily entity`() {
        val dailyVal = getSupplyQuantityRecommendationDailyVal()
        val entity = dailyVal.toEntity()

        assertEquals(entity.skuId, UUID.fromString(dailyVal.skuId))
        assertEquals(entity.dcCode, dailyVal.dcCode)
        assertEquals(entity.supplyDate, LocalDate.of(dailyVal.date.year, dailyVal.date.month, dailyVal.date.day))
        assertEquals(entity.forecastedDemanded, BigDecimal(dailyVal.forecastedDemanded.qty.value))
        assertEquals(entity.productionWeek, YearWeek(dailyVal.productionWeek.year, dailyVal.productionWeek.week))
    }
}
