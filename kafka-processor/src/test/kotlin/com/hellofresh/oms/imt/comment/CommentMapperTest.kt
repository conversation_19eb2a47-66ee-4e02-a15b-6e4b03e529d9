package com.hellofresh.oms.imt.comment

import com.hellofresh.oms.imt.comment.Fixture.getGenericRecordComment
import com.hellofresh.oms.imt.comment.consumer.epochMicrosToLocalDateTime
import com.hellofresh.oms.imt.comment.consumer.toComment
import com.hellofresh.oms.model.YearWeek
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class CommentMapperTest {

    @Test
    fun `should map GenericRecord to Comment entity`() {
        // given
        val id = UUID.randomUUID().toString()
        val resourceType = "sku"
        val resourceId = "PHF-10-10208-4"
        val domain = "imt"
        val dc = "NJ"
        val brand = "hellofresh"
        val week = "202420"
        val commentText = "Test comment"
        val createdBy = "<EMAIL>"
        val updatedBy = "<EMAIL>"
        val now = System.currentTimeMillis()
        val createdAt = now
        val updatedAt = now

        val genericRecord = getGenericRecordComment(
            id = id,
            resourceType = resourceType,
            resourceId = resourceId,
            domain = domain,
            dc = dc,
            brand = brand,
            week = week,
            comment = commentText,
            createdBy = createdBy,
            updatedBy = updatedBy,
            updatedAt = updatedAt
        )

        // when
        val entity = genericRecord.toComment()

        // then
        assertEquals(UUID.nameUUIDFromBytes(id.toByteArray()), entity.id)
        assertEquals(id, entity.sourceId)
        assertEquals(resourceType, entity.resourceType)
        assertEquals(resourceId, entity.resourceId)
        assertEquals(domain, entity.domain)
        assertEquals(dc, entity.dc)
        assertEquals(brand, entity.brand)
        assertEquals(commentText, entity.comment)
        assertEquals(createdBy, entity.createdBy)
        assertNotNull(entity.createdAt)
        assertEquals(updatedBy, entity.updatedBy)
        assertNotNull(entity.updatedAt)
    }

    @Test
    fun `should map GenericRecord with sourceId to Comment entity`() {
        // given
        val sourceId = "12345"
        val resourceType = "po"
        val resourceId = "PO-123"
        val domain = "gpp-po-status"
        val dc = "NJ"
        val brand = "hellofresh"
        val week = "202420"
        val entityWeek = YearWeek("2024", "20")
        val commentText = "Test comment with source ID"
        val createdBy = "<EMAIL>"
        val updatedBy = "<EMAIL>"
        val now = System.currentTimeMillis()
        val updatedAt = now

        val genericRecord = getGenericRecordComment(
            id = sourceId,
            sourceId = sourceId,
            resourceType = resourceType,
            resourceId = resourceId,
            domain = domain,
            dc = dc,
            brand = brand,
            week = week,
            comment = commentText,
            createdBy = createdBy,
            updatedBy = updatedBy,
            updatedAt = updatedAt * 1000
        )

        // when
        val entity = genericRecord.toComment()

        // then
        assertEquals(UUID.nameUUIDFromBytes(sourceId.toByteArray()), entity.id)
        assertEquals(sourceId, entity.sourceId)
        assertEquals(resourceType, entity.resourceType)
        assertEquals(resourceId, entity.resourceId)
        assertEquals(domain, entity.domain)
        assertEquals(dc, entity.dc)
        assertEquals(brand, entity.brand)
        assertEquals(entityWeek, entity.yearWeek)
        assertEquals(commentText, entity.comment)
        assertEquals(createdBy, entity.createdBy)
        assertNotNull(entity.createdAt)
        assertEquals(updatedBy, entity.updatedBy)
        assertEquals(entity.updatedAt, epochMicrosToLocalDateTime(now * 1000))
    }
}
