package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.integration

import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer.Fixture.getSupplyQuantityRecommendationDailyVal
import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.repository.SupplyQuantityRecommendationDailyRepository
import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

class SupplyQuantityRecommendationDailyTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var supplyQuantityRecommendationDailyRepository: SupplyQuantityRecommendationDailyRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, SupplyQuantityRecommendationDailyVal>

    @Value("\${topics.supply-quantity-recommendation-daily}")
    private lateinit var topic: String

    @BeforeEach
    fun setUp() {
        supplyQuantityRecommendationDailyRepository.deleteAll()
    }

    @Test
    fun `should consume and save daily recommendations`() {
        val skuId = UUID.randomUUID().toString()
        val dailyVal = getSupplyQuantityRecommendationDailyVal(skuId = skuId)

        publishMessage(
            listOf(
                getSupplyQuantityRecommendationDailyVal(skuId = skuId),
                dailyVal,
            ),
        )

        assertAwaiting {
            assertEquals(1, supplyQuantityRecommendationDailyRepository.count())

            val entity = supplyQuantityRecommendationDailyRepository.findAll().first()
            assertEquals(dailyVal.skuId, entity.skuId.toString())
            assertEquals(dailyVal.dcCode, entity.dcCode)
            assertEquals(
                LocalDate.of(dailyVal.date.year, dailyVal.date.month, dailyVal.date.day),
                entity.supplyDate,
            )
            assertEquals(BigDecimal(dailyVal.forecastedDemanded.qty.value).setScale(4), entity.forecastedDemanded)
            assertEquals(YearWeek(dailyVal.productionWeek.year, dailyVal.productionWeek.week), entity.productionWeek)
        }
    }

    private fun publishMessage(messages: List<SupplyQuantityRecommendationDailyVal>) {
        messages.forEach { message -> kafkaTemplate.send(topic, message) }
        kafkaTemplate.flush()
    }
}
