package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.repository

import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer.Fixture.getSupplyQuantityRecommendationDaily
import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import java.math.BigDecimal
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class SupplyQuantityRecommendationDailyBulkRepositoryTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var supplyQuantityRecommendationDailyBulkRepository:
        SupplyQuantityRecommendationDailyBulkRepository

    @Autowired
    private lateinit var supplyQuantityRecommendationDailyRepository:
        SupplyQuantityRecommendationDailyRepository

    @BeforeEach
    fun setUp() {
        supplyQuantityRecommendationDailyRepository.deleteAll()
    }

    @Test
    fun `should upsert multiple recommendations`() {
        val recommendations = listOf(getSupplyQuantityRecommendationDaily(), getSupplyQuantityRecommendationDaily())

        supplyQuantityRecommendationDailyBulkRepository.upsertAll(recommendations)

        assertEquals(2, supplyQuantityRecommendationDailyRepository.count())
    }

    @Test
    fun `should update existing recommendation`() {
        val skuId = UUID.randomUUID()

        supplyQuantityRecommendationDailyBulkRepository.upsertAll(
            listOf(
                getSupplyQuantityRecommendationDaily(skuId = skuId, forecastedDemanded = 10),
                getSupplyQuantityRecommendationDaily(skuId = skuId, forecastedDemanded = 20),
            ),
        )
        supplyQuantityRecommendationDailyBulkRepository.upsertAll(
            listOf(getSupplyQuantityRecommendationDaily(skuId = skuId, forecastedDemanded = 30)),
        )

        assertEquals(1, supplyQuantityRecommendationDailyRepository.count())
        assertEquals(
            BigDecimal(30).setScale(4),
            supplyQuantityRecommendationDailyRepository.findAll().first().forecastedDemanded
        )
    }
}
