package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.repository

import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer.Fixture.getSupplyQuantityRecommendationDaily
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate

@ExtendWith(MockitoExtension::class)
class SupplyQuantityRecommendationDailyBulkRepositoryImplTest {

    @Mock
    private lateinit var jt: NamedParameterJdbcTemplate

    @InjectMocks
    private lateinit var supplyQuantityRecommendationDailyBulkRepository:
        SupplyQuantityRecommendationDailyBulkRepositoryImpl

    @Test
    fun `should upsert records`() {
        supplyQuantityRecommendationDailyBulkRepository.upsertAll(
            listOf(
                getSupplyQuantityRecommendationDaily(),
                getSupplyQuantityRecommendationDaily()
            )
        )

        verify(jt).batchUpdate(any(), any<Array<MapSqlParameterSource>>())
    }
}
