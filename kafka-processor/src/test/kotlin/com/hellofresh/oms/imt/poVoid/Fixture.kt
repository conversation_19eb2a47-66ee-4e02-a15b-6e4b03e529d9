package com.hellofresh.oms.imt.poVoid

import com.hellofresh.oms.model.imt.poVoid.PoVoid
import java.time.LocalDateTime
import java.util.UUID
import org.apache.avro.Schema
import org.apache.avro.generic.GenericData
import org.apache.avro.generic.GenericRecord

@Suppress("LongParameterList")
object Fixture {

    fun getGenericRecordPoVoid(
        poNumber: String = "2215NJ300005",
        week: String = "2024-W20",
        brand: String = "hellofresh",
        dc: String = "NJ",
        skuCode: String = "PHF-10-10208-4",
        supplierName: String = "Test Supplier",
        user: String = "testuser",
        source: String = "IMT",
        comment: String? = "Test comment",
        market: String = "us",
        createTimestamp: Long = System.currentTimeMillis(),
        lastUpdated: Long? = System.currentTimeMillis(),
        deletedBy: String? = null
    ): GenericRecord {
        val afterSchema = Schema.createRecord(
            "after",
            "",
            "com.hellofresh.oms.imt.poVoid",
            false
        )
        val afterFields = listOf(
            Schema.Field("po_number", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("id", Schema.create(Schema.Type.LONG), "", 0L),
            Schema.Field("week", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("brand", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("dc", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("sku_code", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("supplier_name", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("user", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("source", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("comment", Schema.create(Schema.Type.STRING), "", null),
            Schema.Field("market", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("create_timestamp", Schema.create(Schema.Type.LONG), "", 0L),
            Schema.Field("last_updated", Schema.create(Schema.Type.LONG), "", null),
            Schema.Field("deleted_by", Schema.create(Schema.Type.STRING), "", null),
            Schema.Field("cre_tmst", Schema.create(Schema.Type.LONG), "", null)
        )
        afterSchema.setFields(afterFields)

        val after = GenericData.Record(afterSchema)
        after.put("po_number", poNumber)
        after.put("id", 0L)
        after.put("cre_tmst", 0L)
        after.put("week", week)
        after.put("brand", brand)
        after.put("dc", dc)
        after.put("sku_code", skuCode)
        after.put("supplier_name", supplierName)
        after.put("user", user)
        after.put("source", source)
        after.put("comment", comment)
        after.put("market", market)
        after.put("create_timestamp", createTimestamp)
        after.put("last_updated", lastUpdated)
        after.put("deleted_by", deletedBy)

        val schema = Schema.createRecord(
            "PoVoid",
            "",
            "com.hellofresh.oms.imt.poVoid",
            false
        )
        val fields = listOf(
            Schema.Field("after", afterSchema, "", null)
        )
        schema.setFields(fields)

        val record = GenericData.Record(schema)
        record.put("after", after)

        return record
    }

    fun getPoVoidEntity(
        id: UUID = UUID.randomUUID(),
        poNumber: String = "2215NJ300005",
        week: String = "2024-W20",
        brand: String = "hellofresh",
        dc: String = "NJ",
        skuCode: String = "PHF-10-10208-4",
        supplierName: String = "Test Supplier",
        user: String = "testuser",
        source: String = "IMT",
        comment: String? = "Test comment",
        market: String = "us",
        createTimestamp: LocalDateTime = LocalDateTime.now(),
        lastUpdated: LocalDateTime? = LocalDateTime.now(),
        deletedBy: String? = null
    ): PoVoid = PoVoid(
        id = id,
        poNumber = poNumber,
        week = week,
        brand = brand,
        dc = dc,
        skuCode = skuCode,
        supplierName = supplierName,
        userEmail = user,
        source = source,
        comment = comment,
        createAt = createTimestamp,
        lastUpdated = lastUpdated,
        deletedBy = deletedBy,
        market = market,
        poReference = poNumber,
    )
}
