package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer

import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer.Fixture.getSupplyQuantityRecommendationDailyVal
import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.service.SupplyQuantityRecommendationDailyService
import com.hellofresh.oms.model.SupplyQuantityRecommendationDaily
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor

@ExtendWith(MockitoExtension::class)
class SupplyQuantityRecommendationDailyConsumerTest {

    @Mock
    private lateinit var supplyQuantityRecommendationDailyService: SupplyQuantityRecommendationDailyService

    @Test
    fun `should call SupplyQuantityRecommendationDaily service method when consuming message`() {
        val subject = SupplyQuantityRecommendationDailyConsumer(supplyQuantityRecommendationDailyService)

        subject.processSupplyQuantityRecommendationDaily()
            .accept(listOf(getSupplyQuantityRecommendationDailyVal()))

        verify(supplyQuantityRecommendationDailyService).process(any())
    }

    @Test
    fun `should not process SupplyQuantityRecommendationDaily with 0 forecast`() {
        val subject = SupplyQuantityRecommendationDailyConsumer(supplyQuantityRecommendationDailyService)

        subject.processSupplyQuantityRecommendationDaily()
            .accept(listOf(getSupplyQuantityRecommendationDailyVal(forecastedDemanded = "0")))

        val captor = argumentCaptor<List<SupplyQuantityRecommendationDaily>>()
        verify(supplyQuantityRecommendationDailyService).process(captor.capture())
        assertTrue(captor.firstValue.isEmpty())
    }
}
