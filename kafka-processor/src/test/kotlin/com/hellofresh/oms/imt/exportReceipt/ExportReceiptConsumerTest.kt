package com.hellofresh.oms.imt.exportReceipt

import com.hellofresh.oms.imt.exportReceipt.Fixture.getGenericRecordExportReceipt
import com.hellofresh.oms.imt.exportReceipt.Fixture.getGenericRecordExportReceiptDelete
import com.hellofresh.oms.imt.exportReceipt.consumer.ExportReceiptConsumer
import com.hellofresh.oms.imt.exportReceipt.service.ExportReceiptService
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.doThrow
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.verifyNoInteractions
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class ExportReceiptConsumerTest {
    @Mock
    private lateinit var exportReceiptServiceMock: ExportReceiptService

    @Test
    fun `should call ExportReceipt service save method when consuming create message`() {
        // given
        val subject = ExportReceiptConsumer(exportReceiptServiceMock)

        // when
        subject.processImtExportReceipt().accept(
            object : Message<List<GenericRecord>> {
                override fun getPayload(): List<GenericRecord> = getGenericRecordExportReceipt(op = "c")

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        verify(exportReceiptServiceMock).save(any())
    }

    @Test
    fun `should call ExportReceipt service delete method when consuming delete message`() {
        // given
        val subject = ExportReceiptConsumer(exportReceiptServiceMock)

        // when
        subject.processImtExportReceipt().accept(
            object : Message<List<GenericRecord>> {
                override fun getPayload(): List<GenericRecord> = getGenericRecordExportReceiptDelete()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        verify(exportReceiptServiceMock).delete(any())
    }

    @Test
    fun `should skip records with po_number before 2025`() {
        val subject = ExportReceiptConsumer(exportReceiptServiceMock)
        val record = getGenericRecordExportReceipt(op = "c", poNumber = "240001")
        val message = object : Message<List<GenericRecord>> {
            override fun getPayload(): List<GenericRecord> = record
            override fun getHeaders(): MessageHeaders = MessageHeaders(emptyMap<String, Any>())
        }
        subject.processImtExportReceipt().accept(message)
        verifyNoInteractions(exportReceiptServiceMock)
    }

    @Test
    fun `should batch save and delete when multiple records are present`() {
        val subject = ExportReceiptConsumer(exportReceiptServiceMock)
        val createRecord = getGenericRecordExportReceipt(op = "c", poNumber = "250001")[0]
        val deleteRecord = getGenericRecordExportReceiptDelete(poNumber = "250002")[0]
        val message = object : Message<List<GenericRecord>> {
            override fun getPayload(): List<GenericRecord> = listOf(createRecord, deleteRecord)
            override fun getHeaders(): MessageHeaders = MessageHeaders(emptyMap<String, Any>())
        }
        subject.processImtExportReceipt().accept(message)
        verify(exportReceiptServiceMock).save(any())
        verify(exportReceiptServiceMock).delete(any())
    }

    @Test
    fun `should log and throw exception when message processing fails`() {
        val subject = ExportReceiptConsumer(exportReceiptServiceMock)
        doThrow(RuntimeException("Simulated error")).`when`(exportReceiptServiceMock).save(any())
        val record = getGenericRecordExportReceipt(op = "c", poNumber = "250001")
        val message = object : Message<List<GenericRecord>> {
            override fun getPayload(): List<GenericRecord> = record
            override fun getHeaders(): MessageHeaders = MessageHeaders(emptyMap<String, Any>())
        }
        assertThrows<RuntimeException> {
            subject.processImtExportReceipt().accept(message)
        }
    }

    @Test
    fun `should log and throw exception when record parsing fails`() {
        val subject = ExportReceiptConsumer(exportReceiptServiceMock)
        val brokenRecord = getGenericRecordExportReceipt(op = "c", poNumber = "250001")[0]
        brokenRecord.put("op", null)
        val message = object : Message<List<GenericRecord>> {
            override fun getPayload(): List<GenericRecord> = listOf(brokenRecord)
            override fun getHeaders(): MessageHeaders = MessageHeaders(emptyMap<String, Any>())
        }
        assertThrows<NullPointerException> {
            subject.processImtExportReceipt().accept(message)
        }
    }

    @Test
    fun `should skip KafkaNull and null records`() {
        val subject = ExportReceiptConsumer(exportReceiptServiceMock)
        val kafkaNullRecord = null
        val message = object : Message<List<GenericRecord?>> {
            override fun getPayload(): List<GenericRecord?> = listOf(kafkaNullRecord)
            override fun getHeaders(): MessageHeaders = MessageHeaders(emptyMap<String, Any>())
        }
        subject.processImtExportReceipt().accept(message as Message<List<GenericRecord>>)
        verifyNoInteractions(exportReceiptServiceMock)
    }
}
