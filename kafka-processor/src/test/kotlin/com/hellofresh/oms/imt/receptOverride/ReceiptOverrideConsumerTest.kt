package com.hellofresh.oms.imt.receptOverride

import com.hellofresh.oms.imt.receptOverride.Fixture.getGenericRecordReceiptOverride
import com.hellofresh.oms.imt.receptOverride.consumer.ReceiptOverrideConsumer
import com.hellofresh.oms.imt.receptOverride.service.ReceiptOverrideService
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class ReceiptOverrideConsumerTest {
    @Mock
    private lateinit var receiptOverrideServiceMock: ReceiptOverrideService

    @Test
    fun `should call ReceiptOverride service method when consuming message`() {
        // given
        val subject = ReceiptOverrideConsumer(receiptOverrideServiceMock)

        // when
        subject.processImtReceiptOverride().accept(
            object : Message<GenericRecord> {
                override fun getPayload(): GenericRecord = getGenericRecordReceiptOverride()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        verify(receiptOverrideServiceMock).process(any())
    }
}
