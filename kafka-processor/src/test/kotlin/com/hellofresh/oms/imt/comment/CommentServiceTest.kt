package com.hellofresh.oms.imt.comment

import com.hellofresh.oms.imt.comment.Fixture.getCommentEntity
import com.hellofresh.oms.imt.comment.repository.CommentRepository
import com.hellofresh.oms.imt.comment.service.CommentService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class CommentServiceTest {
    @Mock
    lateinit var commentRepositoryMock: CommentRepository

    @InjectMocks
    lateinit var service: CommentService

    @Test
    fun `should save new comment`() {
        // given
        val commentEntity = getCommentEntity()
        whenever(
            commentRepositoryMock.save(commentEntity)
        ).thenReturn(commentEntity)

        // when
        service.process(commentEntity)

        // then
        verify(commentRepositoryMock).save(commentEntity)
    }
}
