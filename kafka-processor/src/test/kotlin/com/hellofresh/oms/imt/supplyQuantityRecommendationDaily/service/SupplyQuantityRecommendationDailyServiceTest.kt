package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.service

import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.repository.SupplyQuantityRecommendationDailyBulkRepository
import com.hellofresh.oms.model.SupplyQuantityRecommendationDaily
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.verify

@ExtendWith(MockitoExtension::class)
class SupplyQuantityRecommendationDailyServiceTest {

    @Mock
    lateinit var supplyQuantityRecommendationDailyBulkRepository: SupplyQuantityRecommendationDailyBulkRepository

    @InjectMocks
    lateinit var service: SupplyQuantityRecommendationDailyService

    @Test
    fun `should save new supply quantity recommendation daily`() {
        val entities = listOf(mock(SupplyQuantityRecommendationDaily::class.java))
        service.process(entities)

        verify(supplyQuantityRecommendationDailyBulkRepository).upsertAll(entities)
    }
}
