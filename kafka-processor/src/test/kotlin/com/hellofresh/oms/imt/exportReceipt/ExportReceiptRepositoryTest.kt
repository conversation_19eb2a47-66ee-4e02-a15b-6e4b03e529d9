package com.hellofresh.oms.imt.exportReceipt

import com.hellofresh.oms.imt.exportReceipt.repository.ExportReceiptRepository
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest

@DataJpaTest
@AutoConfigureEmbeddedDatabase(
    type = AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES,
    provider = AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
)
class ExportReceiptRepositoryTest(
    @Autowired private val exportReceiptRepository: ExportReceiptRepository
) {

    @Test
    fun `should save new entity`() {
        val supplierSkuPriceEntity = Fixture.getExportReceiptEntity()

        exportReceiptRepository.save(supplierSkuPriceEntity)

        val inserted = exportReceiptRepository.findAll()
        assertEquals(1, inserted.size)
    }

    @Test
    fun `should save new entity and then update it`() {
        // given
        val updateQuantity = 122.0
        val supplierSkuPriceEntity = Fixture.getExportReceiptEntity()

        // when
        exportReceiptRepository.save(supplierSkuPriceEntity)
        val updatedEntity = supplierSkuPriceEntity.copy(quantityReceived = updateQuantity)
        exportReceiptRepository.save(updatedEntity)
        // then
        val inserted = exportReceiptRepository.findAll()
        assertEquals(1, inserted.size)
        assertEquals(updateQuantity, inserted[0].quantityReceived)
    }
}
