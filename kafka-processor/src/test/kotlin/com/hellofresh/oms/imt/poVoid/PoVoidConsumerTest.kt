package com.hellofresh.oms.imt.poVoid

import com.hellofresh.oms.imt.poVoid.Fixture.getGenericRecordPoVoid
import com.hellofresh.oms.imt.poVoid.consumer.PoVoidConsumer
import com.hellofresh.oms.imt.poVoid.service.PoVoidService
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class PoVoidConsumerTest {
    @Mock
    private lateinit var poVoidServiceMock: PoVoidService

    @Test
    fun `should call PoVoid service method when consuming message`() {
        val subject = PoVoidConsumer(poVoidServiceMock)

        subject.processImtPoVoid().accept(
            object : Message<GenericRecord> {
                override fun getPayload(): GenericRecord = getGenericRecordPoVoid()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        verify(poVoidServiceMock).process(any())
    }
}
