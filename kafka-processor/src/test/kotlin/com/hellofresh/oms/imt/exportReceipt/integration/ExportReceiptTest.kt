package com.hellofresh.oms.imt.exportReceipt.integration

import com.hellofresh.oms.imt.exportReceipt.Fixture.getGenericRecordExportReceipt
import com.hellofresh.oms.imt.exportReceipt.Fixture.getGenericRecordExportReceiptDelete
import com.hellofresh.oms.imt.exportReceipt.consumer.generateUuidBytes
import com.hellofresh.oms.imt.exportReceipt.repository.ExportReceiptRepository
import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.apache.avro.generic.GenericRecord
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

@Tag("integration")
@Suppress("LongMethod")
class ExportReceiptTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var exportReceiptRepository: ExportReceiptRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, GenericRecord>

    @Value("\${topics.imt-om-export-receipts}")
    private lateinit var topic: String

    @BeforeEach
    fun setUp() {
        exportReceiptRepository.deleteAll()
    }

    @Test
    fun `should consume export receipt and save with generated UUID`() {
        // given
        val warehouseId = "HF08"
        val supplierName = "Igors Seasonal Harvest"
        val poNumber = "2513IM432778_O1"
        val casesReceived = 5
        val quantityReceived = 10.5
        val skuCode = "PHF-10-10378-4"
        val skuName = "Spinach, Baby - 5 Ounce (oz)"
        val scmWeekRaw = "202519"
        val status = "Received"
        val unit = "ea"
        val market = "US"
        val supplierCode = "40416"

        val genericRecord = getGenericRecordExportReceipt(
            warehouseId = warehouseId,
            supplierName = supplierName,
            poNumber = poNumber,
            casesReceived = casesReceived,
            quantityReceived = quantityReceived,
            skuCode = skuCode,
            skuName = skuName,
            scmWeekRaw = scmWeekRaw,
            status = status,
            unit = unit,
            market = market,
            supplierCode = supplierCode,
            op = "c"
        )

        // Calculate expected UUID
        val expectedUuid = UUID.nameUUIDFromBytes(
            generateUuidBytes(genericRecord[0]["after"] as GenericRecord)
        )

        // when
        publishMessage(genericRecord)

        // then
        assertAwaiting {
            val persistedReceipts = exportReceiptRepository.findAll()
            assertEquals(1, persistedReceipts.size)

            val savedReceipt = persistedReceipts.first()
            assertEquals(expectedUuid, savedReceipt.id)
            assertEquals(warehouseId, savedReceipt.warehouseId)
            assertEquals(supplierName, savedReceipt.supplierName)
            assertEquals(poNumber.split('_').first(), savedReceipt.poNumber)
            assertEquals(poNumber, savedReceipt.poReference)
            assertEquals(casesReceived, savedReceipt.casesReceived)
            assertEquals(quantityReceived, savedReceipt.quantityReceived)
            assertEquals(skuCode, savedReceipt.skuCode)
            assertEquals(skuName, savedReceipt.skuName)
            assertEquals(status, savedReceipt.status)
            assertEquals(unit, savedReceipt.unit)
            assertEquals(market, savedReceipt.market)
            assertEquals(supplierCode, savedReceipt.supplierCode)
        }
    }

    @Test
    fun `should generate same UUID for same record data`() {
        // given
        val recordData = mapOf(
            "warehouseId" to "HF08",
            "supplierName" to "Test Supplier",
            "poNumber" to "2513IM432778_O1",
            "casesReceived" to 3,
            "quantityReceived" to 7.5,
            "skuCode" to "PHF-10-10378-4",
            "skuName" to "Test SKU",
            "scmWeekRaw" to "202519",
            "status" to "Received",
            "unit" to "ea",
            "market" to "US",
            "supplierCode" to "40416"
        )

        val firstRecord = getGenericRecordExportReceipt(
            warehouseId = recordData["warehouseId"] as String,
            supplierName = recordData["supplierName"] as String,
            poNumber = recordData["poNumber"] as String,
            casesReceived = recordData["casesReceived"] as Int,
            quantityReceived = recordData["quantityReceived"] as Double,
            skuCode = recordData["skuCode"] as String,
            skuName = recordData["skuName"] as String,
            scmWeekRaw = recordData["scmWeekRaw"] as String,
            status = recordData["status"] as String,
            unit = recordData["unit"] as String,
            market = recordData["market"] as String,
            supplierCode = recordData["supplierCode"] as String,
            op = "c"
        )

        val secondRecord = getGenericRecordExportReceipt(
            warehouseId = recordData["warehouseId"] as String,
            supplierName = recordData["supplierName"] as String,
            poNumber = recordData["poNumber"] as String,
            casesReceived = recordData["casesReceived"] as Int,
            quantityReceived = recordData["quantityReceived"] as Double,
            skuCode = recordData["skuCode"] as String,
            skuName = recordData["skuName"] as String,
            scmWeekRaw = recordData["scmWeekRaw"] as String,
            status = recordData["status"] as String,
            unit = recordData["unit"] as String,
            market = recordData["market"] as String,
            supplierCode = recordData["supplierCode"] as String,
            op = "c"
        )

        // when
        val firstUuid = UUID.nameUUIDFromBytes(
            generateUuidBytes(firstRecord[0]["after"] as GenericRecord)
        )
        val secondUuid = UUID.nameUUIDFromBytes(
            generateUuidBytes(secondRecord[0]["after"] as GenericRecord)
        )

        // then
        assertEquals(firstUuid, secondUuid, "Same record data should generate the same UUID")
    }

    @Test
    fun `should insert record then delete it using same UUID for same record data`() {
        // given
        val warehouseId = "HF08"
        val supplierName = "Test Supplier"
        val poNumber = "2513IM432778_O1"
        val casesReceived = 3
        val quantityReceived = 7.5
        val skuCode = "PHF-10-10378-4"
        val skuName = "Test SKU"
        val scmWeekRaw = "202519"
        val status = "Received"
        val unit = "ea"
        val market = "US"
        val supplierCode = "40416"

        val insertRecord = getGenericRecordExportReceipt(
            warehouseId = warehouseId,
            supplierName = supplierName,
            poNumber = poNumber,
            casesReceived = casesReceived,
            quantityReceived = quantityReceived,
            skuCode = skuCode,
            skuName = skuName,
            scmWeekRaw = scmWeekRaw,
            status = status,
            unit = unit,
            market = market,
            supplierCode = supplierCode,
            op = "c"
        )

        val deleteRecord = getGenericRecordExportReceiptDelete(
            warehouseId = warehouseId,
            supplierName = supplierName,
            poNumber = poNumber,
            casesReceived = casesReceived,
            quantityReceived = quantityReceived,
            skuCode = skuCode,
            skuName = skuName,
            scmWeekRaw = scmWeekRaw,
            status = status,
            unit = unit,
            market = market,
            supplierCode = supplierCode
        )

        val insertUuid = UUID.nameUUIDFromBytes(
            generateUuidBytes(insertRecord[0]["after"] as GenericRecord)
        )
        val deleteUuid = UUID.nameUUIDFromBytes(
            generateUuidBytes(deleteRecord[0]["before"] as GenericRecord)
        )

        assertEquals(insertUuid, deleteUuid, "Insert and delete should generate the same UUID for same data")

        publishMessage(insertRecord)

        assertAwaiting {
            val persistedReceipts = exportReceiptRepository.findAll()
            assertEquals(1, persistedReceipts.size)
            assertEquals(insertUuid, persistedReceipts.first().id)
        }

        publishMessage(deleteRecord)

        assertAwaiting {
            val persistedReceipts = exportReceiptRepository.findAll()
            assertTrue(persistedReceipts.isEmpty(), "Record should be deleted")
        }
    }

    @Test
    fun `should handle multiple records with different UUIDs`() {
        // given
        val firstRecord = getGenericRecordExportReceipt(
            warehouseId = "HF08",
            poNumber = "2513IM432778_O1",
            skuCode = "PHF-10-10378-4",
            op = "c"
        )

        val secondRecord = getGenericRecordExportReceipt(
            warehouseId = "HF08",
            poNumber = "2513IM432779_O1", // Different PO number
            skuCode = "PHF-10-10378-4",
            op = "c"
        )

        val firstUuid = UUID.nameUUIDFromBytes(
            generateUuidBytes(firstRecord[0]["after"] as GenericRecord)
        )
        val secondUuid = UUID.nameUUIDFromBytes(
            generateUuidBytes(secondRecord[0]["after"] as GenericRecord)
        )

        // Verify UUIDs are different
        assertTrue(firstUuid != secondUuid, "Different record data should generate different UUIDs")

        // when
        publishMessage(firstRecord)
        publishMessage(secondRecord)

        // then
        assertAwaiting {
            val persistedReceipts = exportReceiptRepository.findAll()
            assertEquals(2, persistedReceipts.size)

            val persistedUuids = persistedReceipts.map { it.id }.toSet()
            assertThat(persistedUuids, equalTo(setOf(firstUuid, secondUuid)))
        }
    }

    @Test
    fun `should batch insert and delete export receipts in one message`() {
        val poNumber1 = "2513IM432778_O1"
        val poNumber2 = "2513IM432779_O1"
        val record1 = getGenericRecordExportReceipt(poNumber = poNumber1, op = "c")[0]
        val record2 = getGenericRecordExportReceipt(poNumber = poNumber2, op = "c")[0]
        val deleteRecord = getGenericRecordExportReceiptDelete(poNumber = poNumber1)[0]
        val batch = listOf(record1, record2, deleteRecord)
        publishMessage(batch)
        assertAwaiting {
            val receipts = exportReceiptRepository.findAll()
            assertEquals(1, receipts.size)
            assertEquals(poNumber2.split('_').first(), receipts[0].poNumber)
        }
    }

    @Test
    fun `should skip records with po_number before 2025 in integration`() {
        val oldPo = "2413IM432778_O1"
        val record = getGenericRecordExportReceipt(poNumber = oldPo, op = "c")[0]
        publishMessage(listOf(record))
        assertAwaiting {
            val receipts = exportReceiptRepository.findAll()
            assertTrue(receipts.isEmpty())
        }
    }

    @Test
    fun `should only keep the last operation for the same record in a batch`() {
        val poNumber = "2513IM432780_O1"
        val recordInsert1 = getGenericRecordExportReceipt(poNumber = poNumber, op = "c")[0]
        val recordDelete = getGenericRecordExportReceiptDelete(poNumber = poNumber)[0]
        val recordInsert2 = getGenericRecordExportReceipt(poNumber = poNumber, op = "c")[0]
        val recordDelete2 = getGenericRecordExportReceiptDelete(poNumber = poNumber)[0]
        val batch = listOf(recordInsert1, recordDelete, recordInsert2, recordDelete2)
        publishMessage(batch)
        assertAwaiting {
            val receipts = exportReceiptRepository.findAll()
            assertTrue(
                receipts.isEmpty(),
                "Only the last operation (delete) should be effective, so no record should remain"
            )
        }
        val batch2 = listOf(recordInsert1, recordDelete, recordInsert2)
        publishMessage(batch2)
        assertAwaiting {
            val receipts = exportReceiptRepository.findAll()
            assertEquals(1, receipts.size)
            assertEquals(poNumber.split('_').first(), receipts[0].poNumber)
        }
    }

    private fun publishMessage(message: List<GenericRecord>) {
        message.forEach { record ->
            kafkaTemplate.send(topic, record)
        }
        kafkaTemplate.flush()
    }
}
