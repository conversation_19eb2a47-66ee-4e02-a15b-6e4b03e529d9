package com.hellofresh.oms.imt.receptOverride

import com.hellofresh.oms.model.imt.recipeOverride.ReceiptOverride
import java.time.LocalDateTime
import org.apache.avro.Schema
import org.apache.avro.generic.GenericData
import org.apache.avro.generic.GenericRecord

@Suppress("LongParameterList")
object Fixture {

    fun getGenericRecordReceiptOverride(
        poNumber: String = "2215NJ300005",
        week: String = "2024-W20",
        brand: String = "hellofresh",
        dc: String = "NJ",
        skuCode: String = "PHF-10-10208-4",
        user: String = "testuser",
        source: String = "IMT",
        quantity: Int = 10,
        market: String = "us",
        cases: Int = 5,
        comment: String? = "Test comment",
        receivingDate: Long? = System.currentTimeMillis(),
        createdTimestamp: Long = System.currentTimeMillis(),
        updatedTimestamp: Long = System.currentTimeMillis(),
        deletedBy: String? = null,
        deletedTimestamp: Long? = null
    ): GenericRecord {
        val afterSchema = Schema.createRecord(
            "after",
            "",
            "com.hellofresh.oms.imt.receptOverride",
            false
        )
        val afterFields = listOf(
            Schema.Field("po_number", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("week", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("brand", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("dc", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("sku_code", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("user", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("source", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("qty", Schema.create(Schema.Type.INT), "", 0),
            Schema.Field("market", Schema.create(Schema.Type.STRING), "", ""),
            Schema.Field("cases", Schema.create(Schema.Type.INT), "", null),
            Schema.Field("comment", Schema.create(Schema.Type.STRING), "", null),
            Schema.Field("receiving_date", Schema.create(Schema.Type.LONG), "", null),
            Schema.Field("cre_tmst", Schema.create(Schema.Type.LONG), "", 0L),
            Schema.Field("upd_tmst", Schema.create(Schema.Type.LONG), "", 0L),
            Schema.Field("deleted_by", Schema.create(Schema.Type.STRING), "", null),
            Schema.Field("deleted_ts", Schema.create(Schema.Type.LONG), "", null)
        )
        afterSchema.setFields(afterFields)

        val after = GenericData.Record(afterSchema)
        after.put("po_number", poNumber)
        after.put("week", week)
        after.put("brand", brand)
        after.put("dc", dc)
        after.put("sku_code", skuCode)
        after.put("user", user)
        after.put("source", source)
        after.put("qty", quantity)
        after.put("market", market)
        after.put("cases", cases)
        after.put("comment", comment)
        after.put("receiving_date", receivingDate)
        after.put("cre_tmst", createdTimestamp)
        after.put("upd_tmst", updatedTimestamp)
        after.put("deleted_by", deletedBy)
        after.put("deleted_ts", deletedTimestamp)

        val schema = Schema.createRecord(
            "ReceiptOverride",
            "",
            "com.hellofresh.oms.imt.receptOverride",
            false
        )
        val fields = listOf(
            Schema.Field("after", afterSchema, "", null)
        )
        schema.setFields(fields)

        val record = GenericData.Record(schema)
        record.put("after", after)

        return record
    }

    fun getReceiptOverrideEntity(
        poNumber: String = "2215NJ300005",
        week: String = "2024-W20",
        brand: String = "hellofresh",
        dc: String = "NJ",
        skuCode: String = "PHF-10-10208-4",
        user: String = "testuser",
        source: String = "IMT",
        quantity: Int = 10,
        market: String = "us",
        cases: Int = 5,
        comment: String? = "Test comment",
        receivingDate: LocalDateTime? = LocalDateTime.now(),
        createdTimestamp: LocalDateTime = LocalDateTime.now(),
        updatedTimestamp: LocalDateTime = LocalDateTime.now(),
        deletedBy: String? = null,
        deletedTimestamp: LocalDateTime? = null
    ): ReceiptOverride = ReceiptOverride(
        hfWeek = week,
        brand = brand,
        dc = dc,
        poNumber = poNumber,
        skuCode = skuCode,
        userEmail = user,
        source = source,
        quantity = quantity,
        updatedAt = updatedTimestamp,
        createdAt = createdTimestamp,
        deletedBy = deletedBy,
        deletedAt = deletedTimestamp,
        market = market,
        cases = cases,
        comment = comment,
        receivingDate = receivingDate,
        poReference = poNumber,
    )
}
