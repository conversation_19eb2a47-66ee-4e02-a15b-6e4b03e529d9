package com.hellofresh.oms.purchaseOrder.integtation

import com.hellofresh.oms.ChangeReasonRepository
import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.model.ChangeReason
import com.hellofresh.oms.model.ChangeReasonType
import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.POType.EMERGENCY
import com.hellofresh.oms.model.POType.STANDARD
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.PurchaseOrderStatus.DELETED
import com.hellofresh.oms.model.PurchaseOrderStatus.INITIATED
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.purchaseOrder.Fixture.getProtoOrder
import com.hellofresh.oms.purchaseOrder.Fixture.getUnitItem
import com.hellofresh.oms.purchaseOrder.repository.PurchaseOrderRepository
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder as ProtoPurchaseOrder
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State as ProtoPoState
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType.PURCHASE_ORDER_TYPE_STANDARD
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate

class PurchaseOrderTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var purchaseOrderRepository: PurchaseOrderRepository

    @Autowired
    private lateinit var changeReasonRepository: ChangeReasonRepository

    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, ProtoPurchaseOrder>

    @Value("\${topics.purchase-order-v1}")
    private lateinit var topics: String

    @BeforeEach
    fun setUp() {
        purchaseOrderRepository.deleteAll()
        changeReasonRepository.deleteAll()
        changeReasonRepository.save(
            ChangeReason(
                id = CHANGE_REASON_ID,
                name = "Change Reason",
                reasonType = ChangeReasonType.ORDER_ITEM_CHANGE,
                allowedMarkets = emptyList(),
            ),
        )
    }

    @Test
    fun `should CREATE new order`() {
        // given
        val givenPoNumber = "PO-0023"

        // when
        publishMessage(
            getProtoOrder(
                orderNumber = givenPoNumber,
            ),
        )

        // then
        assertAwaiting {
            val allPurchaseOrdersMap = purchaseOrderRepository.findAll().map { it.poNumber }
            assertThat(allPurchaseOrdersMap, equalTo(listOf(givenPoNumber)))
        }
    }

    @Test
    fun `should store new order with bumped up version for the same order number`() {
        // given
        val oldVersionId = UUID.randomUUID()
        val persistedOrder = getPurchaseOrderEntity(
            id = oldVersionId,
            poNumber = "PO-1234",
            version = 2,
            emergencyReasonId = CHANGE_REASON_ID,
            type = STANDARD,
        ).let {
            purchaseOrderRepository.save(it)
        }
        val newVersionId = UUID.randomUUID()
        val protoOrder = getProtoOrder(
            orderId = newVersionId,
            orderNumber = persistedOrder.poNumber,
            orderVersion = persistedOrder.version + 1,
            orderType = PURCHASE_ORDER_TYPE_STANDARD,
        )

        // when
        publishMessage(
            protoOrder,
        )

        // then
        assertAwaiting {
            val allPurchaseOrdersMap = purchaseOrderRepository.findAll().map { it.id to it.version }
            assertThat(allPurchaseOrdersMap, equalTo(listOf(oldVersionId to 2, newVersionId to 3)))
        }
    }

    @Test
    fun `should soft DELETE when incoming status is delete`() {
        // given
        val persistedOrder = getPurchaseOrderEntity(
            poNumber = "PO-5845",
            version = 2,
            emergencyReasonId = CHANGE_REASON_ID,
            type = STANDARD,
        )

        purchaseOrderRepository.save(persistedOrder)

        // when
        publishMessage(
            getProtoOrder(
                orderId = persistedOrder.id,
                orderNumber = persistedOrder.poNumber,
                orderVersion = persistedOrder.version,
                status = ProtoPoState.STATE_DELETED,
                orderType = PURCHASE_ORDER_TYPE_STANDARD,
            ),
        )

        // then
        assertAwaiting {
            val allPurchaseOrdersMap = purchaseOrderRepository.findAll().map { it.id to it.status }
            assertThat(allPurchaseOrdersMap, equalTo(listOf(persistedOrder.id to DELETED)))
        }
    }

    @Test
    fun `should store orderItem with negative qty value`() {
        // given
        val givenTotalQuantity = "-0.1"
        val givenSkuId = UUID.randomUUID()

        // when
        publishMessage(
            getProtoOrder(
                orderNumber = "PO-9874",
                orderItems = listOf(
                    getUnitItem(
                        skuId = givenSkuId,
                        quantity = givenTotalQuantity,
                    ),
                ),
            ),
        )

        // then
        assertAwaiting {
            val allPurchaseOrders = purchaseOrderRepository.findAll()
                .flatMap { it.orderItems }.map { it.skuId to it.totalQty }
            assertThat(allPurchaseOrders, equalTo(listOf(givenSkuId to BigDecimal("-0.1000"))))
        }
    }

    @Test
    fun `should store purchase order with empty string as deliveryDateChangeReasonUuid value`() {
        // given
        val protoOrder = getProtoOrder(deliveryDateChangeReasonUUID = "")

        // when
        publishMessage(protoOrder)

        // then
        assertAwaiting {
            val allPurchaseOrders = purchaseOrderRepository.findAll()
            allPurchaseOrders.isNotEmpty() && null == allPurchaseOrders.first().deliveryDateChangeReasonId
        }
    }

    private fun publishMessage(message: ProtoPurchaseOrder) {
        kafkaTemplate.send(topics.split(",").first(), message.revision.number.formatted, message)
        kafkaTemplate.flush()
    }

    @Suppress("LongParameterList")
    fun getPurchaseOrderEntity(
        poNumber: String = "2318NJ021004",
        id: UUID = UUID.fromString("e3fa59aa-91c1-4c78-bfa5-000000000002"),
        yearWeek: YearWeek = YearWeek("2023-W18"),
        status: PurchaseOrderStatus = INITIATED,
        dcCode: String = "NJ",
        supplierCode: String = "113200",
        type: POType = EMERGENCY,
        createdAt: LocalDateTime = LocalDateTime.parse("2022-09-27T15:13:31.000000"),
        shipMethod: ShipMethodEnum = VENDOR,
        version: Int = 2,
        shippingAddress: ShippingAddress = ShippingAddress(
            locationName = "HelloFresh United States - NJ",
            streetAddress = "60 Lister Avenue",
            city = "Newark",
            region = "NJ",
            postalCode = "07105",
            countryCode = "US",
        ),
        orderItems: Set<OrderItem> = emptySet(),
        sendTime: LocalDateTime? = LocalDateTime.parse("2023-04-29T10:00:00.000000"),
        deliveryDateChangeReasonId: UUID? = null,
        supplierId: UUID = UUID.fromString("35661b1f-edd1-4ce2-ad33-f7c982086b2a"),
        comment: String? = null,
        isSynced: Boolean = false,
        emergencyReasonId: UUID?,
    ) = PurchaseOrder(
        poNumber = poNumber,
        id = id,
        version = version,
        type = type,
        yearWeek = yearWeek,
        userId = UUID.randomUUID(),
        userEmail = "<EMAIL>",
        status = status,
        sendTime = sendTime,
        supplierId = supplierId,
        supplierCode = supplierCode,
        dcCode = dcCode,
        shippingMethod = shipMethod,
        shippingAddress = shippingAddress,
        expectedStartTime = LocalDateTime.parse("2023-04-29T10:00:00.000000"),
        expectedEndTime = LocalDateTime.parse("2023-04-29T11:00:00.000000"),
        emergencyReasonUuid = emergencyReasonId,
        createdAt = createdAt,
        updatedAt = createdAt,
        totalPrice = Money(
            currency = "USD",
            amount = BigDecimal(190).setScale(4),
        ),
        comment = comment,
        orderItems = orderItems,
        isSynced = isSynced,
        deliveryDateChangeReasonId = deliveryDateChangeReasonId,
    )

    companion object {
        const val ORDER_NUMBER = "123NJ5000"
        private val CHANGE_REASON_ID = UUID.fromString("37c2e641-ac77-4fec-8b17-83091d4a9b60")
    }
}
