package com.hellofresh.oms.purchaseOrder.service

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.YearWeek
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory

class PurchaseOrderDiffBuilderTest {
    val poId = UUID.randomUUID()

    val poItemOne = OrderItem(
        id = UUID.randomUUID(),
        poId = poId,
        totalQty = BigDecimal.ONE,
        skuId = UUID.randomUUID(),
        price = Money(BigDecimal.TEN, "EUR"),
        totalPrice = Money(BigDecimal.TEN, "EUR"),
        buffer = Permyriad(0),
        correctionReason = "correctionReason",
        packaging = Packaging(
            packagingType = PackagingType.UNIT_TYPE,
            unitOfMeasure = UOM.UNIT,
        ),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        rawQty = BigDecimal.ONE,
        changeReasonId = UUID.randomUUID(),
        casesPerPallet = null,
    )

    val poItemTwo = OrderItem(
        id = UUID.randomUUID(),
        poId = poId,
        totalQty = BigDecimal.TWO,
        skuId = UUID.randomUUID(),
        price = Money(BigDecimal.TEN, "EUR"),
        totalPrice = Money(BigDecimal.TEN, "EUR"),
        buffer = Permyriad(0),
        correctionReason = "correctionReason",
        packaging = Packaging(
            packagingType = PackagingType.UNIT_TYPE,
            unitOfMeasure = UOM.UNIT,
        ),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        rawQty = BigDecimal.TWO,
        changeReasonId = UUID.randomUUID(),
        casesPerPallet = null,
    )

    val shippingAddress = ShippingAddress(
        "locationName",
        "streetAddresa",
        "city",
        "region",
        "PostalCode",
        "countryCode",
    )

    val purchaseOrder = PurchaseOrder(
        id = poId,
        poNumber = "1984NT000000",
        version = 1,
        type = POType.EMERGENCY,
        yearWeek = YearWeek(1984, 5),
        userId = UUID.randomUUID(),
        userEmail = "<EMAIL>",
        status = PurchaseOrderStatus.INITIATED,
        sendTime = LocalDateTime.of(1984, 1, 25, 19, 0, 0),
        supplierId = UUID.randomUUID(),
        supplierCode = "000000",
        dcCode = "NT",
        shippingMethod = ShipMethodEnum.VENDOR,
        shippingAddress = shippingAddress,
        expectedStartTime = LocalDateTime.of(1984, 1, 25, 19, 0, 0),
        expectedEndTime = LocalDateTime.of(1984, 1, 25, 19, 0, 0),
        orderItems = setOf(poItemOne, poItemTwo),
        emergencyReasonUuid = UUID.randomUUID(),
        totalPrice = Money(BigDecimal.TEN, "EUR"),
        comment = "Comment",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        isSynced = false,
        deliveryDateChangeReasonId = UUID.randomUUID(),
        origin = Origin.OTHER,
    )

    @Test
    fun shouldBeEmptyForIdenticalPo() {
        assertEquals(0, PurchaseOrderDiffBuilder.compare(purchaseOrder, purchaseOrder)?.numberOfDiffs)
    }

    @Test
    fun shouldIgnoreTimestamps() {
        val updatedCreatedAt = purchaseOrder.copy(createdAt = LocalDateTime.MIN)
        assertEquals(0, PurchaseOrderDiffBuilder.compare(purchaseOrder, updatedCreatedAt)?.numberOfDiffs)

        val updatedUpdatedAt = purchaseOrder.copy(updatedAt = LocalDateTime.MAX)
        assertEquals(0, PurchaseOrderDiffBuilder.compare(purchaseOrder, updatedUpdatedAt)?.numberOfDiffs)
    }

    @Test
    fun shouldIgnoreUnsyncedFields() {
        val updatedOrigin = purchaseOrder.copy(origin = Origin.MANUAL)
        assertEquals(0, PurchaseOrderDiffBuilder.compare(purchaseOrder, updatedOrigin)?.numberOfDiffs)

        val updatedIsSynced = purchaseOrder.copy(isSynced = true)
        assertEquals(0, PurchaseOrderDiffBuilder.compare(purchaseOrder, updatedIsSynced)?.numberOfDiffs)
    }

    @TestFactory
    fun `diff should detect change to expected fields`(): List<DynamicTest> {
        data class Case(val description: String, val modified: PurchaseOrder, val expectedField: String)

        val base = purchaseOrder
        val cases = listOf(
            Case("id", base.copy(id = UUID.randomUUID()), "id"),
            Case("poNumber", base.copy(poNumber = "AnotherPONumber"), "poNumber"),
            Case("version", base.copy(version = 2), "version"),
            Case("type", base.copy(type = POType.STANDARD), "type"),
            Case("yearWeek", base.copy(yearWeek = YearWeek(2025, 25)), "yearWeek"),
            // Case("userId", base.copy(userId = UUID.randomUUID()), "userId"),
            // REMOVED - we will not consider the UUID in comparison.
            Case("userEmail", base.copy(userEmail = "<EMAIL>"), "userEmail"),
            Case("supplierId", base.copy(supplierId = UUID.randomUUID()), "supplierId"),
            Case("supplierCode", base.copy(supplierCode = "8888888"), "supplierCode"),
            Case("dcCode", base.copy(dcCode = "PR"), "dcCode"),
            Case("shippingMethod", base.copy(shippingMethod = ShipMethodEnum.FREIGHT_ON_BOARD), "shippingMethod"),
            Case(
                "shippingAddress.locationName",
                base.copy(shippingAddress = shippingAddress.copy(locationName = "Another Location")),
                "shippingAddress"
            ),
            Case("expectedStartTime", base.copy(expectedStartTime = LocalDateTime.now()), "expectedStartTime"),
            Case("expectedEndTime", base.copy(expectedEndTime = LocalDateTime.now()), "expectedEndTime"),
            Case("emergencyReasonUuid", base.copy(emergencyReasonUuid = UUID.randomUUID()), "emergencyReasonUuid"),
            Case("totalPrice", base.copy(totalPrice = Money(BigDecimal.ONE, "USD")), "totalPrice"),
            Case("comment", base.copy(comment = "Updated Comment"), "comment"),
            Case(
                "deliveryDateChangeReasonId",
                base.copy(deliveryDateChangeReasonId = UUID.randomUUID()),
                "deliveryDateChangeReasonId"
            ),
        )

        return cases.map { (description, modified, expectedField) ->
            DynamicTest.dynamicTest("should detect change in $description") {
                val diff = PurchaseOrderDiffBuilder.compare(purchaseOrder, modified)
                requireNotNull(diff)
                assertEquals(1, diff.numberOfDiffs, "Expected exactly one diff for $description")
                assertEquals(expectedField, diff.single().fieldName)
            }
        }
    }

    @Test
    fun shouldReportWhenLineItemAdded() {
        val poWithOneItem = purchaseOrder.copy(orderItems = setOf(poItemOne))
        val diff = PurchaseOrderDiffBuilder.compare(poWithOneItem, purchaseOrder)
        requireNotNull(diff)
        assertEquals(1, diff.numberOfDiffs)
        assertEquals("addedLineItems", diff.single().fieldName)
    }

    @Test
    fun shouldReportWhenLineItemRemoved() {
        val poWithOneItem = purchaseOrder.copy(orderItems = setOf(poItemOne))
        val diff = PurchaseOrderDiffBuilder.compare(purchaseOrder, poWithOneItem)
        requireNotNull(diff)
        assertEquals(1, diff.numberOfDiffs)
        assertEquals("removedLineItems", diff.single().fieldName)
    }

    @Test
    fun shouldReportItemsChangedWhenReplaced() {
        val po1 = purchaseOrder.copy(orderItems = setOf(poItemOne))
        val po2 = purchaseOrder.copy(orderItems = setOf(poItemTwo))
        val diff = PurchaseOrderDiffBuilder.compare(po1, po2)
        requireNotNull(diff)
        assertEquals(2, diff.numberOfDiffs)
        assertTrue { diff.find { it.fieldName == "addedLineItems" } != null }
        assertTrue { diff.find { it.fieldName == "removedLineItems" } != null }
    }

    @Test
    fun shouldReportItemsChangedWhenItemModified() {
        val poItemOneModified = poItemOne.copy(totalQty = BigDecimal.TWO)
        val po1 = purchaseOrder.copy(orderItems = setOf(poItemOne))
        val po2 = purchaseOrder.copy(orderItems = setOf(poItemOneModified))
        val diff = PurchaseOrderDiffBuilder.compare(po1, po2)
        requireNotNull(diff)
        assertEquals(1, diff.numberOfDiffs)
        assertEquals("modifiedLineItems", diff.single().fieldName)
    }

    @Test
    fun orderItem_shouldIgnoreOrderItemId() {
        val diff = PurchaseOrderDiffBuilder.compare(poItemOne, poItemOne.copy(id = UUID.randomUUID()))!!
        assertEquals(0, diff.numberOfDiffs)
    }

    @TestFactory
    fun `OrderItem - diff should detect change to expected fields`(): List<DynamicTest> {
        data class Case(val description: String, val modified: OrderItem, val expectedField: String)

        val base = poItemOne
        val cases = listOf(
            Case("poId", base.copy(poId = UUID.randomUUID()), "poId"),
            Case("totalQty", base.copy(totalQty = BigDecimal.valueOf(144L)), "totalQty"),
            Case("skuId", base.copy(skuId = UUID.randomUUID()), "skuId"),
            Case("price", base.copy(price = Money(BigDecimal.valueOf(500L), "AUD")), "price"),
            Case("totalPrice", base.copy(totalPrice = Money(BigDecimal.valueOf(72000), "AUD")), "totalPrice"),
            Case("buffer", base.copy(buffer = Permyriad(60)), "buffer"),
            // Case("correctionReason", base.copy(correctionReason = "Another Correction Reason"), "correctionReason"),
            // REMOVED: We need to change behaviour with correctionReasons
            Case(
                "packaging",
                base.copy(
                    packaging = Packaging(PackagingType.PALLET_TYPE, BigDecimal.valueOf(12), unitOfMeasure = UOM.KG)
                ),
                "packaging"
            ),
            Case("rawQty", base.copy(rawQty = BigDecimal.valueOf(88L)), "rawQty"),
            Case("changeReasonId", base.copy(changeReasonId = UUID.randomUUID()), "changeReasonId"),
            Case("casesPerPallet", base.copy(casesPerPallet = 76), "casesPerPallet"),
        )

        return cases.map { (description, modified, expectedField) ->
            DynamicTest.dynamicTest("should detect change in $description") {
                val diff = PurchaseOrderDiffBuilder.compare(poItemOne, modified)
                requireNotNull(diff)
                assertEquals(1, diff.numberOfDiffs, "Expected exactly one diff for $description")
                assertEquals(expectedField, diff.single().fieldName)
            }
        }
    }
}
