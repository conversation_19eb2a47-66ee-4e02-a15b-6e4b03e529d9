package com.hellofresh.oms.purchaseOrder

import com.hellofresh.oms.purchaseOrder.Fixture.createPurchaseOrderEntity
import com.hellofresh.oms.purchaseOrder.repository.PurchaseOrderRepository
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD

@DataJpaTest
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
@Sql(executionPhase = BEFORE_TEST_METHOD, scripts = ["/data/change-reasons.sql", "/data/po-repository-test.sql"])
class PurchaseOrderRepositoryTest(
    @Autowired private val purchaseOrderRepository: PurchaseOrderRepository
) {
    @Test
    fun `should select order and have null if doesn't exist`() {
        // when
        val result = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc("123")

        val version = result?.version

        // then
        assertNull(version)
    }

    @Test
    fun `should select an order`() {
        // when
        val result = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc("123NJ5000")

        // then
        assertEquals(3, result?.version)
        assertEquals("a3610310-0bd3-456c-8078-d60e3b7cece3", result!!.id.toString())
    }

    @Test
    fun `should save new order`() {
        // given
        val po = createPurchaseOrderEntity()

        // when
        val result = purchaseOrderRepository.saveAndFlush(po)

        // then
        assertNotNull(result)
    }
}
