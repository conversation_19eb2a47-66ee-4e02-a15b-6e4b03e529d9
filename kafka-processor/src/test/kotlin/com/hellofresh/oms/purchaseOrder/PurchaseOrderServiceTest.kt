package com.hellofresh.oms.purchaseOrder

import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus.INITIATED
import com.hellofresh.oms.purchaseOrder.Fixture.getCaseItem
import com.hellofresh.oms.purchaseOrder.Fixture.getProtoOrder
import com.hellofresh.oms.purchaseOrder.repository.PurchaseOrderRepository
import com.hellofresh.oms.purchaseOrder.repository.PurchaseOrderVersionAndType
import com.hellofresh.oms.purchaseOrder.service.PurchaseOrderService
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder as ProtoPurchaseOrder
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State as ProtoPurchaseOrderState
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State.STATE_INITIATED
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType as ProtoPOType
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import java.util.UUID.randomUUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
@Suppress("MaxLineLength")
class PurchaseOrderServiceTest {
    private val purchaseOrderRepositoryMock = mock(PurchaseOrderRepository::class.java)
    private val meterRegistry = mock(MeterRegistry::class.java)
    private val purchaseOrderService = PurchaseOrderService(purchaseOrderRepositoryMock, meterRegistry)

    @BeforeEach
    fun setUp() {
        whenever(meterRegistry.counter(any<String>(), any<Iterable<Tag>>())).thenReturn(mock(Counter::class.java))
    }

    @ParameterizedTest
    @MethodSource("purchaseOrderProvider")
    fun `proto purchase order validation test`(msg: String, protoPo: ProtoPurchaseOrder) {
        val actualMsg = assertThrows<IllegalArgumentException> { purchaseOrderService.process(protoPo) }.message!!
        assertTrue(actualMsg.contains(msg))
    }

    @Test
    fun `should replace the persisted PO when PO with higher version and same type is published`() {
        // given
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER))
            .thenReturn(PurchaseOrderVersionAndType(4, POType.STANDARD, randomUUID(), INITIATED, Origin.MANUAL))

        // when
        performPoUpdate(
            incomingPoVersion = 5,
            type = ProtoPOType.PURCHASE_ORDER_TYPE_STANDARD,
            supplierCode = "00000",
        )

        // then
        val argumentCaptor = argumentCaptor<PurchaseOrder>()
        verify(purchaseOrderRepositoryMock, times(1)).save(argumentCaptor.capture())
        verify(purchaseOrderRepositoryMock, times(1)).deleteById(any())
        assertEquals(argumentCaptor.firstValue.version, 5)
        // assert that the origin is kept the same as the persisted PO
        assertEquals(argumentCaptor.firstValue.origin, Origin.MANUAL)
    }

    @Test
    fun `should not update the persisted PO when older versions of the Purchase Order is published`() {
        // given
        val persistedPoType = POType.STANDARD
        val persistedPoVersion = 2
        val incomingPoType = ProtoPOType.PURCHASE_ORDER_TYPE_STANDARD
        val incomingPoVersion = 1
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER)).thenReturn(
            PurchaseOrderVersionAndType(persistedPoVersion, persistedPoType, randomUUID(), INITIATED, Origin.OTHER),
        )

        // when
        performPoUpdate(incomingPoVersion, incomingPoType)

        // then
        verify(purchaseOrderRepositoryMock, times(0)).save(any())
    }

    @Test
    fun `should not update the persisted emergency PO when standard PO with newer version is published`() {
        // given
        val persistedPoType = POType.EMERGENCY
        val persistedPoVersion = 1
        val incomingPoType = ProtoPOType.PURCHASE_ORDER_TYPE_STANDARD
        val incomingPoVersion = 1
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER))
            .thenReturn(
                PurchaseOrderVersionAndType(persistedPoVersion, persistedPoType, randomUUID(), INITIATED, Origin.OTHER),
            )

        // when
        performPoUpdate(incomingPoVersion, incomingPoType)

        // then
        verify(purchaseOrderRepositoryMock, times(0)).save(any())
    }

    @Test
    fun `should not update the persisted standard PO when PPO is published`() {
        // given
        val persistedPoType = POType.STANDARD
        val persistedPoVersion = 2
        val incomingPoType = ProtoPOType.PURCHASE_ORDER_TYPE_PROVISIONAL
        val incomingPoVersion = 1
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER))
            .thenReturn(
                PurchaseOrderVersionAndType(persistedPoVersion, persistedPoType, randomUUID(), INITIATED, Origin.OTHER),
            )

        // when
        performPoUpdate(incomingPoVersion, incomingPoType)

        // then
        verify(purchaseOrderRepositoryMock, times(1)).save(any())
    }

    @Test
    fun `should delete the already saved version from the database when deleted version of the same PO is published`() {
        // given
        val persistedPoType = POType.STANDARD
        val persistedPoVersion = 1
        val incomingPOStatus = ProtoPurchaseOrderState.STATE_DELETED
        val incomingPoType = ProtoPOType.PURCHASE_ORDER_TYPE_STANDARD
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER))
            .thenReturn(
                PurchaseOrderVersionAndType(persistedPoVersion, persistedPoType, randomUUID(), INITIATED, Origin.OTHER),
            )

        // when
        performPoUpdate(persistedPoVersion, incomingPoType, status = incomingPOStatus)

        // then
        verify(purchaseOrderRepositoryMock, times(1)).save(any())
    }

    @Test
    fun `should store new PO with state DELETED when there is no such order in database yet`() {
        // given
        val incomingPoVersion = 1
        val incomingPOStatus = ProtoPurchaseOrderState.STATE_DELETED
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER)).thenReturn(null)

        // when
        performPoUpdate(incomingPoVersion, ProtoPOType.PURCHASE_ORDER_TYPE_STANDARD, status = incomingPOStatus)

        // then
        val argumentCaptor = argumentCaptor<PurchaseOrder>()
        verify(purchaseOrderRepositoryMock, times(1)).save(argumentCaptor.capture())
        // assert that the origin is set to OTHER when purchase order does not exist in the database
        assertEquals(argumentCaptor.firstValue.origin, Origin.OTHER)
    }

    @Test
    fun `should replace a preorder with a higher version in the database when final version of the PO is published`() {
        // given
        val persistedPoVersion = 3
        val persistedPoType = POType.PREORDER
        val incomingPoVersion = 1
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER))
            .thenReturn(
                PurchaseOrderVersionAndType(persistedPoVersion, persistedPoType, randomUUID(), INITIATED, Origin.OTHER),
            )

        // when
        performPoUpdate(incomingPoVersion, ProtoPOType.PURCHASE_ORDER_TYPE_STANDARD)

        // then
        val argumentCaptor = argumentCaptor<PurchaseOrder>()
        verify(purchaseOrderRepositoryMock, times(1)).save(argumentCaptor.capture())
        assertEquals(argumentCaptor.firstValue.version, incomingPoVersion)
        assertEquals(argumentCaptor.firstValue.type, POType.STANDARD)
    }

    @Test
    fun `should replace a standard order with a higher version in the database when emergency revision of the PO is published`() {
        // given
        val persistedPoVersion = 3
        val persistedPoType = POType.STANDARD
        val incomingPoVersion = 1
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER))
            .thenReturn(
                PurchaseOrderVersionAndType(persistedPoVersion, persistedPoType, randomUUID(), INITIATED, Origin.OTHER),
            )

        // when
        performPoUpdate(incomingPoVersion, ProtoPOType.PURCHASE_ORDER_TYPE_EMERGENCY)

        // then
        val argumentCaptor = argumentCaptor<PurchaseOrder>()
        verify(purchaseOrderRepositoryMock, times(1)).save(argumentCaptor.capture())
        assertEquals(argumentCaptor.firstValue.version, incomingPoVersion)
        assertEquals(argumentCaptor.firstValue.type, POType.EMERGENCY)
    }

    @Test
    fun `should replace the existing PO when a purchase order with a revision equal to the revision of a stored PO is published`() {
        // given
        val persistedPoVersion = 1
        val persistedPoType = POType.EMERGENCY
        val incomingPoVersion = 1
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(PO_NUMBER))
            .thenReturn(
                PurchaseOrderVersionAndType(persistedPoVersion, persistedPoType, randomUUID(), INITIATED, Origin.OTHER),
            )

        // when
        performPoUpdate(incomingPoVersion, ProtoPOType.PURCHASE_ORDER_TYPE_EMERGENCY)

        // then
        verify(purchaseOrderRepositoryMock, times(1)).save(any())
    }

    private fun performPoUpdate(
        incomingPoVersion: Int,
        type: ProtoPOType,
        supplierCode: String = "00000",
        status: ProtoPurchaseOrderState = STATE_INITIATED,
        orderNumber: String = PO_NUMBER
    ): ProtoPurchaseOrder {
        val po = getProtoOrder(
            orderNumber = orderNumber,
            orderVersion = incomingPoVersion,
            orderType = type,
            supplierCode = supplierCode,
            status = status,
        )
        purchaseOrderService.process(po)
        return po
    }

    companion object {
        private const val PO_NUMBER = "2102AA123456"

        @Suppress("UnusedPrivateMember")
        @JvmStatic
        private fun purchaseOrderProvider() = Stream.of(
            arguments(
                "Purchase order doesn't contain any item, skipping, poNumber:",
                getProtoOrder(orderItems = listOf()),
            ),
            arguments(
                "Received an sku with case packaging and size equals to zero, skipping",
                getProtoOrder(orderItems = listOf(getCaseItem(caseSize = "0"))),
            ),
        )
    }
}
