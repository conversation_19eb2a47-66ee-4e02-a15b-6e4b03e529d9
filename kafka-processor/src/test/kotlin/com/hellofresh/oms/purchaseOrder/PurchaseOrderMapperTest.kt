package com.hellofresh.oms.purchaseOrder

import com.google.type.Money as GoogleMoney
import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.CROSSDOCK
import com.hellofresh.oms.model.ShipMethodEnum.FREIGHT_ON_BOARD
import com.hellofresh.oms.model.ShipMethodEnum.OTHER
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.UOM.GAL
import com.hellofresh.oms.model.UOM.KG
import com.hellofresh.oms.model.UOM.L
import com.hellofresh.oms.model.UOM.LBS
import com.hellofresh.oms.model.UOM.OZ
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.purchaseOrder.Fixture.getCaseItem
import com.hellofresh.oms.purchaseOrder.Fixture.getDeliveryDateChangeReasonId
import com.hellofresh.oms.purchaseOrder.Fixture.getProtoOrder
import com.hellofresh.oms.purchaseOrder.Fixture.getUnitItem
import com.hellofresh.oms.purchaseOrder.consumer.toEntity
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod as ProtoShipMethod
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod.SHIPPING_METHOD_CROSSDOCK
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod.SHIPPING_METHOD_FREIGHT_ON_BOARD
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod.SHIPPING_METHOD_OTHER
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod.SHIPPING_METHOD_VENDOR
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem.CasePackaging.UOM as ProtoUOM
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State as ProtoPurchaseOrderState
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType as ProtoPurchaseOrderType
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID.fromString
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource

class PurchaseOrderMapperTest {

    @ParameterizedTest
    @MethodSource("purchaseOrderProvider")
    fun `should map proto purchase order to purchase order entity with different UOM`(protoUom: ProtoUOM, uom: UOM) {
        // given
        val orderId = fromString("f460387c-957d-4b26-980e-0d7b2b50b9fc")
        val userId = fromString("e6e63074-64d9-433a-b6bb-65af59370914")
        val supplierId = fromString("c474ca1b-d246-4aee-80f9-3a1ef4a18941")
        val caseSkuId = fromString("590b318e-e20c-4e42-99fa-21344a62c761")
        val unitSkuId = fromString("a2e3dace-5c34-4f98-bb16-ce1325576401")
        val now = LocalDateTime.parse("2023-03-29T10:40:23.282275345")
        val orderItemChangeUUID = fromString("bf61e16e-c581-43bb-a0c4-8b28b0e58e69")
        val protoUnitItem = getUnitItem(skuId = caseSkuId, changeReasonUUID = orderItemChangeUUID)
        val protoCaseItem = getCaseItem(skuId = unitSkuId, uom = protoUom, changeReasonUUID = orderItemChangeUUID)
        val protoPurchaseOrder = getProtoOrder(
            orderId = orderId, userId = userId, sendTime = now,
            supplierId = supplierId, expectedStartTime = now, expectedEndTime = now,
            createTime = now, updateTime = now, orderItems = listOf(protoUnitItem, protoCaseItem),
        )

        // when
        val poEntity = protoPurchaseOrder.toEntity()

        // then
        assertEquals(UNIT, poEntity.orderItems.first().packaging.unitOfMeasure)
        assertEquals(uom, poEntity.orderItems.last().packaging.unitOfMeasure)
    }

    @ParameterizedTest
    @MethodSource("purchaseOrderTypeProvider")
    fun `should map proto purchase order to purchase order entity with different POType`(
        protoType: ProtoPurchaseOrderType,
        type: POType
    ) {
        // given
        val orderId = fromString("f460387c-957d-4b26-980e-0d7b2b50b9fc")
        val userId = fromString("e6e63074-64d9-433a-b6bb-65af59370914")
        val supplierId = fromString("c474ca1b-d246-4aee-80f9-3a1ef4a18941")
        val caseSkuId = fromString("590b318e-e20c-4e42-99fa-21344a62c761")
        val unitSkuId = fromString("a2e3dace-5c34-4f98-bb16-ce1325576401")
        val now = LocalDateTime.parse("2023-03-29T10:40:23.282275345")
        val protoUnitItem = getUnitItem(skuId = caseSkuId)
        val protoCaseItem = getCaseItem(skuId = unitSkuId)
        val protoPurchaseOrder = getProtoOrder(
            orderId = orderId, userId = userId, orderType = protoType, sendTime = now,
            supplierId = supplierId, expectedStartTime = now, expectedEndTime = now,
            createTime = now, updateTime = now, orderItems = listOf(protoUnitItem, protoCaseItem),
        )

        // when
        val poEntity = protoPurchaseOrder.toEntity()

        // then
        assertEquals(type, poEntity.type)
    }

    @ParameterizedTest
    @MethodSource("purchaseOrderStateProvider")
    fun `should map proto purchase order to purchase order entity with different PurchaseOrderStatus`(
        protoStatus: ProtoPurchaseOrderState,
        status: PurchaseOrderStatus
    ) {
        // given
        val orderId = fromString("f460387c-957d-4b26-980e-0d7b2b50b9fc")
        val userId = fromString("e6e63074-64d9-433a-b6bb-65af59370914")
        val supplierId = fromString("c474ca1b-d246-4aee-80f9-3a1ef4a18941")
        val caseSkuId = fromString("590b318e-e20c-4e42-99fa-21344a62c761")
        val unitSkuId = fromString("a2e3dace-5c34-4f98-bb16-ce1325576401")
        val now = LocalDateTime.parse("2023-03-29T10:40:23.282275345")
        val protoUnitItem = getUnitItem(skuId = caseSkuId)
        val protoCaseItem = getCaseItem(skuId = unitSkuId)
        val protoPurchaseOrder = getProtoOrder(
            orderId = orderId, userId = userId, status = protoStatus, sendTime = now,
            supplierId = supplierId, expectedStartTime = now, expectedEndTime = now,
            createTime = now, updateTime = now, orderItems = listOf(protoUnitItem, protoCaseItem),
        )

        // when
        val poEntity = protoPurchaseOrder.toEntity()

        // then
        assertEquals(status, poEntity.status)
    }

    @ParameterizedTest
    @MethodSource("purchaseOrderShipMethodProvider")
    fun `should map proto purchase order to purchase order entity with different ShipMethod`(
        protoShipMethod: ProtoShipMethod,
        shipMethod: ShipMethodEnum
    ) {
        // given
        val orderId = fromString("f460387c-957d-4b26-980e-0d7b2b50b9fc")
        val userId = fromString("e6e63074-64d9-433a-b6bb-65af59370914")
        val supplierId = fromString("c474ca1b-d246-4aee-80f9-3a1ef4a18941")
        val caseSkuId = fromString("590b318e-e20c-4e42-99fa-21344a62c761")
        val unitSkuId = fromString("a2e3dace-5c34-4f98-bb16-ce1325576401")
        val now = LocalDateTime.parse("2023-03-29T10:40:23.282275345")
        val protoUnitItem = getUnitItem(skuId = caseSkuId)
        val protoCaseItem = getCaseItem(skuId = unitSkuId)
        val protoPurchaseOrder = getProtoOrder(
            orderId = orderId, userId = userId, shippingMethod = protoShipMethod, sendTime = now,
            supplierId = supplierId, expectedStartTime = now, expectedEndTime = now,
            createTime = now, updateTime = now, orderItems = listOf(protoUnitItem, protoCaseItem),
        )

        // when
        val poEntity = protoPurchaseOrder.toEntity()

        // then
        assertEquals(shipMethod, poEntity.shippingMethod)
    }

    @Test
    fun `should map zero values are mapped correctly`() {
        val orderId = fromString("f460387c-957d-4b26-980e-0d7b2b50b9fc")
        val userId = fromString("e6e63074-64d9-433a-b6bb-65af59370914")
        val supplierId = fromString("c474ca1b-d246-4aee-80f9-3a1ef4a18941")
        val caseSkuId = fromString("590b318e-e20c-4e42-99fa-21344a62c761")
        val now = LocalDateTime.parse("2023-03-29T10:40:23.282275345")
        val orderItemChangeUUID = fromString("bf61e16e-c581-43bb-a0c4-8b28b0e58e69")
        val money = GoogleMoney.newBuilder()
            .setCurrencyCode("EUR")
            .setUnits(0)
            .setNanos((0.0001 * 1_000_000_000).toInt())
            .build()
        val protoUnitItem =
            getUnitItem(
                skuId = caseSkuId,
                changeReasonUUID = orderItemChangeUUID,
                orderPrice = 0.0001,
                totalPrice = 0.0001,
            )
        val protoPurchaseOrder = getProtoOrder(
            orderId = orderId, userId = userId, sendTime = now,
            supplierId = supplierId, expectedStartTime = now, expectedEndTime = now,
            createTime = now, updateTime = now, orderItems = listOf(protoUnitItem),
            money = money,
        )

        // when
        val poEntity = protoPurchaseOrder.toEntity()

        // then
        assertEquals(BigDecimal.ZERO, poEntity.orderItems.first().price.amount)
        assertEquals(BigDecimal.ZERO, poEntity.orderItems.first().totalPrice.amount)
        assertEquals(BigDecimal.ZERO, poEntity.totalPrice.amount)
    }

    @Test
    fun `should map proto money to money entity`() {
        // given
        val money = GoogleMoney.newBuilder()
            .setCurrencyCode("EUR")
            .setUnits(198)
            .setNanos(246909356)
            .build()

        val protoPurchaseOrder = getProtoOrder(money = money)

        // when
        val poEntity = protoPurchaseOrder.toEntity()

        // then
        assertEquals("Money(amount=198.246909356, currency=EUR)", poEntity.totalPrice.toString())
    }

    @Test
    fun `should map proto comment to entity`() {
        // given
        val comment = "some comment"
        // when
        val entityComment = getProtoOrder(comment = comment).toEntity().comment
        // then
        assertEquals(comment, entityComment)
    }

    @Test
    fun `should map proto empty comment to entity as null`() {
        // when
        val entityComment = getProtoOrder().toEntity().comment
        // then
        assertNull(entityComment)
    }

    @Test
    fun `should map proto delivery date to entity`() {
        // given
        val deliveryDateChangeReasonUUID = getDeliveryDateChangeReasonId()
        // when
        val entityDeliveryDateChangeReasonId = getProtoOrder(
            deliveryDateChangeReasonUUID = deliveryDateChangeReasonUUID.toString(),
        ).toEntity().deliveryDateChangeReasonId
        // then
        assertEquals(deliveryDateChangeReasonUUID, entityDeliveryDateChangeReasonId)
    }

    @Test
    fun `should map proto purchase order to purchase order entity with pallet size`() {
        // given
        val orderId = fromString("f460387c-957d-4b26-980e-0d7b2b50b9fc")
        val userId = fromString("e6e63074-64d9-433a-b6bb-65af59370914")
        val supplierId = fromString("c474ca1b-d246-4aee-80f9-3a1ef4a18941")
        val unitSkuId = fromString("a2e3dace-5c34-4f98-bb16-ce1325576401")
        val now = LocalDateTime.parse("2023-03-29T10:40:23.282275345")
        val orderItemChangeUUID = fromString("bf61e16e-c581-43bb-a0c4-8b28b0e58e69")
        val protoUom = ProtoUOM.UOM_GAL
        val uom = GAL
        val casesPerPallet = 13
        val protoCaseItem = getCaseItem(
            skuId = unitSkuId,
            uom = protoUom,
            changeReasonUUID = orderItemChangeUUID,
            palletSize = casesPerPallet,
        )
        val protoPurchaseOrder = getProtoOrder(
            orderId = orderId, userId = userId, sendTime = now,
            supplierId = supplierId, expectedStartTime = now, expectedEndTime = now,
            createTime = now, updateTime = now, orderItems = listOf(protoCaseItem),
        )

        @Suppress("MaxLineLength")
        val expectedPo =
            "PurchaseOrder(id=f460387c-957d-4b26-980e-0d7b2b50b9fc, poNumber=1000NJ200, version=1, type=STANDARD, yearWeek=2022-W10, userId=e6e63074-64d9-433a-b6bb-65af59370914, userEmail=<EMAIL>, status=APPROVED, sendTime=2023-03-29T10:40:23.282275345, supplierId=c474ca1b-d246-4aee-80f9-3a1ef4a18941, supplierCode=123456, dcCode=NJ, shippingMethod=OTHER, shippingAddress=ShippingAddress(locationName=Hello Fresh, streetAddress=Street 123, Street 234, city=New J, region=New Jersey, postalCode=1203, countryCode=US), expectedStartTime=2023-03-29T10:40:23.282275345, expectedEndTime=2023-03-29T10:40:23.282275345, orderItems=[OrderItem(id=%s, poId=f460387c-957d-4b26-980e-0d7b2b50b9fc, totalQty=20, skuId=a2e3dace-5c34-4f98-bb16-ce1325576401, price=Money(amount=10, currency=USD), totalPrice=Money(amount=20, currency=USD), buffer=Permyriad(value=1050), correctionReason=Some good reason, packaging=Packaging(packagingType=PALLET_TYPE, caseSize=5, unitOfMeasure=$uom), createdAt=null, updatedAt=null, rawQty=null, changeReasonId=bf61e16e-c581-43bb-a0c4-8b28b0e58e69, casesPerPallet=$casesPerPallet)], emergencyReasonUuid=null, totalPrice=Money(amount=10, currency=EUR), comment=null, createdAt=2023-03-29T10:40:23.282275345, updatedAt=2023-03-29T10:40:23.282275345, isSynced=true, deliveryDateChangeReasonId=37c2e641-ac77-4fec-8b17-83091d4a9b60, origin=OTHER)"

        // when
        val poEntity = protoPurchaseOrder.toEntity()

        // then
        assertEquals(
            String.format(expectedPo, poEntity.orderItems.first().id, poEntity.orderItems.last().id),
            poEntity.toString(),
        )
    }

    companion object {
        @Suppress("UnusedPrivateMember")
        @JvmStatic
        private fun purchaseOrderProvider() = Stream.of(
            arguments(ProtoUOM.UOM_UNIT, UNIT),
            arguments(ProtoUOM.UOM_KG, KG),
            arguments(ProtoUOM.UOM_LBS, LBS),
            arguments(ProtoUOM.UOM_GAL, GAL),
            arguments(ProtoUOM.UOM_LITRE, L),
            arguments(ProtoUOM.UOM_OZ, OZ),
        )

        @Suppress("UnusedPrivateMember")
        @JvmStatic
        private fun purchaseOrderTypeProvider() = Stream.of(
            arguments(ProtoPurchaseOrderType.PURCHASE_ORDER_TYPE_STANDARD, POType.STANDARD),
            arguments(ProtoPurchaseOrderType.PURCHASE_ORDER_TYPE_EMERGENCY, POType.EMERGENCY),
            arguments(ProtoPurchaseOrderType.PURCHASE_ORDER_TYPE_PROVISIONAL, POType.PREORDER),
        )

        @Suppress("UnusedPrivateMember")
        @JvmStatic
        private fun purchaseOrderStateProvider() = Stream.of(
            arguments(ProtoPurchaseOrderState.STATE_APPROVED, PurchaseOrderStatus.APPROVED),
            arguments(ProtoPurchaseOrderState.STATE_DELETED, PurchaseOrderStatus.DELETED),
            arguments(ProtoPurchaseOrderState.STATE_INITIATED, PurchaseOrderStatus.INITIATED),
            arguments(ProtoPurchaseOrderState.STATE_REJECTED, PurchaseOrderStatus.REJECTED),
        )

        @Suppress("UnusedPrivateMember")
        @JvmStatic
        private fun purchaseOrderShipMethodProvider() = Stream.of(
            arguments(SHIPPING_METHOD_CROSSDOCK, CROSSDOCK),
            arguments(SHIPPING_METHOD_VENDOR, VENDOR),
            arguments(SHIPPING_METHOD_FREIGHT_ON_BOARD, FREIGHT_ON_BOARD),
            arguments(SHIPPING_METHOD_OTHER, OTHER),
        )
    }
}
