package com.hellofresh.oms.purchaseOrder.producer

import com.hellofresh.oms.client.featureflag.FeatureFlagClient
import com.hellofresh.oms.purchaseOrder.Fixture.createPurchaseOrderEntity
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import java.time.Duration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.kafka.support.KafkaHeaders
import reactor.test.StepVerifier

class PurchaseOrderProducerTest {

    private val protoMapper: PurchaseOrderProtoMapper = mock()
    private val featureFlagClient: FeatureFlagClient = mock()
    private lateinit var purchaseOrderProducer: PurchaseOrderProducer

    @BeforeEach
    fun setUp() {
        purchaseOrderProducer = PurchaseOrderProducer(protoMapper, featureFlagClient)
    }

    @Test
    fun `should publish purchase order event when feature flag is enabled`() {
        // Given
        val purchaseOrder = createPurchaseOrderEntity(poNumber = "PO123")
        val event = PurchaseOrderEventType.CREATED
        val protoEvent = PurchaseOrderEvent.newBuilder().setPoNumber("PO123").build()
        whenever(protoMapper.mapToProto(any(), any())).thenReturn(protoEvent)
        whenever(featureFlagClient.shouldEnablePublisherForPoTopicFeatureFlag()).thenReturn(true)

        // When
        purchaseOrderProducer.publishPurchaseOrderEvent(purchaseOrder, event)

        // Then
        StepVerifier.create(purchaseOrderProducer.get())
            .expectNextMatches { message ->
                message.payload == protoEvent && message.headers[KafkaHeaders.KEY] == "PO123"
            }
            .verifyTimeout(timeout)
    }

    @Test
    fun `should not publish purchase order event when feature flag is disabled`() {
        // Given
        val purchaseOrder = createPurchaseOrderEntity(poNumber = "PO123")
        val event = PurchaseOrderEventType.CREATED
        val protoEvent = PurchaseOrderEvent.newBuilder().setPoNumber("PO123").build()
        whenever(protoMapper.mapToProto(any(), any())).thenReturn(protoEvent)
        whenever(featureFlagClient.shouldEnablePublisherForPoTopicFeatureFlag()).thenReturn(false)

        // When
        purchaseOrderProducer.publishPurchaseOrderEvent(purchaseOrder, event)

        // Then
        StepVerifier.create(purchaseOrderProducer.get())
            .expectSubscription()
            .expectNoEvent(timeout)
            .thenCancel()
            .verify(timeout)
    }

    companion object {
        private val timeout = Duration.ofSeconds(2)
    }
}
