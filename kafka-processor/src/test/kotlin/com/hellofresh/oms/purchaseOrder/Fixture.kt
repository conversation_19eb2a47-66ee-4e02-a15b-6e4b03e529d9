package com.hellofresh.oms.purchaseOrder

import com.google.protobuf.Timestamp
import com.google.type.Decimal
import com.google.type.Money as GoogleMoney
import com.google.type.PostalAddress
import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.POType.STANDARD
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.PurchaseOrderStatus.INITIATED
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod as ProtoShippingMethod
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder as ProtoPurchaseOrder
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem as ProtoPurchaseOrderItem
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem.CasePackaging as ProtoCasePackaging
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem.CasePackaging.UOM as ProtoUOM
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem.UnitPackaging as ProtoUnitPackaging
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderShipping as ProtoPurchaseOrderShipping
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State as ProtoPurchaseOrderState
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderNumber as ProtoPurchaseOrderNumber
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision as ProtoPurchaseOrderRevision
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType as ProtoPurchaseOrderType
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import java.util.UUID.randomUUID

object Fixture {

    fun getDeliveryDateChangeReasonId(): UUID = UUID.fromString("37c2e641-ac77-4fec-8b17-83091d4a9b60")

    @Suppress("LongParameterList")
    fun getProtoOrder(
        orderItems: List<ProtoPurchaseOrderItem> = listOf(getUnitItem(), getCaseItem()),
        orderType: ProtoPurchaseOrderType = ProtoPurchaseOrderType.PURCHASE_ORDER_TYPE_STANDARD,
        orderNumber: String = "1000NJ200",
        orderVersion: Int = 1,
        orderId: UUID = randomUUID(),
        year: Int = 2022,
        week: Int = 10,
        userId: UUID = randomUUID(),
        userEmail: String = "<EMAIL>",
        status: ProtoPurchaseOrderState = ProtoPurchaseOrderState.STATE_APPROVED,
        sendTime: LocalDateTime? = LocalDateTime.now(),
        supplierId: UUID = randomUUID(),
        supplierCode: String = "123456",
        dcCode: String = "NJ",
        dcHfMarketCode: String = "US",
        shippingMethod: ProtoShippingMethod = ProtoShippingMethod.SHIPPING_METHOD_OTHER,
        shippingAddressLines1: String = "Street 123",
        shippingAddressLines2: String = "Street 234",
        shippingAddressLocality: String = "New J",
        shippingAddressRegionCode: String = "US",
        shippingAddressArea: String = "New Jersey",
        shippingAddressPostalCode: String = "1203",
        shippingAddressOrganization: String = "Hello Fresh",
        expectedStartTime: LocalDateTime = LocalDateTime.now().plusDays(1),
        expectedEndTime: LocalDateTime = LocalDateTime.now().plusDays(1).plusHours(1),
        createTime: LocalDateTime = LocalDateTime.now().plusHours(1),
        updateTime: LocalDateTime = LocalDateTime.now().plusHours(2),
        emergencyReason: String? = "emergency reason",
        money: GoogleMoney = GoogleMoney.newBuilder().setCurrencyCode("EUR").setUnits(10).build(),
        comment: String? = null,
        deliveryDateChangeReasonUUID: String = getDeliveryDateChangeReasonId().toString(),
    ): ProtoPurchaseOrder {
        val order = ProtoPurchaseOrder
            .newBuilder()
            .setRevision(
                ProtoPurchaseOrderRevision.newBuilder()
                    .setNumber(ProtoPurchaseOrderNumber.newBuilder().setFormatted(orderNumber).build())
                    .setType(orderType)
                    .setVersion(orderVersion)
                    .build(),
            )
            .setShipping(
                ProtoPurchaseOrderShipping
                    .newBuilder()
                    .setAddress(
                        PostalAddress.newBuilder()
                            .addAddressLines(shippingAddressLines1)
                            .addAddressLines(shippingAddressLines2)
                            .setOrganization(shippingAddressOrganization)
                            .setLocality(shippingAddressLocality)
                            .setRegionCode(shippingAddressRegionCode)
                            .setAdministrativeArea(shippingAddressArea)
                            .setPostalCode(shippingAddressPostalCode),
                    )
                    .setMethod(shippingMethod)
                    .build(),
            )
            .setId(orderId.toString())
            .setYearWeek(ProtoPurchaseOrder.YearWeek.newBuilder().setWeek(week).setYear(year))
            .setCreatorId(userId.toString())
            .setCreatorEmail(userEmail)
            .setStatus(status)
            .setSupplierId(supplierId.toString())
            .setSupplierCode(supplierCode)
            .setDistributionCenterCode(dcCode)
            .setRegionCode(dcHfMarketCode)
            .setExpectedArrival(
                ProtoPurchaseOrder.PurchaseOrderExpectedArrival.newBuilder()
                    .setStartTime(expectedStartTime.toTimestampProto())
                    .setEndTime(expectedEndTime.toTimestampProto())
                    .build(),
            )
            .setEmergencyReason("EMERGENCY")
            .setTotalPrice(money)
            .setCreateTime(createTime.toTimestampProto())
            .setUpdateTime(updateTime.toTimestampProto())
            .setEmergencyReason(emergencyReason)

        if (comment != null) {
            order.comment = comment
        }

        if (sendTime != null) {
            order.sendTime = sendTime.toTimestampProto()
        }

        order.deliveryDateChangeReasonUuid = deliveryDateChangeReasonUUID
        orderItems.map { orderItem -> order.addOrderItems(orderItem) }
        return order.build()
    }

    @Suppress("LongParameterList")
    fun getUnitItem(
        skuId: UUID = randomUUID(),
        changeReasonUUID: UUID? = null,
        quantity: String = "20",
        buffer: String = "1",
        totalPrice: Double = 10.0,
        orderSize: Int = 20,
        orderPrice: Double = 1.0,
    ): ProtoPurchaseOrderItem = ProtoPurchaseOrderItem
        .newBuilder()
        .setOrderSize(orderSize)
        .setQuantity(Decimal.newBuilder().setValue(quantity))
        .setBuffer(Decimal.newBuilder().setValue(buffer))
        .setPrice(orderPrice.convertDoubleToGoogleMoney())
        .setTotalPrice(totalPrice.convertDoubleToGoogleMoney())
        .setUnitPackaging(ProtoUnitPackaging.getDefaultInstance())
        .setSkuId(skuId.toString())
        .setOrderItemChangeReasonUuid(changeReasonUUID?.toString() ?: "")
        .build()

    private fun Double.convertDoubleToGoogleMoney(): GoogleMoney {
        val units = this.toLong()
        val decimalPart = this - units

        return GoogleMoney
            .newBuilder()
            .setCurrencyCode("EUR")
            .setUnits(units)
            .setNanos((decimalPart * 1_000_000_000).toInt())
            .build()
    }

    @Suppress("LongParameterList")
    fun getCaseItem(
        skuId: UUID = randomUUID(),
        priceAmount: Int = 10,
        priceCurrency: String = "USD",
        totalPriceAmount: Int = 20,
        totalPriceCurrency: String = "USD",
        correctionReason: String = "Some good reason",
        buffer: String = "10.5021",
        quantity: String = "20",
        caseSize: String = "5",
        orderSize: Int = 5,
        changeReasonUUID: UUID? = null,
        uom: ProtoUOM = ProtoUOM.UOM_UNIT,
        palletSize: Int? = null,
    ): ProtoPurchaseOrderItem {
        val protoPurchaseOrder = ProtoPurchaseOrderItem
            .newBuilder()
            .setCorrectionReason(correctionReason)
            .setPrice(buildMoney(currencyCode = priceCurrency, priceAmount.toLong()))
            .setTotalPrice(buildMoney(currencyCode = totalPriceCurrency, totalPriceAmount.toLong()))
            .setBuffer(Decimal.newBuilder().setValue(buffer))
            .setQuantity(Decimal.newBuilder().setValue(quantity))
            .setSkuId(skuId.toString())
            .setOrderItemChangeReasonUuid(changeReasonUUID?.toString() ?: "")
            .setOrderSize(orderSize)
        val protoCasePackaging =
            ProtoCasePackaging
                .newBuilder()
                .setUnit(uom)
                .setSize(Decimal.newBuilder().setValue(caseSize))

        if (palletSize != null) {
            protoCasePackaging.setPalletSize(palletSize)
        }

        return protoPurchaseOrder.setCasePackaging(protoCasePackaging.build()).build()
    }

    private fun LocalDateTime.toTimestampProto(): Timestamp {
        val instant = this.toInstant(ZoneOffset.UTC)
        return Timestamp.newBuilder().setSeconds(instant.epochSecond)
            .setNanos(instant.nano).build()
    }

    private fun buildMoney(currencyCode: String, units: Long, nanos: Int = 0) =
        GoogleMoney.newBuilder().setCurrencyCode(currencyCode)
            .setUnits(units)
            .setNanos(nanos)
            .build()

    @Suppress("LongParameterList")
    fun createPurchaseOrderEntity(
        poNumber: String = "2153VE123456",
        id: UUID = UUID.fromString("37c2e641-ac77-4fec-8b17-83091d4a9b60"),
        yearWeek: YearWeek = YearWeek("2022-W10"),
        status: PurchaseOrderStatus = INITIATED,
        dcCode: String = "VE",
        supplierCode: String = "10123",
        type: POType = STANDARD,
        bufferPermyriad: Permyriad = Permyriad(20),
        createdAt: LocalDateTime = LocalDateTime.now(),
        shipMethod: ShipMethodEnum = VENDOR,
        version: Int = 1,
        casesPerPallet: Int? = 5,
        shippingAddress: ShippingAddress = ShippingAddress(
            "",
            "",
            "Verden",
            "",
            "27283",
            "DE",
        )
    ) = PurchaseOrder(
        poNumber = poNumber,
        version = version,
        type = type,
        id = id,
        yearWeek = yearWeek,
        userId = randomUUID(),
        userEmail = "<EMAIL>",
        status = status,
        sendTime = LocalDateTime.of(2021, 1, 4, 10, 0),
        supplierId = UUID.fromString("45d82eca-280c-4e59-956d-66e354bb6781"),
        supplierCode = supplierCode,
        dcCode = dcCode,
        shippingMethod = shipMethod,
        shippingAddress = shippingAddress,
        expectedStartTime = LocalDateTime.of(2021, 1, 4, 10, 0),
        expectedEndTime = LocalDateTime.of(2021, 1, 4, 10, 0),
        orderItems = setOf(
            OrderItem(
                poId = id,
                skuId = randomUUID(),
                totalQty = BigDecimal(5),
                price = Money(BigDecimal(50), "EUR"),
                totalPrice = Money(BigDecimal(250), "EUR"),
                buffer = bufferPermyriad,
                correctionReason = "NA",
                packaging = Packaging(UNIT_TYPE, null, UOM.UNIT),
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                rawQty = null,
                changeReasonId = null,
                casesPerPallet = casesPerPallet,
            ),
        ),
        totalPrice = Money(BigDecimal(250), "EUR"),
        createdAt = createdAt,
        updatedAt = LocalDateTime.now(),
        isSynced = false,
        deliveryDateChangeReasonId = getDeliveryDateChangeReasonId()
    )
}
