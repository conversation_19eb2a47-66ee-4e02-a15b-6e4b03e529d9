package com.hellofresh.oms.distributionCenter

import com.hellofresh.oms.distributionCenter.consumer.toEntity
import com.hellofresh.oms.model.DistributionCenterStatus
import com.hellofresh.oms.model.getBillingAddress
import com.hellofresh.oms.model.getDeliveryAddress
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class DistributionCenterMapperTest {
    @Test
    fun `should throw if a DC has more than one market code`() {
        val distributionCenterProto = Fixture.createDistributionCenterProto(
            marketCodes = listOf("some market", "another market"),
        )

        assertThrows<IllegalArgumentException> { distributionCenterProto.toEntity(LocalDateTime.now()) }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "true,ACTIVE",
            "false,INACTIVE",
        ],
    )
    fun `should correctly map proto to entity`(enabled: <PERSON>olean, expectedStatus: DistributionCenterStatus) {
        val distributionCenterProto = Fixture.createDistributionCenterProto(enabled = enabled)
        val now = LocalDateTime.now()

        val distributionCenterEntity = distributionCenterProto.toEntity(now)

        assertEquals(expectedStatus, distributionCenterEntity.status)
        assertEquals("NY", distributionCenterEntity.code)
        assertEquals("New York", distributionCenterEntity.name)
        assertEquals("us", distributionCenterEntity.market)

        with(distributionCenterEntity.getDeliveryAddress()) {
            assertNotNull(this)

            assertEquals("US", countryCode)
            assertEquals("New York", state)
            assertEquals("New Jersey", city)
            assertEquals("Street St, 1000th Floor", address)
            assertEquals("123", number)
            assertEquals("12345", zip)
            assertNull(company)
        }

        with(distributionCenterEntity.getBillingAddress()) {
            assertNotNull(this)

            assertEquals("US", countryCode)
            assertEquals("AZ", state)
            assertEquals("Phoenix", city)
            assertEquals("71st Avenue", address)
            assertEquals("666", number)
            assertEquals("67890", zip)
            assertEquals("HelloFresh", company)
        }
    }
}
