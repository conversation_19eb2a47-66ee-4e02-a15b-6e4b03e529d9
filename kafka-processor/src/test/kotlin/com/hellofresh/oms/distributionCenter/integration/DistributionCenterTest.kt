package com.hellofresh.oms.distributionCenter.integration

import com.google.type.PostalAddress
import com.hellofresh.oms.distributionCenter.Fixture
import com.hellofresh.oms.distributionCenter.repository.DistributionCenterRepository
import com.hellofresh.oms.integrationTestUtils.AbstractIntegrationTest
import com.hellofresh.oms.integrationTestUtils.assertAwaiting
import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterStatus
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter as DistributionCenterProto
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.test.context.jdbc.Sql

class DistributionCenterTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var producer: KafkaTemplate<String, DistributionCenterProto>

    @Autowired
    private lateinit var distributionCenterRepository: DistributionCenterRepository

    @Value("\${topics.distribution-center}")
    private lateinit var topic: String

    @Test
    fun `should create a new distribution center`() {
        // given
        val distributionCenterCode = "XX"
        val distributionCenterProto = Fixture.createDistributionCenterProto(code = distributionCenterCode)

        // when
        publishMessage(distributionCenterProto)

        // then
        assertAwaiting {
            with(distributionCenterRepository.findByCode(distributionCenterCode)) {
                assertNotNull(this)
                assertMatching(distributionCenterProto)
            }
        }
    }

    @Test
    @Sql(scripts = ["/data/dc.sql"])
    fun `should update a distribution center`() {
        val distributionCenterCode = "XX"

        val expectedName = "updated name"

        val updatedDistributionCenterProto = Fixture.createDistributionCenterProto(
            code = distributionCenterCode,
            name = expectedName,
            deliveryAddressLocality = "updated locality",
            billingAddressOrganization = "updated organization",
        )

        publishMessage(updatedDistributionCenterProto)

        assertAwaiting {
            with(distributionCenterRepository.findByCode(distributionCenterCode)) {
                assertNotNull(this)
                assertMatching(updatedDistributionCenterProto)
            }
        }
    }

    private fun DistributionCenter.assertMatching(distributionCenterProto: DistributionCenterProto) {
        assertEquals(distributionCenterProto.name, name)
        assertEquals(distributionCenterProto.marketList.first().code, market)
        assertEquals(DistributionCenterStatus.fromBoolean(distributionCenterProto.enabled), status)

        addresses.first().assetMatching(distributionCenterProto.deliveryAddress)
        addresses.last().assetMatching(distributionCenterProto.billingAddress)
    }

    private fun DistributionCenterAddress.assetMatching(addressProto: PostalAddress) {
        assertEquals(addressProto.regionCode, countryCode)
        assertEquals(addressProto.administrativeArea, state)
        assertEquals(addressProto.addressLinesList.drop(1).joinToString("\n"), address)
        assertEquals(addressProto.addressLinesList.first(), number)
        assertEquals(addressProto.postalCode, zip)
        assertEquals(addressProto.locality, city)
        assertEquals(addressProto.organization.ifEmpty { null }, company)
    }

    private fun publishMessage(message: DistributionCenterProto) {
        producer.send(topic, message.id, message)
        producer.flush()
    }
}
