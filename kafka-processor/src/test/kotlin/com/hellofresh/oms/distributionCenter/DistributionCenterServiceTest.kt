package com.hellofresh.oms.distributionCenter

import com.hellofresh.oms.distributionCenter.repository.DistributionCenterRepository
import com.hellofresh.oms.distributionCenter.service.DistributionCenterService
import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterStatus
import com.hellofresh.oms.model.getBillingAddress
import com.hellofresh.oms.model.getDeliveryAddress
import java.time.LocalDateTime
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class DistributionCenterServiceTest {
    private val distributionCenterRepositoryMock: DistributionCenterRepository = Mockito.mock(
        DistributionCenterRepository::class.java,
    )
    private val distributionCenterService = DistributionCenterService(distributionCenterRepositoryMock)

    @Test
    fun `should merge DCs when a DC with the same code already exists`() {
        val existingDcCreatedAt = LocalDateTime.now().minusHours(1)
        val dcCode = "code"
        val existingDc = Fixture.createDistributionCenterEntity(
            code = dcCode,
            status = DistributionCenterStatus.ACTIVE,
            createdAt = existingDcCreatedAt,
            name = "name 1",
            marketCode = "market 1",
            deliveryAddressCountryCode = "delivery country 1",
            deliveryAddressState = "delivery state 1",
            deliveryAddressCity = "delivery city 1",
            deliveryAddressLines = "delivery address 1",
            deliveryAddressNumber = "delivery number 1",
            deliveryAddressZip = "delivery zip 1",
            billingAddressCountryCode = "billing country 1",
            billingAddressState = "billing state 1",
            billingAddressCity = "billing city 1",
            billingAddressLines = "billing address 1",
            billingAddressNumber = "billing number 1",
            billingAddressZip = "billing zip 1",
            billingAddressCompany = "billing company 1",
        )
        whenever(distributionCenterRepositoryMock.findByCode(dcCode)).thenReturn(existingDc)

        val newDc = Fixture.createDistributionCenterEntity(
            code = dcCode,
            status = DistributionCenterStatus.INACTIVE,
            name = "name 2",
            marketCode = "market 2",
            deliveryAddressCountryCode = "delivery country 2",
            deliveryAddressState = "delivery state 2",
            deliveryAddressCity = "delivery city 2",
            deliveryAddressLines = "delivery address 2",
            deliveryAddressNumber = "delivery number 2",
            deliveryAddressZip = "delivery zip 2",
            billingAddressCountryCode = "billing country 2",
            billingAddressState = "billing state 2",
            billingAddressCity = "billing city 2",
            billingAddressLines = "billing address 2",
            billingAddressNumber = "billing number 2",
            billingAddressZip = "billing zip 2",
            billingAddressCompany = "billing company 2",
        )
        distributionCenterService.process(newDc)

        val argumentCaptor = argumentCaptor<DistributionCenter>()
        verify(distributionCenterRepositoryMock, Mockito.times(1)).saveAndFlush(argumentCaptor.capture())

        with(argumentCaptor.firstValue) {
            assertEquals(existingDc.code, dcCode)
            assertEquals(existingDc.createdAt, createdAt)
            assertEquals(newDc.updatedAt, updatedAt)
            assertEquals(newDc.name, name)
            assertEquals(newDc.market, market)
            assertEquals(newDc.status, status)
            assertNotNull(getDeliveryAddress())
                .verify(newDc.getDeliveryAddress()!!, existingDc.getDeliveryAddress()!!)
            assertNotNull(getBillingAddress())
                .verify(newDc.getBillingAddress()!!, existingDc.getBillingAddress()!!)
        }
    }

    @Test
    fun `should create new DC if a DC with the same code does not exist`() {
        val dcCode = "NY"
        whenever(distributionCenterRepositoryMock.findById(dcCode))
            .thenReturn(Optional.empty())

        val newDc = Fixture.createDistributionCenterEntity(code = dcCode)
        distributionCenterService.process(newDc)

        val argumentCaptor = argumentCaptor<DistributionCenter>()
        verify(distributionCenterRepositoryMock, Mockito.times(1)).saveAndFlush(argumentCaptor.capture())

        with(argumentCaptor.firstValue) {
            assertEquals(newDc.code, code)
            assertEquals(newDc.createdAt, createdAt)
            assertEquals(newDc.updatedAt, updatedAt)
            assertEquals(newDc.name, name)
            assertEquals(newDc.market, market)
            assertEquals(newDc.status, status)
            assertNotNull(getDeliveryAddress()).verify(newDc.getDeliveryAddress()!!)
            assertNotNull(getBillingAddress()).verify(newDc.getBillingAddress()!!)
        }
    }

    private fun DistributionCenterAddress.verify(
        newAddress: DistributionCenterAddress,
        existingAddress: DistributionCenterAddress? = null,
    ) {
        val expectedId = existingAddress?.id ?: newAddress.id
        val expectedCreationTimestamp = existingAddress?.createdAt ?: newAddress.createdAt

        assertEquals(id, expectedId)
        assertEquals(createdAt, expectedCreationTimestamp)
        assertEquals(updatedAt, newAddress.updatedAt)
        assertEquals(countryCode, newAddress.countryCode)
        assertEquals(state, newAddress.state)
        assertEquals(city, newAddress.city)
        assertEquals(address, newAddress.address)
        assertEquals(number, newAddress.number)
        assertEquals(zip, newAddress.zip)
        assertEquals(company, newAddress.company)
    }
}
