package com.hellofresh.oms.distributionCenter

import com.google.type.PostalAddress
import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterAddressType
import com.hellofresh.oms.model.DistributionCenterStatus
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter as DistributionCenterProto
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter.Market
import java.time.LocalDateTime
import java.util.UUID

object Fixture {
    @Suppress("LongParameterList")
    fun createDistributionCenterProto(
        id: String = UUID.randomUUID().toString(),
        code: String = "NY",
        name: String = "New York",
        marketCodes: List<String> = listOf("us"),
        enabled: Boolean = true,
        deliveryAddressRegionCode: String = "US",
        deliveryAddressAdministrativeArea: String = "New York",
        deliveryAddressLocality: String = "New Jersey",
        deliveryAddressLine1: String = "123",
        deliveryAddressLine2: String = "Street St, 1000th Floor",
        deliveryAddressPostalCode: String = "12345",
        billingAddressRegionCode: String = "US",
        billingAddressAdministrativeArea: String = "AZ",
        billingAddressLocality: String = "Phoenix",
        billingAddressLine1: String = "666",
        billingAddressLine2: String = "71st Avenue",
        billingAddressPostalCode: String = "67890",
        billingAddressOrganization: String = "HelloFresh",
    ): DistributionCenterProto = DistributionCenterProto.newBuilder()
        .apply {
            this.id = id
            this.code = code
            this.name = name
            this.addAllMarket(marketCodes.map { createMarketProto(it) })
            this.enabled = enabled
            this.deliveryAddress = createAddressProto(
                deliveryAddressRegionCode,
                deliveryAddressAdministrativeArea,
                deliveryAddressLocality,
                listOf(deliveryAddressLine1, deliveryAddressLine2),
                deliveryAddressPostalCode,
                null,
            )
            this.billingAddress = createAddressProto(
                billingAddressRegionCode,
                billingAddressAdministrativeArea,
                billingAddressLocality,
                listOf(billingAddressLine1, billingAddressLine2),
                billingAddressPostalCode,
                billingAddressOrganization,
            )
        }.build()

    @Suppress("LongParameterList")
    fun createDistributionCenterEntity(
        code: String = "NY",
        status: DistributionCenterStatus = DistributionCenterStatus.ACTIVE,
        name: String = "New York",
        marketCode: String = "us",
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = createdAt,
        deliveryAddressId: UUID = UUID.randomUUID(),
        deliveryAddressCountryCode: String = "US",
        deliveryAddressState: String = "New York",
        deliveryAddressCity: String = "New York",
        deliveryAddressLines: String = "Street St\n1000th Floor",
        deliveryAddressNumber: String = "123",
        deliveryAddressZip: String = "12345",
        deliveryAddressCreatedAt: LocalDateTime = createdAt,
        deliveryAddressUpdatedAt: LocalDateTime = createdAt,
        billingAddressId: UUID = UUID.randomUUID(),
        billingAddressCountryCode: String = "US",
        billingAddressState: String = "AZ",
        billingAddressCity: String = "Phoenix",
        billingAddressLines: String = "71st Avenue",
        billingAddressNumber: String = "666",
        billingAddressZip: String = "67890",
        billingAddressCompany: String = "HelloFresh",
        billingAddressCreatedAt: LocalDateTime = createdAt,
        billingAddressUpdatedAt: LocalDateTime = createdAt,
    ) = DistributionCenter(
        code,
        status,
        name,
        marketCode,
        listOf(
            createAddressEntity(
                deliveryAddressId,
                code,
                deliveryAddressCountryCode,
                deliveryAddressState,
                deliveryAddressCity,
                deliveryAddressLines,
                deliveryAddressNumber,
                deliveryAddressZip,
                null,
                DistributionCenterAddressType.DELIVERY,
                deliveryAddressCreatedAt,
                deliveryAddressUpdatedAt,
            ),
            createAddressEntity(
                billingAddressId,
                code,
                billingAddressCountryCode,
                billingAddressState,
                billingAddressCity,
                billingAddressLines,
                billingAddressNumber,
                billingAddressZip,
                billingAddressCompany,
                DistributionCenterAddressType.BILLING,
                billingAddressCreatedAt,
                billingAddressUpdatedAt,
            ),
        ),
        createdAt,
        updatedAt,
    )

    @Suppress("LongParameterList")
    private fun createAddressEntity(
        id: UUID,
        distributionCenterCode: String,
        countryCode: String,
        state: String,
        city: String,
        address: String,
        number: String,
        zip: String,
        company: String?,
        type: DistributionCenterAddressType,
        createdAt: LocalDateTime,
        updatedAt: LocalDateTime,
    ) = DistributionCenterAddress(
        id,
        distributionCenterCode,
        number,
        address,
        zip,
        city,
        state,
        company,
        type,
        createdAt,
        updatedAt,
        countryCode,
    )

    @Suppress("LongParameterList")
    private fun createAddressProto(
        regionCode: String,
        administrativeArea: String,
        locality: String,
        addressLines: List<String>,
        postalCode: String,
        organization: String?,
    ) = PostalAddress.newBuilder()
        .apply {
            this.regionCode = regionCode
            this.administrativeArea = administrativeArea
            this.locality = locality
            this.addAllAddressLines(addressLines)
            this.postalCode = postalCode
            this.organization = organization.orEmpty()
        }
        .build()

    private fun createMarketProto(marketCode: String): Market =
        Market.newBuilder().setCode(marketCode).build()
}
