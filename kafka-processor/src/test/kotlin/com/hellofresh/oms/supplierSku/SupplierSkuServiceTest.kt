package com.hellofresh.oms.supplierSku

import com.hellofresh.oms.supplierSku.repository.SupplierSkuRepository
import com.hellofresh.oms.supplierSku.service.SupplierSkuService
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class SupplierSkuServiceTest {

    @Mock
    lateinit var supplierSkuRepositoryMock: SupplierSkuRepository

    @InjectMocks
    lateinit var subject: SupplierSkuService

    @Test
    fun `should save new supplerSku when it doesn't exist in the database`() {
        // given
        val incomingSupplierSkuEntity = Fixture.getSupplierSkuEntity()
        whenever(supplierSkuRepositoryMock.save(incomingSupplierSkuEntity)).thenReturn(incomingSupplierSkuEntity)

        // when
        val actualSkuEntity = subject.upsertSupplierSku(incomingSupplierSkuEntity)

        // then
        verify(supplierSkuRepositoryMock).save(incomingSupplierSkuEntity)
        assertNotNull(actualSkuEntity)
    }
}
