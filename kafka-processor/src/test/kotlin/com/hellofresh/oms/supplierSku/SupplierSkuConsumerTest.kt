package com.hellofresh.oms.supplierSku

import com.hellofresh.oms.supplierSku.consumer.SupplierSkuConsumer
import com.hellofresh.oms.supplierSku.service.SupplierSkuService
import com.hellofresh.planning.suppliersku.suppliersku as AvroSupplierSku
import java.util.UUID
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

class SupplierSkuConsumerTest {
    private val supplierSkuServiceMock = Mockito.mock(SupplierSkuService::class.java)

    @Test
    fun `should call supplierSku service method when consuming message`() {
        // given
        val subject = SupplierSkuConsumer(supplierSkuServiceMock)

        // when
        subject.processSupplierSku().accept(
            object : Message<AvroSupplierSku> {
                override fun getPayload() = Fixture.getAvroSupplierSku()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        Mockito.verify(supplierSkuServiceMock).upsertSupplierSku(any())
    }
}
