package com.hellofresh.oms.supplierSku

import com.hellofresh.oms.model.SupplierSkuStatus
import com.hellofresh.oms.model.SupplierSkuStatus.ACTIVE
import com.hellofresh.oms.model.SupplierSkuStatus.ARCHIVED
import com.hellofresh.oms.model.SupplierSkuStatus.INACTIVE
import com.hellofresh.oms.model.SupplierSkuStatus.OFFBOARDING
import com.hellofresh.oms.model.SupplierSkuStatus.ONBOARDING
import com.hellofresh.oms.supplierSku.consumer.toEntity
import java.util.UUID.randomUUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@TestInstance(PER_CLASS)
class SkuMapperTest {

    @Test
    fun `should map avro SupplierSku to supplierSku entity`() {
        // given
        val avroSupplierSku = Fixture.getAvroSupplierSku(
            uuid = randomUUID(),
            parentSupplierId = randomUUID(),
            skuId = randomUUID(),
            market = "us",
            status = "Active",
        )

        // when
        val supplierSkuEntity = avroSupplierSku.toEntity()

        // then
        assertAll(
            { assertEquals(avroSupplierSku.id, supplierSkuEntity.uuid) },
            { assertEquals(avroSupplierSku.supplierId, supplierSkuEntity.parentSupplierId) },
            { assertEquals(avroSupplierSku.culinarySkuId, supplierSkuEntity.skuId) },
            { assertEquals("us", supplierSkuEntity.market) },
            { assertEquals("ACTIVE", supplierSkuEntity.status.toString()) },
        )
    }

    @Test
    fun `should throw IllegalArgumentException when mapping AvroSupplierSku to SupplierSku entity`() {
        // given
        val avro = Fixture.getAvroSupplierSku(status = "UnknownStatus")

        // then
        assertThrows<IllegalArgumentException> { avro.toEntity() }.also {
            assertEquals("Unknown SupplierSku status: UnknownStatus", it.message)
        }
    }

    @ParameterizedTest
    @MethodSource("statusProvider")
    fun `should map AvroSupplierSku status to entity status`(
        incomingStatus: String,
        expectedStatus: SupplierSkuStatus
    ) {
        // given
        val avroSupplierSku = Fixture.getAvroSupplierSku(status = incomingStatus)

        // when
        val supplierSku = avroSupplierSku.toEntity()

        // then
        assertEquals(expectedStatus, supplierSku.status)
    }

    @Suppress("UnusedPrivateMember")
    private fun statusProvider() = listOf(
        Arguments.arguments("Inactive ", INACTIVE),
        Arguments.arguments("active", ACTIVE),
        Arguments.arguments(" Onboarding", ONBOARDING),
        Arguments.arguments("archived", ARCHIVED),
        Arguments.arguments("Offboarding", OFFBOARDING),
    )
}
