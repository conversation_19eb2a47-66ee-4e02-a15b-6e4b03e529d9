package com.hellofresh.oms.supplierSku

import com.hellofresh.oms.model.SupplierSku
import com.hellofresh.oms.model.SupplierSkuStatus.ACTIVE
import com.hellofresh.planning.suppliersku.event_types
import com.hellofresh.planning.suppliersku.suppliersku as AvroSupplierSku
import java.util.UUID
import java.util.UUID.randomUUID

object Fixture {
    fun getAvroSupplierSku(
        uuid: UUID = randomUUID(),
        parentSupplierId: UUID = randomUUID(),
        skuId: UUID = randomUUID(),
        market: String = "us",
        status: String = "Active"
    ): AvroSupplierSku = AvroSupplierSku.newBuilder()
        .setId(uuid)
        .setEventType(event_types.entity_created)
        .setSupplierId(parentSupplierId)
        .setSupplierName("Sup Name")
        .setSupplierCode(1235)
        .setCulinarySkuId(skuId)
        .setSupplierName("Sku Name")
        .setCulinarySkuCode("PHF-123-1231")
        .setStatus(status)
        .setMarket(market)
        .setCreatedAt("")
        .setUpdatedAt("")
        .build()

    fun getSupplierSkuEntity(): SupplierSku = SupplierSku(
        uuid = randomUUID(),
        parentSupplierId = randomUUID(),
        skuId = randomUUID(),
        market = "us",
        status = ACTIVE,
        supplierCode = "1234"
    )
}
