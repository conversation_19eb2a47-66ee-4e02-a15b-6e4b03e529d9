spring:
  cloud:
    stream:
      kafka:
        binder:
          autoCreateTopics: true
        bindings:
          processSupplier-in-0:
            consumer:
              resetOffsets: true
          processShipMethod-in-0:
            consumer:
              resetOffsets: true
          processSku-in-0:
            consumer:
              resetOffsets: true
          processSupplierSku-in-0:
            consumer:
              resetOffsets: true
          processSupplierSkuPrice-in-0:
            consumer:
              resetOffsets: true
          processPurchaseOrder-in-0:
            consumer:
              resetOffsets: true
          processDistributionCenter-in-0:
            consumer:
              resetOffsets: true
          processGoodsReceivedNote-in-0:
            consumer:
              resetOffsets: true
          processAcknowledgement-in-0:
            consumer:
              reset-offsets: true
          processAdvanceShippingNotification-in-0:
            consumer:
              reset-offsets: true
          processPurchaseOrderShipment-in-0:
            consumer:
              resetOffsets: true
          processSupplyQuantityRecommendationDaily-in-0:
            consumer:
              resetOffsets: true
          processImtPoVoid-in-0:
            consumer:
              resetOffsets: true
          processImtComment-in-0:
            consumer:
              resetOffsets: true
          processImtExportReceipt-in-0:
            consumer:
              resetOffsets: true
          processTransferOrder-in-0:
            consumer:
              resetOffsets: true

application:
  brokerAddress: ${BROKER_URL:http://localhost:29092}
  schemaRegistryUrl: ${SCHEMA_REGISTRY_URL:http://localhost:28081}

management:
  tracing:
    enabled: false

tapioca:
  base-url: "http://localhost:1919"

auth-service:
  base-url: "http://localhost:1919"
  client-id: "client_id"
  client-secret: "client_secret"
