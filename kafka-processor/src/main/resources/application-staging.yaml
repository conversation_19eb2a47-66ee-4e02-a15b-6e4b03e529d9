application:
  brokerAddress: "https://kafka-staging-hellofresh.aivencloud.com:23419"
  schemaRegistryUrl: "https://kafka-staging-hellofresh.aivencloud.com:23411"
  trustStorePath: classpath:kafka/staging-truststore.jks

spring:
  cloud:
    stream:
      kafka:
        binder:
          configuration:
            # Aiven Cloud Connection parameters
            ssl.truststore.location: ${application.trustStorePath}
            ssl.truststore.password: ${HF_KAFKA_TRUSTSTORE_PASSWORD}
            sasl.mechanism: PLAIN
            security.protocol: SASL_SSL
            ssl.endpoint.identification.algorithm: https
            sasl.jaas.config: "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"${HF_AIVEN_USERNAME}\" password=\"${HF_AIVEN_PASSWORD}\";"

  kafka:
    properties:
      schema.registry.url: ${application.schemaRegistryUrl}
      basic.auth.credentials.source: USER_INFO
      basic.auth.user.info: ${HF_AIVEN_USERNAME}:${HF_AIVEN_PASSWORD}
