topics:
  ship-method: public.planning.supplier.default-ship-methods.v1
  supplier-sku: public.planning.suppliersku.v1
  supplier: public.planning.facility.v1
  sku: public.planning.culinarysku.v1
  supplier-sku-price: public.planning.suppliersku.pricing.v1
  purchase-order-v1: public.supply.procurement.purchase-order.v1
  purchase-order-v2: public.supply.procurement.purchase-order.v2
  distribution-center: public.scm.registry.dc.v1beta1
  supplier-sku-packaging: public.planning.suppliersku.packaging.v1
  goods-received-note: public.distribution-center.inbound.goods-received-note.v1
  purchase-order-acknowledgement: public.supply.purchase-order.acknowledgement.v2
  advance-shipping-notification: public.supply.advance-shipping-notice.v2
  purchase-order-shipment: public.distribution-center.third-party.blujay.inbound.purchase-order-shipment.v1beta1
  imt-om-po-void: imt-om-integration.debezium.inventory.po_void
  imt-om-comment: imt-om-integration.debezium.procurement.comment
  imt-om-export-receipts: imt-om-integration.debezium.highjump.v_procurement_export_receipts
  supply-quantity-recommendation-daily: public.ordering.supply-quantity-recommendation-daily.v1
  transfer-order: public.transfer-order.v1

spring:
  application:
    name: kafka-processor
  datasource:
    driverClassName: org.postgresql.Driver
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      connection-timeout: 30000 # 30 seconds
      idle-timeout: 300000 # 5 minutes
      maximum-pool-size: 10
      minimum-idle: 2
      connection-test-query: "SELECT 1"
      validation-timeout: 5000 # 5 second
      max-lifetime: 1800000 # 30 minutes
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    hibernate.ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    open-in-view: false
  cloud:
    function:
      definition: processSupplier;processShipMethod;processSku;processSupplierSku;processSupplierSkuPrice;processPurchaseOrder;processDistributionCenter;processSupplierSkuPackaging;processGoodsReceivedNote;processAcknowledgement;processAdvanceShippingNotification;processPurchaseOrderShipment;processImtPoVoid;processImtComment;processImtExportReceipt;processTransferOrder;processSupplyQuantityRecommendationDaily;purchase-order-v2
    stream:
      bindings:
        processSupplier-in-0:
          destination: ${topics.supplier}
          group: oms-${spring.application.name}-supplier.v1
        processShipMethod-in-0:
          destination: ${topics.ship-method}
          group: oms-${spring.application.name}-ship-method.v3
        processSku-in-0:
          destination: ${topics.sku}
          group: oms-${spring.application.name}-sku.v1
        processSupplierSku-in-0:
          destination: ${topics.supplier-sku}
          group: oms-${spring.application.name}-supplierSku.v2
        processSupplierSkuPrice-in-0:
          destination: ${topics.supplier-sku-price}
          group: oms-${spring.application.name}-supplierSkuPrice.v4
        processPurchaseOrder-in-0:
          destination: ${topics.purchase-order-v1}
          group: oms-${spring.application.name}-purchaseOrder.v2
        processDistributionCenter-in-0:
          destination: ${topics.distribution-center}
          group: oms-${spring.application.name}-distributionCenter.v1
        processSupplierSkuPackaging-in-0:
          destination: ${topics.supplier-sku-packaging}
          group: oms-${spring.application.name}-supplierSkuPackaging.v1
        processGoodsReceivedNote-in-0:
          destination: ${topics.goods-received-note}
          group: oms-${spring.application.name}-goodsReceivedNote.v1
        processSupplyQuantityRecommendationDaily-in-0:
          destination: ${topics.supply-quantity-recommendation-daily}
          group: oms-${spring.application.name}-supplyQuantityRecommendationDaily.v1
          consumer:
            batch-mode: true
        processAcknowledgement-in-0:
          destination: ${topics.purchase-order-acknowledgement}
          group: oms-${spring.application.name}-purchaseOrderAcknowledgement.v2
        processAdvanceShippingNotification-in-0:
          destination: ${topics.advance-shipping-notification}
          group: oms-${spring.application.name}-advanceShippingNotification.v1
        processPurchaseOrderShipment-in-0:
          destination: ${topics.purchase-order-shipment}
          group: oms-${spring.application.name}-purchase-order-shipment.v1
        processImtPoVoid-in-0:
          destination: ${topics.imt-om-po-void}
          group: oms-${spring.application.name}-imt-po-void.v1
        processImtComment-in-0:
          destination: ${topics.imt-om-comment}
          group: oms-${spring.application.name}-imt-comment.v1
        processImtExportReceipt-in-0:
          destination: ${topics.imt-om-export-receipts}
          group: oms-${spring.application.name}-imt-export-receipts.v3
          content-type: text/plain
          consumer:
              batch-mode: true
        processTransferOrder-in-0:
          destination: ${topics.transfer-order}
          group: oms-${spring.application.name}-transfer-order.v1
        purchase-order-v2-out-0:
          destination: ${topics.purchase-order-v2}
          producer:
            use-native-encoding: true
      kafka:
        binder:
          brokers: ${application.brokerAddress}
          autoCreateTopics: false
          consumer-properties:
            key.deserializer: org.apache.kafka.common.serialization.StringDeserializer
            value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
            spring.deserializer.value.delegate.class: io.confluent.kafka.serializers.KafkaAvroDeserializer
            schema.registry.url: ${application.schemaRegistryUrl}
            specific.avro.reader: true
          producer-properties:
            key.serializer: org.apache.kafka.common.serialization.StringSerializer
        bindings:
          processPurchaseOrder-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.purchaseOrder.serdes.PurchaseOrderProtoDeserializer
          processDistributionCenter-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.distributionCenter.serdes.DistributionCenterProtoDeserializer
          processGoodsReceivedNote-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.goodsReceivedNote.serdes.GoodsReceivedNoteDeserializer
          processAcknowledgement-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.acknowledgement.serdes.PurchaseOrderAcknowledgmentDeserializer
          processAdvanceShippingNotification-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.advanceShippingNotification.serdes.AdvanceShippingNotificationDeserializer
          processPurchaseOrderShipment-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.purchaseOrderShipment.serdes.PurchaseOrderShipmentDeserializer
          processSupplyQuantityRecommendationDaily-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.serdes.SupplyQuantityRecommendationDailyDeserializer
          processImtPoVoid-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.imt.DebeziumDeserializer
                specific.avro.reader: false
          processImtComment-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.imt.DebeziumDeserializer
                specific.avro.reader: false
          processImtExportReceipt-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.imt.DebeziumDeserializer
                specific.avro.reader: false
                max.poll.records: 2000
          processTransferOrder-in-0:
            consumer:
              configuration:
                value.deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
                spring.deserializer.value.delegate.class: com.hellofresh.oms.transferOrder.serdes.TransferOrderDeserializer
          purchase-order-v2-out-0:
            producer:
              configuration:
                value.serializer: com.hellofresh.oms.serdes.PurchaseOrderProtoSerializer

management:
  endpoint:
    health:
      show-details: "ALWAYS"
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState,diskSpace,db,ping,binders
  endpoints:
    access:
      default: read_only
    web:
      exposure:
        include: "*"
  server:
    port: 8081
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  info:
    git:
      mode: full
      enabled: true
  tracing:
    propagation:
      type: B3,W3C
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://${OTLP_EXPORTER_HOST:localhost}:9411/api/v2/spans

logging:
  level:
    org.apache.kafka.clients.Metadata: ERROR
    org.apache.kafka.clients.consumer.internals.ConsumerCoordinator: ERROR
  pattern:
    level: "%5p [%MDC{traceId},%MDC{spanId}]"

tapioca:
  base-url: ${TAPIOCA_BASE_URL}
  po-approve-url: /purchase-orders/{poId}/approve
  po-reject-url: /purchase-orders/{poId}/reject

auth-service:
  base-url: ${AUTH_SERVICE_BASE_URL}
  client-id: ${AUTH_SERVICE_CLIENT_ID}
  client-secret: ${AUTH_SERVICE_CLIENT_SECRET}

webclient:
  connection-timeout: 5s
  response-timeout: 5s
  max-idle-time: 20s
  max-life-time: 60s
  pending-acquire-timeout: 60s
  evict-in-background: 120s

statsig:
  api-key: ${STATSIG_API_KEY}
  environment: ${STATSIG_ENVIRONMENT}
  gates:
    enable-new-approve-reject: order_management_-_enable_new_approve_reject
    enable-po-topic-v2: order_management_-_enable_po_topic_v2

resilience4j:
  retry:
    instances:
      approvePurchaseOrder:
        maxAttempts: 3
        waitDuration: 2s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2
        retryExceptions:
          - com.hellofresh.oms.client.tapioca.exception.TapiocaClientException
          - org.springframework.web.reactive.function.client.WebClientRequestException
        ignoreExceptions:
          - com.hellofresh.oms.client.tapioca.exception.TapiocaConflictException
      rejectPurchaseOrder:
        maxAttempts: 3
        waitDuration: 2s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2
        retryExceptions:
          - com.hellofresh.oms.client.tapioca.exception.TapiocaClientException
          - org.springframework.web.reactive.function.client.WebClientRequestException
        ignoreExceptions:
          - com.hellofresh.oms.client.tapioca.exception.TapiocaConflictException
