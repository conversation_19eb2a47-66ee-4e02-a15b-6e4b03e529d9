package com.hellofresh.oms.shippingMethod.service

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.shippingMethod.repository.ShipMethodRepository
import io.micrometer.core.annotation.Timed
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ShipMethodService(
    @Autowired val shipMethodRepository: ShipMethodRepository
) {
    @Timed
    fun upsertShipMethod(shipMethod: ShipMethod): ShipMethod = shipMethodRepository.save(shipMethod)
}
