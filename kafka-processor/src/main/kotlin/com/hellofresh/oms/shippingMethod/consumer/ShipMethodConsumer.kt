package com.hellofresh.oms.shippingMethod.consumer

import com.hellofresh.oms.shippingMethod.service.ShipMethodService
import com.hellofresh.planning.supplier.shipmethods.ship_methods as AvroShipMethod
import java.util.UUID
import java.util.function.Consumer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class ShipMethodConsumer(
    @Autowired val shipMethodService: ShipMethodService
) {

    @Bean
    fun processShipMethod(): Consumer<Message<AvroShipMethod>> =
        Consumer { record ->
            val headerId = requireNotNull(
                record.headers["kafka_receivedMessageKey"]
            ) { "ShippingMethod id cannot be null" }
            val shipMethodId = UUID.fromString(headerId as String)
            val shipMethod = record.payload

            logger.info(
                "Received ship method :${shipMethod.shipMethod} dc:${shipMethod.distributionCenter} uuid:$shipMethodId"
            )

            shipMethodService.upsertShipMethod(shipMethod = shipMethod.mapToShipMethod(shipMethodId))
        }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(ShipMethodConsumer::class.java)
    }
}
