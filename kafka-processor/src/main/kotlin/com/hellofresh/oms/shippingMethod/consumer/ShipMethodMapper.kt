package com.hellofresh.oms.shippingMethod.consumer

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum.CROSSDOCK
import com.hellofresh.oms.model.ShipMethodEnum.FREIGHT_ON_BOARD
import com.hellofresh.oms.model.ShipMethodEnum.OTHER
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.planning.supplier.shipmethods.ship_methods as AvroShipMethod
import java.util.UUID

fun AvroShipMethod.mapToShipMethod(shipMethodId: UUID) =
    ShipMethod(
        uuid = shipMethodId,
        dcCode = this.distributionCenter,
        supplierId = this.supplierId,
        market = this.market,
        method = toShipMethodEnum(this.shipMethod),
    )

private fun toShipMethodEnum(avroShipMethod: String) =
    when (avroShipMethod) {
        "Vendor Delivered" -> VENDOR
        "Freight On Board" -> FREIGHT_ON_BOARD
        "Crossdock" -> CROSSDOCK
        "Other" -> OTHER
        else -> throw IllegalArgumentException("Unknown ship method: $avroShipMethod")
    }
