package com.hellofresh.oms.goodsReceivedNote.consumer

import com.google.type.DateTime
import com.google.type.Decimal
import com.hellofresh.oms.model.grn.DeliveryLineStateEnum
import com.hellofresh.oms.model.grn.DeliveryStateEnum
import com.hellofresh.oms.model.grn.Grn
import com.hellofresh.oms.model.grn.GrnStateEnum
import com.hellofresh.oms.model.grn.PurchaseOrderDelivery
import com.hellofresh.oms.model.grn.PurchaseOrderDeliveryLine
import com.hellofresh.oms.model.grn.UnitOfMeasureGrnEnum
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue as ProtoGoodsReceivedNoteValue
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState as ProtoDeliveryLineState
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState as ProtoDeliveryState
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState.DELIVERY_STATE_CLOSED
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState.DELIVERY_STATE_OPEN
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState.DELIVERY_STATE_RECEIVED
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState.DELIVERY_STATE_UNSPECIFIED
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState.UNRECOGNIZED
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDelivery as ProtoPurchaseOrderDelivery
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDeliveryLine as ProtoPurchaseOrderDeliveryLine
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.State as ProtoState
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.UnitOfMeasure as ProtoUnitsOfMeasure
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.reflect.KClass

fun KClass<Grn>.tryFrom(grn: ProtoGoodsReceivedNoteValue): Grn {
    val poNumber = tryParsePurchaseOrderNumber(grn.reference)
    val state = GrnStateEnum.tryFrom(grn.state)
    val grnId = UUID.randomUUID()
    return Grn(
        id = grnId,
        poNumber = poNumber,
        dcCode = grn.dcCode,
        deliveryStartTime = grn.takeIf { it.hasDeliveryStartTime() }?.deliveryStartTime?.toLocalDateTimeInUTC(),
        deliveryEndTime = grn.takeIf { it.hasDeliveryEndTime() }?.deliveryEndTime?.toLocalDateTimeInUTC(),
        wmsName = grn.wmsName.name,
        reference = grn.reference,
        deliveries = grn.deliveriesList.map { PurchaseOrderDelivery::class.tryFrom(grnId, it) },
        state = state,
    )
}

private fun KClass<PurchaseOrderDelivery>.tryFrom(grnId: UUID, poDelivery: ProtoPurchaseOrderDelivery): PurchaseOrderDelivery {
    val purchaseOrderDeliveryUuid = UUID.randomUUID()
    return PurchaseOrderDelivery(
        id = purchaseOrderDeliveryUuid,
        grnId = grnId,
        poDeliveryId = poDelivery.id,
        deliveryTime = poDelivery.takeIf { it.hasDeliveryTime() }?.deliveryTime?.toLocalDateTimeInUTC(),
        expectedDeliveryStartTime = poDelivery.takeIf {
            it.hasExpectedDeliveryStartTime()
        }?.expectedDeliveryStartTime?.toLocalDateTimeInUTC(),
        expectedDeliveryEndTime = poDelivery.takeIf {
            it.hasExpectedDeliveryEndTime()
        }?.expectedDeliveryEndTime?.toLocalDateTimeInUTC(),
        state = DeliveryStateEnum.tryFrom(poDelivery.state),
        lines = poDelivery.linesList.map {
            PurchaseOrderDeliveryLine::class.tryFrom(purchaseOrderDeliveryUuid, it)
        },
    )
}

private fun KClass<PurchaseOrderDeliveryLine>.tryFrom(
    purchaseOrderDeliveryId: UUID,
    poDeliveryLine: ProtoPurchaseOrderDeliveryLine
): PurchaseOrderDeliveryLine =
    PurchaseOrderDeliveryLine(
        id = UUID.randomUUID(),
        grnPurchaseOrderDeliveryId = purchaseOrderDeliveryId,
        poDeliveryLineId = poDeliveryLine.id,
        skuCode = poDeliveryLine.skuCode,
        unloadedQuantity = poDeliveryLine.unloadedQuantity.takeIf {
            poDeliveryLine.hasUnloadedQuantity()
        }?.getBigDecimal()?.toDouble(),
        receivedQuantity = poDeliveryLine.receivedQuantity.takeIf {
            poDeliveryLine.hasReceivedQuantity()
        }?.getBigDecimal()?.toDouble(),
        expectedQuantity = poDeliveryLine.expectedQuantity.takeIf {
            poDeliveryLine.hasExpectedQuantity()
        }?.getBigDecimal()?.toDouble(),
        rejectedQuantity = poDeliveryLine.rejectedQuantity.takeIf {
            poDeliveryLine.hasRejectedQuantity()
        }?.getBigDecimal()?.toDouble(),
        palletizedQuantity = poDeliveryLine.palletizedQuantity.takeIf {
            poDeliveryLine.hasPalletizedQuantity()
        }?.getBigDecimal()?.toDouble(),
        expirationDate = poDeliveryLine.takeIf { it.hasExpirationDate() }?.expirationDate?.toLocalDateTimeInUTC(),
        supplierLotNumber = poDeliveryLine.lotNumber,
        caseSize = poDeliveryLine.caseSize.takeIf { poDeliveryLine.hasCaseSize() }?.getBigDecimal()?.toDouble(),
        state = DeliveryLineStateEnum.tryFrom(poDeliveryLine.state),
        skuUom = UnitOfMeasureGrnEnum.tryFrom(poDeliveryLine.skuUom),
    )

/**
 * Protobuf 3 has done away with `required` or `optional` fields. Instead, all fields are required,
 * and when not found, a default value is initialized instead.
 * See:
 *  * https://protobuf.dev/programming-guides/proto3/#default
 *     * Note: "For numeric types, the default value is zero."
 *     * For Decimal() the default value is "" (empty string), not zero.
 *  * https://github.com/protocolbuffers/protobuf/issues/2497
 */
private fun Decimal.getBigDecimal(): BigDecimal = this.value.takeUnless {
    it.isNullOrBlank()
}?.toBigDecimal() ?: BigDecimal.ZERO

private fun tryParsePurchaseOrderNumber(reference: String): String {
    val firstSegment = reference.split('_').firstOrNull() ?: throw PurchaseOrderNumberMappingError(reference)
    val poNumberRegex = Regex("\\d{4}\\p{Alpha}{2}\\d{6}")
    if (firstSegment.matches(poNumberRegex)) {
        return firstSegment
    } else {
        throw PurchaseOrderNumberMappingError(reference)
    }
}

private fun DateTime.toLocalDateTimeInUTC(): LocalDateTime {
    val zoneId: ZoneId = if (this.hasTimeZone()) ZoneId.of(this.timeZone.id) else ZoneId.of("UTC")
    val zonedDateTime = ZonedDateTime.of(
        this.year,
        this.month,
        this.day,
        this.hours,
        this.minutes,
        this.seconds,
        this.nanos,
        zoneId,
    )

    return zonedDateTime.toLocalDateTime()
}

private fun GrnStateEnum.Companion.tryFrom(state: ProtoState): GrnStateEnum =
    when (state) {
        ProtoState.STATE_OPEN -> GrnStateEnum.STATE_OPEN
        ProtoState.STATE_CLOSE -> GrnStateEnum.STATE_CLOSE
        ProtoState.STATE_UNSPECIFIED -> GrnStateEnum.STATE_UNSPECIFIED
        ProtoState.UNRECOGNIZED -> throw EnumMappingError(state.name)
    }

private fun DeliveryStateEnum.Companion.tryFrom(state: ProtoDeliveryState): DeliveryStateEnum =
    when (state) {
        DELIVERY_STATE_OPEN -> DeliveryStateEnum.DELIVERY_STATE_OPEN
        DELIVERY_STATE_RECEIVED -> DeliveryStateEnum.DELIVERY_STATE_RECEIVED
        DELIVERY_STATE_CLOSED -> DeliveryStateEnum.DELIVERY_STATE_CLOSED
        DELIVERY_STATE_UNSPECIFIED -> DeliveryStateEnum.DELIVERY_STATE_UNSPECIFIED
        UNRECOGNIZED -> throw EnumMappingError(state.name)
    }

private fun UnitOfMeasureGrnEnum.Companion.tryFrom(uom: ProtoUnitsOfMeasure): UnitOfMeasureGrnEnum =
    when (uom) {
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_UNSPECIFIED -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_UNSPECIFIED
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_OTHER -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_OTHER
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_UNIT -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_UNIT
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_CASE -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_CASE
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_KG -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_KG
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_LBS -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_LBS
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_OZ -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_OZ
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_LITRE -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_LITRE
        ProtoUnitsOfMeasure.UNIT_OF_MEASURE_GAL -> UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_GAL
        ProtoUnitsOfMeasure.UNRECOGNIZED -> throw EnumMappingError(uom.name)
    }

private fun DeliveryLineStateEnum.Companion.tryFrom(state: ProtoDeliveryLineState): DeliveryLineStateEnum =
    when (state) {
        ProtoDeliveryLineState.DELIVERY_LINE_STATE_EXPECTED -> DeliveryLineStateEnum.DELIVERY_LINE_STATE_EXPECTED
        ProtoDeliveryLineState.DELIVERY_LINE_STATE_OPEN -> DeliveryLineStateEnum.DELIVERY_LINE_STATE_OPEN
        ProtoDeliveryLineState.DELIVERY_LINE_STATE_RECEIVED -> DeliveryLineStateEnum.DELIVERY_LINE_STATE_RECEIVED
        ProtoDeliveryLineState.DELIVERY_LINE_STATE_REJECTED -> DeliveryLineStateEnum.DELIVERY_LINE_STATE_REJECTED
        ProtoDeliveryLineState.DELIVERY_LINE_STATE_CLOSED -> DeliveryLineStateEnum.DELIVERY_LINE_STATE_CLOSED
        ProtoDeliveryLineState.DELIVERY_LINE_STATE_CANCELLED -> DeliveryLineStateEnum.DELIVERY_LINE_STATE_CANCELLED
        ProtoDeliveryLineState.DELIVERY_LINE_STATE_UNSPECIFIED -> DeliveryLineStateEnum.DELIVERY_LINE_STATE_UNSPECIFIED
        ProtoDeliveryLineState.UNRECOGNIZED -> throw EnumMappingError(state.name)
    }

abstract class GoodsReceivedMappingError(message: String) : IllegalArgumentException(message)

class PurchaseOrderNumberMappingError(unparsablePoNumber: String) : GoodsReceivedMappingError(
    "Could not parse Purchase Order Number: $unparsablePoNumber"
)

class EnumMappingError(message: String) : GoodsReceivedMappingError(message)
