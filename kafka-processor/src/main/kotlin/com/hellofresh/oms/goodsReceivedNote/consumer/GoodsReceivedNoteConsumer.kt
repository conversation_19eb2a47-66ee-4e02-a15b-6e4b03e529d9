package com.hellofresh.oms.goodsReceivedNote.consumer

import com.hellofresh.oms.goodsReceivedNote.service.GoodsReceivedNoteService
import com.hellofresh.oms.model.grn.Grn
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue as ProtoGoodsReceivedNoteValue
import java.util.function.Consumer
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class GoodsReceivedNoteConsumer(
    private val goodsReceivedNoteService: GoodsReceivedNoteService
) {
    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processGoodsReceivedNote(): Consumer<Message<ProtoGoodsReceivedNoteValue>> =
        Consumer { record ->
            val goodsReceivedNote = record.payload

            try {
                val grn = Grn::class.tryFrom(record.payload)
                logger.info(
                    "Processing goods received note. purchase order number: {} - DC: {}",
                    grn.poNumber,
                    grn.dcCode
                )

                goodsReceivedNoteService.process(grn)
            } catch (_: PurchaseOrderNumberMappingError) {
                logger.info(
                    "Processing goods received note. Skipped GRN due to unparsable purchase order number: {} - DC: {}",
                    goodsReceivedNote.reference,
                    goodsReceivedNote.dcCode,
                )
            } catch (e: EnumMappingError) {
                logger.error(
                    "Couldn't process GRN due to unparsable Enum: {} - DC: {}",
                    goodsReceivedNote.reference,
                    goodsReceivedNote.dcCode,
                    e,
                )
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process GRN due to an unexpected error: {} - DC: {}, payload: {}",
                    goodsReceivedNote.reference,
                    goodsReceivedNote.dcCode,
                    goodsReceivedNote,
                    e,
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
