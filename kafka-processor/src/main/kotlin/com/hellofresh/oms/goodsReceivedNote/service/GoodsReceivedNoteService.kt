package com.hellofresh.oms.goodsReceivedNote.service

import com.hellofresh.oms.goodsReceivedNote.repository.GoodsReceivedNoteRepository
import com.hellofresh.oms.model.grn.Grn
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class GoodsReceivedNoteService(
    private val goodsReceivedNoteRepository: GoodsReceivedNoteRepository,
) {
    @Timed
    fun process(goodsReceivedNote: Grn) {
        goodsReceivedNoteRepository.findByPoNumber(goodsReceivedNote.poNumber)?.let {
            goodsReceivedNoteRepository.delete(it)
        }

        goodsReceivedNoteRepository.save(goodsReceivedNote)
    }
}
