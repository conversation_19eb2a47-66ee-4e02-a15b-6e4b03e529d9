package com.hellofresh.oms.distributionCenter.service

import com.hellofresh.oms.distributionCenter.repository.DistributionCenterRepository
import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.isBilling
import com.hellofresh.oms.model.isDelivery
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class DistributionCenterService(
    private val distributionCenterRepository: DistributionCenterRepository
) {
    @Timed
    fun process(distributionCenter: DistributionCenter) {
        val mergedDistributionCenter = distributionCenterRepository.findByCode(distributionCenter.code)
            ?.let { distributionCenter.mergeWith(it) }
            ?: distributionCenter

        distributionCenterRepository.saveAndFlush(mergedDistributionCenter)
    }
}

private fun DistributionCenter.mergeWith(other: DistributionCenter) = copy(
    addresses = this.addresses.mergeWith(other.addresses),
    createdAt = other.createdAt,
)

private fun List<DistributionCenterAddress>.mergeWith(other: List<DistributionCenterAddress>): List<DistributionCenterAddress> {
    val result = mutableListOf<DistributionCenterAddress>()

    firstOrNull { it.isDelivery() }
        ?.also { deliveryAddress ->
            result.add(deliveryAddress.mergeWith(other.firstOrNull { it.isDelivery() }))
        }

    firstOrNull { it.isBilling() }
        ?.also { billingAddress ->
            result.add(billingAddress.mergeWith(other.firstOrNull { it.isBilling() }))
        }

    return result
}

private fun DistributionCenterAddress.mergeWith(other: DistributionCenterAddress?): DistributionCenterAddress {
    if (other == null) {
        return this
    }

    return this.copy(id = other.id, createdAt = other.createdAt)
}
