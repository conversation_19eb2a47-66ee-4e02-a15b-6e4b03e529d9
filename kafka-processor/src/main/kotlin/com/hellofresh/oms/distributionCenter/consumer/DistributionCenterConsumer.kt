package com.hellofresh.oms.distributionCenter.consumer

import com.hellofresh.oms.distributionCenter.service.DistributionCenterService
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter as DistributionCenterProto
import java.time.LocalDateTime
import java.util.function.Consumer
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class DistributionCenterConsumer(
    private val distributionCenterService: DistributionCenterService
) {
    @Bean
    fun processDistributionCenter(): Consumer<Message<DistributionCenterProto>> =
        Consumer {
            logger.info("Processing distribution center. Code: {}", it.payload.code)

            val now = LocalDateTime.now()
            val distributionCenter = it.payload.toEntity(now)

            distributionCenterService.process(distributionCenter)
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
