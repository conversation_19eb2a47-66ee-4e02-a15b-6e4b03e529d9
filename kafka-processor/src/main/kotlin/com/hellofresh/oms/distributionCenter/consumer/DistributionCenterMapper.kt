package com.hellofresh.oms.distributionCenter.consumer

import com.google.type.PostalAddress
import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterAddressType
import com.hellofresh.oms.model.DistributionCenterStatus
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter as DistributionCenterProto
import java.time.LocalDateTime
import java.util.UUID

fun DistributionCenterProto.toEntity(now: LocalDateTime): DistributionCenter {
    require(marketList.size == 1) { "A distribution center has to be associated with exactly one market" }

    val deliveryAddress = this.deliveryAddress.toEntity(
        this.code,
        now,
        DistributionCenterAddressType.DELIVERY,
    )
    val billingAddress = this.billingAddress.toEntity(
        this.code,
        now,
        DistributionCenterAddressType.BILLING,
    )
    val status = DistributionCenterStatus.fromBoolean(this.enabled)
    val marketCode = marketList.single().code

    return DistributionCenter(
        code = this.code,
        status = status,
        name = this.name,
        market = marketCode,
        addresses = listOf(deliveryAddress, billingAddress),
        createdAt = now,
        updatedAt = now,
    )
}

private fun PostalAddress.toEntity(
    distributionCenterCode: String,
    now: LocalDateTime,
    addressType: DistributionCenterAddressType
): DistributionCenterAddress {
    val (numberString, addressString) = parseNumberAndAddress(addressLinesList)

    return DistributionCenterAddress(
        id = UUID.randomUUID(),
        dcCode = distributionCenterCode,
        number = numberString,
        address = addressString,
        zip = postalCode,
        city = locality,
        state = administrativeArea,
        company = organization.ifEmpty { null },
        type = addressType,
        createdAt = now,
        updatedAt = now,
        countryCode = regionCode,
    )
}

// DC topic contract doesn't have a separate field for a house number.
// Instead, the number is being stored as the first line of the addressLines field (an empty string if the address doesn't have a house number).
// For older messages, however, it's not always the case, that's why we need this `if (addressLines.size <= 1)` check.
private fun parseNumberAndAddress(addressLines: List<String>) = if (addressLines.size <= 1) {
    "" to addressLines.joinToString("\n")
} else {
    addressLines.first() to addressLines.drop(1).joinToString("\n")
}
