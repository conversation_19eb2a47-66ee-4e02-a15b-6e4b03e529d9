package com.hellofresh.oms.supplierSkuPackaging.consumer

import com.hellofresh.oms.supplierSkuPackaging.service.SupplierSkuPackagingService
import com.hellofresh.planning.suppliersku.packaging.packaging as AvroSupplierSkuPackaging
import java.util.UUID
import java.util.function.Consumer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class SupplierSkuPackagingConsumer(
    private val supplierSkuPackagingService: SupplierSkuPackagingService
) {
    @Bean
    fun processSupplierSkuPackaging(): Consumer<Message<AvroSupplierSkuPackaging>> =
        Consumer { record ->
            val headerId =
                requireNotNull(record.headers["kafka_receivedMessageKey"]) { "SupplierSkuPackaging id cannot be null" }
            val supplierSkuPackagingId = UUID.fromString(headerId as String)
            val supplierSkuPackaging = record.payload

            logger.info(
                "Received SupplierSkuPackaging:" +
                    "uuid:$supplierSkuPackagingId " +
                    "supplierSkuId:${supplierSkuPackaging.supplierSkuId} " +
                    "market:${supplierSkuPackaging.market}",
            )

            supplierSkuPackagingService.upsertSupplierSkuPackaging(supplierSkuPackaging.mapToSupplierSkuPackaging())
        }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(this::class.java)
    }
}
