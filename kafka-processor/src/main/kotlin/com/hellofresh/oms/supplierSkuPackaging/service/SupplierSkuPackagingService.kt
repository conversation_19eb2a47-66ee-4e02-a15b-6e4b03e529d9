package com.hellofresh.oms.supplierSkuPackaging.service

import com.hellofresh.oms.model.SupplierSkuPackaging
import com.hellofresh.oms.supplierSkuPackaging.repository.SupplierSkuPackagingRepository
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class SupplierSkuPackagingService(
    private val supplierSkuPackagingRepository: SupplierSkuPackagingRepository
) {
    @Timed
    fun upsertSupplierSkuPackaging(supplierSkuPackaging: SupplierSkuPackaging): SupplierSkuPackaging =
        supplierSkuPackagingRepository.save(supplierSkuPackaging)
}
