package com.hellofresh.oms.supplierSkuPackaging.consumer

import com.hellofresh.oms.model.SupplierSkuPackaging
import com.hellofresh.planning.suppliersku.packaging.packaging as AvroSupplierSkuPackaging
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

fun AvroSupplierSkuPackaging.mapToSupplierSkuPackaging() =
    SupplierSkuPackaging(
        supplierSkuId = supplierSkuId,
        casesPerPallet = casesPerPallet,
        createdAt = toLocalDateTime(createdAt),
        updatedAt = toLocalDateTime(updatedAt)
    )

private fun toLocalDateTime(dateTime: String) =
    LocalDateTime.parse(dateTime, DateTimeFormatter.ISO_DATE_TIME)
