package com.hellofresh.oms.sku.consumer

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.SkuStatus.ACTIVE
import com.hellofresh.oms.model.SkuStatus.ARCHIVED
import com.hellofresh.oms.model.SkuStatus.DEPLETION_TRACK
import com.hellofresh.oms.model.SkuStatus.INACTIVE
import com.hellofresh.oms.model.SkuStatus.LAUNCH
import com.hellofresh.oms.model.SkuStatus.LIMITED
import com.hellofresh.oms.model.SkuStatus.OFFBOARDING
import com.hellofresh.oms.model.SkuStatus.ONBOARDING
import com.hellofresh.oms.model.SkuStatus.TEMPORARY
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.UOM.GAL
import com.hellofresh.oms.model.UOM.KG
import com.hellofresh.oms.model.UOM.L
import com.hellofresh.oms.model.UOM.LBS
import com.hellofresh.oms.model.UOM.OZ
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.planning.culinarysku.culinarysku as AvroSku
import java.util.UUID

fun AvroSku.mapToSku(): Sku {
    val parsedUom = getUnitOfMeasure(extras)
    return Sku(
        uuid = UUID.fromString(id),
        market = market,
        name = name,
        code = code,
        status = mapToSkuStatus(status),
        brands = brands,
        category = category,
        uom = if (parsedUom.isEmpty()) null else mapToUom(parsedUom),
    )
}

private fun mapToSkuStatus(status: String) = when (status.trim().lowercase()) {
    "launch" -> LAUNCH
    "limited" -> LIMITED
    "onboarding" -> ONBOARDING
    "temporary" -> TEMPORARY
    "depletion track" -> DEPLETION_TRACK
    "active" -> ACTIVE
    "archived" -> ARCHIVED
    "inactive" -> INACTIVE
    "offboarding" -> OFFBOARDING
    else -> throw IllegalArgumentException("Unknown Sku Status: $status")
}

private fun getUnitOfMeasure(extrasJson: String): String {
    val objectMapper = ObjectMapper().findAndRegisterModules()
    val extras: String = objectMapper
        .readTree(extrasJson)
        .path("extras").asText()
    return objectMapper
        .readTree(extras)
        .path("wms_uom").asText()
}

private fun mapToUom(parsedUom: String): UOM = when (parsedUom.trim().lowercase()) {
    "kg" -> KG
    "gal" -> GAL
    "liter" -> L
    "ea" -> UNIT
    "lbs" -> LBS
    "oz" -> OZ
    else -> throw java.lang.IllegalArgumentException("Unknown Unit Of Measure: $parsedUom")
}
