package com.hellofresh.oms.sku.consumer

import com.hellofresh.oms.sku.service.SkuService
import com.hellofresh.planning.culinarysku.culinarysku as AvroSku
import java.util.UUID
import java.util.function.Consumer
import org.slf4j.LoggerFactory.getLogger
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class SkuConsumer(
    val skuService: SkuService
) {
    @Bean
    fun processSku(): Consumer<Message<AvroSku>> =
        Consumer { record ->
            val headerId = requireNotNull(record.headers["kafka_receivedMessageKey"]) { "SKU id cannot be null" }
            val skuId = UUID.fromString(headerId as String)
            val sku = record.payload

            logger.info("Received SKU: uuid:$skuId market:${sku.market} name:${sku.name}")

            skuService.upsertSku(sku = sku.mapToSku())
        }

    companion object {
        private val logger = getLogger(this::class.java)
    }
}
