package com.hellofresh.oms.purchaseOrderShipment.consumer

import com.hellofresh.oms.purchaseOrderShipment.service.PurchaseOrderShipmentService
import com.hellofresh.proto.stream.distributionCenter.thirdParty.blujay.inbound.purchaseOrderShipment.v1beta1.PurchaseOrderShipment
import java.util.function.Consumer
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

const val CROSS_DOCKING_CARRIER_NAME = "CONNELL TRANSPORT"

@Component
class PurchaseOrderShipmentConsumer {

    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processPurchaseOrderShipment(
        purchaseOrderShipmentService: PurchaseOrderShipmentService
    ): Consumer<Message<PurchaseOrderShipment>> =
        Consumer { message ->
            try {
                val poShipment = message.payload.toPurchaseOrderShipment()
                    .takeIf { it.carrierName != CROSS_DOCKING_CARRIER_NAME }
                if (poShipment != null) {
                    logger.info(
                        "Processing PO shipment. [PO ID: ${poShipment.poNumber}]",
                    )
                    purchaseOrderShipmentService.process(poShipment)
                }
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process shipment due to an unexpected error: Exception: $e]",
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
