package com.hellofresh.oms.purchaseOrderShipment.consumer

import com.google.type.DateTime
import com.hellofresh.oms.model.purchaseOrderShipment.PurchaseOrderShipment
import com.hellofresh.proto.stream.distributionCenter.thirdParty.blujay.inbound.purchaseOrderShipment.v1beta1.PurchaseOrderShipment as ProtoPurchaseOrderShipment
import java.time.LocalDateTime

fun ProtoPurchaseOrderShipment.toPurchaseOrderShipment() = PurchaseOrderShipment(
    poNumber = purchaseOrderRevision.number.formatted,
    loadNumber = loadNumber,
    palletCount = palletCount,
    carrierName = carrierName,
    regionCode = originLocation.regionCode,
    postalCode = originLocation.postalCode,
    administrativeArea = originLocation.administrativeArea,
    locality = originLocation.locality,
    organization = originLocation.organization,
    appointmentTime = appointmentTime.toLocalDateTime(),
    executionEvent = executionEvent,
)

fun DateTime.toLocalDateTime(): LocalDateTime? =
    if (this == DateTime.getDefaultInstance()) {
        null
    } else {
        LocalDateTime.of(
            this.year,
            this.month,
            this.day,
            this.hours,
            this.minutes,
            this.seconds,
            this.nanos,
        )
    }
