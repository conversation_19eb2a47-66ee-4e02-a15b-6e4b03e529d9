package com.hellofresh.oms.purchaseOrderShipment.service

import com.hellofresh.oms.model.purchaseOrderShipment.PurchaseOrderShipment
import com.hellofresh.oms.purchaseOrderShipment.repository.PurchaseOrderShipmentRepository
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class PurchaseOrderShipmentService(
    private val purchaseOrderShipmentRepository: PurchaseOrderShipmentRepository,
) {
    @Timed
    fun process(purchaseOrderShipment: PurchaseOrderShipment) {
        purchaseOrderShipmentRepository.save(purchaseOrderShipment)
    }
}
