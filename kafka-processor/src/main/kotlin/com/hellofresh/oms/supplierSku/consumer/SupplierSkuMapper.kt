package com.hellofresh.oms.supplierSku.consumer

import com.hellofresh.oms.model.SupplierSku
import com.hellofresh.oms.model.SupplierSkuStatus.ACTIVE
import com.hellofresh.oms.model.SupplierSkuStatus.ARCHIVED
import com.hellofresh.oms.model.SupplierSkuStatus.INACTIVE
import com.hellofresh.oms.model.SupplierSkuStatus.OFFBOARDING
import com.hellofresh.oms.model.SupplierSkuStatus.ONBOARDING
import com.hellofresh.planning.suppliersku.suppliersku as AvroSupplierSku

fun AvroSupplierSku.toEntity() = SupplierSku(
    uuid = id,
    parentSupplierId = supplierId,
    skuId = culinarySkuId,
    market = market,
    status = mapToSupplierSkuStatus(status),
    supplierCode = supplierCode.toString(),
)

private fun mapToSupplierSkuStatus(avroStatus: String) = when (avroStatus.trim().lowercase()) {
    "active" -> ACTIVE
    "archived" -> ARCHIVED
    "onboarding" -> ONBOARDING
    "inactive" -> INACTIVE
    "offboarding" -> OFFBOARDING
    else -> throw IllegalArgumentException("Unknown SupplierSku status: $avroStatus")
}
