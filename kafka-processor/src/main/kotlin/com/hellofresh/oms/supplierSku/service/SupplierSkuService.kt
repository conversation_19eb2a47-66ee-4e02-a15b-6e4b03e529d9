package com.hellofresh.oms.supplierSku.service

import com.hellofresh.oms.model.SupplierSku
import com.hellofresh.oms.supplierSku.repository.SupplierSkuRepository
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class SupplierSkuService(private val supplierSkuRepository: SupplierSkuRepository) {
    @Timed
    fun upsertSupplierSku(supplierSku: SupplierSku): SupplierSku = supplierSkuRepository.save(supplierSku)
}
