package com.hellofresh.oms.supplierSku.consumer

import com.hellofresh.oms.supplierSku.service.SupplierSkuService
import com.hellofresh.planning.suppliersku.suppliersku as AvroSupplierSku
import java.util.UUID
import java.util.function.Consumer
import org.slf4j.LoggerFactory.getLogger
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class SupplierSkuConsumer(val supplierSkuService: SupplierSkuService) {
    @Bean
    fun processSupplierSku(): Consumer<Message<AvroSupplierSku>> =
        Consumer { record ->
            val headerId = requireNotNull(record.headers["kafka_receivedMessageKey"]) { "Header id cannot be null" }
            val supplierSkuUuid = UUID.fromString(headerId as String)
            val avroSupplierSku = record.payload

            logger.info(
                "Received SupplierSku: " +
                    "uuid: $supplierSkuUuid " +
                    "market: ${avroSupplierSku.market} " +
                    "supplierID: ${avroSupplierSku.supplierId}, " +
                    "skuId: ${avroSupplierSku.culinarySkuId} " +
                    "supplierCode: ${avroSupplierSku.supplierCode}"
            )

            supplierSkuService.upsertSupplierSku(avroSupplierSku.toEntity())
        }

    companion object {
        private val logger = getLogger(this::class.java)
    }
}
