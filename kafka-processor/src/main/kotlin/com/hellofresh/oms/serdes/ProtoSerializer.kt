package com.hellofresh.oms.serdes

import com.google.protobuf.Message
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import org.apache.kafka.common.serialization.Serializer

open class ProtoSerializer<T : Message> : Serializer<T> {
    override fun serialize(topic: String, data: T): ByteArray = data.toByteArray()
}

class PurchaseOrderProtoSerializer : ProtoSerializer<PurchaseOrderEvent>()
