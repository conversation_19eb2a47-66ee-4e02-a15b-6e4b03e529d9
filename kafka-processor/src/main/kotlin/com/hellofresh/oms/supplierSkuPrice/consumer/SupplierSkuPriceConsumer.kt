package com.hellofresh.oms.supplierSkuPrice.consumer

import com.hellofresh.oms.supplierSkuPrice.service.SupplierSkuPriceService
import com.hellofresh.planning.suppliersku.pricing.pricing as AvroSupplierSkuPrice
import java.util.UUID
import java.util.function.Consumer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class SupplierSkuPriceConsumer(
    private val supplierSkuPriceService: SupplierSkuPriceService
) {
    @Bean
    fun processSupplierSkuPrice(): Consumer<Message<AvroSupplierSkuPrice>> =
        Consumer { record ->
            val headerId = requireNotNull(
                record.headers["kafka_receivedMessageKey"]
            ) { "SupplierSkuPrice id cannot be null" }
            val supplierSkuPriceId = UUID.fromString(headerId as String)
            val supplierSkuPrice = record.payload

            logger.info(
                "Received SupplierSkuPrice:" +
                    "uuid:$supplierSkuPriceId " +
                    "supplierSkuId:${supplierSkuPrice.supplierSkuId} " +
                    "market:${supplierSkuPrice.market}",
            )

            supplierSkuPriceService.upsertSupplierSkuPrice(supplierSkuPrice.mapToSupplierSkuPrice())
        }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(this::class.java)
    }
}
