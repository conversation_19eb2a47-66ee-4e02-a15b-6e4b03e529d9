package com.hellofresh.oms.supplierSkuPrice.service

import com.hellofresh.oms.model.SupplierSkuPrice
import com.hellofresh.oms.supplierSkuPrice.repository.SupplierSkuPriceRepository
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class SupplierSkuPriceService(
    private val supplierSkuPriceRepository: SupplierSkuPriceRepository
) {
    @Timed
    fun upsertSupplierSkuPrice(supplierSkuPrice: SupplierSkuPrice): SupplierSkuPrice =
        supplierSkuPriceRepository.save(supplierSkuPrice)
}
