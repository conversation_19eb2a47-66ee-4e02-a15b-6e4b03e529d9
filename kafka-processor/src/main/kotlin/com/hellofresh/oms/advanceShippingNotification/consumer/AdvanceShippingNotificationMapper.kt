package com.hellofresh.oms.advanceShippingNotification.consumer

import com.google.type.DateTime
import com.hellofresh.oms.model.asn.Address
import com.hellofresh.oms.model.asn.AdvanceShippingNotification
import com.hellofresh.oms.model.asn.AdvanceShippingNotificationItem
import com.hellofresh.oms.model.asn.ItemQuantity
import com.hellofresh.oms.model.asn.Shipment
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn
import java.time.LocalDateTime
import java.util.UUID

fun DateTime.toLocalDateTime(): LocalDateTime =
    LocalDateTime.of(
        this.year,
        this.month,
        this.day,
        this.hours,
        this.minutes,
        this.seconds,
        this.nanos,
    )

fun PurchaseOrderAsn.toAdvanceShippingNotification(): AdvanceShippingNotification {
    val id = UUID.randomUUID()
    return AdvanceShippingNotification(
        id = id,
        asnId = this.asnId,
        purchaseOrderId = UUID.fromString(this.purchaseOrderId),
        notes = this.notes,
        supplierId = UUID.fromString(this.supplier.id),
        shipment = Shipment(
            distributionCenter = this.shipment.distributionCenter,
            standardCarrierAlphaCode = this.shipment.standardCarrierAlphaCode,
            shippingMethod = this.shipment.shippingMethod.name,
            createTime = this.shipment.createTime.toLocalDateTime(),
            shipmentTime = this.shipment.shipmentTime.toLocalDateTime(),
            plannedDeliveryTime = this.shipment.plannedDeliveryTime.toLocalDateTime(),
            trackingNumber = this.shipment.trackingNumber,
            numberPallet = this.shipment.numberPallet,
            shippingAddress = Address(
                regionCode = this.shipment.shippingAddress.regionCode,
                postalCode = this.shipment.shippingAddress.postalCode,
                locality = this.shipment.shippingAddress.locality,
                addressLines = this.shipment.shippingAddress.addressLinesList.joinToString(),
                organization = this.shipment.shippingAddress.organization,
            ),
            pickupAddress = Address(
                regionCode = this.shipment.pickupAddress.regionCode,
                postalCode = this.shipment.pickupAddress.postalCode,
                locality = this.shipment.pickupAddress.locality,
                addressLines = this.shipment.pickupAddress.addressLinesList.joinToString(),
                organization = this.shipment.pickupAddress.organization,
            ),
        ),
        items = this.itemsList
            .map { asnItem ->
                asnItem.toAdvanceShippingNotificationItem(id)
            }
            .groupBy { it.id }
            .mapValues { (_, items) -> items.maxByOrNull { it.updatedAt } ?: items.first() }
            .values
            .toList(),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
    )
}

fun PurchaseOrderAsn.AsnItem.toAdvanceShippingNotificationItem(asnId: UUID): AdvanceShippingNotificationItem =
    AdvanceShippingNotificationItem(
        id = UUID.fromString(id),
        skuCode = skuCode,
        shippingState = shippingState.name,
        shippedQuantity = ItemQuantity(
            shippedQuantity.orderSize,
            shippedQuantity.casePackaging.size.value.takeIf { !it.isEmpty() }?.toDouble(),
            shippedQuantity.casePackaging.unit.name,
            shippedQuantity.palletId,
            shippedQuantity.crateType,
        ),
        promisedQuantity = ItemQuantity(
            promisedQuantity.orderSize,
            promisedQuantity.casePackaging.size.value.takeIf { !it.isEmpty() }?.toDouble(),
            promisedQuantity.casePackaging.unit.name,
            promisedQuantity.palletId,
            promisedQuantity.crateType,
        ),
        packingSize = packingSize.value.toDouble(),
        lotNumber = lot.number,
        expirationTime = takeIf { lot.hasExpirationTime() }?.lot?.expirationTime?.toLocalDateTime(),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        asnId = asnId,
    )
