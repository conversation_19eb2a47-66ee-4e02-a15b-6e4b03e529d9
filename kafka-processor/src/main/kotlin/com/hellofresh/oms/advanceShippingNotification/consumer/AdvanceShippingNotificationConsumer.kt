package com.hellofresh.oms.advanceShippingNotification.consumer

import com.hellofresh.oms.advanceShippingNotification.service.AdvanceShippingNotificationService
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn
import java.util.function.Consumer
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class AdvanceShippingNotificationConsumer(
    private val advanceShippingNotificationService: AdvanceShippingNotificationService
) {
    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processAdvanceShippingNotification(): Consumer<Message<PurchaseOrderAsn>> =
        Consumer { message ->
            try {
                val asn = message.payload.toAdvanceShippingNotification()
                logger.info(
                    "Processing PO ASN. [PO ID: ${asn.purchaseOrderId}]"
                )
                advanceShippingNotificationService.process(asn)
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process ASN due to an unexpected error: [PO ID: {}, Exception: {}]",
                    e,
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
