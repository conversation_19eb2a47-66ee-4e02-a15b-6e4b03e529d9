package com.hellofresh.oms.advanceShippingNotification.service

import com.hellofresh.oms.advanceShippingNotification.repository.AdvanceShippingNotificationRepository
import com.hellofresh.oms.model.asn.AdvanceShippingNotification
import io.micrometer.core.annotation.Timed
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class AdvanceShippingNotificationService(
    private val advanceShippingNotificationRepository: AdvanceShippingNotificationRepository,
) {
    @Timed
    @Transactional
    fun process(advanceShippingNotification: AdvanceShippingNotification) {
        advanceShippingNotificationRepository.save(advanceShippingNotification)
    }
}
