@file:Suppress("TooManyFunctions")

package com.hellofresh.oms.purchaseOrder.consumer

import com.google.protobuf.Timestamp
import com.google.type.Money as GoogleMoney
import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.POType.EMERGENCY
import com.hellofresh.oms.model.POType.PREORDER
import com.hellofresh.oms.model.POType.STANDARD
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType.CASE_TYPE
import com.hellofresh.oms.model.PackagingType.PALLET_TYPE
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder as PurchaseOrderEntity
import com.hellofresh.oms.model.PurchaseOrderStatus.APPROVED
import com.hellofresh.oms.model.PurchaseOrderStatus.DELETED
import com.hellofresh.oms.model.PurchaseOrderStatus.INITIATED
import com.hellofresh.oms.model.PurchaseOrderStatus.REJECTED
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.CROSSDOCK
import com.hellofresh.oms.model.ShipMethodEnum.FREIGHT_ON_BOARD
import com.hellofresh.oms.model.ShipMethodEnum.OTHER
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.UOM.GAL
import com.hellofresh.oms.model.UOM.KG
import com.hellofresh.oms.model.UOM.L
import com.hellofresh.oms.model.UOM.LBS
import com.hellofresh.oms.model.UOM.OZ
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod.SHIPPING_METHOD_CROSSDOCK
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod.SHIPPING_METHOD_FREIGHT_ON_BOARD
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod.SHIPPING_METHOD_OTHER
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod.SHIPPING_METHOD_VENDOR
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem.CasePackaging.UOM as ProtoUOM
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderShipping
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State.STATE_APPROVED
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State.STATE_DELETED
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State.STATE_INITIATED
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.State.STATE_REJECTED
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType.PURCHASE_ORDER_TYPE_EMERGENCY
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType.PURCHASE_ORDER_TYPE_PROVISIONAL
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType.PURCHASE_ORDER_TYPE_STANDARD
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID

fun PurchaseOrder.toEntity() = PurchaseOrderEntity(
    poNumber = revision.number.formatted,
    version = revision.version,
    type = revision.type.toType(),
    id = UUID.fromString(id),
    yearWeek = YearWeek(yearWeek.year, yearWeek.week),
    userId = UUID.fromString(creatorId),
    userEmail = creatorEmail,
    status = status.toStatus(),
    sendTime = sendTime.toLocalDateTimeUtc(),
    supplierId = UUID.fromString(supplierId),
    supplierCode = supplierCode,
    dcCode = distributionCenterCode,
    shippingMethod = shipping.method.toShipMethodEntity(),
    shippingAddress = shipping.toShippingAddress(),
    expectedStartTime = expectedArrival.startTime.toLocalDateTimeUtc()!!,
    expectedEndTime = expectedArrival.endTime.toLocalDateTimeUtc()!!,
    orderItems = orderItemsList.mapToOrderItemEntities(id),
    createdAt = createTime.toLocalDateTimeUtc()!!,
    updatedAt = updateTime.toLocalDateTimeUtc(),
    totalPrice = totalPrice.toMoney(),
    emergencyReasonUuid = emergencyReasonUuid.toUUIDorNull(),
    comment = comment.ifEmpty { null },
    isSynced = true,
    deliveryDateChangeReasonId = deliveryDateChangeReasonUuid.toUUIDorNull(),
)

private fun ShippingMethod.toShipMethodEntity(): ShipMethodEnum = when (this) {
    SHIPPING_METHOD_CROSSDOCK -> CROSSDOCK
    SHIPPING_METHOD_VENDOR -> VENDOR
    SHIPPING_METHOD_FREIGHT_ON_BOARD -> FREIGHT_ON_BOARD
    SHIPPING_METHOD_OTHER -> OTHER
    else -> throw IllegalArgumentException("Unspecified shipping method $this")
}

private fun List<PurchaseOrderItem>.mapToOrderItemEntities(poId: String) =
    map { orderItem: PurchaseOrderItem ->
        OrderItem(
            poId = UUID.fromString(poId),
            skuId = UUID.fromString(orderItem.skuId),
            totalQty = orderItem.quantity.value.toBigDecimal(),
            rawQty = null,
            price = orderItem.price.toMoney(),
            totalPrice = orderItem.totalPrice.toMoney(),
            buffer = Permyriad.fromPercent(orderItem.buffer.value.toBigDecimal()),
            correctionReason = orderItem.correctionReason.ifEmpty { null },
            packaging = getPackaging(orderItem),
            createdAt = orderItem.createTime.toLocalDateTimeUtc(),
            changeReasonId = orderItem.orderItemChangeReasonUuid.toUUIDorNull(),
            updatedAt = orderItem.updateTime.toLocalDateTimeUtc(),
            casesPerPallet = if (orderItem.hasCasePackaging() && orderItem.casePackaging.hasPalletSize()) orderItem.casePackaging.palletSize else null,
        )
    }.toSet()

private fun getPackaging(orderItem: PurchaseOrderItem): Packaging {
    if (!orderItem.hasCasePackaging()) {
        return Packaging(packagingType = UNIT_TYPE, unitOfMeasure = UNIT)
    }
    val packagingType =
        if (!orderItem.casePackaging.hasPalletSize() || orderItem.casePackaging.palletSize == 0) {
            CASE_TYPE
        } else {
            PALLET_TYPE
        }
    return Packaging(
        packagingType,
        orderItem.casePackaging.size.value.toBigDecimal(),
        orderItem.casePackaging.unit.toUnitOfMeasure(),
    )
}

private fun GoogleMoney.toMoney(): Money {
    val amount = units.toBigDecimal() + nanos.toBigDecimal().divide(1_000_000_000.toBigDecimal())
    return Money.fromTapioca(amount, currencyCode)
}

private fun String?.toUUIDorNull(): UUID? = if (this.isNullOrBlank()) null else UUID.fromString(this)

private fun PurchaseOrderType.toType() = when (this) {
    PURCHASE_ORDER_TYPE_STANDARD -> STANDARD
    PURCHASE_ORDER_TYPE_EMERGENCY -> EMERGENCY
    PURCHASE_ORDER_TYPE_PROVISIONAL -> PREORDER
    else -> throw IllegalArgumentException("Should never reach purchase order type {$name}")
}

private fun PurchaseOrder.State.toStatus() = when (this) {
    STATE_INITIATED -> INITIATED
    STATE_APPROVED -> APPROVED
    STATE_REJECTED -> REJECTED
    STATE_DELETED -> DELETED
    else -> throw IllegalArgumentException("Should never reach purchase order status {$name} ")
}

private fun PurchaseOrderShipping.toShippingAddress() =
    ShippingAddress(
        address.organization,
        address.addressLinesList.joinToString(", "),
        address.locality,
        address.administrativeArea,
        address.postalCode,
        address.regionCode,
    )

private fun Timestamp.toLocalDateTimeUtc() = if (seconds != 0L) {
    LocalDateTime.ofEpochSecond(
        seconds,
        nanos,
        ZoneOffset.UTC,
    )
} else {
    null
}

private fun ProtoUOM.toUnitOfMeasure() =
    when (this) {
        ProtoUOM.UOM_UNIT -> UNIT
        ProtoUOM.UOM_KG -> KG
        ProtoUOM.UOM_LBS -> LBS
        ProtoUOM.UOM_GAL -> GAL
        ProtoUOM.UOM_LITRE -> L
        ProtoUOM.UOM_OZ -> OZ
        else -> throw IllegalArgumentException("Should never reach UOM type: $name")
    }
