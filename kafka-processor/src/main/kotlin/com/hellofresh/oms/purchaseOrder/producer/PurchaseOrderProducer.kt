package com.hellofresh.oms.purchaseOrder.producer

import com.hellofresh.oms.client.featureflag.FeatureFlagClient
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent.Event as ProtoEvent
import io.github.resilience4j.retry.annotation.Retry
import java.util.function.Supplier
import org.slf4j.LoggerFactory
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.Message
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Sinks
import reactor.core.publisher.Sinks.EmissionException
import reactor.core.publisher.Sinks.EmitResult.FAIL_NON_SERIALIZED
import reactor.util.concurrent.Queues

// The qualifier of this bean is directly related to the `spring.cloud.function.definition` property
@Service("purchase-order-v2")
class PurchaseOrderProducer(
    private val protoMapper: PurchaseOrderProtoMapper,
    private val featureFlagClient: FeatureFlagClient,
) : Supplier<Flux<Message<PurchaseOrderEvent>>> {

    private val sink = Sinks.many().multicast()
        .onBackpressureBuffer<Message<PurchaseOrderEvent>>(
            Queues.SMALL_BUFFER_SIZE,
            false,
        )

    override fun get(): Flux<Message<PurchaseOrderEvent>> = sink.asFlux()

    @Retry(name = "publishPurchaseOrder")
    fun publishPurchaseOrderEvent(purchaseOrder: PurchaseOrder, event: PurchaseOrderEventType) {
        val messageKey = purchaseOrder.poNumber
        val message = MessageBuilder.withPayload(protoMapper.mapToProto(purchaseOrder, event))
            .setHeader(KafkaHeaders.KEY, messageKey)
            .build()

        if (featureFlagClient.shouldEnablePublisherForPoTopicFeatureFlag()) {
            logger.info("PO topic V2 is enabled. Publishing purchase order to Kafka.")
            val emitResult = sink.tryEmitNext(message)

            if (emitResult == FAIL_NON_SERIALIZED) {
                logger.warn(
                    "Received retriable FAIL_NON_SERIALIZED. [id: $messageKey]",
                )
                throw RetryableEmissionException()
            }

            if (emitResult.isFailure) {
                logger.error(
                    "Failed to emit from PurchaseOrderProducer. [id: $messageKey, result: $emitResult]",
                )
                throw EmissionException(emitResult)
            }
        } else {
            logger.info("PO topic V2 is disabled. Skipping publishing to Kafka.")
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(PurchaseOrderProducer::class.java)
    }
}

// This enum was created to hide the proto enum from the rest of the codebase.
// The proto enum has unnecessary values that are not used in the codebase. e.g. EVENT_UNSPECIFIED and UNRECOGNIZED
enum class PurchaseOrderEventType(val protoEvent: ProtoEvent) {
    CREATED(ProtoEvent.EVENT_CREATED),
    MODIFIED(ProtoEvent.EVENT_MODIFIED),
    STATUS_UPDATED(ProtoEvent.EVENT_STATUS_UPDATED),
    CANCELLED(ProtoEvent.EVENT_CANCELLED)
}

class RetryableEmissionException : RuntimeException()
