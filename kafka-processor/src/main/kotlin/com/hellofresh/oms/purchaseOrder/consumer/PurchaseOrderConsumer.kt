package com.hellofresh.oms.purchaseOrder.consumer

import com.hellofresh.oms.purchaseOrder.service.PurchaseOrderService
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder
import java.util.function.Consumer
import org.slf4j.LoggerFactory.getLogger
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class PurchaseOrderConsumer(
    private val purchaseOrderService: PurchaseOrderService
) {
    @Bean
    fun processPurchaseOrder(): Consumer<Message<PurchaseOrder>> =
        Consumer { record ->
            val purchaseOrder = record.payload
            val poId = purchaseOrder.id
            val poNumber = purchaseOrder.revision.number.formatted
            val revision = purchaseOrder.revision.version

            logger.info("Processing purchase order, poId: $poId, poNumber: $poNumber, revision: $revision")
            purchaseOrderService.process(purchaseOrder)
        }

    companion object {
        private val logger = getLogger(this::class.java)
    }
}
