package com.hellofresh.oms.purchaseOrder.service

import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.PurchaseOrder
import java.util.UUID
import org.apache.commons.lang3.builder.DiffBuilder
import org.apache.commons.lang3.builder.DiffResult
import org.apache.commons.lang3.builder.ToStringStyle.JSON_STYLE

object PurchaseOrderDiffBuilder {

    fun compare(lhs: PurchaseOrder, rhs: PurchaseOrder): DiffResult<PurchaseOrder?>? {
        val (addedItems, removedItems, modifiedItems) = diffPoOrderItems(lhs, rhs)

        return DiffBuilder.builder<PurchaseOrder>()
            .setLeft(lhs)
            .setRight(rhs)
            .setStyle(JSON_STYLE)
            .build()
            .append("id", lhs.id, rhs.id)
            .append("poNumber", lhs.poNumber, rhs.poNumber)
            .append("version", lhs.version, rhs.version)
            .append("type", lhs.type, rhs.type)
            .append("yearWeek", lhs.yearWeek, rhs.yearWeek)
            .append("userEmail", lhs.userEmail, rhs.userEmail)
            .append("supplierId", lhs.supplierId, rhs.supplierId)
            .append("supplierCode", lhs.supplierCode, rhs.supplierCode)
            .append("dcCode", lhs.dcCode, rhs.dcCode)
            .append("shippingMethod", lhs.shippingMethod, rhs.shippingMethod)
            .append("shippingAddress", lhs.shippingAddress, rhs.shippingAddress)
            .append("expectedStartTime", lhs.expectedStartTime, rhs.expectedStartTime)
            .append("expectedEndTime", lhs.expectedEndTime, rhs.expectedEndTime)
            .append("emergencyReasonUuid", lhs.emergencyReasonUuid, rhs.emergencyReasonUuid)
            .append("totalPrice", lhs.totalPrice, rhs.totalPrice)
            .append("comment", lhs.comment, rhs.comment)
            .append(
                "deliveryDateChangeReasonId",
                lhs.deliveryDateChangeReasonId,
                rhs.deliveryDateChangeReasonId,
            )
            .append("addedLineItems", emptyArray<UUID>(), addedItems.toTypedArray())
            .append("removedLineItems", emptyArray<UUID>(), removedItems.toTypedArray())
            .append("modifiedLineItems", emptyArray<UUID>(), modifiedItems.toTypedArray())
            .build()
        // .append("userId", lhs.userId, rhs.userId)
        // Removed - the IDs we get from Tapioca do not normally match the IDs we store (Azure IDs).
    }

    fun compare(lhs: OrderItem?, rhs: OrderItem?): DiffResult<OrderItem>? {
        if (lhs == null || rhs == null) return null

        return DiffBuilder.builder<OrderItem>()
            .setLeft(lhs)
            .setRight(rhs)
            .setStyle(JSON_STYLE)
            .build()
            .append("poId", lhs.poId, rhs.poId)
            .append("totalQty", lhs.totalQty, rhs.totalQty)
            .append("skuId", lhs.skuId, rhs.skuId)
            .append("price", lhs.price, rhs.price)
            .append("totalPrice", lhs.totalPrice, rhs.totalPrice)
            .append("buffer", lhs.buffer, rhs.buffer)
            .append("packaging", lhs.packaging, rhs.packaging)
            .append("rawQty", lhs.rawQty, rhs.rawQty)
            .append("changeReasonId", lhs.changeReasonId, rhs.changeReasonId)
            .append("casesPerPallet", lhs.casesPerPallet, rhs.casesPerPallet)
            .build()
        // .append("correctionReason", lhs.correctionReason, rhs.correctionReason)
        // Removed temporarily - when we edit a PO Order Item in OMS we are not filling this.
        // TODO: Set correctionReason on editing a PO in OMS
    }

    fun diffPoOrderItems(leftPo: PurchaseOrder, rightPo: PurchaseOrder): Triple<Set<UUID>, Set<UUID>, Set<UUID>> {
        val addedItems = mutableSetOf<UUID>()
        val removedItems = mutableSetOf<UUID>()
        val modifiedItems = mutableSetOf<UUID>()

        val leftMap = leftPo.orderItems.associateBy { it.skuId }
        val rightMap = rightPo.orderItems.associateBy { it.skuId }

        val allSkuIds = leftMap.keys union rightMap.keys

        for (skuId in allSkuIds) {
            val leftOrderItem = leftMap[skuId]
            val rightOrderItem = rightMap[skuId]

            when {
                leftOrderItem == null -> addedItems.add(skuId)
                rightOrderItem == null -> removedItems.add(skuId)
                compare(leftOrderItem, rightOrderItem)!!.numberOfDiffs > 0 -> modifiedItems.add(skuId)
            }
        }

        return Triple(addedItems, removedItems, modifiedItems)
    }

    fun getListOfOrderItemsDiffs(leftPo: PurchaseOrder, rightPo: PurchaseOrder): List<String> {
        val leftMap = leftPo.orderItems.associateBy { it.skuId }
        val rightMap = rightPo.orderItems.associateBy { it.skuId }

        val allSkuIds = leftMap.keys union rightMap.keys

        return allSkuIds.filter { leftMap[it] != null && rightMap[it] != null }
            .mapNotNull { compare(leftMap[it], rightMap[it])?.toString(JSON_STYLE) }
    }
}
