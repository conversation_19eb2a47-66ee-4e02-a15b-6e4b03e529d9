package com.hellofresh.oms.purchaseOrder.service

import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.POType.EMERGENCY
import com.hellofresh.oms.model.POType.PREORDER
import com.hellofresh.oms.model.POType.STANDARD
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.PurchaseOrderStatus.DELETED
import com.hellofresh.oms.purchaseOrder.consumer.toEntity
import com.hellofresh.oms.purchaseOrder.repository.PurchaseOrderRepository
import com.hellofresh.oms.purchaseOrder.repository.PurchaseOrderVersionAndType
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder as ProtoPurchaseOrder
import io.micrometer.core.annotation.Timed
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import java.math.BigDecimal.ZERO
import java.util.UUID
import org.apache.commons.lang3.builder.ToStringStyle.JSON_STYLE
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

data class InboundPoMessageAnalysis(
    val poNumber: String,
    val poId: UUID,
    val event: InboundPoMessageEvent,
    val version: Int,
    val latestKnownVersion: Int? = null,
    val status: PurchaseOrderStatus? = null,
    val lastKnownStatus: PurchaseOrderStatus? = null,
    val headerDiff: String? = null,
    val lineItemsDiff: List<String>? = null
) {
    companion object {
        fun fromInboundPurchaseOrder(
            po: PurchaseOrder,
            event: InboundPoMessageEvent,
        ): InboundPoMessageAnalysis = InboundPoMessageAnalysis(
            poNumber = po.poNumber,
            poId = po.id,
            event = event,
            version = po.version,
            status = po.status,
        )

        fun fromInboundAndStoredPurchaseOrder(
            inboundPO: PurchaseOrder,
            storedPO: PurchaseOrder,
            event: InboundPoMessageEvent,
            headerDiff: String? = null,
            lineItemsDiff: List<String>? = null,
        ): InboundPoMessageAnalysis = InboundPoMessageAnalysis(
            poNumber = inboundPO.poNumber,
            poId = inboundPO.id,
            event = event,
            version = inboundPO.version,
            status = inboundPO.status,
            latestKnownVersion = storedPO.version,
            lastKnownStatus = storedPO.status,
            headerDiff = headerDiff,
            lineItemsDiff = lineItemsDiff,
        )
    }
}

enum class InboundPoMessageEvent {
    CREATION,
    NEW_REVISION,
    UPDATED_REVISION,
    STATUS_CHANGE,
    PO_SENT,
    BACKWARDS_SYNC,
    OLD_REVISION_UPDATE,
}

@Service
class PurchaseOrderService(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val meterRegistry: MeterRegistry
) {
    @Timed
    fun process(purchaseOrder: ProtoPurchaseOrder) {
        val incomingPoEntity = purchaseOrder.validatePurchaseOrder().toEntity()

        val analysis = analyzeInboundPO(incomingPoEntity)
        logger.info("Analysed Inbound PO: {}", analysis)
        meterRegistry.counter("inbound_kafka_po", listOf(Tag.of("event", analysis.event.toString()))).increment()

        val poFromDb = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(incomingPoEntity.poNumber)
        if (poFromDb == null || incomingPoEntity.shouldBeUpdated(poFromDb)) {
            var origin = Origin.OTHER
            poFromDb?.let {
                // In case the purchase order already exists, reuse its origin
                origin = poFromDb.origin
                logger.debug("Purchase order exist in our database. Updating ${getPoDataLog(purchaseOrder)}")
                verifyStatus(incomingPoEntity, poFromDb)
                purchaseOrderRepository.deleteById(incomingPoEntity.id)
            }
            purchaseOrderRepository.save(incomingPoEntity.copy(origin = origin))
        }
    }

    @Suppress("ReturnCount", "LongMethod")
    private fun analyzeInboundPO(incomingPoEntity: PurchaseOrder): InboundPoMessageAnalysis {
        val optionalStoredPoRevision = purchaseOrderRepository.findById(incomingPoEntity.id)
        val storedPoLatestVersion = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(
            incomingPoEntity.poNumber,
        )

        if (storedPoLatestVersion == null) {
            // We don't know about this PO Number - it must be a new PO
            return InboundPoMessageAnalysis.fromInboundPurchaseOrder(
                incomingPoEntity,
                InboundPoMessageEvent.CREATION,
            )
        }

        // So, we already know about this PO - it's not a new PO
        if (storedPoLatestVersion.type == incomingPoEntity.type &&
            storedPoLatestVersion.version > incomingPoEntity.version
        ) {
            // We know about a later version of this PO - this is probably
            // Tapioca telling us that it has deleted an older version.
            return InboundPoMessageAnalysis(
                poNumber = incomingPoEntity.poNumber,
                poId = incomingPoEntity.id,
                event = InboundPoMessageEvent.OLD_REVISION_UPDATE,
                version = incomingPoEntity.version,
                latestKnownVersion = storedPoLatestVersion.version,
            )
        }

        // So, we already know about this PO - it's not a new PO
        if (optionalStoredPoRevision.isEmpty) {
            // We don't know about this revision - it must be a new revision
            return InboundPoMessageAnalysis(
                poNumber = incomingPoEntity.poNumber,
                poId = incomingPoEntity.id,
                event = InboundPoMessageEvent.NEW_REVISION,
                version = incomingPoEntity.version,
                latestKnownVersion = storedPoLatestVersion.version,
            )
        }

        val storedPoRevision = optionalStoredPoRevision.get()

        // Check for diffs in the 'important' fields of the PO
        val diff = PurchaseOrderDiffBuilder.compare(storedPoRevision, incomingPoEntity)

        if (diff != null && diff.numberOfDiffs > 0) {
            // We've updated a revision in-place. Tapioca does this sometimes and it's annoying

            return InboundPoMessageAnalysis.fromInboundAndStoredPurchaseOrder(
                incomingPoEntity,
                storedPoRevision,
                event = InboundPoMessageEvent.UPDATED_REVISION,
                headerDiff = diff.toString(JSON_STYLE),
                lineItemsDiff = PurchaseOrderDiffBuilder.getListOfOrderItemsDiffs(storedPoRevision, incomingPoEntity),
            )
        }

        if (storedPoRevision.status != incomingPoEntity.status) {
            // Only the status has changed - it's a status update!
            return InboundPoMessageAnalysis.fromInboundAndStoredPurchaseOrder(
                incomingPoEntity,
                storedPoRevision,
                event = InboundPoMessageEvent.STATUS_CHANGE,
            )
        }

        fun inboundIsSentButUnsentInDb() = storedPoRevision.sendTime == null && incomingPoEntity.sendTime != null

        fun sendTimeInDbIsBeforeInbound() = storedPoRevision.sendTime != null &&
            storedPoRevision.sendTime!!.isBefore(incomingPoEntity.sendTime)

        if (inboundIsSentButUnsentInDb() || sendTimeInDbIsBeforeInbound()) {
            // The PO has been sent / re-sent.
            return InboundPoMessageAnalysis.fromInboundAndStoredPurchaseOrder(
                incomingPoEntity,
                storedPoRevision,
                event = InboundPoMessageEvent.PO_SENT,
            )
        }

        // Nothing has changed - this is Tapioca syncing back an update we've made to it.
        return InboundPoMessageAnalysis.fromInboundAndStoredPurchaseOrder(
            incomingPoEntity,
            storedPoRevision,
            event = InboundPoMessageEvent.BACKWARDS_SYNC,
        )
    }

    private fun verifyStatus(
        incomingPurchaseOrder: PurchaseOrder,
        existingPurchaseOrder: PurchaseOrderVersionAndType,
    ) {
        if (incomingPurchaseOrder.status != existingPurchaseOrder.status) {
            val statusComparisonMessage = "Incoming status: ${incomingPurchaseOrder.status}, existing status: ${existingPurchaseOrder.status}"
            logger.debug(
                "Status of incoming purchase order is different from the one in the database. $statusComparisonMessage",
            )
            if (incomingPurchaseOrder.status == DELETED || existingPurchaseOrder.status == DELETED) {
                logger.warn("Purchase order delete process is out of sync. $statusComparisonMessage")
            }
        } else {
            logger.debug("Status of incoming purchase order is the same as the one in the database.")
        }
    }

    private fun ProtoPurchaseOrder.validatePurchaseOrder(): ProtoPurchaseOrder {
        require(this.orderItemsCount > 0) { "Purchase order doesn't contain any item, skipping, ${getPoDataLog(this)}" }
        require(
            this.orderItemsList.none { it.hasCasePackaging() && it.casePackaging.size.value.toBigDecimal() <= ZERO },
        ) { "Received an sku with case packaging and size equals to zero, skipping, ${getPoDataLog(this)}" }
        return this
    }

    private fun PurchaseOrder.shouldBeUpdated(persistedPo: PurchaseOrderVersionAndType) =
        when (this.type) {
            persistedPo.type -> this.version >= persistedPo.version
            EMERGENCY -> true
            PREORDER, STANDARD -> persistedPo.type != EMERGENCY
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)

        private fun getPoDataLog(po: ProtoPurchaseOrder) = "poNumber: ${po.revision.number.formatted}, revision: ${po.revision.formatted}, poId: ${po.id}"
    }
}
