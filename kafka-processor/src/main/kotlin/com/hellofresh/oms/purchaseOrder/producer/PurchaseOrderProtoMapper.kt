package com.hellofresh.oms.purchaseOrder.producer

import com.google.protobuf.Timestamp
import com.google.type.Money as ProtoMoney
import com.google.type.PostalAddress
import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.purchaseOrder.repository.EmergencyReasonRepository
import com.hellofresh.oms.sku.repository.SkuRepository
import com.hellofresh.oms.supplier.repository.SupplierRepository
import com.hellofresh.proto.shared.supply.logistic.v1.ShippingMethod
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent.PurchaseOrder as ProtoPurchaseOrder
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZoneOffset
import org.springframework.stereotype.Component

@Component
@Suppress("TooManyFunctions")
class PurchaseOrderProtoMapper(
    private val supplierRepository: SupplierRepository,
    private val emergencyReasonRepository: EmergencyReasonRepository,
    private val skuRepository: SkuRepository
) {

    fun mapToProto(
        purchaseOrder: PurchaseOrder,
        event: PurchaseOrderEventType,
    ): PurchaseOrderEvent {
        val emergencyReason = emergencyReasonRepository.findById(
            checkNotNull(purchaseOrder.emergencyReasonUuid) {
                "Emergency reason UUID is null for purchase order ${purchaseOrder.id}"
            },
        ).orElseThrow { IllegalStateException("EmergencyReason ${purchaseOrder.emergencyReasonUuid} not found") }
        val supplier = supplierRepository.findById(purchaseOrder.supplierId)
            .orElseThrow { IllegalStateException("Supplier ${purchaseOrder.supplierId} not found") }

        val purchaseOrderBuilder = ProtoPurchaseOrder.newBuilder()
            .setMetadata(
                ProtoPurchaseOrder.Metadata.newBuilder()
                    .setMarket(supplier.market)
                    .setState(purchaseOrder.toProtoState())
                    .setYearWeek(purchaseOrder.yearWeek.toProtoYearWeek())
                    .setCreateTime(purchaseOrder.createdAt.toProtoTimestamp())
                    .setComment(purchaseOrder.comment ?: "")
                    .setReason(emergencyReason.name)
                    .setOrigin(purchaseOrder.toProtoOrigin())
                    .build(),
            )
            .setCreator(
                ProtoPurchaseOrder.Creator.newBuilder()
                    .setId(purchaseOrder.userId.toString()).setEmail(purchaseOrder.userEmail),
            )
            .setShipping(
                ProtoPurchaseOrder.Shipping.newBuilder()
                    .setDistributionCenterCode(purchaseOrder.dcCode)
                    .setAddress(purchaseOrder.shippingAddress.toProtoPostalAddress())
                    .setMethod(purchaseOrder.shippingMethod.toProtoMethod())
                    .setExpectedArrival(
                        ProtoPurchaseOrder.ExpectedArrival.newBuilder()
                            .setStartTime(purchaseOrder.expectedStartTime.toProtoTimestamp())
                            .setEndTime(purchaseOrder.expectedEndTime.toProtoTimestamp()),
                    ),
            )
            .setSupplier(
                ProtoPurchaseOrder.Supplier.newBuilder()
                    .setSupplierId(supplier.id.toString())
                    .setSupplierCode(supplier.code.toString()),
            )
            .setTotalPrice(purchaseOrder.totalPrice.toProtoMoney())

        purchaseOrder.orderItems.forEach { orderItem ->
            purchaseOrderBuilder.addOrderItems(orderItem.toProtoOrderItem())
        }

        return PurchaseOrderEvent.newBuilder()
            .setPoNumber(purchaseOrder.poNumber)
            .setPoId(purchaseOrder.id.toString())
            .setVersion(purchaseOrder.version)
            .setEventType(event.toProtoEventType())
            .setPayload(purchaseOrderBuilder).build()
    }

    private fun ShippingAddress.toProtoPostalAddress(): PostalAddress = PostalAddress.newBuilder()
        .setPostalCode(postalCode)
        .setRegionCode(countryCode)
        .setAdministrativeArea(region)
        .setLocality(city)
        .addAddressLines(streetAddress)
        .setOrganization(locationName)
        .build()

    private fun OrderItem.toProtoOrderItem(): ProtoPurchaseOrder.OrderItem {
        val sku = skuRepository.findById(skuId)
            .orElseThrow { IllegalStateException("Sku $skuId not found") }
        return ProtoPurchaseOrder.OrderItem.newBuilder()
            .setSku(sku.toProtoSku())
            .setBufferPermyriad(buffer.value)
            .setUom(packaging.unitOfMeasure.toProtoUom())
            .setTotalQuantity(totalQty.toInt())
            .setTotalPrice(totalPrice.toProtoMoney())
            .setPackaging(toProtoPackaging())
            .build()
    }

    private fun OrderItem.toProtoPackaging(): ProtoPurchaseOrder.Packaging =
        when (this.packaging.packagingType) {
            PackagingType.UNIT_TYPE -> ProtoPurchaseOrder.Packaging.newBuilder()
                .setUnitPackaging(
                    ProtoPurchaseOrder.UnitPackaging.newBuilder()
                        .setUnitCount(totalQty.toInt())
                        .setPricePerUnit(price.toProtoMoney()),
                ).build()

            PackagingType.CASE_TYPE -> ProtoPurchaseOrder.Packaging.newBuilder()
                .setCasePackaging(
                    ProtoPurchaseOrder.CasePackaging.newBuilder()
                        .setCaseCount(totalQty.toInt())
                        .setPricePerCase(price.toProtoMoney())
                        .setUnitsPerCase(packaging.caseSize.toString()),
                ).build()

            PackagingType.PALLET_TYPE -> ProtoPurchaseOrder.Packaging.newBuilder()
                .setPalletPackaging(
                    ProtoPurchaseOrder.PalletPackaging.newBuilder()
                        .setPalletCount(totalQty.toInt())
                        .setPricePerCase(price.toProtoMoney())
                        .setCasesPerPallet(
                            checkNotNull(casesPerPallet) {
                                "Cases per pallet is null for OrderItem. UUID: $id"
                            },
                        )
                        .setUnitsPerCase(packaging.caseSize.toString()),
                ).build()
        }

    private fun Sku.toProtoSku(): ProtoPurchaseOrder.SKU = ProtoPurchaseOrder.SKU.newBuilder()
        .setId(uuid.toString())
        .setCode(code)
        .setName(name)
        .setCategoryCode(category)
        .build()

    private fun PurchaseOrderEventType.toProtoEventType(): PurchaseOrderEvent.Event =
        when (this) {
            PurchaseOrderEventType.CREATED -> PurchaseOrderEvent.Event.EVENT_CREATED
            PurchaseOrderEventType.MODIFIED -> PurchaseOrderEvent.Event.EVENT_MODIFIED
            PurchaseOrderEventType.CANCELLED -> PurchaseOrderEvent.Event.EVENT_CANCELLED
            PurchaseOrderEventType.STATUS_UPDATED -> PurchaseOrderEvent.Event.EVENT_STATUS_UPDATED
        }

    private fun PurchaseOrder.toProtoState(): ProtoPurchaseOrder.State =
        when (this.status) {
            PurchaseOrderStatus.INITIATED -> if (this.sendTime != null) {
                ProtoPurchaseOrder.State.STATE_SENT
            } else {
                ProtoPurchaseOrder.State.STATE_INITIATED
            }

            PurchaseOrderStatus.APPROVED -> ProtoPurchaseOrder.State.STATE_APPROVED
            PurchaseOrderStatus.REJECTED -> ProtoPurchaseOrder.State.STATE_REJECTED
            PurchaseOrderStatus.DELETED -> ProtoPurchaseOrder.State.STATE_CANCELLED
        }

    private fun ShipMethodEnum.toProtoMethod(): ShippingMethod =
        when (this) {
            ShipMethodEnum.VENDOR -> ShippingMethod.SHIPPING_METHOD_VENDOR
            ShipMethodEnum.CROSSDOCK -> ShippingMethod.SHIPPING_METHOD_CROSSDOCK
            ShipMethodEnum.FREIGHT_ON_BOARD -> ShippingMethod.SHIPPING_METHOD_FREIGHT_ON_BOARD
            ShipMethodEnum.OTHER -> ShippingMethod.SHIPPING_METHOD_OTHER
        }

    private fun UOM.toProtoUom(): ProtoPurchaseOrder.UOM =
        when (this) {
            UOM.UNIT -> ProtoPurchaseOrder.UOM.UOM_UNIT
            UOM.KG -> ProtoPurchaseOrder.UOM.UOM_KG
            UOM.L -> ProtoPurchaseOrder.UOM.UOM_LITRE
            UOM.LBS -> ProtoPurchaseOrder.UOM.UOM_LBS
            UOM.GAL -> ProtoPurchaseOrder.UOM.UOM_GAL
            UOM.OZ -> ProtoPurchaseOrder.UOM.UOM_OZ
        }

    private fun PurchaseOrder.toProtoOrigin(): ProtoPurchaseOrder.Origin =
        when (this.origin) {
            Origin.OTHER -> ProtoPurchaseOrder.Origin.ORIGIN_OTHER
            Origin.PLANNED -> ProtoPurchaseOrder.Origin.ORIGIN_PLANNED
            Origin.MANUAL -> ProtoPurchaseOrder.Origin.ORIGIN_MANUAL
        }

    private fun YearWeek.toProtoYearWeek() =
        ProtoPurchaseOrder.YearWeek.newBuilder().setYear(getYear()).setWeek(getWeek()).build()

    fun LocalDateTime.toProtoTimestamp(): Timestamp = Timestamp.newBuilder()
        .setSeconds(this.toInstant(ZoneOffset.UTC).epochSecond)
        .build()

    fun Money.toProtoMoney(): ProtoMoney {
        val units = amount.toLong()
        val nanos = amount.abs()
            .subtract(BigDecimal(units).abs())
            .multiply(PRECISION)
            .toInt()

        return ProtoMoney.newBuilder()
            .setUnits(units)
            .setNanos(nanos)
            .setCurrencyCode(currency)
            .build()
    }

    companion object {
        // This is the precision used for the Money conversion
        private val PRECISION = BigDecimal(1_000_000_000)
    }
}
