package com.hellofresh.oms.supplier.consumer

import com.hellofresh.oms.supplier.service.SupplierService
import com.hellofresh.planning.remps.facility.facility as AvroSupplier
import java.util.UUID
import java.util.function.Consumer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component class SupplierConsumer(
    @Autowired val supplierService: SupplierService,
) {

    @Bean fun processSupplier(): Consumer<Message<AvroSupplier>> = Consumer { record ->
        val headerId = requireNotNull(record.headers["UUID"]) { "Supplier id cannot be null" } as ByteArray
        val supplierId = UUID.fromString(String(headerId, Charsets.US_ASCII))
        val supplier = record.payload
        logger.info("Received supplier code :${supplier.code} market:${supplier.market} id:$supplierId")

        // Workaround; if the supplier is Canadian, then we will add the DC code as a suffix to the supplier name.
        // Can be removed when CA is fully onboarded to GSS.
        if (supplier.isCanadian()) supplier.applyCanadianSuffixModifications()

        supplierService.upsertSupplier(supplier.mapToSupplier(supplierId))
    }

    private fun AvroSupplier.isCanadian() = this.market == "ca"

    private fun AvroSupplier.applyCanadianSuffixModifications() {
        for ((suffix, replacement) in suffixReplacements) {
            if (this.name.endsWith(suffix)) {
                this.name = this.name.removeSuffix(suffix) + replacement
                break
            }
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(SupplierConsumer::class.java)

        private val suffixReplacements = mapOf(
            "AA" to "AB",
            "OA" to "ON",
            "BA" to "BC",
        )
    }
}
