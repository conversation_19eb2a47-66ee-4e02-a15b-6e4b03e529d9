package com.hellofresh.oms.supplier.service

import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.shippingMethod.repository.ShipMethodRepository
import com.hellofresh.oms.supplier.repository.SupplierRepository
import io.micrometer.core.annotation.Timed
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class SupplierService(
    @Autowired val supplierRepository: SupplierRepository,
    @Autowired val shipMethodRepository: ShipMethodRepository,
) {
    @Timed
    fun upsertSupplier(supplier: SupplierExtended): SupplierExtended {
        val shipMethods = shipMethodRepository.findAllBySupplierId(supplier.id)
        return supplierRepository.save(supplier.copy(shipMethods = shipMethods))
    }
}
