package com.hellofresh.oms.supplier.consumer

object SupplierConstants {

    /**
     We need this since SPS provides distribution center for us market as "US" which makes
     it impossible to fetch supplier entity by dc it belongs to.
     */
    val US_DISTRIBUTION_CENTERS = listOf(
        "NJ", "EZ", "GD", "TF", "RC", "ZE", "FN", "DC", "SC", "RD", "SF", "UC", "US", "TX", "TN", "LX", "GL", "MF", "HN", "FP",
        "FC", "FT", "FJ", "CP", "GW", "FG", "GA", "PI", "TT", "DM", "NX", "JK", "FW", "DL", "PA", "JO", "TI", "AZ", "WL", "WD",
        "LP", "ET", "EC", "EV", "GS", "HD", "FZ", "RM", "RT", "IS", "IC", "IB", "IN", "IJ", "IO", "FD", "FE", "FF", "FH", "FK",
        "MS", "PS", "SR", "PP", "MM", "FS", "PF", "NA", "GM", "CW", "FL", "NF", "FO", "AF", "IF", "CM", "AM", "SW", "CO", "LH",
        "FV", "DF", "IW", "IX", "RA", "MT", "FX",
    )
}
