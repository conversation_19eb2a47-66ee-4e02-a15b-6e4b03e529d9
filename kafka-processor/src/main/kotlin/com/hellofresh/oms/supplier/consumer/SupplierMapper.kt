package com.hellofresh.oms.supplier.consumer

import com.hellofresh.oms.model.SupplierContact
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.model.supplier.SupplierStatus
import com.hellofresh.oms.model.supplier.SupplierStatus.ACTIVE
import com.hellofresh.oms.model.supplier.SupplierStatus.ARCHIVED
import com.hellofresh.oms.model.supplier.SupplierStatus.INACTIVE
import com.hellofresh.oms.model.supplier.SupplierStatus.OFFBOARDING
import com.hellofresh.oms.model.supplier.SupplierStatus.ONBOARDING
import com.hellofresh.oms.supplier.consumer.SupplierConstants.US_DISTRIBUTION_CENTERS
import com.hellofresh.planning.remps.facility.contact_person as AvroContactPerson
import com.hellofresh.planning.remps.facility.facility as AvroSupplier
import com.hellofresh.planning.remps.facility.statuses as AvroSupplierStatus
import com.hellofresh.planning.remps.facility.statuses.Active as Active
import com.hellofresh.planning.remps.facility.statuses.Archived as Archived
import com.hellofresh.planning.remps.facility.statuses.Inactive as Inactive
import com.hellofresh.planning.remps.facility.statuses.Offboarding
import com.hellofresh.planning.remps.facility.statuses.Onboarding as Onboarding
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

private const val PO_CONTACT_ROLE = "purchase-order-contact"

fun AvroSupplier.mapToSupplier(supplierID: UUID): SupplierExtended = SupplierExtended(
    id = supplierID,
    parentId = UUID.fromString(this.parentId.toString()),
    code = this.code,
    name = this.name.trim(),
    status = this.status.toSupplierStatus(),
    market = this.market,
    currency = this.currency,
    type = this.type,
    contacts = this.contactPersons.toSupplierContact(supplierID),
    createdAt = mapToLocalDateTime(this.createdAt),
    updatedAt = mapToLocalDateTime(this.updatedAt),
    dcCodes = this.distributionCenters.resolveDistributionCenters(this.market),
    supplierAddress = SupplierAddress(
        city = this.address.city,
        country = this.address.country,
        state = this.address.state,
        address = this.address.address,
        number = this.address.number,
        postCode = this.address.postCode,
    ),
    shipMethods = emptyList(),
)

private fun AvroSupplierStatus.toSupplierStatus(): SupplierStatus = when (this) {
    Active -> ACTIVE
    Onboarding -> ONBOARDING
    Inactive -> INACTIVE
    Archived -> ARCHIVED
    Offboarding -> OFFBOARDING
}

private fun List<AvroContactPerson>.toSupplierContact(supplierId: UUID): Set<SupplierContact> = this.asSequence()
    .filter { it.roles.contains(PO_CONTACT_ROLE) }
    .map { it.emails }.flatten()
    .map { SupplierContact(supplierId, it) }
    .toSet()

private fun mapToLocalDateTime(updatedAt: String) = LocalDateTime.parse(updatedAt, DateTimeFormatter.ISO_DATE_TIME)

private fun List<String>.resolveDistributionCenters(market: String): List<String> =
    if (market.lowercase() == "us") US_DISTRIBUTION_CENTERS else this
