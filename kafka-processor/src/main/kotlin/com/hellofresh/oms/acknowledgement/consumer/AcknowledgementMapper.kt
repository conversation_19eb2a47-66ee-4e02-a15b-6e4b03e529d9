package com.hellofresh.oms.acknowledgement.consumer

import com.google.type.DateTime
import com.google.type.Decimal
import com.hellofresh.oms.model.acknowledgement.Acknowledgement
import com.hellofresh.oms.model.acknowledgement.AcknowledgementLineItem
import com.hellofresh.oms.model.acknowledgement.AcknowledgementLineStateEnum
import com.hellofresh.oms.model.acknowledgement.UnitOfMeasureAcknowledgementLineEnum
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement as ProtoPurchaseOrderAcknowledgement
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.AcknowledgementItem as ProtoPurchaseOrderAcknowledgementItem
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.AcknowledgementItem.State as ProtoPurchaseOrderAcknowledgementItemState
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement.Quantity.CasePackaging.UnitOfMeasure as ProtoPurchaseOrderAcknowledgementItemUom
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.reflect.KClass

fun KClass<Acknowledgement>.tryFrom(ack: ProtoPurchaseOrderAcknowledgement): Acknowledgement {
    val acknowledgementId = UUID.randomUUID()

    return Acknowledgement(
        id = acknowledgementId,
        poId = UUID.fromString(ack.purchaseOrderId),
        poNumber = ack.purchaseOrderNumber,
        createTime = ack.takeIf { it.hasCreateTime() }?.createTime?.toLocalDateTimeInUTC(),
        pickupTime = ack.takeIf { it.hasPickupTime() }?.pickupTime?.toLocalDateTimeInUTC(),
        pickupRegion = ack.pickupAddress.regionCode,
        pickupPostalCode = ack.pickupAddress.postalCode,
        pickupAdministrativeArea = ack.pickupAddress.administrativeArea,
        pickupLocality = ack.pickupAddress.locality,
        pickupOrganization = ack.pickupAddress.organization,
        pickupAddress = ack.pickupAddress.addressLinesList,
        lines = ack.itemsList.map { AcknowledgementLineItem::class.tryFrom(it, acknowledgementId) },
    )
}

fun KClass<AcknowledgementLineItem>.tryFrom(ackItem: ProtoPurchaseOrderAcknowledgementItem, acknowledgementId: UUID): AcknowledgementLineItem {
    val state = AcknowledgementLineStateEnum.tryFrom(ackItem.state)
    val uom = UnitOfMeasureAcknowledgementLineEnum.tryFrom(ackItem.promisedQuantity.casePackaging.unit)

    return AcknowledgementLineItem(
        id = UUID.randomUUID(),
        acknowledgementId = acknowledgementId,
        skuId = UUID.fromString(ackItem.id),
        skuCode = ackItem.skuCode,
        state = state,
        numberOfPallets = ackItem.palletsQuantity,
        promisedTime = ackItem.takeIf { it.hasPromisedTime() }?.promisedTime?.toLocalDateTimeInUTC(),
        numberOfUnits = ackItem.promisedQuantity.orderSize.toDouble(),
        unitsPerCase = ackItem.promisedQuantity.takeIf {
            it.hasCasePackaging()
        }?.casePackaging?.size?.getBigDecimal()?.toDouble(),
        uom = uom
    )
}

private fun AcknowledgementLineStateEnum.Companion.tryFrom(state: ProtoPurchaseOrderAcknowledgementItemState): AcknowledgementLineStateEnum =
    when (state) {
        ProtoPurchaseOrderAcknowledgementItemState.STATE_UNSPECIFIED -> AcknowledgementLineStateEnum.STATE_UNSPECIFIED
        ProtoPurchaseOrderAcknowledgementItemState.STATE_OPEN -> AcknowledgementLineStateEnum.STATE_OPEN
        ProtoPurchaseOrderAcknowledgementItemState.STATE_ACCEPTED -> AcknowledgementLineStateEnum.STATE_ACCEPTED
        ProtoPurchaseOrderAcknowledgementItemState.STATE_ACCEPTED_WITH_CHANGES -> AcknowledgementLineStateEnum.STATE_ACCEPTED_WITH_CHANGES
        ProtoPurchaseOrderAcknowledgementItemState.STATE_REJECTED -> AcknowledgementLineStateEnum.STATE_REJECTED

        ProtoPurchaseOrderAcknowledgementItemState.UNRECOGNIZED -> throw EnumMappingError(state.name)
    }

private fun UnitOfMeasureAcknowledgementLineEnum.Companion.tryFrom(uom: ProtoPurchaseOrderAcknowledgementItemUom): UnitOfMeasureAcknowledgementLineEnum =
    when (uom) {
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_UNSPECIFIED -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_UNSPECIFIED
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_OTHER -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_OTHER
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_UNIT -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_UNIT
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_CASE -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_CASE
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_KG -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_KG
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_LBS -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_LBS
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_OZ -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_OZ
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_LITRE -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_LITRE
        ProtoPurchaseOrderAcknowledgementItemUom.UNIT_OF_MEASURE_GAL -> UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_GAL

        ProtoPurchaseOrderAcknowledgementItemUom.UNRECOGNIZED -> throw EnumMappingError(uom.name)
    }

private fun DateTime.toLocalDateTimeInUTC(): LocalDateTime {
    val zoneId: ZoneId = if (this.hasTimeZone()) ZoneId.of(this.timeZone.id) else ZoneId.of("UTC")

    val zonedDateTime = ZonedDateTime.of(
        this.year,
        this.month,
        this.day,
        this.hours,
        this.minutes,
        this.seconds,
        this.nanos,
        zoneId,
    )

    return zonedDateTime.toLocalDateTime()
}

/**
 * Protobuf 3 has done away with `required` or `optional` fields. Instead, all fields are required,
 * and when not found, a default value is initialized instead.
 * See:
 *  * https://protobuf.dev/programming-guides/proto3/#default
 *     * Note: "For numeric types, the default value is zero."
 *     * For Decimal() the default value is "" (empty string), not zero.
 *  * https://github.com/protocolbuffers/protobuf/issues/2497
 */
private fun Decimal.getBigDecimal(): BigDecimal = this.value.takeUnless {
    it.isNullOrBlank()
}?.toBigDecimal() ?: BigDecimal.ZERO

abstract class PurchaseOrderAcknowledgementMappingError(message: String) : IllegalArgumentException(message)

class EnumMappingError(message: String) : PurchaseOrderAcknowledgementMappingError(message)
