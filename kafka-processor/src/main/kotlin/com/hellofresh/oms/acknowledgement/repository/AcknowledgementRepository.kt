package com.hellofresh.oms.acknowledgement.repository

import com.hellofresh.oms.model.acknowledgement.Acknowledgement
import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface AcknowledgementRepository : JpaRepository<Acknowledgement, UUID> {
    fun findAllByPoId(poId: UUID): List<Acknowledgement>

    @Query(
        """
        SELECT DISTINCT a FROM Acknowledgement a
        LEFT JOIN FETCH a.lines
        WHERE a.poId = :poId
    """
    )
    fun findAllByPoIdWithLines(@Param("poId") poId: UUID): List<Acknowledgement>
}
