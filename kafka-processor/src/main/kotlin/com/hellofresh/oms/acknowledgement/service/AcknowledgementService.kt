package com.hellofresh.oms.acknowledgement.service

import com.hellofresh.oms.acknowledgement.repository.AcknowledgementRepository
import com.hellofresh.oms.client.featureflag.FeatureFlagClient
import com.hellofresh.oms.client.tapioca.TapiocaClient
import com.hellofresh.oms.client.tapioca.domain.ApprovePurchaseOrderRequest
import com.hellofresh.oms.client.tapioca.domain.RejectPurchaseOrderRequest
import com.hellofresh.oms.model.acknowledgement.Acknowledgement
import com.hellofresh.oms.model.acknowledgement.AcknowledgementLineStateEnum.STATE_ACCEPTED
import com.hellofresh.oms.model.acknowledgement.AcknowledgementLineStateEnum.STATE_ACCEPTED_WITH_CHANGES
import com.hellofresh.oms.model.acknowledgement.AcknowledgementLineStateEnum.STATE_REJECTED
import io.micrometer.core.annotation.Timed
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class AcknowledgementService(
    private val acknowledgementRepository: AcknowledgementRepository,
    private val featureFlagClient: FeatureFlagClient,
    private val tapiocaClient: TapiocaClient,
    private val meterRegistry: MeterRegistry,
) {
    @Timed
    fun process(acknowledgement: Acknowledgement) {
        acknowledgementRepository.save(acknowledgement)

        val shouldUseNewApproveReject = featureFlagClient.shouldUseNewApproveRejectFeatureFlag()

        if (shouldUseNewApproveReject) {
            val poAcknowledgements = acknowledgementRepository.findAllByPoIdWithLines(acknowledgement.poId)

            val shouldApprove = poAcknowledgements.all { ack ->
                ack.lines.all { it.state == STATE_ACCEPTED }
            }

            val shouldReject = poAcknowledgements.any { ack ->
                ack.lines.any { it.state in setOf(STATE_REJECTED, STATE_ACCEPTED_WITH_CHANGES) }
            }

            if (shouldApprove) {
                handlePoApprove(acknowledgement)
            } else if (shouldReject) {
                handlePoReject(acknowledgement)
            }
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun handlePoApprove(acknowledgement: Acknowledgement) {
        try {
            logger.info(
                "Approving a Purchase Order after receiving Acknowledgment. poId = $acknowledgement.poId",
            )

            tapiocaClient.approvePurchaseOrder(ApprovePurchaseOrderRequest(acknowledgement.poId))
        } catch (e: Throwable) {
            logger.error("Error while approving a Purchase Order after receiving Acknowledgement", e)

            incrementFailingAttempt(FAILING_APPROVING_PO_ATTEMPT)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun handlePoReject(acknowledgement: Acknowledgement) {
        try {
            logger.info(
                "Rejecting a Purchase Order after receiving Acknowledgment. poId = $acknowledgement.poId",
            )

            tapiocaClient.rejectPurchaseOrder(RejectPurchaseOrderRequest(acknowledgement.poId))
        } catch (e: Throwable) {
            logger.error("Error while rejecting a Purchase Order after receiving Acknowledgement", e)

            incrementFailingAttempt(FAILING_REJECTING_PO_ATTEMPT)
        }
    }

    private fun incrementFailingAttempt(metricName: String) {
        val actionTagValue = when (metricName) {
            FAILING_APPROVING_PO_ATTEMPT -> "APPROVE"
            FAILING_REJECTING_PO_ATTEMPT -> "REJECT"
            else -> "UNKNOWN"
        }

        Counter.builder(metricName)
            .tag("action", actionTagValue)
            .register(meterRegistry)
            .increment()
    }

    companion object {
        private const val FAILING_APPROVING_PO_ATTEMPT = "failing_approving_po_attempt"
        private const val FAILING_REJECTING_PO_ATTEMPT = "failing_rejecting_po_attempt"
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
