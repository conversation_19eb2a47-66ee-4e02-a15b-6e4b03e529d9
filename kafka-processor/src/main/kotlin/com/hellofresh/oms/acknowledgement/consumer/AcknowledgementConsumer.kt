package com.hellofresh.oms.acknowledgement.consumer

import com.hellofresh.oms.acknowledgement.service.AcknowledgementService
import com.hellofresh.oms.model.acknowledgement.Acknowledgement
import com.hellofresh.proto.stream.supply.purchaseOrder.acknowledgement.v2.PurchaseOrderAcknowledgement
import java.util.function.Consumer
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class AcknowledgementConsumer(
    private val acknowledgementService: AcknowledgementService
) {
    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processAcknowledgement(): Consumer<Message<PurchaseOrderAcknowledgement>> =
        Consumer { message ->
            val purchaseOrderAcknowledgement = message.payload

            try {
                val acknowledgement = Acknowledgement::class.tryFrom(message.payload)

                logger.info(
                    "Processing PO Acknowledgment. [PO Number: {}. PO ID: {}]",
                    acknowledgement.poNumber,
                    acknowledgement.poId
                )

                acknowledgementService.process(acknowledgement)
            } catch (e: EnumMappingError) {
                logger.error(
                    "Couldn't process PO Acknowledgment due to unparsable Enum: [PO ID: {}, Exception: {}]",
                    purchaseOrderAcknowledgement.purchaseOrderId,
                    e,
                )
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process PO Acknowledgement due to an unexpected error: [PO ID: {}, Exception: {}]",
                    purchaseOrderAcknowledgement.purchaseOrderId,
                    e,
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
