package com.hellofresh.oms.imt.comment.consumer

import com.hellofresh.oms.model.Comment
import com.hellofresh.oms.model.YearWeek
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import org.apache.avro.generic.GenericRecord

const val WEEK_MAX_LENGTH = 6
const val HF_YEAR_OFFSET = 4
const val HF_WEEK_OFFSET = 2
const val MICROS_TO_MILLIS = 1_000

fun GenericRecord.toComment(): Comment {
    val after = this["after"] as GenericRecord
    return Comment(
        id = UUID.nameUUIDFromBytes(after["id"].toString().toByteArray()),
        sourceId = after["id"]?.toString(),
        resourceType = after["resource_type"].toString(),
        resourceId = after["resource_id"].toString(),
        domain = after["domain"].toString(),
        dc = after["site"].toString(),
        brand = after["brand"].toString(),
        yearWeek = calculateYearWeek(after["week"].toString()),
        comment = after["comment"].toString(),
        createdBy = after["updated_by"].toString(), // IMT does not have created_by or created_at
        updatedBy = after["updated_by"].toString(),
        updatedAt = epochMicrosToLocalDateTime(after["last_updated"] as Long),
        createdAt = epochMicrosToLocalDateTime(after["last_updated"] as Long),
    )
}

fun calculateYearWeek(imtWeek: String): YearWeek {
    require(imtWeek.length == WEEK_MAX_LENGTH) { "imtWeek must be 6 characters long" }
    val year = imtWeek.take(HF_YEAR_OFFSET)
    val week = imtWeek.takeLast(HF_WEEK_OFFSET)
    return YearWeek(year, week)
}

fun epochMicrosToLocalDateTime(epochMicros: Long): LocalDateTime =
    (epochMicros / MICROS_TO_MILLIS).let {
        Instant.ofEpochMilli(it)
            .atZone(ZoneOffset.UTC)
            .toLocalDateTime()
    }
