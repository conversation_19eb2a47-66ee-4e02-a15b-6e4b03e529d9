package com.hellofresh.oms.imt.poVoid.consumer

import com.hellofresh.oms.imt.poVoid.service.PoVoidService
import java.util.function.Consumer
import org.apache.avro.generic.GenericRecord
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class PoVoidConsumer(
    private val poVoidService: PoVoidService
) {
    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processImtPoVoid(): Consumer<Message<GenericRecord>> =
        Consumer { message ->
            try {
                val value = message.payload.toPoVoid()
                logger.warn("processing po void: {}", value)
                poVoidService.process(value)
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process po void unexpected error{$e}",
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
