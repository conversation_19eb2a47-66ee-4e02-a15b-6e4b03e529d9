package com.hellofresh.oms.imt.comment.consumer

import com.hellofresh.oms.imt.comment.service.CommentService
import java.util.function.Consumer
import org.apache.avro.generic.GenericRecord
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class CommentConsumer(
    private val commentService: CommentService
) {
    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processImtComment(): Consumer<Message<GenericRecord>> =
        Consumer { message ->
            try {
                val value = message.payload.toComment()
                logger.warn("processing imt comment: {}", value)
                commentService.process(value)
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process imt comment unexpected error{$e}",
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
