package com.hellofresh.oms.imt.receptOverride.service

import com.hellofresh.oms.imt.receptOverride.repository.ReceiptOverrideRepository
import com.hellofresh.oms.model.imt.recipeOverride.ReceiptOverride
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class ReceiptOverrideService(
    private val receiptOverrideRepository: ReceiptOverrideRepository,
) {
    @Timed
    fun process(receiptOverride: ReceiptOverride) {
        receiptOverrideRepository.save(receiptOverride)
    }
}
