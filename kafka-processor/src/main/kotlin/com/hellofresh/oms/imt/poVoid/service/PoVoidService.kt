package com.hellofresh.oms.imt.poVoid.service

import com.hellofresh.oms.imt.poVoid.repository.PoVoidRepository
import com.hellofresh.oms.model.imt.poVoid.PoVoid
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class PoVoidService(
    private val poVoidRepository: PoVoidRepository,
) {
    @Timed
    fun process(poVoid: PoVoid) {
        poVoidRepository.save(poVoid)
    }
}
