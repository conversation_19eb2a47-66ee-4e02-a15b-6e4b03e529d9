package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.repository

import com.hellofresh.oms.model.SupplyQuantityRecommendationDaily
import io.micrometer.core.annotation.Timed
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
class SupplyQuantityRecommendationDailyBulkRepositoryImpl(
    private val jt: NamedParameterJdbcTemplate,
) : SupplyQuantityRecommendationDailyBulkRepository {

    @Timed
    @Transactional
    @Suppress("LongMethod")
    override fun upsertAll(recommendations: List<SupplyQuantityRecommendationDaily>) {
        if (recommendations.isEmpty()) {
            return
        }

        val values = recommendations
            .asReversed()
            .distinctBy { Triple(it.skuId, it.dcCode, it.supplyDate) }
            .asReversed()

        val sql = StringBuilder(
            """
                INSERT INTO supply_quantity_recommendation_daily (
                    sku_id, dc_code, supply_date, forecasted_demanded,
                    production_year_week, created_at, updated_at
                ) VALUES """,
        )

        val paramValues = MapSqlParameterSource()
        values.forEachIndexed { idx, value ->
            sql.append(
                """ (
                    :skuId$idx, :dcCode$idx, :supplyDate$idx, :forecastedDemanded$idx,
                    :productionYearWeek$idx, :createdAt$idx, :updatedAt$idx
                )"""
            )

            if (idx < values.size - 1) {
                sql.append(", ")
            }

            paramValues.addValue("skuId$idx", value.skuId)
            paramValues.addValue("dcCode$idx", value.dcCode)
            paramValues.addValue("supplyDate$idx", value.supplyDate)
            paramValues.addValue("forecastedDemanded$idx", value.forecastedDemanded)
            paramValues.addValue("productionYearWeek$idx", value.productionWeek.value)
            paramValues.addValue("createdAt$idx", value.createdAt)
            paramValues.addValue("updatedAt$idx", value.updatedAt)
        }

        sql.append(
            """
                ON CONFLICT (sku_id, dc_code, supply_date) DO UPDATE SET
                    forecasted_demanded = excluded.forecasted_demanded,
                    production_year_week = excluded.production_year_week,
                    created_at = excluded.created_at,
                    updated_at = excluded.updated_at
            """
        )

        jt.batchUpdate(sql.toString(), arrayOf(paramValues))
    }
}
