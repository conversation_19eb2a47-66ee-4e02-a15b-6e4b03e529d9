package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.service

import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.repository.SupplyQuantityRecommendationDailyBulkRepository
import com.hellofresh.oms.model.SupplyQuantityRecommendationDaily
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class SupplyQuantityRecommendationDailyService(
    private val supplyQuantityRecommendationDailyBulkRepository: SupplyQuantityRecommendationDailyBulkRepository
) {

    @Timed
    fun process(entities: List<SupplyQuantityRecommendationDaily>) {
        supplyQuantityRecommendationDailyBulkRepository.upsertAll(entities)
    }
}
