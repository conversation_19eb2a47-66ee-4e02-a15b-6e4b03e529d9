package com.hellofresh.oms.imt.receptOverride.consumer

import com.hellofresh.oms.model.imt.recipeOverride.ReceiptOverride
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import org.apache.avro.generic.GenericRecord

fun GenericRecord.toReceiptOverride(): ReceiptOverride {
    val after = this["after"] as GenericRecord
    return ReceiptOverride(
        poNumber = after["po_number"].toString().split('_').first(),
        poReference = after["po_number"].toString(),
        hfWeek = after["week"].toString(),
        brand = after["brand"].toString(),
        dc = after["dc"].toString(),
        skuCode = after["sku_code"].toString(),
        quantity = after["qty"] as? Int ?: 0,
        source = after["source"].toString(),
        userEmail = after["user"].toString(),
        createdAt = epochMillisToLocalDateTime(after["cre_tmst"] as? Long),
        updatedAt = epochMillisToLocalDateTime(after["upd_tmst"] as? Long),
        comment = after["comment"]?.toString(),
        market = after["market"].toString(),
        deletedBy = after["deleted_by"]?.toString(),
        deletedAt = epochMillisToLocalDateTime(after["deleted_ts"] as? Long),
        receivingDate = epochMillisToLocalDateTime(after["receiving_date"] as? Long),
        cases = after["cases"]?.toString()?.toInt(),
    )
}

fun epochMillisToLocalDateTime(epochMillis: Long?): LocalDateTime? =
    epochMillis?.let {
        Instant.ofEpochMilli(it)
            .atZone(ZoneOffset.UTC)
            .toLocalDateTime()
    }
