package com.hellofresh.oms.imt.receptOverride.consumer

import com.hellofresh.oms.imt.receptOverride.service.ReceiptOverrideService
import java.util.function.Consumer
import org.apache.avro.generic.GenericRecord
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class ReceiptOverrideConsumer(
    private val receiptOverrideService: ReceiptOverrideService
) {
    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processImtReceiptOverride(): Consumer<Message<GenericRecord>> =
        Consumer { message ->
            try {
                val value = message.payload.toReceiptOverride()
                logger.warn("processing receipt override: {}", value)
                receiptOverrideService.process(value)
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process receipt override unexpected error{$e}",
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
