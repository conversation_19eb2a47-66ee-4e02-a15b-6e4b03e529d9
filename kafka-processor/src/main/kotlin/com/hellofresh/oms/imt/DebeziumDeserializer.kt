package com.hellofresh.oms.imt

import io.confluent.kafka.serializers.KafkaAvroDeserializer
import org.apache.avro.generic.GenericRecord
import org.apache.kafka.common.serialization.Deserializer

class DebeziumDeserializer : Deserializer<GenericRecord> {

    private val delegate = KafkaAvroDeserializer()

    override fun configure(configs: Map<String, *>?, isKey: <PERSON><PERSON><PERSON>) =
        delegate.configure(configs, isKey)

    override fun deserialize(topic: String?, data: ByteArray?): GenericRecord? =
        delegate.deserialize(topic, data) as? GenericRecord

    override fun close() =
        delegate.close()
}
