package com.hellofresh.oms.imt.poVoid.consumer

import com.hellofresh.oms.model.imt.poVoid.PoVoid
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import org.apache.avro.generic.GenericRecord

fun GenericRecord.toPoVoid(): PoVoid {
    val after = this["after"] as GenericRecord
    return PoVoid(
        id = UUID.nameUUIDFromBytes(after["id"].toString().toByteArray()),
        poNumber = after["po_number"].toString().split('_').first(),
        poReference = after["po_number"].toString(),
        week = after["week"].toString(),
        brand = after["brand"].toString(),
        dc = after["dc"].toString(),
        skuCode = after["sku_code"].toString(),
        supplierName = after["supplier_name"]?.toString(),
        createAt = epochMillisToLocalDateTime(after["cre_tmst"] as Long),
        lastUpdated = epochMillisToLocalDateTime(after["last_updated"] as Long?),
        deletedBy = after["deleted_by"]?.toString(),
        source = after["source"].toString(),
        userEmail = after["user"].toString(),
        comment = after["comment"]?.toString(),
        market = after["market"].toString(),
    )
}

fun epochMillisToLocalDateTime(epochMillis: Long?): LocalDateTime? =
    epochMillis?.let {
        Instant.ofEpochMilli(it)
            .atZone(ZoneOffset.UTC)
            .toLocalDateTime()
    }
