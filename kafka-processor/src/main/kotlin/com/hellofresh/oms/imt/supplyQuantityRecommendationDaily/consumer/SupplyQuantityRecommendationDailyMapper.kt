package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer

import com.hellofresh.oms.model.SupplyQuantityRecommendationDaily
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

fun SupplyQuantityRecommendationDailyVal.toEntity() = SupplyQuantityRecommendationDaily(
    skuId = UUID.fromString(skuId),
    dcCode = dcCode,
    supplyDate = LocalDate.of(date.year, date.month, date.day),
    forecastedDemanded = BigDecimal(forecastedDemanded.qty.value),
    productionWeek = YearWeek(productionWeek.year, productionWeek.week),
    createdAt = LocalDateTime.now(),
    updatedAt = LocalDateTime.now(),
)
