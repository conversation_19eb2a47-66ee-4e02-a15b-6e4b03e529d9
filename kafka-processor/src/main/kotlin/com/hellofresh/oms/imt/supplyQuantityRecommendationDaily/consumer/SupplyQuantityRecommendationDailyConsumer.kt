package com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.consumer

import com.hellofresh.oms.imt.supplyQuantityRecommendationDaily.service.SupplyQuantityRecommendationDailyService
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import java.math.BigDecimal
import java.util.function.Consumer
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.stereotype.Component

@Component
class SupplyQuantityRecommendationDailyConsumer(
    private val supplyQuantityRecommendationDailyService: SupplyQuantityRecommendationDailyService
) {
    @Bean
    fun processSupplyQuantityRecommendationDaily(): Consumer<List<SupplyQuantityRecommendationDailyVal>> =
        Consumer { messages ->
            logger.info("Received ${messages.size} SupplyQuantityRecommendationDailyVal messages")
            val entities = messages
                .filter { it.isDateInitialized() && it.hasForecast() }
                .map { it.toEntity() }
            supplyQuantityRecommendationDailyService.process(entities)
        }

    private fun SupplyQuantityRecommendationDailyVal.isDateInitialized() =
        date.year != 0 && date.month != 0 && date.day != 0

    private fun SupplyQuantityRecommendationDailyVal.hasForecast() =
        forecastedDemanded.qty.value.toBigDecimalOrNull()?.compareTo(BigDecimal.ZERO) != 0

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
