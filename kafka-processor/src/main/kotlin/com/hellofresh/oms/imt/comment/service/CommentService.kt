package com.hellofresh.oms.imt.comment.service

import com.hellofresh.oms.imt.comment.repository.CommentRepository
import com.hellofresh.oms.model.Comment
import io.micrometer.core.annotation.Timed
import org.springframework.stereotype.Service

@Service
class CommentService(
    private val commentRepository: CommentRepository,
) {
    @Timed
    fun process(comment: Comment) {
        commentRepository.save(comment)
    }
}
