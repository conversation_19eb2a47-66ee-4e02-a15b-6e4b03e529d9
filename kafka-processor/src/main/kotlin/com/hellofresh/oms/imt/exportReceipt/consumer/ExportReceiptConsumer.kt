package com.hellofresh.oms.imt.exportReceipt.consumer

import com.hellofresh.oms.imt.exportReceipt.service.ExportReceiptService
import java.util.UUID
import java.util.function.Consumer
import org.apache.avro.generic.GenericRecord
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

private const val DELETE_MESSAGE = "d"
private const val OPERATION = "op"
private const val PO_NUMBER = "po_number"
private const val AFTER_SCHEMA = "after"
private const val BEFORE_SCHEMA = "before"
private const val CUT_OFF_YEAR = "25"

@Component
@Suppress("TooGenericExceptionCaught")
class ExportReceiptConsumer(
    private val exportReceiptService: ExportReceiptService
) {

    @Bean
    fun processImtExportReceipt(): Consumer<Message<List<GenericRecord>>> =
        Consumer { messages ->
            val operationsMap = mutableMapOf<UUID, Pair<String, GenericRecord>>()
            messages.payload.forEach { message ->
                parseAndAddRecord(message, operationsMap)
            }
            processBatch(operationsMap)
        }

    private fun parseAndAddRecord(message: GenericRecord?, operationsMap: MutableMap<UUID, Pair<String, GenericRecord>>) {
        if (message == null) {
            logger.debug("Received KafkaNull payload, skipping.")
            return
        }
        try {
            val operationType = message[OPERATION].toString()
            val record = if (operationType == DELETE_MESSAGE) {
                message[BEFORE_SCHEMA] as GenericRecord
            } else {
                message[AFTER_SCHEMA] as GenericRecord
            }
            val recordId = UUID.nameUUIDFromBytes(generateUuidBytes(record))
            val poNumber = record[PO_NUMBER].toString()
            if (!poNumber.startsWith(CUT_OFF_YEAR)) {
                logger.debug("purchase order is before 2025, skipping.")
                return
            }
            operationsMap[recordId] = operationType to record
        } catch (e: Exception) {
            logger.error("Couldn't parse export receipt message: $e", e)
            throw e
        }
    }

    private fun processBatch(operationsMap: Map<UUID, Pair<String, GenericRecord>>) {
        val (toSave, toDelete) = operationsMap.entries
            .partition { it.value.first != DELETE_MESSAGE }
            .let { (saveEntries, deleteEntries) ->
                Pair(
                    saveEntries.map { (_, recordPair) -> recordPair.second.toExportReceipt() },
                    deleteEntries.map { it.key }
                )
            }
        try {
            if (toDelete.isNotEmpty()) {
                logger.debug("Applying batch delete for ${toDelete.size} UUIDs")
                exportReceiptService.delete(toDelete)
            }
            if (toSave.isNotEmpty()) {
                logger.debug("Applying batch insert/update for ${toSave.size} records")
                exportReceiptService.save(toSave)
            }
        } catch (e: Exception) {
            logger.error("Couldn't process batch export receipts, error: $e", e)
            throw e
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
