package com.hellofresh.oms.imt.exportReceipt.repository

import com.hellofresh.oms.model.imt.exportReceipt.ExportReceipt
import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface ExportReceiptRepository : JpaRepository<ExportReceipt, UUID> {

    @Transactional
    @Modifying
    @Query("DELETE FROM ExportReceipt WHERE id IN :ids")
    fun deleteAllById(ids: List<UUID>)
}
