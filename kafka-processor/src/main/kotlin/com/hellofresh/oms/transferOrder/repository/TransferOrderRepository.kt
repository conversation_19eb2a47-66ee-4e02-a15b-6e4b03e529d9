package com.hellofresh.oms.transferOrder.repository

import com.hellofresh.oms.model.transferOrder.TransferOrder
import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TransferOrderRepository : JpaRepository<TransferOrder, UUID> {
    fun findByTransferOrderNumber(transferOrderNumber: String): TransferOrder?
}
