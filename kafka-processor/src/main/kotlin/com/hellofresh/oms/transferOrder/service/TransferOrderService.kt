package com.hellofresh.oms.transferOrder.service

import com.hellofresh.oms.model.transferOrder.TransferOrder
import com.hellofresh.oms.transferOrder.repository.TransferOrderRepository
import io.micrometer.core.annotation.Timed
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class TransferOrderService(
    private val transferOrderRepository: TransferOrderRepository
) {

    @Timed
    fun upsertTransferOrder(transferOrder: TransferOrder): TransferOrder {
        logger.info(
            "Upserting transfer order: transferOrderNumber={}, status={}, sourceDc={}, destinationDc={}",
            transferOrder.transferOrderNumber,
            transferOrder.status,
            transferOrder.sourceDcCode,
            transferOrder.destinationDcCode
        )

        return transferOrderRepository.save(transferOrder)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
