package com.hellofresh.oms.transferOrder.consumer

import com.hellofresh.oms.transferOrder.service.TransferOrderService
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder as ProtoTransferOrder
import java.util.function.Consumer
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

@Component
class TransferOrderConsumer(
    private val transferOrderService: TransferOrderService
) {
    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processTransferOrder(): Consumer<Message<ProtoTransferOrder>> =
        Consumer { message ->
            try {
                val transferOrder = message.payload.toTransferOrder()
                logger.info(
                    "Processing transfer order: transferOrderNumber={}",
                    transferOrder.transferOrderNumber
                )

                transferOrderService.upsertTransferOrder(transferOrder)
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process transfer order due to an unexpected error: [Exception: {}]",
                    e.message,
                    e
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
