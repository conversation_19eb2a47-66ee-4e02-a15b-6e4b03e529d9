package com.hellofresh.oms.client.featureflag

import com.statsig.sdk.Statsig
import com.statsig.sdk.StatsigUser
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class FeatureFlagClient(
    @Value("\${statsig.gates.enable-new-approve-reject}")
    private val enableNewApproveReject: String,
    @Value("\${statsig.gates.enable-po-topic-v2}")
    private val enablePublisherForPoTopicStatsigGate: String,
) {
    fun shouldUseNewApproveRejectFeatureFlag() =
        Statsig.checkGateSync(StatsigUser(APP_USER_ID), enableNewApproveReject)

    fun shouldEnablePublisherForPoTopicFeatureFlag() =
        Statsig.checkGateSync(StatsigUser(APP_USER_ID), enablePublisherForPoTopicStatsigGate)

    companion object {
        private const val APP_USER_ID = "order-management-service"
    }
}
