package com.hellofresh.oms.client.tapioca

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.hellofresh.oms.client.tapioca.domain.ApprovePurchaseOrderRequest
import com.hellofresh.oms.client.tapioca.domain.RejectPurchaseOrderRequest
import com.hellofresh.oms.client.tapioca.exception.TapiocaClientException
import com.hellofresh.oms.client.tapioca.exception.TapiocaConflictException
import io.github.resilience4j.retry.annotation.Retry
import io.micrometer.core.annotation.Timed
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatusCode
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientRequestException
import reactor.core.publisher.Mono

@Component
class TapiocaClient(
    @Qualifier("tapiocaWebClient") private val webClient: WebClient,
    @Value("\${tapioca.po-approve-url}") val poApproveUrl: String,
    @Value("\${tapioca.po-reject-url}") val poRejectUrl: String,
) {
    val jsonMessageKey = "message"

    @Timed
    @Retry(name = "approvePurchaseOrder")
    @Throws(
        TapiocaClientException::class,
        TapiocaConflictException::class,
        WebClientRequestException::class,
    )
    fun approvePurchaseOrder(approvePurchaseOrderRequest: ApprovePurchaseOrderRequest): ResponseEntity<Unit> =
        webClient.put()
            .uri(poApproveUrl, approvePurchaseOrderRequest.purchaseOrderId)
            .retrieve()
            .onStatus(
                { it.isSameCodeAs(HttpStatus.CONFLICT) },
                {
                    it.bodyToMono(JsonNode::class.java).defaultIfEmpty(it.toObjectNodeWithMessage()).flatMap { node ->
                        Mono.error(TapiocaConflictException(node.path(jsonMessageKey).asText()))
                    }
                },
            )
            .onStatus({ it.is4xxClientError || it.is5xxServerError }, ::parseError)
            .toEntity(Unit::class.java)
            .doOnSuccess { logInfoPoWasApproved(approvePurchaseOrderRequest.purchaseOrderId) }
            .block()!!

    @Timed
    @Retry(name = "rejectPurchaseOrder")
    @Throws(
        TapiocaClientException::class,
        TapiocaConflictException::class,
        WebClientRequestException::class,
    )
    fun rejectPurchaseOrder(rejectPurchaseOrderRequest: RejectPurchaseOrderRequest): ResponseEntity<Unit> =
        webClient.put()
            .uri(poRejectUrl, rejectPurchaseOrderRequest.purchaseOrderId)
            .retrieve()
            .onStatus(
                { it.isSameCodeAs(HttpStatus.CONFLICT) },
                {
                    it.bodyToMono(JsonNode::class.java).defaultIfEmpty(it.toObjectNodeWithMessage()).flatMap { node ->
                        Mono.error(TapiocaConflictException(node.path(jsonMessageKey).asText()))
                    }
                },
            )
            .onStatus({ it.is4xxClientError || it.is5xxServerError }, ::parseError)
            .toEntity(Unit::class.java)
            .doOnSuccess { logInfoPoWasRejected(rejectPurchaseOrderRequest.purchaseOrderId) }
            .block()!!

    private fun ClientResponse.toObjectNodeWithMessage() =
        JsonNodeFactory.instance.objectNode().put("message", statusCode().toString())

    private fun parseError(clientResponse: ClientResponse): Mono<Throwable> =
        clientResponse.bodyToMono(JsonNode::class.java)
            .defaultIfEmpty(clientResponse.toObjectNodeWithMessage())
            .doOnNext { logWarningResponse(clientResponse.statusCode().value(), it) }
            .onErrorMap { error ->
                logWarningErrorProcessingResponse(clientResponse.statusCode(), error)
                TapiocaClientException(
                    "Error while processing Tapioca response [status:${
                        clientResponse.statusCode().value()
                    }, body: $error]",
                )
            }
            .flatMap { Mono.error(TapiocaClientException(it.path(jsonMessageKey).asText())) }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)

        private fun logInfoPoWasApproved(poId: UUID) =
            logger.info(
                "Purchase order was successfully approved. [poId=$poId]",
            )

        private fun logInfoPoWasRejected(poId: UUID) =
            logger.info(
                "Purchase order was successfully rejected. [poId=$poId]",
            )

        private fun logWarningResponse(statusCode: Int, jsonNode: JsonNode) =
            logger.warn("Tapioca response received. [status:$statusCode, body:${jsonNode.toPrettyString()}]")

        private fun logWarningErrorProcessingResponse(statusCode: HttpStatusCode, error: Throwable?) {
            logger.warn("Error while processing Tapioca response [status:${statusCode.value()}]", error)
        }
    }
}
