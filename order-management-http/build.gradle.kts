import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.kotlin.jpa)
    alias(libs.plugins.openapi.generator)
    id("jacoco")
    id("com.google.cloud.tools.jib")
    id("gg.jte.gradle") version libs.versions.jte
}

group = "$group.order-management-http"

extra["springCloudVersion"] = libs.versions.springCloud.get()
extra["testcontainersVersion"] = libs.versions.testcontainers.get()

dependencies {
    // spring
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.security:spring-security-oauth2-client")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")

    implementation(libs.springdoc.openapi)
    implementation(libs.jjwt.gson)
    implementation(libs.jjwt.impl)

    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("com.fasterxml.jackson.core:jackson-annotations")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    implementation(project(":model"))

    // JTE for template engine
    implementation(libs.jte.spring.boot.starter)
    implementation(libs.jte)
    implementation(libs.jte.kotlin)

    // PDF Generator
    implementation(libs.openpdf)
    implementation(libs.flying.saucer.core)
    implementation(libs.flying.saucer.pdf)

    // tracing
    implementation("io.micrometer:micrometer-tracing-bridge-otel")
    implementation("io.micrometer:micrometer-registry-prometheus")
    implementation("io.opentelemetry:opentelemetry-exporter-zipkin")
    implementation(libs.datasource.micrometer.spring.boot)

    // Logging
    implementation("org.springframework.boot:spring-boot-starter-log4j2")
    implementation("org.apache.logging.log4j:log4j-layout-template-json")
    implementation(libs.kotlin.csv)

    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")

    implementation("org.springframework.boot:spring-boot-properties-migrator")

    // Feature flags
    implementation(libs.statsig.serversdk)

    // Kafka
    implementation("org.springframework.cloud:spring-cloud-starter")
    implementation("org.springframework.cloud:spring-cloud-stream")
    implementation("org.springframework.cloud:spring-cloud-stream-binder-kafka")
    implementation(libs.hf.schema.registry)

    // Retry
    implementation("org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j")

    // Mail
    implementation(libs.sendgrid)

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    // database
    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("org.flywaydb:flyway-database-postgresql")

    // test
    testImplementation(kotlin("test"))
    testImplementation(project(":db-migration"))
    testImplementation("org.springframework.cloud:spring-cloud-starter-contract-stub-runner")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.boot:spring-boot-starter-aop")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")
    testImplementation("org.testcontainers:kafka")
    testImplementation(libs.mockito.kotlin)
    testImplementation("org.flywaydb:flyway-core")
    testImplementation(libs.squareup.okhttp3.mockwebserver)
    testImplementation(libs.squareup.okhttp3.okhttp)
    testImplementation(libs.hamcrest)
    testImplementation(libs.model.assert)
    testImplementation(libs.zonky.embedded.database)
    testImplementation(libs.jsoup)

    testRuntimeOnly(libs.zonky.embedded.postgres)
}

dependencyManagement {
    imports {
        mavenBom("org.testcontainers:testcontainers-bom:${property("testcontainersVersion")}")
        mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
    }
}

configurations {
    implementation {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
    }
    testImplementation {
        exclude(group = "ch.qos.logback", module = "logback-classic")
    }
}

// generated sources of openapi generate task
val generatedSourcesDir = "${project.layout.buildDirectory.get().asFile}/generated/openapi"
sourceSets.getByName("main").java {
    srcDir("$generatedSourcesDir/src/main/kotlin")
}
sourceSets.getByName("main").resources {
    srcDir("../logging/resources")
}

// Generate all OpenApi specs in the static folder, register a task named openApiGenerateVersioned that depends on all the generated tasks
File("$projectDir/src/main/resources/static").walkTopDown()
    .filter { it.name.endsWith(".yaml") && it.name.startsWith("api") }.toList()
    .filter {
        it.nameWithoutExtension != "api"
    }.map { file -> // skip api.yaml file because that's handled by the main openApiGenerate task below
        tasks.register(
            "openApiGenerate-${file.nameWithoutExtension}",
            GenerateTask::class,
        ) {
            description = "Generates the OpenAPI spec for -${file.nameWithoutExtension}"
            group = JavaBasePlugin.DOCUMENTATION_GROUP
            configureOpenApiGenerateTask(this, file.name)
        }
    }.let {
        tasks.register("openApiGenerateVersioned") {
            description = "Generates the versioned OpenAPI spec"
            group = JavaBasePlugin.DOCUMENTATION_GROUP
            dependsOn(it)
        }
    }

tasks {
    compileKotlin {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_21
        }
    }
    compileTestKotlin {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_21
        }
    }

    test {
        useJUnitPlatform {
            excludeTags("integration")
        }
    }
    openApiGenerate.configure {
        configureOpenApiGenerateTask(this, "api.yaml")
        dependsOn("openApiGenerateVersioned")
    }
    compileKotlin.configure {
        dependsOn("openApiGenerate")
    }

    test.configure {
        systemProperty("spring.profiles.active", "test")
    }
}

task<Test>("integrationTest") {
    description = "Runs integration tests."
    group = "verification"
    shouldRunAfter(tasks.test)
    useJUnitPlatform {
        includeTags("integration")
    }
}

jib {
    from {
        image = project.property("jib.from.image").toString()
    }
    to {
        image = "order-management-http:latest"
    }
    container {
        project.findProperty("jib.container.jvmFlags")?.toString()?.split(' ')?.let {
            jvmFlags = it
        }
    }
}

fun configureOpenApiGenerateTask(
    generateTask: GenerateTask,
    fileName: String,
) {
    val apiName = fileName.replace(".yaml", "").replace("-", ".")
    generateTask.generatorName.set("kotlin-spring")
    generateTask.validateSpec.set(true)
    generateTask.inputSpec.set("$projectDir/src/main/resources/static/$fileName")
    generateTask.outputs.cacheIf { false }
    generateTask.outputs.upToDateWhen { false }
    generateTask.outputDir.set(generatedSourcesDir)
    generateTask.apiPackage.set("com.hellofresh.oms.orderManagement.generated.$apiName.routes")
    generateTask.modelPackage.set("com.hellofresh.oms.orderManagement.generated.$apiName.model")
    generateTask.configOptions.put("interfaceOnly", "true")
    generateTask.configOptions.put("enumPropertyNaming", "UPPERCASE")
    generateTask.configOptions.put("documentationProvider", "none")
    generateTask.configOptions.put("useSpringBoot3", "true")
    generateTask.typeMappings.put("double", "java.math.BigDecimal")
    generateTask.typeMappings.put("java.time.OffsetDateTime", "java.time.LocalDateTime")
    generateTask.additionalProperties.put("useTags", true)
}

buildscript {
    configurations.all {
        resolutionStrategy {
            // Workaround for https://github.com/OpenAPITools/openapi-generator/issues/15876
            force("org.yaml:snakeyaml:2.4")
            // Workaround for https://github.com/OpenAPITools/openapi-generator/issues/18753
            force("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.2")
        }
    }
}

jte {
    generate()
}

tasks.jar {
    dependsOn(tasks.generateJte)
    dependsOn(tasks.precompileJte)
    from(
        fileTree("jte-classes") {
            include("**/*.class")
            include("**/*.bin")
        }
    )
}
