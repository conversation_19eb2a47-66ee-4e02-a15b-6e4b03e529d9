package com.hellofresh.oms.orderManagementHttp.configuration

import java.time.Duration
import java.time.temporal.ChronoUnit
import org.slf4j.LoggerFactory
import org.springframework.boot.actuate.availability.ReadinessStateHealthIndicator
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.availability.ApplicationAvailability
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component("readinessStateHealthIndicator")
class GracefulReadinessStateHealthIndicator(availability: ApplicationAvailability) : ReadinessStateHealthIndicator(
    availability
) {
    private var shuttingDown = false

    @EventListener
    @Suppress("UnusedParameter")
    fun onContextClosedEvent(event: ContextClosedEvent) {
        if (shuttingDown) {
            return
        }

        shuttingDown = true
        try {
            logger.info("Readiness probe set as OUT_OF_SERVICE. Delay before commencing graceful shutdown initiated")
            Thread.sleep(Duration.of(WAIT_TIME, ChronoUnit.SECONDS).toMillis())
        } catch (e: InterruptedException) {
            logger.error("Delay before commencing graceful shutdown interrupted", e)
        }
        logger.info("Delay before commencing graceful shutdown finished")
    }

    override fun getHealth(includeDetails: Boolean): Health =
        if (shuttingDown) Health.outOfService().build() else super.getHealth(includeDetails)

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
        private const val WAIT_TIME = 10L
    }
}
