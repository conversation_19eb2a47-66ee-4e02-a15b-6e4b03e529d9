package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus.DELETED
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.client.tapioca.TapiocaClient
import com.hellofresh.oms.orderManagementHttp.client.tapioca.exception.TapiocaClientException
import com.hellofresh.oms.orderManagementHttp.exception.OrderingToolException
import com.hellofresh.oms.orderManagementHttp.exception.PurchaseOrderNotFoundException
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderEventType
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import io.micrometer.core.annotation.Timed
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class PurchaseOrderDeleteService(
    private val purchaseOrderService: PurchaseOrderService,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val tapiocaClient: TapiocaClient,
    private val purchaseOrderProducer: PurchaseOrderProducer,
) {
    @Timed
    @Transactional
    fun processDeletePurchaseOrder(
        poNumber: String,
        loggedInUser: LoggedInUserInfo
    ) {
        logger.info("Deleting Purchase Order: poNumber = $poNumber")
        val currentPurchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(poNumber)
            ?: throw PurchaseOrderNotFoundException(poNumber)

        try {
            tapiocaClient.deletePurchaseOrder(currentPurchaseOrder.id, loggedInUser.userId)
        } catch (c: TapiocaClientException) {
            logger.info("Failed to delete for poNumber = $poNumber, error = $c")
            throw OrderingToolException.from(c)
        }

        val persistedPurchaseOrder = markPurchaseOrderAsDeleted(currentPurchaseOrder)

        purchaseOrderProducer.publishPurchaseOrderEvent(persistedPurchaseOrder, PurchaseOrderEventType.CANCELLED)

        logger.info("Deleted Purchase Order: poNumber = $poNumber")
    }

    private fun markPurchaseOrderAsDeleted(persistedPurchaseOrder: PurchaseOrder) =
        purchaseOrderService.savePurchaseOrder(
            persistedPurchaseOrder.copy(
                status = DELETED,
            ),
        )

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
