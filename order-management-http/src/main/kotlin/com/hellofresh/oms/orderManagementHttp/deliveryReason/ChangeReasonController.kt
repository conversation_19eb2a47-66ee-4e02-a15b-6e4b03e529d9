package com.hellofresh.oms.orderManagementHttp.deliveryReason

import com.hellofresh.oms.model.ChangeReasonType
import com.hellofresh.oms.orderManagement.generated.api.model.ChangeReasonApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.ChangeReasonListApiResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.ChangeReasonsApi
import org.springframework.http.ResponseEntity
import org.springframework.http.ResponseEntity.ok
import org.springframework.web.bind.annotation.RestController

@RestController
class ChangeReasonController(
    private val service: ChangeReasonService
) : ChangeReasonsApi {

    override fun getChangeReasons(reasonType: String, market: String?): ResponseEntity<ChangeReasonListApiResponse> {
        val changeReasonType = ChangeReasonType.convert(reasonType)
        val reasons = service.findAllBy(changeReasonType, market)
        val apiReasons = reasons.map { ChangeReasonApiResponse(it.id, it.name, it.allowedMarkets) }
        val response = ChangeReasonListApiResponse(reasonType, apiReasons)

        return ok(response)
    }
}
