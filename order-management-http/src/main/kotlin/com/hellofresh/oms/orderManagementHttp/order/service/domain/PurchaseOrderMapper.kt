package com.hellofresh.oms.orderManagementHttp.order.service.domain

import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum as ShipMethodEnumRequest
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.CROSSDOCK
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.FREIGHT_ON_BOARD
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.OTHER
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.orderManagementHttp.order.service.CreatePurchaseOrderRequestDto
import java.time.LocalDateTime
import java.util.UUID

fun ShipMethodEnumRequest.toModel(): ShipMethodEnum = when (this) {
    CROSSDOCK -> ShipMethodEnum.CROSSDOCK
    FREIGHT_ON_BOARD -> ShipMethodEnum.FREIGHT_ON_BOARD
    OTHER -> ShipMethodEnum.OTHER
    VENDOR -> ShipMethodEnum.VENDOR
}

@Suppress("LongParameterList")
fun CreatePurchaseOrderRequestDto.toModel(
    poId: UUID,
    poNumber: String,
    supplier: SupplierExtended,
    emergencyReason: EmergencyReason,
    shippingAddress: ShippingAddress,
    now: LocalDateTime,
): PurchaseOrder {
    val orderItems = mapOrderItems(poId, now)
    val totalPrice = Money(orderItems.sumOf { it.totalPrice.amount }, orderItems.first().totalPrice.currency)

    return PurchaseOrder.createPurchaseOrder(
        poId = poId,
        poNumber = poNumber,
        yearWeek = YearWeek(this.dcWeek),
        userId = this.userId,
        userEmail = this.userEmail,
        supplierId = this.supplierId,
        supplierCode = supplier.code.toString(),
        dcCode = this.dcCode,
        shippingMethod = ShipMethodEnum.valueOf(this.shippingMethod.name),
        shippingAddress = shippingAddress,
        expectedStartTime = this.deliveryWindow.start,
        expectedEndTime = this.deliveryWindow.end,
        emergencyReasonUuid = emergencyReason.uuid,
        totalPrice = totalPrice,
        comment = this.comment,
        currentTime = now,
        orderItems = orderItems,
        origin = this.origin,
    )
}

private fun CreatePurchaseOrderRequestDto.mapOrderItems(
    poId: UUID,
    now: LocalDateTime
) = this.orderItems.map {
    val packagingDomain = PackagingDomain.from(it.packaging)
    OrderItem.createOrderItem(
        poId = poId,
        skuId = it.skuId,
        price = packagingDomain.getPricePerItem().toMoney(),
        buffer = Permyriad.fromPercent(it.bufferPercent),
        packaging = packagingDomain.toPackaging(),
        currentTime = now,
        changeReasonId = null,
        casesPerPallet = packagingDomain.getCasesPerPalletOrNull(),
        totalNumberOfUnits = packagingDomain.getTotalNumberOfUnits(),
        totalPrice = packagingDomain.getTotalPrice().toMoney(),
    )
}.toSet()
