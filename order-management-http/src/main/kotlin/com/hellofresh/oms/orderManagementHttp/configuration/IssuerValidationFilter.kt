package com.hellofresh.oms.orderManagementHttp.configuration

import jakarta.servlet.FilterChain
import jakarta.servlet.ServletException
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import java.io.IOException
import org.slf4j.LoggerFactory
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken
import org.springframework.web.filter.OncePerRequestFilter

class IssuerValidationFilter(
    private val validationPathPrefix: String,
    private val allowedIssuers: List<String>
) : OncePerRequestFilter() {

    override fun shouldNotFilter(request: HttpServletRequest): Boolean =
        !request.requestURI.startsWith(validationPathPrefix)

    @Throws(IOException::class, ServletException::class)
    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        val authentication = SecurityContextHolder.getContext().authentication
        if (authentication is JwtAuthenticationToken) {
            val jwt = authentication.token as Jwt
            val issuer = jwt.claims["iss"] as? String

            if (issuer == null || !allowedIssuers.contains(issuer)) {
                Companion.logger.warn(
                    "Service is not allowed to access this resource. Service id: $issuer, resource: ${request.requestURI}",
                )

                response.sendError(
                    HttpServletResponse.SC_FORBIDDEN,
                    "Service is not allowed to access this resource",
                )

                return
            }
        }

        filterChain.doFilter(request, response)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
