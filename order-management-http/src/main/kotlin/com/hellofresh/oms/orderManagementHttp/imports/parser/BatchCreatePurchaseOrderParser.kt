package com.hellofresh.oms.orderManagementHttp.imports.parser

import com.github.doyaaaaaken.kotlincsv.dsl.csvReader
import com.github.doyaaaaaken.kotlincsv.util.MalformedCSVException
import com.hellofresh.oms.orderManagement.generated.api.model.LineError
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.BUFFER_VALUE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.CASE_SIZE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.CASE_UOM_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.COMMENTS
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.CUSTOM_ORDER_NUMBER_CODE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.DELIVERY_DATE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.DISTRIBUTION_CENTER_VALUE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.END_TIME_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.ORDER_SIZE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.ORDER_UNIT_VALUE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.PRICE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.REASON_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.SHIP_METHOD_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.SKU_VALUE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.START_TIME_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.SUPPLIER_CODE_VALUE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.WEEK_VALUE_KEY
import java.io.InputStream
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.web.context.annotation.RequestScope

@Component
@RequestScope
class BatchCreatePurchaseOrderParser(
    @Value("\${batch-imports.epo-max-lines}") private val maxLines: Int,
) {
    private val errorList: MutableList<LineError> = mutableListOf()

    @Throws(FileHasTooManyLinesException::class, InvalidFileException::class)
    fun parse(fileInputStream: InputStream): BatchCreatePurchaseOrderParserResponse {
        val allLinesMap = readLinesWithHeader(fileInputStream)

        if (allLinesMap.size > maxLines) {
            throw FileHasTooManyLinesException(maxLines)
        }

        if (allLinesMap.isEmpty()) {
            throw InvalidFileException()
        }

        val batchDTOs = allLinesMap
            .mapIndexed(toBulkOrderRowDto())

        return BatchCreatePurchaseOrderParserResponse(batchDTOs, errorList)
    }

    private fun readLinesWithHeader(fileInputStream: InputStream) = try {
        csvReader()
            .readAllWithHeader(fileInputStream)
            .filterEmptyRows()
    } catch (e: MalformedCSVException) {
        logger.error("Unable to parse batch import file", e)
        throw InvalidFileException()
    }

    private fun toBulkOrderRowDto() = { index: Int, row: Map<String, String> ->
        val rowNumber = index + HEADER_SIZE + 1
        BatchOrderRowDto(
            rowNumber = rowNumber,
            weekValue = row.getRequired(WEEK_VALUE_KEY, rowNumber),
            distributionCenterValue = row.getRequired(DISTRIBUTION_CENTER_VALUE_KEY, rowNumber),
            customOrderNumberCode = validateCustomerOrderNumberCode(row, rowNumber),
            reason = row.getRequired(REASON_KEY, rowNumber),
            supplierCodeValue = row.getRequired(SUPPLIER_CODE_VALUE_KEY, rowNumber),
            deliveryDate = row.getRequired(DELIVERY_DATE_KEY, rowNumber),
            startTime = row.getRequired(START_TIME_KEY, rowNumber),
            endTime = row.getRequired(END_TIME_KEY, rowNumber),
            skuValue = row.getRequired(SKU_VALUE_KEY, rowNumber),
            orderSize = row.getRequired(ORDER_SIZE_KEY, rowNumber),
            orderUnitValue = row.getRequired(ORDER_UNIT_VALUE_KEY, rowNumber),
            bufferValue = row.getRequired(BUFFER_VALUE_KEY, rowNumber),
            caseSize = row[CASE_SIZE_KEY.headerName].orEmpty(),
            caseUom = row.getRequired(CASE_UOM_KEY, rowNumber),
            price = row.getRequired(PRICE_KEY, rowNumber),
            shipMethod = row.getRequired(SHIP_METHOD_KEY, rowNumber),
            comments = row[COMMENTS.headerName].orEmpty(),
        )
    }

    private fun validateCustomerOrderNumberCode(row: Map<String, String>, rowNumber: Int): String {
        val customOrderNumberCode = row[CUSTOM_ORDER_NUMBER_CODE_KEY.headerName].orEmpty()
        if (customOrderNumberCode.isNotBlank()) {
            errorList.add(
                LineError(
                    rowNumber,
                    """"${CUSTOM_ORDER_NUMBER_CODE_KEY.headerName}" is not supported at the moment.
                | Please use the "PO Editor" for modifying a purchase order
                    """.trimMargin(),
                ),
            )
        }
        return customOrderNumberCode
    }

    // this function removes rows where all values are empty
    private fun List<Map<String, String>>.filterEmptyRows() =
        this.filter { it.entries.any { entry -> entry.value.isNotBlank() } }

    private fun Map<String, String>.getRequired(key: BatchOrderHeader, rowNumber: Int) =
        this[key.headerName].orEmpty().ifBlank {
            errorList.add(LineError(rowNumber, """value for "${key.headerName}" is required"""))
            EMPTY_STRING
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
        private const val HEADER_SIZE = 1
        private const val EMPTY_STRING = ""
    }
}

enum class BatchOrderHeader(val headerName: String) {
    WEEK_VALUE_KEY("week.value"),
    DISTRIBUTION_CENTER_VALUE_KEY("distributionCenter.value"),
    CUSTOM_ORDER_NUMBER_CODE_KEY("customOrderNumberCode"),
    REASON_KEY("reason"),
    SUPPLIER_CODE_VALUE_KEY("supplierCode.value"),
    DELIVERY_DATE_KEY("deliveryDate"),
    START_TIME_KEY("startTime"),
    END_TIME_KEY("endTime"),
    SKU_VALUE_KEY("sku.value"),
    ORDER_SIZE_KEY("orderSize"),
    ORDER_UNIT_VALUE_KEY("orderUnit.value"),
    BUFFER_VALUE_KEY("buffer.value"),
    CASE_SIZE_KEY("case.size"),
    CASE_UOM_KEY("case.uom"),
    PRICE_KEY("price"),
    SHIP_METHOD_KEY("shipMethod"),
    COMMENTS("comments"),
}

data class BatchCreatePurchaseOrderParserResponse(
    val parsedLines: List<BatchOrderRowDto>,
    val lineErrors: List<LineError>,
)

class FileHasTooManyLinesException(sizeLimit: Int) :
    RuntimeException("File has too many lines. The limit is $sizeLimit")

class InvalidFileException :
    RuntimeException("Import file is not correctly formatted. Use to the existing template")
