package com.hellofresh.oms.orderManagementHttp.distributionCenters

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface DistributionCentersRepository : JpaRepository<DistributionCenter, String> {
    fun findAllByMarketAndStatusAndIsVisibleOrderByName(
        market: String,
        status: DistributionCenterStatus,
        isVisible: Boolean,
    ): List<DistributionCenter>

    fun findByCode(dcCode: String): DistributionCenter?
}
