@file:Suppress("TooManyFunctions")

package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.POType.EMERGENCY
import com.hellofresh.oms.model.POType.PREORDER
import com.hellofresh.oms.model.POType.STANDARD
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.PurchaseOrderStatus.APPROVED
import com.hellofresh.oms.model.PurchaseOrderStatus.DELETED
import com.hellofresh.oms.model.PurchaseOrderStatus.INITIATED
import com.hellofresh.oms.model.PurchaseOrderStatus.REJECTED
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.UOM.GAL
import com.hellofresh.oms.model.UOM.KG
import com.hellofresh.oms.model.UOM.L
import com.hellofresh.oms.model.UOM.LBS
import com.hellofresh.oms.model.UOM.OZ
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.grn.Grn
import com.hellofresh.oms.model.grn.GrnStateEnum
import com.hellofresh.oms.model.grn.GrnStateEnum.STATE_CLOSE
import com.hellofresh.oms.model.grn.GrnStateEnum.STATE_OPEN
import com.hellofresh.oms.model.grn.GrnStateEnum.STATE_UNSPECIFIED
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.model.supplier.SupplierStatus
import com.hellofresh.oms.orderManagement.generated.api.model.CasePackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryAddressDto
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowResponseDto
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersResponsePageResult
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.OrderItemResponse
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PalletPackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusEnum.CLOSED
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusEnum.OPENED
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusEnum.UNSPECIFIED
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderResponse
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderSendStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderSendStatusEnum.SENT
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum
import com.hellofresh.oms.orderManagement.generated.api.model.SkuResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierAddressDto
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.UnitPackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomain
import com.hellofresh.oms.orderManagementHttp.order.service.domain.fromSortOrder
import com.hellofresh.oms.util.Calculator
import java.math.RoundingMode
import kotlin.reflect.KClass
import org.springframework.data.domain.Page

fun YearWeek.toDto(): String = this.toString()

fun POType.toDto(): PurchaseOrderTypeEnum = when (this) {
    STANDARD -> PurchaseOrderTypeEnum.STANDARD
    EMERGENCY -> PurchaseOrderTypeEnum.EMERGENCY
    PREORDER -> PurchaseOrderTypeEnum.PREORDER
}

fun PurchaseOrderStatus.toDto(): PurchaseOrderStatusEnum = when (this) {
    INITIATED -> PurchaseOrderStatusEnum.INITIATED
    APPROVED -> PurchaseOrderStatusEnum.APPROVED
    REJECTED -> PurchaseOrderStatusEnum.REJECTED
    DELETED -> PurchaseOrderStatusEnum.DELETED
}

fun ShippingAddress.toDto() = DeliveryAddressDto(
    address = this.streetAddress,
    city = this.city,
    countryCode = this.countryCode,
    postalCode = this.postalCode,
    region = this.region,
    locationName = this.locationName,
)

fun KClass<ListPurchaseOrdersResponse>.from(
    purchaseOrderPage: Page<PurchaseOrder>,
    suppliers: List<SupplierExtended>,
    outboxItems: List<OutboxItem>,
    grns: List<Grn>,
): ListPurchaseOrdersResponse = ListPurchaseOrdersResponse(
    purchaseOrders = purchaseOrderPage.content.map {
        PurchaseOrderResponse::class.from(
            it,
            suppliers.find { sup -> sup.id == it.supplierId },
            outboxItems.find { outboxItem -> outboxItem.poId == it.id },
            grns.find { grn -> grn.poNumber == it.poNumber },
        )
    },
    pageResult = ListPurchaseOrdersResponsePageResult(
        number = purchaseOrderPage.number,
        pageSize = purchaseOrderPage.size,
        totalElements = purchaseOrderPage.totalElements.toInt(),
        totalPages = purchaseOrderPage.totalPages,
        sort = purchaseOrderPage.sort.get().toList().map { ListPurchaseOrdersSortEnum::class.fromSortOrder(it) },
    ),
)

fun KClass<PurchaseOrderResponse>.from(
    purchaseOrder: PurchaseOrder,
    supplier: SupplierExtended?,
    outboxItem: OutboxItem?,
    grn: Grn?
): PurchaseOrderResponse = PurchaseOrderResponse(
    countryCode = purchaseOrder.shippingAddress.countryCode,
    createdAt = purchaseOrder.createdAt,
    dcCode = purchaseOrder.dcCode,
    dcWeek = purchaseOrder.yearWeek.value,
    deliveryAddress = purchaseOrder.shippingAddress.toDto(),
    deliveryWindow = DeliveryWindowResponseDto(
        start = purchaseOrder.expectedStartTime,
        end = purchaseOrder.expectedEndTime,
        changeReasonId = purchaseOrder.deliveryDateChangeReasonId,
    ),
    emergencyReasonId = purchaseOrder.emergencyReasonUuid,
    id = purchaseOrder.id,
    isSynced = purchaseOrder.isSynced,
    sendStatus = if (purchaseOrder.sendTime != null) SENT else outboxItem?.status.toDto(),
    poNumber = purchaseOrder.poNumber,
    poType = purchaseOrder.type.toDto(),
    sendTime = purchaseOrder.sendTime,
    shippingMethod = ShipMethodEnum.valueOf(
        purchaseOrder.shippingMethod.name,
    ),
    status = purchaseOrder.status.toDto(),
    supplierId = purchaseOrder.supplierId,
    updatedAt = purchaseOrder.updatedAt,
    userEmail = purchaseOrder.userEmail,
    version = purchaseOrder.version,
    comment = purchaseOrder.comment.orEmpty(),
    supplier = supplier?.let { SupplierResponse::class.from(it) },
    grnStatus = grn?.state.toDto(),
)

private fun OutboxItemStatus?.toDto(): PurchaseOrderSendStatusEnum = when (this) {
    OutboxItemStatus.SENT -> PurchaseOrderSendStatusEnum.SENT
    OutboxItemStatus.FAILED -> PurchaseOrderSendStatusEnum.FAILED
    OutboxItemStatus.PENDING -> PurchaseOrderSendStatusEnum.PENDING
    else -> PurchaseOrderSendStatusEnum.NOT_SENT
}

private fun GrnStateEnum?.toDto(): PurchaseOrderGrnStatusEnum? = when (this) {
    STATE_OPEN -> OPENED
    STATE_CLOSE -> CLOSED
    STATE_UNSPECIFIED -> UNSPECIFIED
    else -> null
}

fun KClass<SupplierResponse>.from(supplier: SupplierExtended): SupplierResponse = SupplierResponse(
    id = supplier.id,
    code = supplier.code,
    address = SupplierAddressDto::class.from(supplier.supplierAddress),
    currency = supplier.currency,
    market = supplier.market,
    name = supplier.name,
    parentId = supplier.parentId,
    status = supplier.status.toApi(),
    type = supplier.type,
    createdAt = supplier.createdAt,
    updatedAt = supplier.updatedAt,
)

private fun SupplierStatus.toApi(): SupplierStatusEnum = when (this) {
    SupplierStatus.ONBOARDING -> SupplierStatusEnum.ONBOARDING
    SupplierStatus.ACTIVE -> SupplierStatusEnum.ACTIVE
    SupplierStatus.INACTIVE -> SupplierStatusEnum.INACTIVE
    SupplierStatus.ARCHIVED -> SupplierStatusEnum.ARCHIVED
    SupplierStatus.OFFBOARDING -> SupplierStatusEnum.OFFBOARDING
}

private fun KClass<SupplierAddressDto>.from(address: SupplierAddress): SupplierAddressDto = SupplierAddressDto(
    address = address.address,
    city = address.city,
    countryCode = address.country,
    region = address.state,
    postalCode = address.postCode,
    number = address.number,
)

public fun KClass<OrderItemResponse>.from(orderItem: OrderItem, sku: Sku): OrderItemResponse =
    OrderItemResponse(
        id = orderItem.id,
        skuId = orderItem.skuId,
        sku = SkuResponse::class.from(sku),
        bufferPercent = orderItem.buffer.toPercent(),
        correctionReason = orderItem.correctionReason,
        createdAt = orderItem.createdAt,
        updatedAt = orderItem.updatedAt,
        packaging = PackagingRequest::class.from(orderItem),
    )

private fun KClass<SkuResponse>.from(sku: Sku): SkuResponse = SkuResponse(
    id = sku.uuid,
    name = sku.name,
    market = sku.market,
    code = sku.code,
    status = sku.status.name,
    uom = sku.uom?.toDto(),
    category = sku.category,
    brands = sku.brands,
)

private fun UOM.toDto(): UomEnum = when (this) {
    UNIT -> UomEnum.UNIT
    KG -> UomEnum.KG
    L -> UomEnum.L
    LBS -> UomEnum.LBS
    GAL -> UomEnum.GAL
    OZ -> UomEnum.OZ
}

private fun KClass<PackagingRequest>.from(orderItem: OrderItem): PackagingRequest =
    when (orderItem.packaging.packagingType) {
        PackagingType.CASE_TYPE -> CasePackagingRequest(
            packagingType = PackagingTypeEnum.CASE,
            numberOfCases = Calculator.calculateNumberCases(
                orderItem.totalQty,
                orderItem.packaging.caseSize!!,
            ),
            unitsPerCase = orderItem.packaging.caseSize!!,
            pricePerCase = orderItem.price.toDto(),
            uom = orderItem.packaging.unitOfMeasure.toDto(),
        )

        PackagingType.PALLET_TYPE -> PalletPackagingRequest(
            packagingType = PackagingTypeEnum.PALLET,
            numberOfPallets = Calculator.calculateNumberOfPallets(
                orderItem.totalQty,
                orderItem.packaging.caseSize!!,
                orderItem.casesPerPallet!!.toBigDecimal(),
            ),
            casesPerPallet = orderItem.casesPerPallet!!,
            unitsPerCase = orderItem.packaging.caseSize!!,
            pricePerCase = orderItem.price.toDto(),
            uom = orderItem.packaging.unitOfMeasure.toDto(),
        )

        PackagingType.UNIT_TYPE -> UnitPackagingRequest(
            packagingType = PackagingTypeEnum.UNIT,
            numberOfUnits = orderItem.totalQty.toInt(),
            pricePerUnit = orderItem.price.toDto(),
        )
    }

fun Money.toDto(): MoneyDto = MoneyDto(
    amount = this.amount.setScale(MoneyDomain.PRECISION.value, RoundingMode.HALF_EVEN).toPlainString(),
    currency = this.currency,
)
