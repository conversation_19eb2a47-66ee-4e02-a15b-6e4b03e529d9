package com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.intake

import com.hellofresh.oms.orderManagement.generated.api.model.GrnResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.GoodsReceivedNotesApi
import com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.service.GoodsReceivedNoteService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class GoodsReceivedNoteController(
    private val goodsReceivedNoteService: GoodsReceivedNoteService,
    private val goodsReceivedNoteResponseConverter: GoodsReceivedNoteResponseConverter,
) : GoodsReceivedNotesApi {
    override fun getGrn(poNumber: String): ResponseEntity<GrnResponse> {
        val grn = goodsReceivedNoteService.getGoodsReceivedNote(poNumber)
            ?: return ResponseEntity.notFound().build()

        return ResponseEntity.ok(
            goodsReceivedNoteResponseConverter.convert(grn),
        )
    }
}
