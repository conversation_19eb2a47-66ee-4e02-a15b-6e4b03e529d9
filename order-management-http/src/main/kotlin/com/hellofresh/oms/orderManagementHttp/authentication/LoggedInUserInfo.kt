package com.hellofresh.oms.orderManagementHttp.authentication

import java.util.UUID
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt

data class LoggedInUserInfo(
    val userId: UUID,
    val userEmail: String,
    val userName: String?,
    val roles: List<String>?,
    val issuer: String?,
)

fun getIssuer(): String {
    val jwt = SecurityContextHolder.getContext().authentication.principal as Jwt
    require(jwt.hasClaim("iss")) { "iss claim must be present in jwt" }
    return jwt.getClaimAsString("iss")
}

fun getLoggedInUser(): LoggedInUserInfo {
    val jwt = SecurityContextHolder.getContext().authentication.principal as Jwt

    require(jwt.hasClaim("sub")) { "sub claim must be present in jwt" }
    require(jwt.hasClaim("email")) { "email claim must be present in jwt" }

    return LoggedInUserInfo(
        Result.runCatching { UUID.fromString(jwt.subject) } // Google Oauth
            .recoverCatching { UUID.fromString(jwt.getClaimAsString("oid")) } // Azure Oauth
            .getOrThrow(),
        jwt.getClaimAsString("email"),
        jwt.getClaimAsMap("metadata")?.get("name")?.toString(),
        jwt.getClaimAsStringList("roleclaim"),
        jwt.getClaimAsString("iss"),
    )
}

// This map is a conversion between azure roles to internal market codes
private val roleToMarketMap = mapOf(
    "purchasing.au.all.manager" to "au",
    "purchasing.ca.all.manager" to "ca",
    "purchasing.de.all.manager" to "dach",
    "purchasing.es.all.manager" to "es",
    "purchasing.eu.all.manager" to "eu",
    "purchasing.fr.all.manager" to "fr",
    "purchasing.ie.all.manager" to "ie",
    "purchasing.it.all.manager" to "it",
    "purchasing.nl.all.manager" to "beneluxfr",
    "purchasing.se.all.manager" to "dkse",
    "purchasing.us.all.manager" to "us",
    "purchasing.nz.all.manager" to "nz",
    "purchasing.gb.all.manager" to "gb",
)

fun LoggedInUserInfo.getAllowedMarkets(): List<String> =
    roles?.mapNotNull { roleToMarketMap[it] } ?: emptyList()
