package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.getBillingAddress
import com.hellofresh.oms.orderManagementHttp.configuration.PdfConfigurationProperties
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.order.service.domain.PurchaseOrderForPdfTemplate
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import io.micrometer.core.annotation.Timed
import java.util.Locale
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class PurchaseOrderReportService(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val supplierRepository: SupplierRepository,
    private val distributionCentersRepository: DistributionCentersRepository,
    private val pdfService: PdfService,
    private val pdfConfigurationProperties: PdfConfigurationProperties,
    private val skuRepository: SkuRepository,
) {

    @Timed
    fun generatePurchaseOrderPdf(poId: String): Pair<ByteArray, String> {
        logger.info("Generating PDF for Purchase Order: poNumber = $poId")

        val purchaseOrder = purchaseOrderRepository.findById(UUID.fromString(poId)).orElseThrow {
            IllegalArgumentException("Purchase Order not found. ID: $poId")
        }

        val supplier = supplierRepository.findById(purchaseOrder.supplierId).orElseThrow {
            IllegalArgumentException("Supplier not found. ID: ${purchaseOrder.supplierId}")
        }

        val dcBillingAddress = distributionCentersRepository.findByCode(purchaseOrder.dcCode)?.getBillingAddress()
            ?: throw IllegalArgumentException("Billing address not found for DC. Code: ${purchaseOrder.dcCode}")
        val locale = Locale.forLanguageTag(pdfConfigurationProperties.localePerMarket[supplier.market] ?: "en-UK")

        val disclaimers = pdfConfigurationProperties.disclaimerPerMarket[supplier.market] ?: emptyList()

        val skus = skuRepository.findAllById(purchaseOrder.orderItems.map { it.skuId })

        val pdfPurchaseOrder = PurchaseOrderForPdfTemplate.from(
            po = purchaseOrder,
            supplier = supplier,
            dcBillingAddress = dcBillingAddress,
            disclaimers = disclaimers,
            skus = skus,
            locale = locale
        )

        val htmlContent = pdfService.renderHtmlContent(pdfPurchaseOrder)
        val pdfBytes = pdfService.generatePdfFromHtml(htmlContent)
        val filename = "purchase-order-${pdfPurchaseOrder.poNumber}"

        return Pair(pdfBytes, filename)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
