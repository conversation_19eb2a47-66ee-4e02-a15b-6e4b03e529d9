package com.hellofresh.oms.orderManagementHttp.supplier

import com.hellofresh.oms.model.supplier.SupplierExtended
import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

@Repository
interface SupplierRepository : JpaRepository<SupplierExtended, UUID>, JpaSpecificationExecutor<SupplierExtended>
