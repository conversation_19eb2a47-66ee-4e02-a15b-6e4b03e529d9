package com.hellofresh.oms.orderManagementHttp.icsTicket.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime

data class IcsTicketDto(
    val id: Int,
    val subject: String?,
    val priority: Int?,
    val status: Int?,
    val type: String?,
    @JsonProperty("created_at")
    val createdAt: LocalDateTime,
    @JsonProperty("updated_at")
    val updatedAt: LocalDateTime,
    @JsonProperty("custom_fields")
    val customFields: IcsTicketCustomFields,
)

data class IcsTicketCustomFields(
    val week: String?,
    val site: String?,
    @JsonProperty("sku_code")
    val skuCode: String?,
    @JsonProperty("ticket_link")
    val ticketLink: String?,
    @JsonProperty("request_type")
    val requestType: String?,
    @JsonProperty("production_impact")
    val productionImpact: String?,
    val po: String?,
)

data class IcsTicketsDto(
    val tickets: List<IcsTicketDto>
)
