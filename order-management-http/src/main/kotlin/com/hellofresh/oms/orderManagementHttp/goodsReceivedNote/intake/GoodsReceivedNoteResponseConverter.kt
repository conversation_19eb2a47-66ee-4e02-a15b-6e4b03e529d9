package com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.intake

import com.hellofresh.oms.model.grn.Grn
import com.hellofresh.oms.model.grn.GrnStateEnum
import com.hellofresh.oms.model.grn.UnitOfMeasureGrnEnum
import com.hellofresh.oms.orderManagement.generated.api.model.GrnDeliveryLineDto
import com.hellofresh.oms.orderManagement.generated.api.model.GrnDeliveryLineUnitOfMeasureEnum
import com.hellofresh.oms.orderManagement.generated.api.model.GrnResponse
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusEnum
import com.hellofresh.oms.orderManagementHttp.sku.SkuService
import java.math.BigDecimal
import org.springframework.core.convert.converter.Converter
import org.springframework.stereotype.Component

@Component
class GoodsReceivedNoteResponseConverter(
    private val skuService: SkuService,
) : Converter<Grn, GrnResponse> {
    override fun convert(grn: Grn): GrnResponse {
        val distinctSkuCodes = grn.deliveries.flatMap { it.lines }.mapNotNull { it.skuCode }.toSet()
        val skus = skuService.getSkusByPoNumberAndCodes(grn.poNumber, distinctSkuCodes)
        return GrnResponse(
            id = grn.id,
            status = grn.state.toDto(),
            summarizedDeliveryLines = grn.deliveries
                .flatMap { it.lines }
                .groupBy { it.skuCode }
                .map {
                    val sku = skus.find { sku -> sku.code == it.key }
                    val receivedQuantity = it.value
                        .sumOf { line ->
                            line.palletizedQuantity?.toBigDecimal()
                                ?: BigDecimal.ZERO
                        }
                    GrnDeliveryLineDto(
                        skuCode = it.key,
                        skuName = sku?.name,
                        receivedQuantity = receivedQuantity,
                        skuUom = it.value.first().skuUom.toDto(),
                    )
                },
        )
    }

    private fun UnitOfMeasureGrnEnum.toDto(): GrnDeliveryLineUnitOfMeasureEnum = when (this) {
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_KG -> GrnDeliveryLineUnitOfMeasureEnum.KG
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_UNIT -> GrnDeliveryLineUnitOfMeasureEnum.UNIT
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_LBS -> GrnDeliveryLineUnitOfMeasureEnum.LBS
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_UNSPECIFIED -> GrnDeliveryLineUnitOfMeasureEnum.UNSPECIFIED
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_OZ -> GrnDeliveryLineUnitOfMeasureEnum.OZ
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_GAL -> GrnDeliveryLineUnitOfMeasureEnum.GAL
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_LITRE -> GrnDeliveryLineUnitOfMeasureEnum.LITRE
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_CASE -> GrnDeliveryLineUnitOfMeasureEnum.CASE
        UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_OTHER -> GrnDeliveryLineUnitOfMeasureEnum.OTHER
    }

    private fun GrnStateEnum.toDto() = when (this) {
        GrnStateEnum.STATE_OPEN -> PurchaseOrderGrnStatusEnum.OPENED
        GrnStateEnum.STATE_CLOSE -> PurchaseOrderGrnStatusEnum.CLOSED
        GrnStateEnum.STATE_UNSPECIFIED -> PurchaseOrderGrnStatusEnum.UNSPECIFIED
    }
}
