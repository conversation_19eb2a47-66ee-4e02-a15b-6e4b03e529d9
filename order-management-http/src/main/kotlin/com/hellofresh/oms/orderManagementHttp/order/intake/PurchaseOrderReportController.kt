package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.orderManagement.generated.api.routes.PurchaseOrderReportApi
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderReportService
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class PurchaseOrderReportController(
    private val purchaseOrderReportService: PurchaseOrderReportService,
) : PurchaseOrderReportApi {
    override fun generatePdfForPurchaseOrder(poNumber: String, poUuid: String): ResponseEntity<ByteArray> {
        val (pdf, filename) = purchaseOrderReportService.generatePurchaseOrderPdf(poUuid)

        val headers = HttpHeaders().apply {
            add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"$filename.pdf\"")
            add(HttpHeaders.CONTENT_TYPE, "application/pdf")
        }

        return ResponseEntity(pdf, headers, HttpStatus.OK)
    }
}
