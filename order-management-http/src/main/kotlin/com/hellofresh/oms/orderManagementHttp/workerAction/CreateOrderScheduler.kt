package com.hellofresh.oms.orderManagementHttp.workerAction

import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class CreateOrderScheduler(
    private val workerActionRepository: WorkerActionRepository,
) {
    fun queueCreateOrder(
        purchaseOrder: PurchaseOrder
    ) {
        workerActionRepository.save(
            WorkerAction.createWorkerAction(
                WorkerActionData.CreatePurchaseOrder(
                    purchaseOrderId = purchaseOrder.id,
                ),
                userEmail = purchaseOrder.userEmail,
                userId = purchaseOrder.userId,
            ),
        )
        logger.info(
            "CREATE_ORDER worker action scheduled. [poNumber={}, queueId={}]",
            purchaseOrder.poNumber,
            purchaseOrder.id,
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
