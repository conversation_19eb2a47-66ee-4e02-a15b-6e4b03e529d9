package com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.orderManagement.generated.api.model.EditOrderItemRequest
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import com.hellofresh.oms.orderManagementHttp.order.service.domain.PackagingDomain
import com.hellofresh.oms.util.Calculator
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class OrderItemsField internal constructor(
    private val domainOrderItems: Set<OrderItemDomain>,
    val totalPrice: Money,
    private val changedDomainOrderItems: Set<OrderItemDomain>
) : EditableField {

    fun getSkuIds() = this.domainOrderItems.map { it.skuId }

    fun getChangeReasonIdsForChangedItems(): List<UUID?> = this.changedDomainOrderItems.map { it.changeReasonId }

    fun getUomsForChangedItems(): List<UOM> = this.changedDomainOrderItems.map { it.packaging.unitOfMeasure }

    fun toOrderItemEntities(
        poId: UUID,
        currentTime: LocalDateTime,
    ): Set<OrderItem> = this.domainOrderItems.map {
        OrderItem.createOrderItem(
            poId = poId,
            skuId = it.skuId,
            price = it.price,
            buffer = it.buffer,
            changeReasonId = it.changeReasonId,
            packaging = it.packaging,
            currentTime = currentTime,
            totalCaseQuantity = it.totalCaseQuantity,
            totalPrice = it.totalPrice,
            casesPerPallet = it.casesPerPallet
        )
    }.toSet()

    fun isEqual(totalPrice: Money, orderItems: Set<OrderItem>): Boolean {
        val sortedOrderItemDomain = this.domainOrderItems.sortedBy { it.skuId }
        val sortedOrderItemEntities = orderItems.sortedBy { it.skuId }

        val sameSize = this.domainOrderItems.size == orderItems.size
        val sameContent = sortedOrderItemDomain.zip(sortedOrderItemEntities).all { (itemDomain, persistedItem) ->
            areDomainAndEntityEqual(itemDomain, persistedItem)
        }
        return this.totalPrice == totalPrice && sameSize && sameContent
    }

    fun getPricesForChangedItems(): List<Money> = this.changedDomainOrderItems.map { it.price }

    companion object {

        private fun areDomainAndEntityEqual(domainOrderItem: OrderItemDomain, orderItem: OrderItem): Boolean =
            Calculator.areEqual(domainOrderItem.totalQty, orderItem.totalQty) &&
                domainOrderItem.skuId == orderItem.skuId &&
                domainOrderItem.price == orderItem.price &&
                domainOrderItem.totalPrice == orderItem.totalPrice &&
                domainOrderItem.buffer == orderItem.buffer &&
                domainOrderItem.packaging == orderItem.packaging &&
                domainOrderItem.changeReasonId == orderItem.changeReasonId

        private fun getChangedOrderItems(
            domainOrderItems: Set<OrderItemDomain>,
            persistedOrderItems: Set<OrderItem>
        ): Set<OrderItemDomain> =
            domainOrderItems.filter { domainItem ->
                val persistedItem = persistedOrderItems.find { it.skuId == domainItem.skuId }
                persistedItem == null || !areDomainAndEntityEqual(domainItem, persistedItem)
            }.toSet()

        fun from(
            editPurchaseOrderRequest: EditPurchaseOrderRequest,
            persistedPurchaseOrder: PurchaseOrder,
        ): OrderItemsField? {
            if (editPurchaseOrderRequest.orderItems.isNullOrEmpty()) {
                return null
            }
            val domainOrderItems = editPurchaseOrderRequest.orderItems.map { orderItem ->
                OrderItemDomain.from(orderItem)
            }.toSet()
            val totalPrice = Money(
                domainOrderItems.sumOf { it.totalPrice.amount },
                domainOrderItems.first().totalPrice.currency
            )

            return OrderItemsField(
                domainOrderItems = domainOrderItems,
                totalPrice = totalPrice,
                changedDomainOrderItems = getChangedOrderItems(
                    domainOrderItems,
                    persistedPurchaseOrder.orderItems,
                ),
            )
        }
    }

    internal data class OrderItemDomain(
        val totalQty: BigDecimal,
        val skuId: UUID,
        val price: Money,
        val totalPrice: Money,
        val buffer: Permyriad,
        val changeReasonId: UUID?,
        val packaging: Packaging,
        val totalCaseQuantity: Int,
        val casesPerPallet: Int?
    ) {
        companion object {
            fun from(
                orderItem: EditOrderItemRequest
            ): OrderItemDomain {
                val packagingDomain = PackagingDomain.from(orderItem.packaging)
                return OrderItemDomain(
                    totalQty = packagingDomain.getTotalNumberOfUnits(),
                    skuId = orderItem.skuId,
                    price = packagingDomain.getPricePerItem().toMoney(),
                    totalPrice = packagingDomain.getTotalPrice().toMoney(),
                    buffer = Permyriad.fromPercent(orderItem.bufferPercent),
                    changeReasonId = orderItem.changeReasonId,
                    packaging = packagingDomain.toPackaging(),
                    totalCaseQuantity = packagingDomain.getNumberOfItemsToPurchase(),
                    casesPerPallet = packagingDomain.getCasesPerPalletOrNull()
                )
            }
        }
    }
}
