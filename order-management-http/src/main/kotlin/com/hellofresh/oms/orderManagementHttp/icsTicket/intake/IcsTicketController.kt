package com.hellofresh.oms.orderManagementHttp.icsTicket.intake

import com.hellofresh.oms.orderManagement.generated.api.model.IcsTicketResponseInner
import com.hellofresh.oms.orderManagement.generated.api.routes.ICSTicketsApi
import com.hellofresh.oms.orderManagementHttp.icsTicket.service.IcsTicketService
import com.hellofresh.oms.orderManagementHttp.icsTicket.service.toResponse
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class IcsTicketController(
    private val icsTicketService: IcsTicketService,
) : ICSTicketsApi {

    override fun getIcsTickets(
        poNumber: String,
    ): ResponseEntity<List<IcsTicketResponseInner>> =
        ResponseEntity.ok(
            icsTicketService.getIcsTickets(poNumber).map { it.toResponse() }
        )
}
