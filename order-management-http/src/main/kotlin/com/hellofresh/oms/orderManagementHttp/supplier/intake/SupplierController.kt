package com.hellofresh.oms.orderManagementHttp.supplier.intake

import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShippingMethodResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierListApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierShippingMethodsApiResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.SupplierApi
import com.hellofresh.oms.orderManagementHttp.supplier.service.SupplierService
import java.util.UUID
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class SupplierController(
    private val supplierService: SupplierService
) : SupplierApi {
    override fun getSuppliers(
        market: String,
        page: Int,
        size: Int,
        search: String?,
        sort: ListSuppliersSortEnum?
    ): ResponseEntity<SupplierListApiResponse> {
        val suppliers = supplierService.getSuppliers(
            market = market,
            search = search,
            pageNumber = page,
            pageSize = size,
            sort = sort,
        )
        return ResponseEntity.ok(SupplierListApiResponse::class.from(suppliers))
    }

    override fun getSupplierShippingMethods(supplierId: UUID): ResponseEntity<SupplierShippingMethodsApiResponse> {
        val supplierShipMethods = supplierService.getSupplierShippingMethods(supplierId)
        val entities = supplierShipMethods.map { supplierShippingMethodDto ->
            ShippingMethodResponse::class.from(supplierShippingMethodDto)
        }
        val defaultShippingMethod = ShipMethodEnum.VENDOR

        return ResponseEntity.ok(SupplierShippingMethodsApiResponse(entities, defaultShippingMethod))
    }
}
