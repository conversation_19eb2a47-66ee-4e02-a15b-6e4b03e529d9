package com.hellofresh.oms.orderManagementHttp.exception

import com.hellofresh.oms.orderManagement.generated.api.model.ErrorApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.IncorrectImportFileResponse
import com.hellofresh.oms.orderManagementHttp.imports.IncorrectImportFileException
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.DuplicateSkusInPurchaseOrderException
import com.hellofresh.oms.orderManagementHttp.imports.parser.FileHasTooManyLinesException
import com.hellofresh.oms.orderManagementHttp.imports.parser.InvalidFileException
import jakarta.validation.ConstraintViolationException
import java.time.LocalDateTime.now
import java.time.format.DateTimeParseException
import org.apache.tomcat.util.http.fileupload.impl.SizeException
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatus.BAD_REQUEST
import org.springframework.http.HttpStatus.CONFLICT
import org.springframework.http.HttpStatus.NOT_FOUND
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.ServletRequestBindingException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.context.request.ServletWebRequest

@RestControllerAdvice
class ControllerErrorHandler {
    @ResponseStatus(BAD_REQUEST)
    @ExceptionHandler(
        value = [
            ConstraintViolationException::class,
            IllegalArgumentException::class,
            ServletRequestBindingException::class,
            HttpMessageNotReadableException::class,
            OrderCreationException::class,
            DateTimeParseException::class,
            InvalidFileException::class,
            DuplicateSkusInPurchaseOrderException::class,
        ],
    )
    @ResponseBody
    fun handleBadRequest(e: Exception, request: ServletWebRequest) =
        getErrorResponseDto(e.message, request, BAD_REQUEST)

    @ResponseStatus(BAD_REQUEST)
    @ExceptionHandler(
        value = [IncorrectImportFileException::class],
    )
    @ResponseBody
    fun handleIncorrectImportFileException(e: IncorrectImportFileException) =
        getIncorrectImportFileErrorResponseDto(e)

    @ResponseStatus(NOT_FOUND)
    @ExceptionHandler(
        value = [
            SupplierNotFoundException::class,
            PurchaseOrderNotFoundException::class,
            CommunicationPreferenceNotFoundException::class,
            CommentNotFoundException::class,
        ],
    )
    @ResponseBody
    fun handleNotFound(e: Exception, request: ServletWebRequest) =
        getErrorResponseDto(e.message, request, NOT_FOUND)

    @ResponseStatus(CONFLICT)
    @ExceptionHandler(
        value = [
            CommunicationPreferenceAlreadyExistException::class,
            PurchaseOrderNotSyncedException::class,
            OrderingToolException::class,
            PurchaseOrderAlreadyExistException::class,
        ],
    )
    @ResponseBody
    fun handleConflict(e: Exception, request: ServletWebRequest) =
        getErrorResponseDto(e.message, request, CONFLICT)

    @ResponseBody
    @ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
    @ExceptionHandler(
        value = [
            SizeException::class,
            FileHasTooManyLinesException::class,
        ],
    )
    fun handlePayloadTooLarge(e: Exception, request: ServletWebRequest) =
        getErrorResponseDto(e.message, request, HttpStatus.PAYLOAD_TOO_LARGE)

    @ResponseStatus(BAD_REQUEST)
    @ExceptionHandler(
        value = [MethodArgumentNotValidException::class],
    )
    @ResponseBody
    fun handleMethodArgumentNotValidException(e: MethodArgumentNotValidException, request: ServletWebRequest) =
        getErrorResponseDto("""Field "${e.fieldError?.field}" ${e.fieldError?.defaultMessage}""", request, BAD_REQUEST)

    private fun getErrorResponseDto(message: String?, request: ServletWebRequest, httpStatus: HttpStatus) =
        ErrorApiResponse(
            timestamp = now(),
            error = httpStatus.reasonPhrase,
            path = request.request.requestURI,
            status = httpStatus.value(),
            message = message,
        )

    private fun getIncorrectImportFileErrorResponseDto(exception: IncorrectImportFileException) =
        IncorrectImportFileResponse(exception.errors.toList())
}
