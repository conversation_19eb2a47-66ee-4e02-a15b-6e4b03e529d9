package com.hellofresh.oms.orderManagementHttp.distributionCenters

import com.hellofresh.oms.orderManagement.generated.api.model.DistributionCentersResponseDto
import com.hellofresh.oms.orderManagement.generated.api.routes.DistributionCentersApi
import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
class DistributionCentersController(
    private val distributionCentersService: DistributionCentersService
) : DistributionCentersApi {

    override fun getDcs(
        @NotNull
        @Valid
        @RequestParam(required = true, value = "market")
        market: String
    ): ResponseEntity<DistributionCentersResponseDto> {
        val dcs = distributionCentersService.getDistributionCenters(market.lowercase())
        return ResponseEntity.ok(
            DistributionCentersResponseDto(dcs.mapToDto())
        )
    }
}
