package com.hellofresh.oms.orderManagementHttp.configuration

import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum
import java.util.Locale
import org.springframework.context.annotation.Configuration
import org.springframework.core.convert.converter.Converter
import org.springframework.format.FormatterRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Configuration
class WebConfiguration : WebMvcConfigurer {
    override fun addFormatters(registry: FormatterRegistry) {
        registry.addConverter(StringToListPurchaseOrdersSortEnumConverter())
        registry.addConverter(StringToListSupplierSortEnumConverter())
        registry.addConverter(StringToCommunicationPreferenceScopeTypeEnumCaseInsensitiveConverter())
    }
}

class StringToListPurchaseOrdersSortEnumConverter : Converter<String, ListPurchaseOrdersSortEnum> {
    override fun convert(source: String): ListPurchaseOrdersSortEnum = ListPurchaseOrdersSortEnum.entries.find {
        it.value == source
    } ?: throw IllegalArgumentException("Could not convert string into ListPurchaseOrdersSortEnum: [$source]")
}

class StringToListSupplierSortEnumConverter : Converter<String, ListSuppliersSortEnum> {
    override fun convert(source: String): ListSuppliersSortEnum = ListSuppliersSortEnum.entries.find {
        it.value == source
    } ?: throw IllegalArgumentException("Could not convert string into ListSuppliersSortEnum: [$source]")
}

class StringToCommunicationPreferenceScopeTypeEnumCaseInsensitiveConverter :
    Converter<String, CommunicationPreferenceTypeEnum> {
    override fun convert(
        source: String
    ): CommunicationPreferenceTypeEnum = CommunicationPreferenceTypeEnum.entries.find {
        it.value == source.uppercase(Locale.getDefault())
    } ?: throw IllegalArgumentException("Could not convert string into CommunicationPreferenceTypeEnum: [$source]")
}
