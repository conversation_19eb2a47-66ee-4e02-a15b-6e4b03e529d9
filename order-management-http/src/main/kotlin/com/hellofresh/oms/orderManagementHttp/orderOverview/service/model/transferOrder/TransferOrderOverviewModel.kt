package com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder

import java.math.BigDecimal

data class TransferOrderOverviewModel(
    val skuCode: String,
    val sourceDcCode: String,
    val skuName: String,
    val category: String,
    val skuUom: String?,
    val packagingType: String,
    val totalOrderedQuantity: BigDecimal,
    val totalReceivedQuantity: BigDecimal?,
    val totalCasesReceived: Int?,
    val totalPriceOrdered: String,
    val totalPriceReceived: String?,
    val weightedAvgCasePrice: String?,
    val weightedAvgCaseSize: BigDecimal?,
    val weightedAvgCaseSizeReceived: BigDecimal?,
    val assignedBuyerFirstName: String?,
    val assignedBuyerLastName: String?,
    val transferOrderBreakdown: List<TransferOrderBreakdownItem>
)
