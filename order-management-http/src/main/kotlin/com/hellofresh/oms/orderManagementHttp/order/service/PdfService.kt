package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.orderManagementHttp.order.service.domain.PurchaseOrderForPdfTemplate
import gg.jte.TemplateEngine
import gg.jte.output.StringOutput
import java.io.ByteArrayOutputStream
import org.springframework.core.io.ClassPathResource
import org.springframework.stereotype.Service
import org.xhtmlrenderer.pdf.ITextRenderer

private const val ORDER_PDF_TEMPLATE = "order_pdf_template.kte"

@Service
class PdfService(private val templateEngine: TemplateEngine) {
    private val renderer = ITextRenderer()

    fun renderHtmlContent(purchaseOrder: PurchaseOrderForPdfTemplate): String =
        StringOutput().apply {
            templateEngine.render(
                ORDER_PDF_TEMPLATE,
                mapOf("order" to purchaseOrder),
                this,
            )
        }.toString()

    fun generatePdfFromHtml(htmlContent: String): ByteArray =
        ByteArrayOutputStream().use { byteArrayOutputStream ->
            synchronized(renderer) {
                renderer.setDocumentFromString(htmlContent, ClassPathResource("static").url.toExternalForm())
                renderer.layout()
                renderer.createPDF(byteArrayOutputStream)
                renderer.finishPDF()
            }
            byteArrayOutputStream.toByteArray()
        }
}
