package com.hellofresh.oms.orderManagementHttp.client.featureflag

import com.statsig.sdk.Statsig
import com.statsig.sdk.StatsigUser
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class FeatureFlagClient(
    @Value("\${statsig.gates.force-email-communication-preference}")
    private val enableForceIncludeEmailCommunicationPreferenceStatsigGate: String,
    @Value("\${statsig.gates.enable-po-topic-v2}")
    private val enablePublisherForPoTopicStatsigGate: String,
) {
    fun shouldForceIncludeEmailPreferenceFeatureFlag() =
        Statsig.checkGateSync(StatsigUser(APP_USER_ID), enableForceIncludeEmailCommunicationPreferenceStatsigGate)

    fun shouldEnablePublisherForPoTopicFeatureFlag() =
        Statsig.checkGateSync(StatsigUser(APP_USER_ID), enablePublisherForPoTopicStatsigGate)

    companion object {
        private const val APP_USER_ID = "order-management-service"
    }
}
