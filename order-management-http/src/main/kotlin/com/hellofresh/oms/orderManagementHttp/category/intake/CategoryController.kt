package com.hellofresh.oms.orderManagementHttp.category.intake

import com.hellofresh.oms.orderManagement.generated.api.model.CategoryListApiResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.CategoriesApi
import com.hellofresh.oms.orderManagementHttp.category.service.domain.CategoryService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class CategoryController(
    private val categoryService: CategoryService,
    private val responseConverter: CategoryResponseConverter,
) : CategoriesApi {
    override fun getCategories(market: String): ResponseEntity<CategoryListApiResponse> =
        ResponseEntity.ok(
            responseConverter.convert(
                categoryService.getCategories(market),
            ),
        )
}
