package com.hellofresh.oms.orderManagementHttp.receiptOverride.service

import com.hellofresh.oms.orderManagement.generated.api.model.ReceiptOverrideResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UpsertReceiptOverrideRequest
import com.hellofresh.oms.orderManagementHttp.receiptOverride.ReceiptOverrideRepository
import jakarta.transaction.Transactional
import java.util.UUID
import org.springframework.stereotype.Service

@Service
class ReceiptOverrideService(
    private val receiptOverrideRepository: ReceiptOverrideRepository,
) {

    @Transactional
    fun upsertReceiptOverride(request: UpsertReceiptOverrideRequest): ReceiptOverrideResponse {
        val existingReceiptOverride = receiptOverrideRepository.findByPoNumberAndSkuCode(
            request.poNumber,
            request.skuCode
        )
        val receiptOverride = request.toEntity(existingReceiptOverride?.id ?: UUID.randomUUID())
        return receiptOverrideRepository.save(receiptOverride).toResponse()
    }

    @Transactional
    fun deleteReceiptOverride(poNumber: String, skuCode: String) =
        receiptOverrideRepository.deleteByPoNumberAndSkuCode(poNumber, skuCode)
}
