package com.hellofresh.oms.orderManagementHttp.viewstate.intake

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.orderManagement.generated.api.model.CreateViewStateRequest
import com.hellofresh.oms.orderManagement.generated.api.model.CreateViewStateResponse
import com.hellofresh.oms.orderManagement.generated.api.model.ListViewStateResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UpdateViewStateRequest
import com.hellofresh.oms.orderManagement.generated.api.model.ViewStateResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.ViewStateApi
import com.hellofresh.oms.orderManagementHttp.authentication.getLoggedInUser
import com.hellofresh.oms.orderManagementHttp.viewstate.service.ViewStateService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class ViewStateController(
    private val viewStateService: ViewStateService,
    private val objectMapper: ObjectMapper,
    private val viewStateMapper: ViewStateResponseMapper
) : ViewStateApi {
    override fun createViewState(
        createViewStateRequest: CreateViewStateRequest
    ): ResponseEntity<CreateViewStateResponse> {
        val loggedInUser = getLoggedInUser()
        val createdState = viewStateService.createViewState(
            userId = loggedInUser.userId,
            userEmail = loggedInUser.userEmail,
            resource = createViewStateRequest.resource,
            name = createViewStateRequest.name,
            state = objectMapper.writeValueAsString(createViewStateRequest.state),
        )

        return ResponseEntity.ok(viewStateMapper.toCreateResponse(createdState))
    }

    override fun listViewStates(
        resource: String
    ): ResponseEntity<ListViewStateResponse> {
        val loggedInUser = getLoggedInUser()
        val viewStates = viewStateService.findViewStates(
            userId = loggedInUser.userId,
            resource = resource,
        )

        return ResponseEntity.ok(viewStateMapper.toListViewStateResponse(viewStates))
    }

    override fun getViewState(
        stateId: Long
    ): ResponseEntity<ViewStateResponse> {
        val loggedInUser = getLoggedInUser()
        val viewState = viewStateService.getViewState(
            stateId = stateId,
            userId = loggedInUser.userId,
        ) ?: return ResponseEntity.notFound().build()

        return ResponseEntity.ok(viewStateMapper.toViewStateResponse(viewState))
    }

    override fun updateViewState(
        stateId: Long,
        updateViewStateRequest: UpdateViewStateRequest
    ): ResponseEntity<CreateViewStateResponse> {
        val loggedInUser = getLoggedInUser()
        val existingViewState = viewStateService.getViewState(
            stateId = stateId,
            userId = loggedInUser.userId,
        ) ?: return ResponseEntity.notFound().build()
        val updatedState = viewStateService.updateViewState(
            viewState = existingViewState,
            newState = updateViewStateRequest.state?.let { objectMapper.writeValueAsString(it) },
            newName = updateViewStateRequest.name,
            newResource = updateViewStateRequest.resource,
        )

        return ResponseEntity.ok(viewStateMapper.toCreateResponse(updatedState))
    }

    override fun deleteViewState(
        stateId: Long,
    ): ResponseEntity<CreateViewStateResponse> {
        val loggedInUser = getLoggedInUser()
        val existingViewState = viewStateService.getViewState(
            stateId = stateId,
            userId = loggedInUser.userId,
        ) ?: return ResponseEntity.notFound().build()
        viewStateService.deleteViewState(existingViewState)

        return ResponseEntity.ok(viewStateMapper.toCreateResponse(existingViewState))
    }
}
