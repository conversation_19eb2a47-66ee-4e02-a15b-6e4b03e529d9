package com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder

import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusViewRepository
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.transferOrder.TransferOrderOverviewModel
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class TransferOrderOverviewService(
    private val poOverviewConfigs: PoOverviewConfigs,
    private val transferOrderStatusViewRepository: TransferOrderStatusViewRepository
) {
    fun getOverview(
        dcCode: String?,
        brand: String,
        dcWeeks: List<String>
    ): List<TransferOrderOverviewModel> {
        logger.debug("Getting transfer order overview for dcCode: {}, dcWeeks: {}, brand: {}", dcCode, dcWeeks, brand)

        val dcCodes: List<String> = if (dcCode.isNullOrEmpty()) {
            poOverviewConfigs.brandDcConfig.filter { it.key == brand }
                .flatMap { it.value.dcs }
        } else {
            listOf(dcCode)
        }

        return transferOrderStatusViewRepository.findAllTransferOrderDetails(dcCodes, dcWeeks)
            .groupBy {
                "${it.sourceDcCode}_${it.skuCode}" }
            .map { (_, statusDetailsList) ->
                statusDetailsList.mapToTransferOrderOverviewModel()
            }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
