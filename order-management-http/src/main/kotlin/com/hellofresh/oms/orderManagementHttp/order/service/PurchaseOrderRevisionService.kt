package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrderRevisionsResponse
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderResponse
import com.hellofresh.oms.orderManagementHttp.exception.SupplierNotFoundException
import com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.service.GoodsReceivedNoteService
import com.hellofresh.oms.orderManagementHttp.order.intake.from
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.outbox.service.OutboxService
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import org.springframework.stereotype.Service

@Service
class PurchaseOrderRevisionService(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val supplierRepository: SupplierRepository,
    private val outboxService: OutboxService,
    private val goodsReceivedNoteService: GoodsReceivedNoteService,
) {
    fun getRevisions(poNumber: String): ListPurchaseOrderRevisionsResponse = ListPurchaseOrderRevisionsResponse(
        revisions = purchaseOrderRepository.findAllByPoNumber(poNumber).map { revision ->
            val supplier = supplierRepository.findById(revision.supplierId)
                .orElseThrow { SupplierNotFoundException(revision.supplierId.toString()) }
            val outboxItems = outboxService.getOutboxItemsFor(poNumber)
            val grn = goodsReceivedNoteService.getGoodsReceivedNote(poNumber)
            PurchaseOrderResponse::class.from(
                revision,
                supplier = supplier,
                outboxItems.sortedBy { oi -> oi.createdAt }.reversed()
                    .find { outboxItem -> outboxItem.poId == revision.id },
                grn,
            )
        },
    )
}
