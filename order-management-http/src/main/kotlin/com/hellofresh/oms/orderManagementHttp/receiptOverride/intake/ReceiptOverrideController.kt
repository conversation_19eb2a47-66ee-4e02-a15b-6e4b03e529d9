package com.hellofresh.oms.orderManagementHttp.receiptOverride.intake

import com.hellofresh.oms.orderManagement.generated.api.model.ReceiptOverrideResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UpsertReceiptOverrideRequest
import com.hellofresh.oms.orderManagement.generated.api.routes.ReceiptOverridesApi
import com.hellofresh.oms.orderManagementHttp.receiptOverride.service.ReceiptOverrideService
import org.springframework.http.HttpStatus.OK
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class ReceiptOverrideController(
    private val receiptOverrideService: ReceiptOverrideService,
) : ReceiptOverridesApi {

    override fun upsertReceiptOverride(request: UpsertReceiptOverrideRequest): ResponseEntity<ReceiptOverrideResponse> =
        ResponseEntity.status(OK).body(
            receiptOverrideService.upsertReceiptOverride(request)
        )

    override fun deleteReceiptOverride(poNumber: String, skuCode: String): ResponseEntity<Unit> {
        receiptOverrideService.deleteReceiptOverride(poNumber, skuCode)
        return ResponseEntity.noContent().build()
    }
}
