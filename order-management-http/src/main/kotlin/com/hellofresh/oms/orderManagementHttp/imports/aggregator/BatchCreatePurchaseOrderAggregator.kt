package com.hellofresh.oms.orderManagementHttp.imports.aggregator

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.supplier.Supplier
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagementHttp.imports.validator.BatchOrderRowValidatedDto
import java.math.BigDecimal
import java.util.UUID
import org.springframework.stereotype.Component
import org.springframework.web.context.annotation.RequestScope

@Component
@RequestScope
class BatchCreatePurchaseOrderAggregator {
    fun aggregate(validatedLines: List<BatchOrderRowValidatedDto>): List<PurchaseOrderDto> {
        val linesByPoKey = validatedLines.groupBy {
            PurchaseOrderKey(
                yearWeek = it.yearWeek,
                distributionCenter = it.distributionCenter,
                shipMethod = it.shipMethod,
                supplier = it.supplier,
                deliveryWindow = it.deliveryWindow,
                emergencyReason = it.emergencyReason,
            )
        }
        val aggregatedLines = linesByPoKey.map { (key, orderItems) ->
            PurchaseOrderDto(
                yearWeek = key.yearWeek,
                distributionCenter = key.distributionCenter,
                supplier = key.supplier,
                deliveryWindow = key.deliveryWindow,
                emergencyReason = key.emergencyReason,
                shipMethod = key.shipMethod,
                orderItems = orderItems.map {
                    PurchaseOrderItemDto(
                        sku = it.sku,
                        orderSize = it.orderSize,
                        bufferValue = it.bufferValue,
                        caseSize = it.caseSize,
                        price = it.price,
                        packagingType = it.packagingType,
                        uom = it.uom,
                    )
                },
                comments = orderItems.map { it.comments }.filterNot { it.isNullOrBlank() }.firstOrNull(),
            )
        }

        aggregatedLines.forEach { po ->
            val duplicateSkus = po.orderItems
                .groupBy { it.sku.code }
                .filter { it.value.size > 1 }
                .keys
            if (duplicateSkus.isNotEmpty()) {
                throw DuplicateSkusInPurchaseOrderException(duplicateSkus)
            }
        }

        return aggregatedLines
    }

    data class PurchaseOrderKey(
        val yearWeek: YearWeek,
        val distributionCenter: DistributionCenter,
        val shipMethod: ShipMethodEnum,
        val supplier: Supplier,
        val deliveryWindow: DeliveryWindowDto,
        val emergencyReason: EmergencyReason,
    )
}

data class PurchaseOrderDto(
    val id: UUID? = null,
    val poNumber: String? = null,
    val yearWeek: YearWeek,
    val distributionCenter: DistributionCenter,
    val shipMethod: ShipMethodEnum,
    val supplier: Supplier,
    val deliveryWindow: DeliveryWindowDto,
    val orderItems: List<PurchaseOrderItemDto>,
    val emergencyReason: EmergencyReason,
    val comments: String? = null,
)

data class PurchaseOrderItemDto(
    val sku: Sku,
    val orderSize: BigDecimal,
    val bufferValue: BigDecimal,
    val caseSize: BigDecimal,
    val price: BigDecimal,
    val packagingType: PackagingType,
    val uom: UOM,
)

class DuplicateSkusInPurchaseOrderException(skus: Set<String>) :
    RuntimeException("Duplicate SKUs found in the file: ${skus.joinToString(", ")}")
