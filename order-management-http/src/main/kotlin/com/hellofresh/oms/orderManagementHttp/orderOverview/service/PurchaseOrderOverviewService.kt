package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.PurchaseOrderStatusViewRepository
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.PurchaseOrderOverviewModel
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class PurchaseOrderOverviewService(
    private val poOverviewConfigs: PoOverviewConfigs,
    private val purchaseOrderStatusViewRepository: PurchaseOrderStatusViewRepository
) {
    fun getOverview(
        dcCode: String?,
        brand: String,
        dcWeeks: List<String>
    ): List<PurchaseOrderOverviewModel> {
        logger.debug("Getting purchase order overview for dcCode: {}, dcWeeks: {}, brand: {}", dcCode, dcWeeks, brand)

        val dcCodes: List<String> = if (dcCode.isNullOrEmpty()) {
            poOverviewConfigs.brandDcConfig.filter { it.key == brand }
                .flatMap { it.value.dcs }
        } else {
            listOf(dcCode)
        }

        return purchaseOrderStatusViewRepository.findAllPurchaseOrderDetails(dcCodes, dcWeeks)
            .map { it.mapToPurchaseOrderOverviewModel(brand) }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
