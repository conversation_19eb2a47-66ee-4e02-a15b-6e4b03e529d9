package com.hellofresh.oms.orderManagementHttp.imports.intake

import com.hellofresh.oms.orderManagement.generated.api.model.ListBatchImportOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.BatchImportOrdersHistoryApi
import com.hellofresh.oms.orderManagementHttp.imports.service.ImportHistoryService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class BatchImportOrdersHistoryController(
    private val importHistoryService: ImportHistoryService,
    private val responseConverter: BatchImportOrdersHistoryResponseConverter,
) : BatchImportOrdersHistoryApi {
    override fun getBatchHistory(market: String, page: Int, size: Int): ResponseEntity<ListBatchImportOrdersResponse> {
        val pageResult = importHistoryService.getImportHistoryListSortedByCreatedAtDescending(market, page, size)
        return ResponseEntity.ok(responseConverter.convert(pageResult))
    }
}
