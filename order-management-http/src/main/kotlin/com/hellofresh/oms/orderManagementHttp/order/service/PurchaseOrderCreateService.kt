package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.isDelivery
import com.hellofresh.oms.orderManagement.generated.api.model.AddressDto
import com.hellofresh.oms.orderManagement.generated.api.model.CreateOrderItemRequest
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderIdentifier
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.exception.OrderCreationException
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderEventType
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.service.domain.PackagingDomain
import com.hellofresh.oms.orderManagementHttp.order.service.domain.toModel
import io.micrometer.core.annotation.Timed
import java.time.LocalDateTime
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class PurchaseOrderCreateService(
    private val purchaseOrderService: PurchaseOrderService,
    private val purchaseOrderValidatorService: PurchaseOrderValidatorService,
    private val distributionCentersRepository: DistributionCentersRepository,
    private val purchaseOrderProducer: PurchaseOrderProducer,
) {
    @Timed
    fun processPurchaseOrder(
        createPurchaseOrderRequest: CreatePurchaseOrderRequestDto,
        sendImmediately: Boolean,
    ): PurchaseOrder {
        logger.info("$createPurchaseOrderRequest - ${createPurchaseOrderRequest.userEmail}")
        purchaseOrderValidatorService.validateCreateRequest(createPurchaseOrderRequest)

        val poIdentifier = if (createPurchaseOrderRequest.purchaseOrderIdentifier != null) {
            purchaseOrderValidatorService.validatePurchaseOrderIdentifier(
                createPurchaseOrderRequest.purchaseOrderIdentifier,
            )
            createPurchaseOrderRequest.purchaseOrderIdentifier
        } else {
            PurchaseOrderIdentifier(
                UUID.randomUUID(),
                purchaseOrderService.generatePoNumber(
                    YearWeek(createPurchaseOrderRequest.dcWeek),
                    createPurchaseOrderRequest.dcCode,
                ),
            )
        }

        val supplier = purchaseOrderValidatorService.validateSupplier(createPurchaseOrderRequest.supplierId)
        val emergencyReason = purchaseOrderValidatorService.validateEmergencyReason(
            createPurchaseOrderRequest.emergencyReasonId,
        )
        val distributionCenter = findDistributionCenterOrThrow(createPurchaseOrderRequest.dcCode)
        val uoms = createPurchaseOrderRequest.orderItems.map { PackagingDomain.from(it.packaging).unitOfMeasure }

        purchaseOrderValidatorService.validateUoms(uoms, market = supplier.market)
        purchaseOrderValidatorService.validateSkus(createPurchaseOrderRequest.orderItems.map { it.skuId })
        val shippingAddress = getShippingAddressOrDeliveryAddress(createPurchaseOrderRequest, distributionCenter)

        val purchaseOrder = createPurchaseOrderRequest.toModel(
            poId = poIdentifier.id,
            poNumber = poIdentifier.poNumber,
            supplier = supplier,
            emergencyReason = emergencyReason,
            now = LocalDateTime.now(),
            shippingAddress = shippingAddress,
        )

        val savedPurchaseOrder = purchaseOrderService.saveAndQueuePurchaseOrder(purchaseOrder, sendImmediately)

        purchaseOrderProducer.publishPurchaseOrderEvent(savedPurchaseOrder, PurchaseOrderEventType.CREATED)

        return savedPurchaseOrder
    }

    private fun findDistributionCenterOrThrow(dcCode: String) =
        distributionCentersRepository.findByCode(dcCode)
            ?: throw OrderCreationException(
                "Distribution center with provided dc code not found: $dcCode",
            )

    private fun getShippingAddressOrDeliveryAddress(
        createPurchaseOrderRequest: CreatePurchaseOrderRequestDto,
        distributionCenter: DistributionCenter,
    ): ShippingAddress = if (createPurchaseOrderRequest.deliveryAddress == null) {
        val dcAddress = distributionCenter.addresses.firstOrNull { it.isDelivery() } ?: throw OrderCreationException(
            "Distribution center has no Address of type DELIVERY. ${distributionCenter.code}",
        )
        ShippingAddress(
            locationName = distributionCenter.name,
            streetAddress = dcAddress.address,
            city = dcAddress.city,
            region = dcAddress.state,
            postalCode = dcAddress.zip,
            countryCode = dcAddress.countryCode,
        )
    } else {
        ShippingAddress(
            locationName = distributionCenter.name,
            streetAddress = createPurchaseOrderRequest.deliveryAddress.address,
            city = createPurchaseOrderRequest.deliveryAddress.city,
            region = createPurchaseOrderRequest.deliveryAddress.region,
            postalCode = createPurchaseOrderRequest.deliveryAddress.postalCode,
            countryCode = createPurchaseOrderRequest.deliveryAddress.countryCode,
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}

data class CreatePurchaseOrderRequestDto(
    val userId: UUID,
    val userEmail: String,
    val deliveryWindow: DeliveryWindowDto,
    val orderItems: List<CreateOrderItemRequest>,
    val deliveryAddress: AddressDto? = null,
    val dcCode: String,
    val dcWeek: String,
    val supplierId: UUID,
    val shippingMethod: ShipMethodEnum,
    val emergencyReasonId: UUID,
    val comment: String? = null,
    val purchaseOrderIdentifier: PurchaseOrderIdentifier? = null,
    val origin: Origin,
)
