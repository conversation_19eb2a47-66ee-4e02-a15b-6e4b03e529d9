package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrderRevisionsResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.PurchaseOrderRevisionApi
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderRevisionService
import org.springframework.http.ResponseEntity
import org.springframework.http.ResponseEntity.ok
import org.springframework.web.bind.annotation.RestController

@RestController
class PurchaseOrderRevisionController(
    private val purchaseOrderRevisionService: PurchaseOrderRevisionService,
) : PurchaseOrderRevisionApi {
    override fun getRevisions(poNumber: String): ResponseEntity<ListPurchaseOrderRevisionsResponse> =
        ok(purchaseOrderRevisionService.getRevisions(poNumber))
}
