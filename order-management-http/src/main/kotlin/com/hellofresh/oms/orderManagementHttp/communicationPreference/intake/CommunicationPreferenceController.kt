package com.hellofresh.oms.orderManagementHttp.communicationPreference.intake

import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceResponse
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferencesResponse
import com.hellofresh.oms.orderManagement.generated.api.model.CreateCommunicationPreferenceRequest
import com.hellofresh.oms.orderManagement.generated.api.model.CreateCommunicationPreferenceResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UpdateCommunicationPreferenceRequest
import com.hellofresh.oms.orderManagement.generated.api.routes.CommunicationPreferenceApi
import com.hellofresh.oms.orderManagementHttp.communicationPreference.service.CommunicationPreferenceService
import java.util.UUID
import org.springframework.http.ResponseEntity
import org.springframework.http.ResponseEntity.noContent
import org.springframework.http.ResponseEntity.notFound
import org.springframework.http.ResponseEntity.ok
import org.springframework.web.bind.annotation.RestController

@RestController
class CommunicationPreferenceController(
    private val communicationPreferenceService: CommunicationPreferenceService,
    private val responseConverter: CommunicationPreferenceResponseConverter,
    private val detailedResponseConverter: CommunicationPreferenceDetailedResponseConverter,
) : CommunicationPreferenceApi {

    override fun getCommunicationPreference(
        market: String,
        dcCode: String,
        supplierId: UUID
    ): ResponseEntity<CommunicationPreferenceResponse> =
        communicationPreferenceService.getCommunicationPreference(
            market = market,
            dcCode = dcCode,
            supplierId = supplierId,
        )?.let { ok(responseConverter.convert(it)) } ?: notFound().build()

    override fun getCommunicationPreferences(
        type: CommunicationPreferenceTypeEnum,
        market: String?,
    ): ResponseEntity<CommunicationPreferencesResponse> = ok(
        CommunicationPreferencesResponse(
            communicationPreferenceService.getCommunicationPreferences(type, market)
                .map { detailedResponseConverter.convert(it) },
        ),
    )

    override fun updateCommunicationPreferences(
        scopeType: CommunicationPreferenceTypeEnum,
        scopeValue: String,
        updateCommunicationPreferenceRequest: UpdateCommunicationPreferenceRequest
    ): ResponseEntity<CommunicationPreferenceResponse> {
        val result = communicationPreferenceService.updateCommunicationPreferences(
            scopeType = scopeType,
            scopeValue = scopeValue,
            communicationPreferenceUpdateRequest = updateCommunicationPreferenceRequest,
        )

        return ok(responseConverter.convert(result.preference))
    }

    override fun deleteCommunicationPreferences(
        scopeType: CommunicationPreferenceTypeEnum,
        scopeValue: String
    ): ResponseEntity<Unit> =
        communicationPreferenceService.deleteCommunicationPreferences(scopeType, scopeValue)
            .let { noContent().build() }

    override fun createCommunicationPreferences(
        createCommunicationPreferenceRequest: CreateCommunicationPreferenceRequest,
    ): ResponseEntity<CreateCommunicationPreferenceResponse> =
        ok(
            CreateCommunicationPreferenceResponse(
                communicationPreferenceService.createCommunicationPreferences(createCommunicationPreferenceRequest).id,
            ),
        )
}
