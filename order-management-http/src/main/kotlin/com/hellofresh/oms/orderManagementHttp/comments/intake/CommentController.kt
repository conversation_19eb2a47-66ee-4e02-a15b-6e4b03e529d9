package com.hellofresh.oms.orderManagementHttp.comments.intake

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.CommentListResponse
import com.hellofresh.oms.orderManagement.generated.api.model.CreateCommentRequest
import com.hellofresh.oms.orderManagement.generated.api.model.CreateCommentResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UpdateCommentRequest
import com.hellofresh.oms.orderManagement.generated.api.routes.CommentsApi
import com.hellofresh.oms.orderManagementHttp.authentication.getLoggedInUser
import com.hellofresh.oms.orderManagementHttp.comments.service.CommentService
import com.hellofresh.oms.orderManagementHttp.comments.service.CommentService.CreateCommentCommand
import java.util.UUID
import org.springframework.http.HttpStatus.CREATED
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class CommentController(
    private val service: CommentService,
    private val responseMapper: ResponseMapper,
    private val requestMapper: RequestMapper,
) : CommentsApi {
    override fun getComments(
        resourceType: List<String>,
        domains: List<String>,
        weeks: List<String>,
        dcs: List<String>?,
        brands: List<String>?
    ): ResponseEntity<CommentListResponse> {
        val filter = requestMapper.toGetCommentsFilter(
            resourceTypes = resourceType,
            weeks = weeks.map { YearWeek(it) },
            domains = domains,
            dcs = dcs,
            brands = brands,
        )
        val comments = service.getComments(filter)

        return ResponseEntity.ok(responseMapper.toListResponse(comments))
    }

    override fun createComment(
        createCommentRequest: CreateCommentRequest
    ): ResponseEntity<CreateCommentResponse> {
        val loggedInUser = getLoggedInUser()
        val comment = service.createComment(
            CreateCommentCommand(
                dc = createCommentRequest.dc,
                yearWeek = YearWeek(createCommentRequest.week),
                domain = createCommentRequest.domain,
                resourceType = createCommentRequest.resourceType,
                resourceId = createCommentRequest.resourceId,
                comment = createCommentRequest.comment,
                brand = createCommentRequest.brand.toString(),
                createdBy = loggedInUser.userEmail,
            ),
        )
        return ResponseEntity.status(CREATED).body(
            CreateCommentResponse(
                id = comment.id,
            ),
        )
    }

    override fun updateComment(
        id: UUID,
        updateCommentRequest: UpdateCommentRequest
    ): ResponseEntity<CreateCommentResponse> {
        val updated = service.updateComment(
            id = id,
            newComment = updateCommentRequest.comment,
            updatedBy = getLoggedInUser().userEmail,
        )

        return ResponseEntity.ok(CreateCommentResponse(id = updated.id))
    }
}
