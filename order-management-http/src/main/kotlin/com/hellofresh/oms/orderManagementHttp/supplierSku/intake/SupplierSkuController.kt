package com.hellofresh.oms.orderManagementHttp.supplierSku.intake

import com.hellofresh.oms.orderManagement.generated.api.model.SupplierSkusListApiResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.SupplierSKUApi
import com.hellofresh.oms.orderManagementHttp.supplierSku.service.SupplierSkuService
import java.time.LocalDate
import java.util.UUID
import org.springframework.http.ResponseEntity
import org.springframework.http.ResponseEntity.ok
import org.springframework.web.bind.annotation.RestController

@RestController
class SupplierSkuController(
    private val supplierSkuService: SupplierSkuService
) : SupplierSKUApi {
    override fun getSupplierSkus(
        deliveryDate: String,
        market: String,
        includeAll: Boolean,
        dcCode: String,
        supplierId: UUID,
        page: Int,
        size: Int,
        search: String?,
    ): ResponseEntity<SupplierSkusListApiResponse> = ok(
        supplierSkuService.getSkus(
            SupplierSkuSearch(
                includeAll,
                market,
                search,
                supplierId,
                dcCode,
                LocalDate.parse(deliveryDate),
                page,
                size,
            ),
        ).toSupplierSkuApiResponse(),
    )
}

data class SupplierSkuSearch(
    val includeAll: Boolean,
    val market: String,
    val search: String?,
    val supplierId: UUID,
    val dcCode: String,
    val deliveryDate: LocalDate,
    val page: Int,
    val size: Int,
)
