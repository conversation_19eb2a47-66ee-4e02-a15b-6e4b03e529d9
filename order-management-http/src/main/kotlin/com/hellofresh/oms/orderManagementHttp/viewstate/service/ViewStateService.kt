package com.hellofresh.oms.orderManagementHttp.viewstate.service

import com.hellofresh.oms.model.viewstate.ViewState
import com.hellofresh.oms.orderManagementHttp.viewstate.out.ViewStateRepository
import java.time.LocalDateTime
import java.util.UUID
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ViewStateService(
    private val viewStateRepository: ViewStateRepository
) {
    @Transactional
    fun createViewState(
        userId: UUID,
        userEmail: String,
        resource: String,
        name: String,
        state: String
    ): ViewState {
        val newViewState = ViewState(
            resource = resource,
            name = name,
            userId = userId,
            userEmail = userEmail,
            state = state,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )

        return viewStateRepository.save(newViewState)
    }

    @Transactional
    fun updateViewState(
        viewState: ViewState,
        newState: String?,
        newName: String?,
        newResource: String?
    ): ViewState {
        val updatedViewState = viewState.copy(
            name = newName ?: viewState.name,
            resource = newResource ?: viewState.resource,
            state = newState ?: viewState.state,
            updatedAt = LocalDateTime.now(),
        )

        return viewStateRepository.save(updatedViewState)
    }

    fun getViewState(stateId: Long, userId: UUID) = viewStateRepository.findByUserIdAndId(userId, stateId)

    fun findViewStates(userId: UUID, resource: String) = viewStateRepository.findByUserIdAndResource(userId, resource)

    @Transactional
    fun deleteViewState(viewState: ViewState) {
        viewStateRepository.delete(viewState)
    }
}
