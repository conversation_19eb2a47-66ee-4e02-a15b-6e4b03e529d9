package com.hellofresh.oms.orderManagementHttp.client.tapioca

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.hellofresh.oms.orderManagementHttp.client.tapioca.domain.SendPurchaseOrderRequest
import com.hellofresh.oms.orderManagementHttp.client.tapioca.exception.TapiocaClientException
import io.micrometer.core.annotation.Timed
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatusCode
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientRequestException
import reactor.core.publisher.Mono

@Component
class TapiocaClient(
    @Qualifier("tapiocaWebClient") private val webClient: WebClient,
    @Value("\${tapioca.po-send-url}") val poSendUrl: String,
    @Value("\${tapioca.po-delete-url}") val poDeleteUrl: String,
) {
    val jsonMessageKey = "message"

    @Timed
    @Throws(TapiocaClientException::class, WebClientRequestException::class)
    fun sendPurchaseOrder(sendPurchaseOrderRequest: SendPurchaseOrderRequest): ResponseEntity<Unit> =
        webClient.put()
            .uri(poSendUrl, sendPurchaseOrderRequest.purchaseOrderId)
            .bodyValue(TapiocaSendPurchaseOrderRequest(sendPurchaseOrderRequest.senderId))
            .retrieve()
            .onStatus({ it.is4xxClientError || it.is5xxServerError }, ::parseError)
            .toEntity(Unit::class.java)
            .doOnSuccess { logInfoPoWasSent(sendPurchaseOrderRequest.purchaseOrderId) }
            .block()!!

    private fun parseError(clientResponse: ClientResponse): Mono<Throwable> =
        clientResponse.bodyToMono(JsonNode::class.java)
            .defaultIfEmpty(clientResponse.toObjectNodeWithMessage())
            .doOnNext { logWarningResponse(clientResponse.statusCode().value(), it) }
            .onErrorMap { error ->
                logWarningErrorProcessingResponse(clientResponse.statusCode(), error)
                TapiocaClientException(
                    "Error while processing Tapioca response [status:${
                        clientResponse.statusCode().value()
                    }, body: $error]",
                )
            }
            .flatMap { Mono.error(TapiocaClientException(it.path(jsonMessageKey).asText())) }

    fun deletePurchaseOrder(id: UUID, userId: UUID): ResponseEntity<Unit> =
        webClient.delete()
            .uri(poDeleteUrl, id)
            .retrieve()
            .onStatus({ it.is4xxClientError || it.is5xxServerError }, ::parseError)
            .toEntity(Unit::class.java)
            .doOnNext { logger.info("Purchase order deletion accepted by Tapioca. [poId=$id, userId=$userId]") }
            .block()!!

    private fun ClientResponse.toObjectNodeWithMessage() =
        JsonNodeFactory.instance.objectNode().put(
            jsonMessageKey,
            statusCode().toString(),
        )

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)

        private fun logWarningResponse(statusCode: Int, jsonNode: JsonNode) =
            logger.warn("Tapioca response received. [status:$statusCode, body:${jsonNode.toPrettyString()}]")

        private fun logInfoPoWasSent(poId: UUID) =
            logger.info("Purchase order successfully sent to Tapioca. [poId=$poId]")

        private fun logWarningErrorProcessingResponse(statusCode: HttpStatusCode, error: Throwable?) {
            logger.warn("Error while processing Tapioca response [status:${statusCode.value()}]", error)
        }

        private data class TapiocaSendPurchaseOrderRequest(val senderId: UUID)
    }
}
