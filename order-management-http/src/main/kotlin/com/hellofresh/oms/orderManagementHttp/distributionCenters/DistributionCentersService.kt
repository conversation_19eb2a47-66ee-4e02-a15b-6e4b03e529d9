package com.hellofresh.oms.orderManagementHttp.distributionCenters

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterStatus.ACTIVE
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class DistributionCentersService(
    @Autowired private val distributionCentersRepository: DistributionCentersRepository,
) {
    fun getDistributionCenters(market: String): List<DistributionCenter> =
        distributionCentersRepository.findAllByMarketAndStatusAndIsVisibleOrderByName(
            market = market,
            status = ACTIVE,
            isVisible = true,
        )
}
