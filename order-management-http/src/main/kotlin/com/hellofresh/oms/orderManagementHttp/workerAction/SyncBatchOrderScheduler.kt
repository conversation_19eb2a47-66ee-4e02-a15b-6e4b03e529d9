package com.hellofresh.oms.orderManagementHttp.workerAction

import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerAction.Companion.createWorkerAction
import com.hellofresh.oms.model.WorkerActionData.SyncBatchOrder
import com.hellofresh.oms.model.importHistory.ImportHistory
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class SyncBatchOrderScheduler(
    private val workerActionRepository: WorkerActionRepository,
) {
    fun queueSyncBatchOrder(
        poNumber: String,
        importHistory: ImportHistory,
    ): WorkerAction {
        val workerAction = createWorkerAction(
            SyncBatchOrder(
                purchaseOrderNumber = poNumber,
                importHistoryId = importHistory.id,
            ),
            userEmail = importHistory.userEmail,
            userId = importHistory.userId,
        )
        logger.info(
            "Queuing `SYNC_BATCH_ORDER` worker action. [purchaseOrder={}, userEmail={}, historyId={}]",
            poNumber,
            importHistory.userEmail,
            importHistory.id,
        )
        return workerActionRepository.save(workerAction)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
