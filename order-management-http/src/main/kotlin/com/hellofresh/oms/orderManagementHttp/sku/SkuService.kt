package com.hellofresh.oms.orderManagementHttp.sku

import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.orderManagementHttp.supplierSku.intake.SupplierSkuSearch
import io.micrometer.core.annotation.Timed
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service

@Service
class SkuService(
    private val skuRepository: SkuRepository
) {

    @Timed
    fun getSkusForMarketOrderBySupplierAssignedFirst(
        searchQuery: SupplierSkuSearch,
        supplierSkuIds: List<UUID>
    ): Page<Sku> {
        val skusResult = skuRepository.findAllSkusForMarketAndSupplier(
            market = searchQuery.market,
            codeName = searchQuery.search,
            supplierSkuIds = supplierSkuIds,
            includeAll = searchQuery.includeAll,
            pageable = PageRequest.of(searchQuery.page, searchQuery.size),
        )

        logger.debug(
            "SKUs result base on requested criteria is as follow: totalElements=${skusResult.totalElements}, size=${skusResult.size}",
        )

        return skusResult
    }

    fun getSkusByPoNumberAndCodes(
        poNumber: String,
        skuCodes: Set<String>
    ) = skuRepository.findSkuByPoNumberAndCodesIn(poNumber, skuCodes)

    companion object {
        private val logger = LoggerFactory.getLogger(SkuService::class.java)
    }
}
