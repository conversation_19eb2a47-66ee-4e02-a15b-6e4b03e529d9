package com.hellofresh.oms.orderManagementHttp.outbox.intake

import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.orderManagement.generated.api.model.SendLogItem
import com.hellofresh.oms.orderManagement.generated.api.model.SendLogResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.SendLogApi
import com.hellofresh.oms.orderManagementHttp.outbox.service.OutboxService
import kotlin.reflect.KClass
import org.springframework.http.ResponseEntity
import org.springframework.http.ResponseEntity.ok
import org.springframework.web.bind.annotation.RestController

@RestController
class SendLogController(
    private val outboxService: OutboxService
) : SendLogApi {
    override fun getSendLog(poNumber: String): ResponseEntity<SendLogResponse> {
        val outBoxItems = outboxService.getOutboxItemsFor(poNumber)
        return ok(SendLogResponse::class.from(outBoxItems))
    }
    companion object {
        private fun KClass<SendLogResponse>.from(outBoxItems: List<OutboxItem>): SendLogResponse =
            SendLogResponse(
                sendLogItems = outBoxItems.map {
                    SendLogItem(
                        id = it.id,
                        poId = it.poId,
                        poNumber = it.poNumber,
                        userEmail = it.userEmail,
                        userId = it.userId,
                        lastStatusChangeAt = it.lastStatusChangeAt,
                        createdAt = it.createdAt,
                        version = it.version
                    )
                },
            )
    }
}
