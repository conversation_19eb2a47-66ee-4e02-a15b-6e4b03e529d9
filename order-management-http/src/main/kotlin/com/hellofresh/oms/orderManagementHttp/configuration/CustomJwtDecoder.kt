package com.hellofresh.oms.orderManagementHttp.configuration

import com.nimbusds.jwt.JWT
import com.nimbusds.jwt.JWTParser
import com.nimbusds.jwt.PlainJWT
import org.slf4j.LoggerFactory
import org.springframework.core.convert.converter.Converter
import org.springframework.security.oauth2.jose.jws.SignatureAlgorithm
import org.springframework.security.oauth2.jwt.BadJwtException
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.security.oauth2.jwt.MappedJwtClaimSetConverter

class CustomJwtDecoder(
    private val jwtDecoderByJwkKeySetUri: JwtDecoder,
    private val jwtDecoderByJwtSecretKey: JwtDecoder,
) : JwtDecoder {

    private val claimSetConverter: Converter<Map<String, Any>, Map<String, Any>> =
        MappedJwtClaimSetConverter.withDefaults(emptyMap())

    override fun decode(token: String): Jwt {
        val jwt: JWT = JWTParser.parse(token)
        if (jwt is PlainJWT) {
            logger.trace("Failed to decode unsigned token")
            throw BadJwtException("Unsupported algorithm of " + jwt.header.algorithm)
        }

        val createdJwt = createJwt(token, jwt)
        return if (isAzureToken(createdJwt)) {
            jwtDecoderByJwkKeySetUri.decode(createdJwt.tokenValue)
        } else {
            jwtDecoderByJwtSecretKey.decode(createdJwt.tokenValue)
        }
    }

    private fun isAzureToken(parsedJwt: Jwt): Boolean =
        parsedJwt.headers.getOrDefault("alg", "") == SignatureAlgorithm.RS256.name

    private fun createJwt(token: String, parsedJwt: JWT): Jwt =
        try {
            // Verify the signature
            val headers: Map<String, Any> = LinkedHashMap(parsedJwt.header.toJSONObject())
            val claims: Map<String, Any>? = this.claimSetConverter.convert(parsedJwt.jwtClaimsSet.claims)

            Jwt.withTokenValue(token)
                .headers { h: MutableMap<String, Any> -> h.putAll(headers) }
                .claims { c: MutableMap<String?, Any?> -> claims?.let { c.putAll(it) } }
                .build()
        } catch (ex: java.lang.Exception) {
            logger.trace("Failed to process JWT", ex)
            throw BadJwtException(String.format(DECODING_ERROR_MESSAGE_TEMPLATE, ex.message), ex)
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)

        private const val DECODING_ERROR_MESSAGE_TEMPLATE = "An error occurred while attempting to decode the Jwt: %s"
    }
}
