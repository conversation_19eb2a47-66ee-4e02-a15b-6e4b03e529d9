package com.hellofresh.oms.orderManagementHttp.supplier.service.domain

import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum.MINUS_NAME
import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum.PLUS_NAME
import kotlin.reflect.KClass
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Direction.ASC
import org.springframework.data.domain.Sort.Direction.DESC
import org.springframework.data.domain.Sort.Order

private const val NAME_PROPERTY = "name"

private val orderToEnumMap: Map<Order, ListSuppliersSortEnum> =
    ListSuppliersSortEnum.entries.associateBy { it.toSortOrder() }

fun KClass<ListSuppliersSortEnum>.fromSortOrder(sortOrder: Sort.Order): ListSuppliersSortEnum =
    orderToEnumMap[sortOrder] ?: throw IllegalArgumentException(
        "ListSuppliersSortEnum does not contain a value for: $sortOrder",
    )

fun ListSuppliersSortEnum.toSortOrder(): Order {
    fun ListSuppliersSortEnum.direction() = when (this) {
        PLUS_NAME -> ASC
        MINUS_NAME -> DESC
    }

    fun ListSuppliersSortEnum.property() = when (this) {
        PLUS_NAME, MINUS_NAME -> NAME_PROPERTY
    }

    return Order(this.direction(), this.property())
}
