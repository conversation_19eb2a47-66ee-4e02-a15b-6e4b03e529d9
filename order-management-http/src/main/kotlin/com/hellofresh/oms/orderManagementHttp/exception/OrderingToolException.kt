package com.hellofresh.oms.orderManagementHttp.exception

import com.hellofresh.oms.orderManagementHttp.client.tapioca.exception.TapiocaClientException

class OrderingToolException(errorMessage: String) : RuntimeException(errorMessage) {

    companion object {
        fun from(e: TapiocaClientException): OrderingToolException =
            OrderingToolException("""OT responded with error: ${e.message.orEmpty()}""")
    }
}
