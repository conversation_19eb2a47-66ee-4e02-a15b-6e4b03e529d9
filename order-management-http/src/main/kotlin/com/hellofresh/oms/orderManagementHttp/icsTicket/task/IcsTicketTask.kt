package com.hellofresh.oms.orderManagementHttp.icsTicket.task

import com.hellofresh.oms.orderManagementHttp.icsTicket.service.IcsTicketService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = "ics.tickets",
    name = ["enabled"],
    havingValue = "true",
)
class IcsTicketTask(
    private val icsTicketService: IcsTicketService,
    @Value("\${ics.tickets.page_size}") private val pageSize: Int,
) {

    @Scheduled(fixedRate = 1800000)
    fun processFetchIcsTickets() {
        val updatedSince = icsTicketService.resolveLatestFetchTime()

        var page = 1
        while (true) {
            val icsTickets = icsTicketService.fetchTickets(
                page = page,
                pageSize = pageSize,
                updatedSince = updatedSince,
            )
            if (icsTickets.isNullOrEmpty() || icsTickets.size < pageSize) {
                logger.info("No more tickets returned at page $page. Stopping.")
                break
            }
            page++
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
