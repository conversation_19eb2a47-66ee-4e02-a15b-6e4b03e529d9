package com.hellofresh.oms.orderManagementHttp.supplier.intake

import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.CROSSDOCK
import com.hellofresh.oms.model.ShipMethodEnum.FREIGHT_ON_BOARD
import com.hellofresh.oms.model.ShipMethodEnum.OTHER
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum as ShipMethodApi
import com.hellofresh.oms.orderManagement.generated.api.model.ShippingMethodResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierAddressDto
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierListApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierListApiResponsePageResult
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierStatusEnum
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.SupplierDto
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.SupplierListDto
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.SupplierShippingMethodDto
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.fromSortOrder
import kotlin.reflect.KClass

fun KClass<SupplierListApiResponsePageResult>.from(supplierListDto: SupplierListDto) =
    SupplierListApiResponsePageResult(
        number = supplierListDto.pageNumber,
        pageSize = supplierListDto.pageSize,
        totalElements = supplierListDto.totalElements.toInt(),
        totalPages = supplierListDto.totalPages,
        sort = supplierListDto.sort.get().toList().map { ListSuppliersSortEnum::class.fromSortOrder(it) },
    )

fun KClass<SupplierAddressDto>.from(
    supplierAddressDto: com.hellofresh.oms.orderManagementHttp.supplier.service.domain.SupplierAddressDto
): SupplierAddressDto = SupplierAddressDto(
    address = supplierAddressDto.address,
    city = supplierAddressDto.city,
    countryCode = supplierAddressDto.countryCode,
    number = supplierAddressDto.number,
    postalCode = supplierAddressDto.postalCode,
    region = supplierAddressDto.region,
)

fun KClass<SupplierResponse>.from(supplierDto: SupplierDto): SupplierResponse = supplierDto.run {
    SupplierResponse(
        id = id,
        code = code,
        address = SupplierAddressDto::class.from(address),
        currency = currency,
        market = market,
        name = name,
        parentId = parentId,
        status = SupplierStatusEnum.valueOf(status.name),
        type = type,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )
}

fun KClass<SupplierListApiResponse>.from(supplierListDto: SupplierListDto) = SupplierListApiResponse(
    suppliers = supplierListDto.suppliers.map { SupplierResponse::class.from(it) },
    pageResult = SupplierListApiResponsePageResult::class.from(supplierListDto),
)

fun KClass<ShipMethodApi>.from(shipMethod: ShipMethodEnum): ShipMethodApi =
    when (shipMethod) {
        VENDOR -> ShipMethodApi.VENDOR
        CROSSDOCK -> ShipMethodApi.CROSSDOCK
        FREIGHT_ON_BOARD -> ShipMethodApi.FREIGHT_ON_BOARD
        OTHER -> ShipMethodApi.OTHER
    }

fun KClass<ShippingMethodResponse>.from(shipMethod: SupplierShippingMethodDto) = shipMethod.run {
    ShippingMethodResponse(
        id = id,
        dcCode = dcCode,
        supplierId = supplierId,
        market = market,
        shippingMethod = ShipMethodApi::class.from(shippingMethod),
    )
}
