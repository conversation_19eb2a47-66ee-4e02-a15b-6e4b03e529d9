package com.hellofresh.oms.orderManagementHttp.sku

import com.hellofresh.oms.model.Sku
import java.util.UUID
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SkuRepository : JpaRepository<Sku, UUID> {
    @Query(
        "SELECT * FROM sku s " +
            "WHERE lower(s.market) = lower(:market) " +
            "AND (:codeName IS NULL " +
            "OR lower(s.name) LIKE lower(concat('%', :codeName,'%')) " +
            "OR lower(s.code) LIKE lower(concat('%', :codeName,'%')))",
        nativeQuery = true,
    )
    fun findAllByMarketAndCodeOrNameLikeCaseInsensitive(
        market: String,
        codeName: String?,
        pageable: Pageable,
    ): Page<Sku>

    @Query(
        """
        SELECT s.*
        FROM sku s
            LEFT JOIN (SELECT DISTINCT sku_id, uuid
                       FROM supplier_sku
                       WHERE uuid IN (:supplierSkuIds)) ss ON s.uuid = ss.sku_id
        WHERE lower(s.market) = lower(:market)
          AND (:includeAll = true OR ss.uuid IS NOT NULL)
          AND (:codeName IS NULL
            OR lower(s.name) LIKE lower(concat('%', :codeName, '%'))
            OR lower(s.code) LIKE lower(concat('%', :codeName, '%')))
          AND s.status != 'ARCHIVED'
        ORDER BY ss.uuid NULLS LAST;
        """,
        nativeQuery = true,
    )
    fun findAllSkusForMarketAndSupplier(
        market: String,
        codeName: String?,
        supplierSkuIds: List<UUID>,
        includeAll: Boolean,
        pageable: Pageable,
    ): Page<Sku>

    @Query(
        "SELECT DISTINCT s.category FROM Sku s WHERE lower(s.market) = lower(:market)",
    )
    fun findDistinctCategoryByMarket(market: String): Set<String>

    @Query(
        """
        SELECT sk.*
        FROM purchase_order po
            JOIN supplier su ON po.supplier_id = su.id
            JOIN sku sk ON sk.market = su.market
        WHERE sk.code IN (:skuCodes)
        AND po.po_number = :poNumber
        """,
        nativeQuery = true,
    )
    fun findSkuByPoNumberAndCodesIn(poNumber: String, skuCodes: Set<String>): List<Sku>
    fun findByCode(code: String): List<Sku>
}
