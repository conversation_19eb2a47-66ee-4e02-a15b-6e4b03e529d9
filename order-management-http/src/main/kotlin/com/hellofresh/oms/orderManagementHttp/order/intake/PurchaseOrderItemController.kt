package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrderItemsResponse
import com.hellofresh.oms.orderManagement.generated.api.model.OrderItemResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.PurchaseOrderItemApi
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class PurchaseOrderItemController(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val skuRepository: SkuRepository,
) : PurchaseOrderItemApi {
    override fun getOrderItems(poNumber: String): ResponseEntity<ListPurchaseOrderItemsResponse> {
        val purchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(poNumber)
            ?: return ResponseEntity.notFound().build()
        val skus = skuRepository.findAllById(purchaseOrder.orderItems.map { it.skuId })
        return ResponseEntity.ok(
            ListPurchaseOrderItemsResponse(
                purchaseOrder.orderItems.map {
                    val sku = skus.first { sku -> sku.uuid == it.skuId }
                    OrderItemResponse::class.from(it, sku)
                }.sortedBy { it.sku.name },
            ),
        )
    }
}
