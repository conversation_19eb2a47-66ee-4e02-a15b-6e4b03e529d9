package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewStatusEnum
import java.math.BigDecimal

private const val SUPPLIER_NAME_AUTOBAGGER = "Autobagger"
private const val STATUS_CLOSED = "Closed"
private const val STATUS_REJECTED = "Rejected"
private const val STATUS_CANCELLED = "Cancelled"
private const val STATUS_CLOSED_PARTIAL_REJECTION = "Closed - Partial Rejection"

@Suppress("ReturnCount")
fun calculateHjStatus(
    params: PoStatusParams
): PoOverviewStatusEnum {
    if (params.isVoided) return PoOverviewStatusEnum.VOIDED
    if (isAutobaggerReceived(params.supplierName, params.hjStatus)) return PoOverviewStatusEnum.RECEIVED_ACCURATE
    return when {
        params.hjStatus == STATUS_CLOSED -> handleClosedStatus(
            params.hjOverrideQuantity,
            params.hjReceiptClosedQuantity,
            params.quantityOrdered,
        )
        params.hjStatus == STATUS_REJECTED -> PoOverviewStatusEnum.DELIVERY_REJECTED
        params.hjStatus == STATUS_CANCELLED -> PoOverviewStatusEnum.RECEIPT_CANCELLED
        params.hjStatus == STATUS_CLOSED_PARTIAL_REJECTION -> PoOverviewStatusEnum.RECEIVED_PARTIAL_REJECTION
        params.isBlankReceipt -> handleNullOrEmptyStatus(params.isPastDue)
        else -> PoOverviewStatusEnum.IN_PROGRESS_HJ
    }
}

private fun isAutobaggerReceived(supplierName: String?, hjStatus: String?): Boolean =
    supplierName?.equals(SUPPLIER_NAME_AUTOBAGGER, ignoreCase = true) == true && (hjStatus == STATUS_CLOSED || hjStatus == STATUS_REJECTED)

private fun handleClosedStatus(
    hjOverrideQuantity: BigDecimal?,
    hjReceiptClosedQuantity: BigDecimal?,
    quantityOrdered: BigDecimal
): PoOverviewStatusEnum {
    val quantityReceived = hjOverrideQuantity ?: hjReceiptClosedQuantity ?: BigDecimal.ZERO
    return when {
        quantityReceived.compareTo(quantityOrdered) == 0 -> PoOverviewStatusEnum.RECEIVED_ACCURATE
        quantityReceived.compareTo(quantityOrdered) > 0 -> PoOverviewStatusEnum.RECEIVED_OVER
        else -> PoOverviewStatusEnum.RECEIVED_UNDER
    }
}

private fun handleNullOrEmptyStatus(isPastDue: Boolean): PoOverviewStatusEnum =
    if (isPastDue) PoOverviewStatusEnum.NOT_DELIVERED_PAST_DUE else PoOverviewStatusEnum.IN_PROGRESS_HJ

data class PoStatusParams(
    val isVoided: Boolean,
    val supplierName: String?,
    val hjStatus: String?,
    val hjOverrideQuantity: BigDecimal?,
    val hjReceiptClosedQuantity: BigDecimal?,
    val quantityOrdered: BigDecimal,
    val isPastDue: Boolean,
    val isBlankReceipt: Boolean,
)
