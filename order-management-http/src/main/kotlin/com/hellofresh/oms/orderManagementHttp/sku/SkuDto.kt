package com.hellofresh.oms.orderManagementHttp.sku

import java.util.UUID
import org.springframework.data.domain.Sort

data class SkuListDto(
    val pageNumber: Int,
    val pageSize: Int,
    val totalElements: Long,
    val totalPages: Int,
    val sort: Sort,
    val skus: List<SkuDto>,
)

data class SkuDto(
    val id: UUID,
    val name: String,
    val code: String,
    val market: String,
    val status: String,
)
