package com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField

import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import java.time.LocalDateTime
import java.util.UUID

data class ExpectedTimeField(
    val expectedStartTime: LocalDateTime,
    val expectedEndTime: LocalDateTime,
    val deliveryDateChangeReasonId: UUID?,
) : EditableField {
    fun isEqual(
        expectedStartTime: LocalDateTime,
        expectedEndTime: LocalDateTime,
        deliveryDateReasonId: UUID?
    ): Boolean =
        this.expectedStartTime == expectedStartTime &&
            this.expectedEndTime == expectedEndTime &&
            this.deliveryDateChangeReasonId == deliveryDateReasonId

    companion object {
        fun from(editPurchaseOrderRequest: EditPurchaseOrderRequest): ExpectedTimeField? =
            if (editPurchaseOrderRequest.deliveryWindow != null) {
                ExpectedTimeField(
                    expectedStartTime = editPurchaseOrderRequest.deliveryWindow.start,
                    expectedEndTime = editPurchaseOrderRequest.deliveryWindow.end,
                    deliveryDateChangeReasonId = editPurchaseOrderRequest.deliveryWindow.changeReasonId,
                )
            } else {
                null
            }
    }
}
