package com.hellofresh.oms.orderManagementHttp.imports.intake

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.PackagingType.CASE_TYPE
import com.hellofresh.oms.model.PackagingType.PALLET_TYPE
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.importHistory.ImportHistory
import com.hellofresh.oms.model.supplier.Supplier
import com.hellofresh.oms.orderManagement.generated.api.model.BatchCreatePurchaseOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.model.BatchImportOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.model.BatchOrderItemDto
import com.hellofresh.oms.orderManagement.generated.api.model.CasePackagingResponse
import com.hellofresh.oms.orderManagement.generated.api.model.DistributionCenterDto
import com.hellofresh.oms.orderManagement.generated.api.model.EmergencyReasonDto
import com.hellofresh.oms.orderManagement.generated.api.model.ListBatchImportOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum.CASE
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum.UNIT
import com.hellofresh.oms.orderManagement.generated.api.model.PageResultDto
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderPreviewDto
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum as ShipMethodEnumResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SkuDto
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierDto
import com.hellofresh.oms.orderManagement.generated.api.model.UnitPackagingResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.PurchaseOrderDto
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.PurchaseOrderItemDto
import com.hellofresh.oms.orderManagementHttp.imports.service.ImportHistoryService
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomain
import java.math.BigDecimal
import java.math.RoundingMode
import org.springframework.core.convert.converter.Converter
import org.springframework.data.domain.Page
import org.springframework.stereotype.Component

@Component
class BatchCreatePurchaseOrdersResponseConverter :
    Converter<List<PurchaseOrderDto>, BatchCreatePurchaseOrdersResponse> {
    override fun convert(source: List<PurchaseOrderDto>): BatchCreatePurchaseOrdersResponse =
        BatchCreatePurchaseOrdersResponse(
            source.map { purchaseOrder ->
                PurchaseOrderPreviewDto(
                    id = purchaseOrder.id,
                    poNumber = purchaseOrder.poNumber,
                    week = purchaseOrder.yearWeek.value,
                    distributionCenter = purchaseOrder.distributionCenter.toResponse(),
                    shippingMethod = purchaseOrder.shipMethod.toResponse(),
                    supplier = purchaseOrder.supplier.toResponse(),
                    deliveryWindow = purchaseOrder.deliveryWindow,
                    emergencyReason = purchaseOrder.emergencyReason.toResponse(),
                    comments = purchaseOrder.comments,
                    orderItems = purchaseOrder.orderItems.map { it.toResponse(purchaseOrder.supplier.currency) },
                )
            },
        )

    private fun PurchaseOrderItemDto.toResponse(currency: String) = BatchOrderItemDto(
        sku = sku.toResponse(),
        bufferPercent = bufferValue,
        packaging = when (packagingType) {
            CASE_TYPE -> CasePackagingResponse(
                packagingType = CASE,
                numberOfCases = orderSize.toInt(),
                unitsPerCase = caseSize,
                pricePerCase = price.toMoneyDto(currency),
                uom = uom.toResponse(),
            )

            PALLET_TYPE -> throw UnsupportedOperationException(
                "PALLET_TYPE is not supported at the moment in OT and will not be supported for now.",
            )

            UNIT_TYPE -> UnitPackagingResponse(
                packagingType = UNIT,
                numberOfUnits = orderSize.toInt(),
                pricePerUnit = price.toMoneyDto(currency),
            )
        },
    )

    private fun UOM.toResponse() = when (this) {
        UOM.KG -> UomEnum.KG
        UOM.LBS -> UomEnum.LBS
        UOM.L -> UomEnum.L
        UOM.GAL -> UomEnum.GAL
        UOM.OZ -> UomEnum.OZ
        UOM.UNIT -> UomEnum.UNIT
    }

    private fun BigDecimal.toMoneyDto(currency: String) = MoneyDto(
        amount = this.setScale(MoneyDomain.PRECISION.value, RoundingMode.HALF_EVEN).toPlainString(),
        currency = currency,
    )

    private fun EmergencyReason.toResponse() = EmergencyReasonDto(
        id = this.uuid,
        name = this.name,
    )

    private fun Supplier.toResponse() = SupplierDto(
        id = this.id,
        code = this.code.toString(),
        name = this.name,
    )

    private fun DistributionCenter.toResponse() = DistributionCenterDto(
        code = this.code,
        name = this.name,
    )

    private fun Sku.toResponse() = SkuDto(
        id = this.uuid,
        code = this.code,
        name = this.name,
    )

    private fun ShipMethodEnum.toResponse(): ShipMethodEnumResponse = when (this) {
        ShipMethodEnum.CROSSDOCK -> ShipMethodEnumResponse.CROSSDOCK
        ShipMethodEnum.FREIGHT_ON_BOARD -> ShipMethodEnumResponse.FREIGHT_ON_BOARD
        ShipMethodEnum.VENDOR -> ShipMethodEnumResponse.VENDOR
        ShipMethodEnum.OTHER -> ShipMethodEnumResponse.OTHER
    }
}

@Component
class BatchImportOrdersHistoryResponseConverter(
    private val importHistoryService: ImportHistoryService,
) : Converter<Page<ImportHistory>, ListBatchImportOrdersResponse> {
    override fun convert(source: Page<ImportHistory>): ListBatchImportOrdersResponse {
        val purchaseOrders = importHistoryService.getPurchaseOrdersByNumbers(
            source.content.flatMap { importHistory ->
                importHistory.getSummary().purchaseOrders
            },
        )

        val historyList = source.content
            .map {
                val poNumbers: List<String> = it.getSummary().purchaseOrders
                val filteredPurchaseOrders = purchaseOrders.filter { po -> poNumbers.contains(po.poNumber) }

                BatchImportOrdersResponse(
                    id = it.id,
                    filename = it.filename,
                    uploadedBy = it.userEmail,
                    uploadedAt = it.createdAt,
                    distributionCenters = filteredPurchaseOrders.map { po -> po.dcCode }.distinct(),
                    weeks = filteredPurchaseOrders.map { po -> po.yearWeek.toString() }.distinct(),
                    purchaseOrders = poNumbers,
                )
            }

        return ListBatchImportOrdersResponse(
            items = historyList,
            pageResult = PageResultDto(
                number = source.number,
                pageSize = source.size,
                totalElements = source.totalElements.toInt(),
                totalPages = source.totalPages,
            ),
        )
    }
}
