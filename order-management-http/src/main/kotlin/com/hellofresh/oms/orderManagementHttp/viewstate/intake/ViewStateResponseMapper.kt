package com.hellofresh.oms.orderManagementHttp.viewstate.intake

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.model.viewstate.ViewState
import com.hellofresh.oms.orderManagement.generated.api.model.CreateViewStateResponse
import com.hellofresh.oms.orderManagement.generated.api.model.ListViewStateResponse
import com.hellofresh.oms.orderManagement.generated.api.model.ViewStateListItem
import com.hellofresh.oms.orderManagement.generated.api.model.ViewStateResponse
import org.springframework.stereotype.Component

@Component
class ViewStateResponseMapper(private val objectMapper: ObjectMapper) {
    fun toCreateResponse(viewState: ViewState) = CreateViewStateResponse(
        requireNotNull(viewState.id) { VIEW_STATE_ID_NULL_ERROR },
    )

    fun toViewStateResponse(viewState: ViewState) = ViewStateResponse(
        id = requireNotNull(viewState.id) { VIEW_STATE_ID_NULL_ERROR },
        resource = viewState.resource,
        name = viewState.name,
        state = objectMapper.readValue(
            viewState.state,
            object : TypeReference<Map<String, Any>>() {}
        ),
    )

    fun toListViewStateResponse(viewStates: List<ViewState>) = ListViewStateResponse(
        items = viewStates.map { viewState ->
            ViewStateListItem(
                id = requireNotNull(viewState.id) { VIEW_STATE_ID_NULL_ERROR },
                name = viewState.name,
            )
        },
    )

    companion object {
        private const val VIEW_STATE_ID_NULL_ERROR = "ViewState ID must not be null!"
    }
}
