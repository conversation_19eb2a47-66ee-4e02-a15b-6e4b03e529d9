package com.hellofresh.oms.orderManagementHttp.supplier

import java.util.UUID
import org.springframework.data.domain.Sort

data class SupplierListDto(
    val pageNumber: Int,
    val pageSize: Int,
    val totalElements: Long,
    val totalPages: Int,
    val sort: Sort,
    val suppliers: List<SupplierDto>,
)

data class SupplierDto(
    val id: UUID,
    val name: String,
    val code: String,
    val market: String,
    val defaultShippingMethods: List<ShippingMethodDto>,
    val address: AddressDto,
    val currency: String,
)

data class ShippingMethodDto(
    val dcCode: String,
    val method: Method
)

data class AddressDto(
    val country: String,
    val city: String,
    val zipcode: String,
    val address: String,
    val number: String,
)

enum class Method(val value: String) {
    CROSSDOCK("CROSSDOCK"),
    FREIGHT_ON_BOARD("FREIGHT_ON_BOARD"),
    VEN<PERSON><PERSON>("VENDOR"),
    <PERSON><PERSON><PERSON>("OTHER")
}
