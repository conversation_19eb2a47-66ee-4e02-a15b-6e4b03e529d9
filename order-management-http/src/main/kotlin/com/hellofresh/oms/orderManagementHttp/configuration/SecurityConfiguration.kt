package com.hellofresh.oms.orderManagementHttp.configuration

import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import java.time.Instant
import java.util.UUID
import javax.crypto.spec.SecretKeySpec
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.core.annotation.Order
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.jose.jws.JwsAlgorithms
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter
import org.springframework.web.filter.OncePerRequestFilter

@Configuration
@Profile("!local")
@Order(1)
class SecurityConfiguration(
    @Value("\${spring.security.oauth2.resourceserver.jwt.secret-key}") private val jwtSecretKey: String,
    @Value("\${security.allowed-issuers}") private val allowedIssuers: String,
) {
    @Bean
    fun filterChain(
        http: HttpSecurity,
        jwtDecoderByJwkKeySetUri: JwtDecoder,
    ): SecurityFilterChain {
        http
            // disable CSRF since the requests are coming from different origins
            // and JWT token is always used for authentication
            .csrf { it.disable() }
            .authorizeHttpRequests { authz ->
                authz
                    // Paths that are intentionally left open
                    .requestMatchers(
                        "/swagger-ui/**",
                        "/swagger-resources/**",
                        "/api.yaml",
                        "/v3/api-docs",
                        "/v3/api-docs.yaml",
                        "/v3/api-docs/**",
                        "/actuator/health",
                        "/actuator/health/liveness",
                        "/actuator/health/readiness",
                        "/actuator/prometheus",
                    ).permitAll()
                    .anyRequest().authenticated()
            }
            .oauth2ResourceServer { oauth2 ->
                oauth2
                    .jwt { jwt ->
                        jwt.decoder(jwtDecoder(jwtDecoderByJwkKeySetUri))
                        jwt.jwtAuthenticationConverter(jwtAuthenticationConverter())
                    }
            }
            .addFilterBefore(
                IssuerValidationFilter("/service/", allowedIssuers.split(",")),
                BasicAuthenticationFilter::class.java
            )

        return http.build()
    }

    private fun jwtDecoder(jwtDecoderByJwkKeySetUri: JwtDecoder): JwtDecoder {
        val jwtDecoderByJwtSecretKey = NimbusJwtDecoder
            .withSecretKey(SecretKeySpec(jwtSecretKey.encodeToByteArray(), JwsAlgorithms.HS256))
            .build()

        return CustomJwtDecoder(jwtDecoderByJwkKeySetUri, jwtDecoderByJwtSecretKey)
    }

    /**
     The default implementation of JwtGrantedAuthoritiesConverter extracts the
     authorities from "scopes" or "scp" claim. In the token that we get from
     auth-service, the authorities are in the "roles" claim. That's why we need
     to provide a custom authentication converter which is configured to extract
     authorities from the roles claim.
     */
    private fun jwtAuthenticationConverter() = JwtAuthenticationConverter().apply {
        val jwtGrantedAuthoritiesConverter = JwtGrantedAuthoritiesConverter().apply {
            this.setAuthoritiesClaimName("roles")
            this.setAuthorityPrefix("ROLE_")
        }
        this.setJwtGrantedAuthoritiesConverter(jwtGrantedAuthoritiesConverter)
    }
}

@Configuration
@Profile("local")
@Order(0)
class LocalSecurityConfiguration {

    @Bean
    fun localFilterChain(http: HttpSecurity): SecurityFilterChain =
        http.csrf { it.disable() }
            .addFilterBefore(LocalUserInjectionFilter(), BasicAuthenticationFilter::class.java).build()

    class LocalUserInjectionFilter : OncePerRequestFilter() {
        override fun doFilterInternal(
            request: HttpServletRequest,
            response: HttpServletResponse,
            filterChain: FilterChain,
        ) {
            injectLocalUserInSecurityContext()
            filterChain.doFilter(request, response)
        }

        private fun injectLocalUserInSecurityContext() {
            val claims = mapOf(
                "sub" to UUID.randomUUID().toString(),
                "email" to "<EMAIL>",
            )
            val jwtToken =
                Jwts.builder()
                    .signWith(
                        Keys.hmacShaKeyFor("some_long_secret_key_at_least_256_bits".toByteArray()),
                        Jwts.SIG.HS256,
                    )
                    .claims(claims)
                    .compact()
            val jwt = Jwt(
                "Bearer $jwtToken",
                Instant.now(),
                Instant.now().plusSeconds(TOKEN_VALIDITY_SECONDS),
                claims,
                claims,
            )

            SecurityContextHolder.setContext(
                SecurityContextHolder.createEmptyContext().apply {
                    authentication = JwtAuthenticationToken(jwt).apply { isAuthenticated = true }
                },
            )
        }

        companion object {
            const val TOKEN_VALIDITY_SECONDS = 100L
        }
    }
}
