package com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.service

import com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.out.GoodsReceivedNoteRepository
import org.springframework.stereotype.Service

@Service
class GoodsReceivedNoteService(private val goodsReceivedNoteRepository: GoodsReceivedNoteRepository) {
    fun getGoodsReceivedNote(poNumber: String) = goodsReceivedNoteRepository.findByPoNumber(poNumber)
    fun getGoodsReceivedNotes(poNumbers: List<String>) = goodsReceivedNoteRepository.findAllByPoNumberIn(poNumbers)
}
