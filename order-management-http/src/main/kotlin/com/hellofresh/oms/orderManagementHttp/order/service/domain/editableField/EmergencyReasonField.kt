package com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField

import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import java.util.UUID

data class EmergencyReasonField(val emergencyReasonId: UUID) : EditableField {
    fun isEqual(emergencyReasonUuid: UUID?): Boolean =
        this.emergencyReasonId == emergencyReasonUuid

    companion object {
        fun from(
            editPurchaseOrderRequest: EditPurchaseOrderRequest
        ): EmergencyReasonField? = editPurchaseOrderRequest.emergencyReasonId?.let {
            EmergencyReasonField(it)
        }
    }
}
