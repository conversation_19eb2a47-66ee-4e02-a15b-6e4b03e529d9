package com.hellofresh.oms.orderManagementHttp.imports.validator

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.LineError
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.BUFFER_VALUE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.CASE_SIZE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.CASE_UOM_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.DELIVERY_DATE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.END_TIME_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.ORDER_SIZE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.ORDER_UNIT_VALUE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.PRICE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.SHIP_METHOD_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.START_TIME_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderHeader.WEEK_VALUE_KEY
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchOrderRowDto
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierSimpleRepository
import com.hellofresh.oms.util.Calculator.PRECISION
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.RoundingMode.HALF_EVEN
import java.math.RoundingMode.UP
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import kotlin.Result.Companion.failure
import kotlin.Result.Companion.success
import kotlin.enums.EnumEntries
import org.springframework.stereotype.Component
import org.springframework.web.context.annotation.RequestScope

/**
 * This class was "translated" from `HelloFresh\Infrastructure\Importer\Strategy\BulkEpoImporterStrategy` existing in OT.
 * @see
 * <a href="https://github.com/hellofresh/tapioca/blob/master/src/HelloFresh/Infrastructure/Importer/Strategy/BulkEpoImporterStrategy.php">
 *     BulkEpoImportStrategy
 * </a>
 */
@Component
@RequestScope
@Suppress("TooManyFunctions", "MaxLineLength")
class BatchCreatePurchaseOrderValidator(
    private val distributionCentersRepository: DistributionCentersRepository,
    private val supplierRepository: SupplierSimpleRepository,
    private val emergencyReasonRepository: EmergencyReasonRepository,
    private val skuRepository: SkuRepository,
) {
    fun validate(
        parsedLines: List<BatchOrderRowDto>,
        allowedMarkets: List<String>,
    ): BatchCreatePurchaseOrderValidatorResponse {
        val validatedLines = parsedLines.map { it.validate(allowedMarkets) }

        return BatchCreatePurchaseOrderValidatorResponse(
            validLines = validatedLines.filter { !it.hasErrors() }.map { it.toDto() },
            lineErrors = validatedLines.flatMap { it.collectErrors() }
                .map { it.toLineErrorDto() }.toSet(),
        )
    }

    /**
     * Part of the logic in here was translated from `BulkEpoImporterStrategy->validateBulkEpoView` existing in OT
     */
    private fun BatchOrderRowDto.validate(allowedMarkets: List<String>): BatchOrderRowResultDto {
        val distributionCenter = distributionCentersRepository.findByCode(distributionCenterValue)
        val suppliersByCode = supplierRepository.findByCode(supplierCodeValue)
        val skusByCode = skuRepository.findByCode(skuValue)
        val packagingType = validateEnum(orderUnitValue, ORDER_UNIT_VALUE_KEY, BatchRowPackagingType.entries)
        val buffer = validateNonNegativeNumber(bufferValue, BUFFER_VALUE_KEY)
        return BatchOrderRowResultDto(
            rowNumber = this.rowNumber,
            startTime = validateDateTimeField(startTime, START_TIME_KEY, TIME_FORMAT) {
                LocalTime.parse(it, DateTimeFormatter.ofPattern(TIME_FORMAT))
            },
            endTime = validateDateTimeField(endTime, END_TIME_KEY, TIME_FORMAT) {
                LocalTime.parse(it, DateTimeFormatter.ofPattern(TIME_FORMAT))
            },
            deliveryDate = validateDeliveryDate(deliveryDate),
            yearWeek = validateDateTimeField(weekValue, WEEK_VALUE_KEY, YearWeek.YEAR_WEEK_PATTERN) {
                YearWeek(it)
            },
            orderSize = validateOrderSize(buffer.getOrNull()),
            bufferValue = buffer,
            price = validateNonNegativeNumber(price, PRICE_KEY),
            caseSize = validateCaseSize(packagingType.getOrNull()),
            packagingType = packagingType,
            uom = validateEnum(caseUom, CASE_UOM_KEY, BatchRowUom.entries),
            shipMethod = validateEnum(shipMethod, SHIP_METHOD_KEY, BatchRowShipMethod.entries),
            distributionCenter = validateDistributionCenter(
                distributionCenter,
                distributionCenterValue,
                allowedMarkets,
            ),
            supplier = validateEntityByMarket(
                suppliersByCode,
                distributionCenter,
                "supplier",
                supplierCodeValue,
            ) { it.market },
            emergencyReason = validateReason(reason, distributionCenter),
            sku = validateEntityByMarket(
                skusByCode,
                distributionCenter,
                "sku",
                skuValue,
            ) { it.market },
            comments = validateComments(comments),
        )
    }

    @Suppress("SwallowedException")
    private fun <T> BatchOrderRowDto.validateDateTimeField(
        value: String,
        key: BatchOrderHeader,
        format: String,
        parseFunction: (String) -> T
    ): Result<T> = try {
        success(parseFunction(value))
    } catch (e: DateTimeParseException) {
        failure(lineValidationError("""value for "${key.headerName}" must be in the correct format "$format""""))
    }

    private fun BatchOrderRowDto.validateDeliveryDate(
        deliveryDate: String,
    ): Result<LocalDate> {
        val parsedDate = validateDateTimeField(deliveryDate, DELIVERY_DATE_KEY, DATE_FORMAT) {
            LocalDate.parse(it, DateTimeFormatter.ISO_LOCAL_DATE)
        }.getOrElse { return failure(it) }

        return if (parsedDate.isBefore(LocalDate.now())) {
            failure(
                lineValidationError(
                    """value for "${DELIVERY_DATE_KEY.headerName}" must be today's date or a future date""",
                ),
            )
        } else {
            success(parsedDate)
        }
    }

    private fun BatchOrderRowDto.validateNonNegativeNumber(value: String, key: BatchOrderHeader) =
        value.toBigDecimalOrNull()?.takeIf { it >= BigDecimal.ZERO }
            ?.let { success(it) }
            ?: failure(lineValidationError("""value for "${key.headerName}" must be zero or a positive number"""))

    private fun BatchOrderRowDto.validatePositiveNumber(value: String, key: BatchOrderHeader) =
        value.toBigDecimalOrNull()?.takeIf { it > BigDecimal.ZERO }
            ?.let { success(it) }
            ?: failure(lineValidationError("""value for "${key.headerName}" must be a positive number"""))

    /**
     * caseSize is only required when the packaging type is CASE_TYPE
     * This was translated from `BulkEpoImporterStrategy->validateBulkEpoView` existing in OT
     */
    private fun BatchOrderRowDto.validateCaseSize(packagingType: BatchRowPackagingType?): Result<BigDecimal> =
        if (packagingType == BatchRowPackagingType.CASE_TYPE) {
            validatePositiveNumber(caseSize, CASE_SIZE_KEY)
        } else {
            // If the packaging type is not CASE_TYPE, then caseSize is not required (ignored)
            success(BigDecimal.ZERO)
        }

    private fun <T> BatchOrderRowDto.validateEnum(
        value: String,
        key: BatchOrderHeader,
        enumEntries: EnumEntries<T>
    ): Result<T> where T : Enum<T>, T : ImportEnum =
        enumEntries.find { it.toString().equals(value, true) }?.let {
            success(it)
        } ?: failure(
            lineValidationError(
                """value for "${key.headerName}" must be one of [${enumEntries.filter { !it.alias }.joinToString(", ")}]"""
                    .trimMargin(),
            ),
        )

    private fun <T> BatchOrderRowDto.validateEntityByMarket(
        entities: List<T>,
        distributionCenter: DistributionCenter?,
        entityName: String,
        entityValue: String,
        marketExtractor: (T) -> String
    ): Result<T> = entities.takeIf { it.isNotEmpty() }?.let { list ->
        distributionCenter?.let { dc ->
            list.find { marketExtractor(it) == dc.market }?.let {
                success(it)
            } ?: failure(
                LineValidationError(
                    rowNumber,
                    """$entityName "$entityValue" does not exist in market "${dc.market}".
                |Market is defined by the given distribution center
                    """.trimMargin(),
                ),
            )
        } ?: failure(
            LineValidationError(
                rowNumber, """distribution center "$distributionCenterValue" does not exist""",
            ),
        )
    } ?: failure(
        lineValidationError(
            """$entityName "$entityValue" does not exist""",
        ),
    )

    private fun BatchOrderRowDto.validateReason(
        reason: String,
        distributionCenter: DistributionCenter?
    ): Result<EmergencyReason> {
        distributionCenter ?: return failure(lineValidationError("""distribution center "$distributionCenterValue" does not exist"""))
        val reasons = emergencyReasonRepository
            .findAllByMarketIgnoreCaseAndDisabledIsFalse(distributionCenter.market)

        val error = lineValidationError(
            message = "emergency reason \"${reason}\" does not exist or is disabled. " +
                "Value for reason must be one of [${reasons.joinToString(",") { it.name }}]" +
                " in ${distributionCenter.market.uppercase()} market",
        )
        val reasonEntity: EmergencyReason? = reasons.find { it.name.equals(reason, ignoreCase = true) }
        return reasonEntity?.let { success(it) } ?: failure(error)
    }

    @Suppress("MaxLineLength")
    private fun BatchOrderRowDto.validateDistributionCenter(
        entity: DistributionCenter?,
        entityValue: String,
        allowedMarkets: List<String>
    ): Result<DistributionCenter> =
        entity?.let {
            if (allowedMarkets.contains(entity.market.lowercase())) {
                success(it)
            } else {
                failure(
                    lineValidationError(
                        """user has no permission on the distribution center market. DistributionCenter = "$entityValue", Market = "${entity.market}"""",
                    ),
                )
            }
        } ?: failure(
            lineValidationError("""distribution center "$entityValue" does not exist"""),
        )

    private fun validateComments(comments: String) =
        if (comments.length <= COMMENTS_MAX_LENGTH) {
            success(comments)
        } else {
            success(comments.substring(0, COMMENTS_MAX_LENGTH))
        }

    private fun BatchOrderRowDto.lineValidationError(message: String) = LineValidationError(rowNumber, message)

    private fun BatchOrderRowDto.validateOrderSize(buffer: BigDecimal?): Result<BigDecimal> {
        val validatedOrderSize = validatePositiveNumber(orderSize, ORDER_SIZE_KEY)
        return if (buffer != null && validatedOrderSize.isSuccess) {
            success(getOrderSizeWithBuffer(validatedOrderSize.getOrThrow(), buffer))
        } else {
            validatedOrderSize
        }
    }

    fun getOrderSizeWithBuffer(
        orderSize: BigDecimal,
        buffer: BigDecimal,
    ): BigDecimal =
        orderSize.times(buffer.divide(BigDecimal(HUNDRED), PRECISION, HALF_EVEN).plus(ONE))
            .setScale(QTD_PRECISION, UP)

    class LineValidationError(private val rowNumber: Int, message: String) : Throwable(message) {
        fun toLineErrorDto() = LineError(rowNumber, message.orEmpty())
    }

    companion object {
        private const val DATE_FORMAT = "yyyy-MM-dd"
        private const val TIME_FORMAT = "H:m"
        private const val COMMENTS_MAX_LENGTH = 240
        private const val HUNDRED = 100
        private const val QTD_PRECISION = 0
    }
}

data class BatchCreatePurchaseOrderValidatorResponse(
    val validLines: List<BatchOrderRowValidatedDto>,
    val lineErrors: Set<LineError>,
)
