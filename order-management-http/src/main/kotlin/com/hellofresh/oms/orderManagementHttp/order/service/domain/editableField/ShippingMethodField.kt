package com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField

import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import com.hellofresh.oms.orderManagementHttp.order.service.domain.toModel

data class ShippingMethodField(val shipMethod: ShipMethodEnum) : EditableField {
    fun isEqual(shippingMethod: ShipMethodEnum): Boolean = this.shipMethod == shippingMethod

    companion object {
        fun from(
            editPurchaseOrderRequest: EditPurchaseOrderRequest
        ): ShippingMethodField? = editPurchaseOrderRequest.shippingMethod?.let {
            ShippingMethodField(shipMethod = it.toModel())
        }
    }
}
