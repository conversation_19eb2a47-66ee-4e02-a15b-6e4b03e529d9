package com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrderBreakdownItem as TransferOrderBreakdownItemDto
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.transferOrder.TransferOrderOverviewModel
import java.math.BigDecimal
import java.math.RoundingMode

fun TransferOrderOverviewModel.mapToTransferOrderOverviewModelToApiResponse(): TransferOrdersOverviewResponseInner =
    TransferOrdersOverviewResponseInner(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        status = calculateStatus(
            this.transferOrderBreakdown.map { it.status },
            this.totalReceivedQuantity,
            this.totalCasesReceived
        ),
        skuCode = this.skuCode,
        sourceDcCode = this.sourceDcCode,
        skuName = this.skuName,
        category = this.category,
        skuUom = this.skuUom,
        packagingType = when (this.packagingType.lowercase()) {
            "case" -> TransferOrdersOverviewResponseInner.PackagingType.CASE
            "unit" -> TransferOrdersOverviewResponseInner.PackagingType.UNIT
            else -> TransferOrdersOverviewResponseInner.PackagingType.UNIT
        },
        totalOrderedQuantity = this.totalOrderedQuantity,
        totalReceivedQuantity = this.totalReceivedQuantity,
        totalCasesReceived = this.totalCasesReceived,
        totalPriceOrdered = this.totalPriceOrdered,
        totalPriceReceived = this.totalPriceReceived,
        weightedAvgCasePrice = this.weightedAvgCasePrice,
        weightedAvgCaseSize = this.weightedAvgCaseSize,
        weightedAvgCaseSizeReceived = this.weightedAvgCaseSizeReceived,
        assignedBuyerFirstName = this.assignedBuyerFirstName,
        assignedBuyerLastName = this.assignedBuyerLastName,
        transferOrderBreakdown = this.transferOrderBreakdown.map { it.mapToTransferOrderBreakdownItemDto() }
    )

fun TransferOrderBreakdownItem.mapToTransferOrderBreakdownItemDto(): TransferOrderBreakdownItemDto =
    TransferOrderBreakdownItemDto(
        status = this.status,
        destinationDcCode = this.destinationDcCode,
        week = this.week.value,
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived,
        caseSize = this.caseSize,
        totalQuantity = this.totalQuantity,
        casePrice = this.casePrice,
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod
    )

fun List<TransferOrderStatusDetails>.mapToTransferOrderOverviewModel(): TransferOrderOverviewModel {
    val first = this.first()

    val totalOrderedQuantity = this.sumOf { it.quantityOrdered }
    val totalReceivedQuantity = this.mapNotNull { it.quantityReceived }.takeIf { it.isNotEmpty() }?.sumOf { it }
    val totalCasesReceived = this.mapNotNull { it.casesReceived }.takeIf { it.isNotEmpty() }?.sum()
    val weightedAvgCaseSize = calculateWeightedAverage(
        this.mapNotNull { it.caseSize?.toBigDecimal()?.let { size -> size to it.quantityOrdered.toBigDecimal() } }
    )
    val weightedAvgCaseSizeReceived = calculateWeightedAverage(
        this.mapNotNull { it.caseSize?.toBigDecimal()?.let { size -> size to (it.quantityReceived ?: BigDecimal.ZERO) } }
    )
    return TransferOrderOverviewModel(
        transferOrderId = first.transferOrderId,
        transferOrderNumber = first.transferOrderNumber,
        skuCode = first.skuCode,
        sourceDcCode = first.sourceDcCode,
        skuName = first.skuName,
        category = first.category,
        skuUom = first.skuUom,
        packagingType = first.packagingType,
        totalOrderedQuantity = totalOrderedQuantity.toBigDecimal(),
        totalReceivedQuantity = totalReceivedQuantity,
        totalCasesReceived = totalCasesReceived?.toInt(),
        totalPriceOrdered = first.totalPrice.toString(),
        totalPriceReceived = if (totalCasesReceived != null) {
            this.first().casePrice.times(totalCasesReceived).toString()
        } else null,
        weightedAvgCasePrice = this.first().casePrice.toString(),
        weightedAvgCaseSize = weightedAvgCaseSize,
        weightedAvgCaseSizeReceived = weightedAvgCaseSizeReceived,
        assignedBuyerFirstName = first.assignedBuyerFirstName,
        assignedBuyerLastName = first.assignedBuyerLastName,
        transferOrderBreakdown = this.map { it.mapToTransferOrderBreakdownItem() }
    )
}

fun TransferOrderStatusDetails.mapToTransferOrderBreakdownItem(): TransferOrderBreakdownItem =
    TransferOrderBreakdownItem(
        status = this.receiptStatus,
        destinationDcCode = this.destinationDcCode,
        week = YearWeek(this.week),
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived?.toInt(),
        caseSize = this.caseSize?.toBigDecimal(),
        totalQuantity = this.quantityOrdered.toBigDecimal(),
        casePrice = this.casePrice.toString(),
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod
    )

private fun calculateWeightedAverage(values: List<Pair<BigDecimal, BigDecimal>>): BigDecimal? {
    val totalWeight = values.sumOf { it.second }

    if (values.isEmpty() || totalWeight == BigDecimal.ZERO) return null

    val weightedSum = values.sumOf { (value, weight) -> value * weight }
    return weightedSum.divide(totalWeight, 2, RoundingMode.HALF_UP)
}

private fun calculateConsolidatedStatus(
    transferOrderDetails: List<TransferOrderStatusDetails>,
    totalOrderedQuantity: BigDecimal,
    totalReceivedQuantity: BigDecimal?
): String {
    val receiptStatuses = transferOrderDetails.map { it.receiptStatus }

    // Rule 1: If any line has "Delivery Rejected" or "Received - Partial Rejection",
    // then consolidated status is "Received - Partial Rejection"
    if (receiptStatuses.any { it == "Delivery Rejected" || it == "Received - Partial Rejection" }) {
        // Rule 2: If ALL lines have "Delivery Rejected", then status is "Delivery Rejected"
        if (receiptStatuses.all { it == "Delivery Rejected" }) {
            return "Delivery Rejected"
        }
        return "Received - Partial Rejection"
    }

    // Rules 3-5: Compare received vs ordered quantities
    if (totalReceivedQuantity != null) {
        return when {
            totalReceivedQuantity < totalOrderedQuantity -> "Received - Under"
            totalReceivedQuantity > totalOrderedQuantity -> "Received - Over"
            totalReceivedQuantity == totalOrderedQuantity -> "Received - Accurate"
            else -> "Unknown"
        }
    }

    // Default status if no received quantity
    return "Pending"
}

private fun calculateStatus(
    statuses: List<String>,
    totalReceivedQuantity: BigDecimal?,
    totalCasesReceived: Int?
): String {
    // This function is used for API response status calculation
    // You can implement similar logic here if needed for the API response
    return statuses.firstOrNull() ?: "Unknown"
}
