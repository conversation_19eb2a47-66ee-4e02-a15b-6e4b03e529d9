package com.hellofresh.oms.orderManagementHttp.order.service.domain

import com.hellofresh.oms.model.Money
import java.math.BigDecimal
import java.math.RoundingMode

class TotalPrice<T : MoneyDomainPrecision>(
    amount: BigDecimal,
    currency: String,
    precision: T
) : MoneyDomain<T>(amount, currency, precision) {

    companion object {
        fun <T : MoneyDomainPrecision> from(
            numberOfItemsToPurchase: Int,
            pricePerItem: BigDecimal,
            currency: String,
            precision: T
        ): TotalPrice<T> {
            val caseOrUnitItemTotalPrice =
                BigDecimal(numberOfItemsToPurchase)
                    .times(pricePerItem)
                    .setScale(precision.value, RoundingMode.HALF_EVEN)

            return TotalPrice(
                amount = caseOrUnitItemTotalPrice,
                currency = currency,
                precision = precision,
            )
        }

        fun <T : MoneyDomainPrecision> from(money: Money, precision: T): TotalPrice<T> = TotalPrice<T>(
            money.amount.setScale(precision.value, RoundingMode.HALF_EVEN),
            money.currency,
            precision,
        )
    }
}
