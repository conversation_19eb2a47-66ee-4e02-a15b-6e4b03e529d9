package com.hellofresh.oms.orderManagementHttp.category.intake

import com.hellofresh.oms.orderManagement.generated.api.model.CategoryListApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.CategoryResponse
import org.springframework.core.convert.converter.Converter
import org.springframework.stereotype.Component

@Component
class CategoryResponseConverter : Converter<Set<String>, CategoryListApiResponse> {
    override fun convert(categories: Set<String>): CategoryListApiResponse =
        CategoryListApiResponse(
            items = categories.map { CategoryResponse(it) },
        )
}
