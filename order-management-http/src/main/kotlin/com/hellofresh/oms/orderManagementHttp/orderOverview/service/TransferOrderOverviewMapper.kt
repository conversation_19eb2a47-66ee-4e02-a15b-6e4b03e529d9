package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrderBreakdownItemDto
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewModel
import java.math.BigDecimal
import java.math.RoundingMode

fun TransferOrderOverviewModel.mapToTransferOrderOverviewModelToApiResponse(): TransferOrdersOverviewResponseInner =
    TransferOrdersOverviewResponseInner(
        skuCode = this.skuCode,
        sourceDcCode = this.sourceDcCode,
        skuName = this.skuName,
        category = this.category,
        skuUom = this.skuUom,
        packagingType = when (this.packagingType.lowercase()) {
            "case" -> TransferOrdersOverviewResponseInner.PackagingType.CASE
            "unit" -> TransferOrdersOverviewResponseInner.PackagingType.UNIT
            else -> TransferOrdersOverviewResponseInner.PackagingType.UNIT
        },
        totalOrderedQuantity = this.totalOrderedQuantity,
        totalReceivedQuantity = this.totalReceivedQuantity,
        totalCasesReceived = this.totalCasesReceived,
        totalPriceOrdered = this.totalPriceOrdered,
        totalPriceReceived = this.totalPriceReceived,
        weightedAvgCasePrice = this.weightedAvgCasePrice,
        weightedAvgCaseSize = this.weightedAvgCaseSize,
        weightedAvgCaseSizeReceived = this.weightedAvgCaseSizeReceived,
        assignedBuyerFirstName = this.assignedBuyerFirstName,
        assignedBuyerLastName = this.assignedBuyerLastName,
        transferOrderBreakdown = this.transferOrderBreakdown.map { it.mapToTransferOrderBreakdownItemDto() }
    )

fun TransferOrderBreakdownItem.mapToTransferOrderBreakdownItemDto(): TransferOrderBreakdownItemDto =
    TransferOrderBreakdownItemDto(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        status = this.status,
        destinationDcCode = this.destinationDcCode,
        week = this.week.value,
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived,
        caseSize = this.caseSize,
        totalQuantity = this.totalQuantity,
        casePrice = this.casePrice,
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod
    )

fun List<TransferOrderStatusDetails>.mapToTransferOrderOverviewModel(): TransferOrderOverviewModel {
    val first = this.first()
    
    // Calculate aggregated metrics
    val totalOrderedQuantity = this.sumOf { it.totalQuantity }
    val totalReceivedQuantity = this.mapNotNull { it.quantityReceived }.takeIf { it.isNotEmpty() }?.sumOf { it }
    val totalCasesReceived = this.mapNotNull { it.casesReceived }.takeIf { it.isNotEmpty() }?.sum()
    
    // Calculate weighted averages
    val weightedAvgCaseSize = calculateWeightedAverage(
        this.mapNotNull { details -> details.caseSize?.let { it to details.totalQuantity } }
    )
    val weightedAvgCaseSizeReceived = calculateWeightedAverage(
        this.mapNotNull { details -> 
            details.caseSize?.let { caseSize ->
                details.quantityReceived?.let { quantity ->
                    caseSize to quantity
                }
            }
        }
    )
    
    return TransferOrderOverviewModel(
        skuCode = first.skuCode,
        sourceDcCode = first.sourceDcCode,
        skuName = first.skuName,
        category = first.category,
        skuUom = first.skuUom,
        packagingType = first.packagingType,
        totalOrderedQuantity = totalOrderedQuantity,
        totalReceivedQuantity = totalReceivedQuantity,
        totalCasesReceived = totalCasesReceived,
        totalPriceOrdered = this.first().priceAmount, // Simplified for now
        totalPriceReceived = this.first().priceAmount, // Simplified for now
        weightedAvgCasePrice = this.first().priceAmount, // Simplified for now
        weightedAvgCaseSize = weightedAvgCaseSize,
        weightedAvgCaseSizeReceived = weightedAvgCaseSizeReceived,
        assignedBuyerFirstName = first.assignedBuyerFirstName,
        assignedBuyerLastName = first.assignedBuyerLastName,
        transferOrderBreakdown = this.map { it.mapToTransferOrderBreakdownItem() }
    )
}

fun TransferOrderStatusDetails.mapToTransferOrderBreakdownItem(): TransferOrderBreakdownItem =
    TransferOrderBreakdownItem(
        transferOrderId = this.transferOrderId,
        transferOrderNumber = this.transferOrderNumber,
        status = this.status,
        destinationDcCode = this.destinationDcCode,
        week = this.week,
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived,
        caseSize = this.caseSize,
        totalQuantity = this.totalQuantity,
        casePrice = this.priceAmount,
        reasonText = this.reasonText,
        shippingMethod = this.shippingMethod
    )

private fun calculateWeightedAverage(values: List<Pair<BigDecimal, BigDecimal>>): BigDecimal? {
    if (values.isEmpty()) return null
    
    val totalWeight = values.sumOf { it.second }
    if (totalWeight == BigDecimal.ZERO) return null
    
    val weightedSum = values.sumOf { (value, weight) -> value * weight }
    return weightedSum.divide(totalWeight, 2, RoundingMode.HALF_UP)
}
