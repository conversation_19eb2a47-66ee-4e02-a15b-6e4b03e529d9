package com.hellofresh.oms.orderManagementHttp.imports.intake

import com.hellofresh.oms.orderManagement.generated.api.routes.TemplatesApi
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.Resource
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class TemplatesController(
    @Value("classpath:/static/templates/batch_create_po.csv")
    private val batchCreatePoTemplate: Resource
) : TemplatesApi {
    override fun getBatchCreatePoTemplate(): ResponseEntity<Resource> =
        ResponseEntity.ok(batchCreatePoTemplate)
}
