package com.hellofresh.oms.orderManagementHttp.receiptOverride

import com.hellofresh.oms.model.imt.recipeOverride.ReceiptOverride
import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ReceiptOverrideRepository : JpaRepository<ReceiptOverride, UUID> {

    fun findByPoNumberAndSkuCode(poNumber: String, skuCode: String): ReceiptOverride?

    fun deleteByPoNumberAndSkuCode(poNumber: String, skuCode: String)
}
