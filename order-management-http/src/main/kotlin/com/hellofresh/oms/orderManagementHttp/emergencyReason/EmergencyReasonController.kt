package com.hellofresh.oms.orderManagementHttp.emergencyReason

import com.hellofresh.oms.orderManagement.generated.api.model.EmergencyReasonListApiResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.EmergencyReasonsApi
import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
class EmergencyReasonController(
    private val emergencyReasonService: EmergencyReasonService
) : EmergencyReasonsApi {

    override fun getEmergencyReasons(
        @NotNull
        @Valid
        @RequestParam(required = true, value = "market")
        market: String
    ): ResponseEntity<EmergencyReasonListApiResponse> = ResponseEntity.ok(
        EmergencyReasonListApiResponse(
            emergencyReasonService.findEnabledEmergencyReasonByMarket(market).mapToApiResponse(),
        ),
    )
}
