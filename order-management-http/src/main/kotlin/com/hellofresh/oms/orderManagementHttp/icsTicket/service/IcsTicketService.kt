package com.hellofresh.oms.orderManagementHttp.icsTicket.service

import com.hellofresh.oms.orderManagementHttp.icsTicket.IcsTicketRepository
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketDto
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketsDto
import io.micrometer.core.annotation.Timed
import java.time.Duration
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClient

@Service
class IcsTicketService(
    private val icsWebClient: WebClient,
    private val icsTicketRepository: IcsTicketRepository,
    @Value("\${ics.tickets.url}") private val icsTicketsUrl: String,
    @Value("\${ics.tickets.update_offset}") private val updateOffsetDuration: Duration,
) {

    @Timed
    fun getIcsTickets(poNumber: String) =
        icsTicketRepository.findByPoNumber(poNumber)

    @Timed
    fun fetchTickets(page: Int, pageSize: Int, updatedSince: String): List<IcsTicketDto>? {
        logger.debug("Fetching ICS tickets from $icsTicketsUrl, starting at page $page with size $pageSize.")

        val pagedUrl = "$icsTicketsUrl&per_page=$pageSize&page=$page&updated_since=$updatedSince&order_type=asc"
        val ticketsDto = icsWebClient.get()
            .uri(pagedUrl)
            .retrieve()
            .bodyToMono(IcsTicketsDto::class.java)
            .doOnError { ex -> logger.error("Error fetching ICS tickets: ${ex.message}", ex) }
            .block()

        val tickets = ticketsDto?.tickets?.filter {
            !it.customFields.po.isNullOrBlank()
        }

        if (!tickets.isNullOrEmpty()) {
            icsTicketRepository.upsertAll(tickets.map { it.toEntity() })

            logger.debug("Processed ${tickets.size} tickets out of ${ticketsDto.tickets.size} from page $page.")
        } else {
            logger.debug("No new tickets found for page $page.")
        }

        return ticketsDto?.tickets
    }

    fun resolveLatestFetchTime(): String {
        val lastUpdatedAt = icsTicketRepository.findLatestUpdatedAt()
        val updatedSince = DateTimeFormatter.ofPattern(UPDATED_SINCE_PATTERN)
            .format(lastUpdatedAt ?: LocalDateTime.now().minus(updateOffsetDuration.toMillis(), ChronoUnit.MILLIS))
        return updatedSince
    }

    companion object {
        const val UPDATED_SINCE_PATTERN = "yyyy-MM-dd"

        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
