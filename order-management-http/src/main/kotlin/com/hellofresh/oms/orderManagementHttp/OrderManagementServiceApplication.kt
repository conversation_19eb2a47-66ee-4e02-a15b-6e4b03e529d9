package com.hellofresh.oms.orderManagementHttp

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication
@EntityScan(basePackages = ["com.hellofresh.oms.model"])
@ConfigurationPropertiesScan
@EnableScheduling
class OrderManagementServiceApplication

@Suppress("SpreadOperator")
fun main(args: Array<String>) {
    runApplication<OrderManagementServiceApplication>(*args)
}
