package com.hellofresh.oms.orderManagementHttp.outbox.service

import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.OutboxItemStatus.FAILED
import com.hellofresh.oms.model.OutboxItemStatus.SENT
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.client.tapioca.TapiocaClient
import com.hellofresh.oms.orderManagementHttp.client.tapioca.domain.SendPurchaseOrderRequest
import com.hellofresh.oms.orderManagementHttp.client.tapioca.exception.TapiocaClientException
import com.hellofresh.oms.orderManagementHttp.exception.OrderingToolException
import com.hellofresh.oms.orderManagementHttp.exception.PurchaseOrderNotFoundException
import com.hellofresh.oms.orderManagementHttp.exception.PurchaseOrderNotSyncedException
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderEventType
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.outbox.out.OutboxRepository
import java.time.Clock
import java.time.LocalDateTime
import java.util.UUID
import org.springframework.stereotype.Service

@Service
class OutboxService(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val outboxRepository: OutboxRepository,
    private val tapiocaClient: TapiocaClient,
    private val purchaseOrderProducer: PurchaseOrderProducer,
    private val clock: Clock,
) {
    // Synchronous sending of PO
    fun sendOrder(poUuid: UUID, loggedInUser: LoggedInUserInfo): UUID {
        val po = purchaseOrderRepository.findById(poUuid).orElseThrow {
            PurchaseOrderNotFoundException(poUuid.toString())
        }

        if (!po.isSynced) {
            throw PurchaseOrderNotSyncedException(poUuid.toString())
        }

        return try {
            tapiocaClient.sendPurchaseOrder(SendPurchaseOrderRequest(poUuid, loggedInUser.userId))
            val persistedPo = purchaseOrderRepository.save(
                po.copy(
                    sendTime = LocalDateTime.now(clock),
                    isSynced = false,
                )
            )
            purchaseOrderProducer.publishPurchaseOrderEvent(persistedPo, PurchaseOrderEventType.STATUS_UPDATED)
            persistOutboxItemFor(po, loggedInUser, SENT).id
        } catch (c: TapiocaClientException) {
            persistOutboxItemFor(po, loggedInUser, FAILED)
            throw OrderingToolException.from(c)
        }
    }

    fun persistOutboxItemFor(
        po: PurchaseOrder,
        loggedInUser: LoggedInUserInfo,
        status: OutboxItemStatus,
    ): OutboxItem = outboxRepository.save(
        OutboxItem.createItem(
            po.id,
            po.poNumber,
            LocalDateTime.now(),
            loggedInUser.userEmail,
            loggedInUser.userId,
            po.version,
            status,
        ),
    )

    fun getOutboxItemsFor(poNumber: String): List<OutboxItem> =
        if (purchaseOrderRepository.existsByPoNumber(poNumber)) {
            outboxRepository.findAllByPoNumber(poNumber)
        } else {
            throw PurchaseOrderNotFoundException(poNumber)
        }

    fun getOutboxItemsFor(poNumbers: List<String>): List<OutboxItem> =
        outboxRepository.findAllByPoNumberIn(poNumbers)
            .groupBy { it.poNumber }
            .map {
                it.value.sortedBy { oi -> oi.createdAt }.reversed().first()
            }

    fun getLatestOutboxItemFor(poId: UUID): OutboxItem? =
        outboxRepository.findFirstByPoIdOrderByCreatedAtDesc(poId)
}
