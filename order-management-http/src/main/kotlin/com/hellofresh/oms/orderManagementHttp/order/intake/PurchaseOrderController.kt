package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.orderManagement.generated.api.model.CreatePurchaseOrderRequest
import com.hellofresh.oms.orderManagement.generated.api.model.CreatePurchaseOrderResponse
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusFilterEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderResponse
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderSendStatusFilterEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.SendBulkOrdersRequest
import com.hellofresh.oms.orderManagement.generated.api.model.ShippingMethodEnum
import com.hellofresh.oms.orderManagement.generated.api.routes.PurchaseOrderApi
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.authentication.getLoggedInUser
import com.hellofresh.oms.orderManagementHttp.exception.PurchaseOrderNotFoundException
import com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.service.GoodsReceivedNoteService
import com.hellofresh.oms.orderManagementHttp.order.service.CreatePurchaseOrderRequestDto
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderCreateService
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderDeleteService
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderService
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderUpdateService
import com.hellofresh.oms.orderManagementHttp.order.service.QueryPurchaseOrderService
import com.hellofresh.oms.orderManagementHttp.outbox.service.OutboxService
import com.hellofresh.oms.orderManagementHttp.supplier.service.SupplierService
import com.hellofresh.oms.orderManagementHttp.workerAction.SendOrderScheduler
import java.time.LocalDateTime
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus.NO_CONTENT
import org.springframework.http.ResponseEntity
import org.springframework.http.ResponseEntity.ok
import org.springframework.web.bind.annotation.RestController

@RestController
@Suppress("LongParameterList")
class PurchaseOrderController(
    private val outboxService: OutboxService,
    private val sendOrderScheduler: SendOrderScheduler,
    private val purchaseOrderService: PurchaseOrderService,
    private val queryPurchaseOrderService: QueryPurchaseOrderService,
    private val supplierService: SupplierService,
    private val goodsReceivedNoteService: GoodsReceivedNoteService,
    private val purchaseOrderCreateService: PurchaseOrderCreateService,
    private val purchaseOrderUpdateService: PurchaseOrderUpdateService,
    private val purchaseOrderDeleteService: PurchaseOrderDeleteService,
) : PurchaseOrderApi {
    override fun createOrder(
        createPurchaseOrderRequest: CreatePurchaseOrderRequest
    ): ResponseEntity<CreatePurchaseOrderResponse> {
        logger.info("Creating purchase order: user ${getLoggedInUser().userEmail}, $createPurchaseOrderRequest")
        val purchaseOrder = purchaseOrderCreateService.processPurchaseOrder(
            createPurchaseOrderRequest = createPurchaseOrderRequest.toDto(getLoggedInUser()),
            sendImmediately = false,
        )
        return ok(CreatePurchaseOrderResponse(purchaseOrder.id))
    }

    override fun updateOrder(
        poNumber: String,
        editPurchaseOrderRequest: EditPurchaseOrderRequest
    ): ResponseEntity<CreatePurchaseOrderResponse> {
        val orderId = purchaseOrderUpdateService.processEditPurchaseOrder(
            poNumber,
            editPurchaseOrderRequest,
            getLoggedInUser(),
        )
        return ok(CreatePurchaseOrderResponse(orderId))
    }

    override fun getOrders(
        dcWeeks: List<String>,
        dcCodes: List<String>,
        page: Int,
        size: Int,
        deliveryWindowStartFrom: LocalDateTime?,
        deliveryWindowStartTo: LocalDateTime?,
        sort: ListPurchaseOrdersSortEnum?,
        shippingMethod: ShippingMethodEnum?,
        categories: List<String>?,
        supplierIds: List<UUID>?,
        poNumber: String?,
        status: PurchaseOrderStatusEnum?,
        type: PurchaseOrderTypeEnum?,
        userEmail: String?,
        sku: String?,
        sendStatus: PurchaseOrderSendStatusFilterEnum?,
        grnStatus: PurchaseOrderGrnStatusFilterEnum?,
    ): ResponseEntity<ListPurchaseOrdersResponse> {
        val yearWeeks = dcWeeks.map { YearWeek(it) }
        val purchaseOrderPage =
            queryPurchaseOrderService.paginateBy(
                page = page,
                size = size,
                dcWeeks = yearWeeks,
                dcCodes = dcCodes,
                deliveryWindowStartFrom = deliveryWindowStartFrom,
                deliveryWindowStartTo = deliveryWindowStartTo,
                shippingMethod = shippingMethod,
                sort = sort,
                categories = categories,
                supplierIds = supplierIds,
                poNumber = poNumber,
                status = status,
                type = type,
                userEmail = userEmail,
                sku = sku,
                sendStatus = sendStatus,
                grnStatus = grnStatus,
            )
        val suppliers: List<SupplierExtended> = supplierService.getSuppliersByIds(
            purchaseOrderPage.map { it.supplierId }.toList(),
        )
        val outboxItems = outboxService.getOutboxItemsFor(
            purchaseOrderPage.map { it.poNumber }.toList(),
        )
        val grnStatusList = goodsReceivedNoteService.getGoodsReceivedNotes(
            purchaseOrderPage.map { it.poNumber }.toList(),
        )
        return ok(ListPurchaseOrdersResponse::class.from(purchaseOrderPage, suppliers, outboxItems, grnStatusList))
    }

    override fun deleteOrder(poNumber: String): ResponseEntity<Unit> {
        try {
            purchaseOrderDeleteService.processDeletePurchaseOrder(poNumber, getLoggedInUser())
        } catch (_: PurchaseOrderNotFoundException) {
            logger.warn("Purchase order not found: $poNumber")
        }

        return ResponseEntity(
            NO_CONTENT,
        )
    }

    override fun getOrder(poNumber: String): ResponseEntity<PurchaseOrderResponse> =
        purchaseOrderService.findLatestPurchaseOrderByPoNumber(poNumber)?.let { purchaseOrder ->
            val supplier = supplierService.getSupplierById(purchaseOrder.supplierId)
            val outboxItem = outboxService.getLatestOutboxItemFor(purchaseOrder.id)
            val grnStatus = goodsReceivedNoteService.getGoodsReceivedNote(purchaseOrder.poNumber)
            ok(PurchaseOrderResponse::class.from(purchaseOrder, supplier, outboxItem, grnStatus))
        } ?: ResponseEntity.notFound().build()

    override fun sendOrders(poNumber: String, poUuid: UUID): ResponseEntity<Unit> {
        outboxService.sendOrder(poUuid, getLoggedInUser())
        return ok(Unit)
    }

    override fun sendBulkOrders(sendBulkOrdersRequest: SendBulkOrdersRequest): ResponseEntity<Unit> {
        sendOrderScheduler.queueBulkOrders(sendBulkOrdersRequest, getLoggedInUser())
        return ok(Unit)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}

private fun CreatePurchaseOrderRequest.toDto(loggedInUserInfo: LoggedInUserInfo) = CreatePurchaseOrderRequestDto(
    deliveryWindow = this.deliveryWindow,
    orderItems = this.orderItems,
    deliveryAddress = this.deliveryAddress,
    dcCode = this.dcCode,
    dcWeek = this.dcWeek,
    supplierId = this.supplierId,
    shippingMethod = this.shippingMethod,
    emergencyReasonId = this.emergencyReasonId,
    comment = this.comment?.ifEmpty { null },
    userId = loggedInUserInfo.userId,
    userEmail = loggedInUserInfo.userEmail,
    origin = Origin.MANUAL,
)
