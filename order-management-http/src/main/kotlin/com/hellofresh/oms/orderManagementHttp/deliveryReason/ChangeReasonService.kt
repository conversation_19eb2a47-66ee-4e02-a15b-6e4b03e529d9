package com.hellofresh.oms.orderManagementHttp.deliveryReason

import com.hellofresh.oms.model.ChangeReasonType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ChangeReasonService(
    @Autowired private val repository: ChangeReasonRepository
) {
    fun findAllBy(changeReasonType: ChangeReasonType, allowedMarkets: String?) =
        if (allowedMarkets.isNullOrBlank()) {
            repository.findAllByReasonType(changeReasonType)
        } else {
            repository.findAllByReasonTypeAndAllowedMarkets(changeReasonType.name, allowedMarkets)
        }
}
