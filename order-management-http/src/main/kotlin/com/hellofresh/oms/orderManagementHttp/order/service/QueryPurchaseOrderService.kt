package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusFilterEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderSendStatusFilterEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShippingMethodEnum
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepositoryImpl
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepositoryImpl.PurchaseOrderFilter
import java.time.LocalDateTime
import java.util.UUID
import org.springframework.data.domain.Page
import org.springframework.stereotype.Service

@Service
class QueryPurchaseOrderService(
    private val purchaseOrderRepository: PurchaseOrderRepositoryImpl,
) {
    @Suppress("LongParameterList")
    fun paginateBy(
        page: Int,
        size: Int,
        dcWeeks: List<YearWeek>,
        dcCodes: List<String>,
        deliveryWindowStartFrom: LocalDateTime?,
        deliveryWindowStartTo: LocalDateTime?,
        sort: ListPurchaseOrdersSortEnum?,
        shippingMethod: ShippingMethodEnum?,
        categories: List<String>?,
        supplierIds: List<UUID>?,
        poNumber: String?,
        status: PurchaseOrderStatusEnum?,
        type: PurchaseOrderTypeEnum?,
        userEmail: String?,
        sku: String?,
        sendStatus: PurchaseOrderSendStatusFilterEnum?,
        grnStatus: PurchaseOrderGrnStatusFilterEnum?
    ): Page<PurchaseOrder> =
        purchaseOrderRepository.findAllPurchaseOrdersPaginated(
            PurchaseOrderFilter(
                page = page,
                size = size,
                dcWeeks = dcWeeks,
                dcCodes = dcCodes,
                deliveryWindowStartFrom = deliveryWindowStartFrom,
                deliveryWindowStartTo = deliveryWindowStartTo,
                shippingMethod = shippingMethod,
                categories = categories,
                supplierIds = supplierIds,
                poNumber = poNumber,
                status = status,
                type = type,
                sort = sort,
                userEmail = userEmail,
                sku = sku,
                sendStatus = sendStatus,
                grnStatus = grnStatus,
            ),
        )
}
