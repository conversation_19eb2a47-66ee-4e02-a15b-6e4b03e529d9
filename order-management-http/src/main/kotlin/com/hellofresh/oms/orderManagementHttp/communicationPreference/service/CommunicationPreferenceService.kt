package com.hellofresh.oms.orderManagementHttp.communicationPreference.service

import com.hellofresh.oms.model.communicationPreference.CommunicationPreference
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceDetailed
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceEnum as CommunicationPreferenceEnumResponse
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.ALL
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.DC
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.MARKET
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.SUPPLIER
import com.hellofresh.oms.orderManagement.generated.api.model.CreateCommunicationPreferenceRequest
import com.hellofresh.oms.orderManagement.generated.api.model.UpdateCommunicationPreferenceRequest
import com.hellofresh.oms.orderManagementHttp.client.featureflag.FeatureFlagClient
import com.hellofresh.oms.orderManagementHttp.communicationPreference.out.CommunicationPreferenceRepository
import com.hellofresh.oms.orderManagementHttp.communicationPreference.out.CommunicationPreferenceRepositoryImpl
import com.hellofresh.oms.orderManagementHttp.exception.CommunicationPreferenceNotFoundException
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import java.util.UUID
import org.springframework.stereotype.Service

@Service
@Suppress("TooManyFunctions")
class CommunicationPreferenceService(
    private val communicationPreferenceRepository: CommunicationPreferenceRepository,
    private val communicationPreferenceRepositoryImpl: CommunicationPreferenceRepositoryImpl,
    private val communicationPreferenceValidatorService: CommunicationPreferenceValidatorService,
    private val featureFlagClient: FeatureFlagClient,
    private val meterRegistry: MeterRegistry,
) {
    fun getCommunicationPreference(market: String, dcCode: String, supplierId: UUID): CommunicationPreferenceEnum? {
        val communicationPreference = communicationPreferenceRepository
            .findCommunicationPreferencesByMarketOrDcCodeOrSupplierId(
                market = market,
                dcCode = dcCode,
                supplierId = supplierId,
            ).let { preferences ->
                preferences.find { it.supplierId != null }
                    ?: preferences.find { it.dcCode != null }
                    ?: preferences.find { it.market != null }
            }
        if (communicationPreference == null) {
            incrementMarketDefaultNotFoundCounter(market)
        }

        val shouldForceIncludeEmail = featureFlagClient.shouldForceIncludeEmailPreferenceFeatureFlag()
        return if (shouldForceIncludeEmail &&
            communicationPreference?.preference == CommunicationPreferenceEnum.E2OPEN
        ) {
            CommunicationPreferenceEnum.EMAIL_AND_E2OPEN
        } else {
            communicationPreference?.preference
        }
    }

    fun getCommunicationPreferences(
        type: CommunicationPreferenceTypeEnum,
        marketCode: String?,
    ): List<CommunicationPreferenceDetailed> = communicationPreferenceRepositoryImpl.findByMarket(type, marketCode)

    fun updateCommunicationPreferences(
        scopeType: CommunicationPreferenceTypeEnum,
        scopeValue: String,
        communicationPreferenceUpdateRequest: UpdateCommunicationPreferenceRequest
    ): CommunicationPreference {
        val communicationPreference = getCommunicationPreferenceByScope(scopeType, scopeValue)
        return communicationPreferenceRepository.save(
            communicationPreference.copy(
                preference = communicationPreferenceUpdateRequest.preference.toModel(),
            ),
        )
    }

    fun deleteCommunicationPreferences(scopeType: CommunicationPreferenceTypeEnum, scopeValue: String) =
        getCommunicationPreferenceByScope(scopeType, scopeValue).let { communicationPreferenceRepository.delete(it) }

    fun createCommunicationPreferences(createRequest: CreateCommunicationPreferenceRequest): CommunicationPreference =
        communicationPreferenceValidatorService.validate(createRequest).run {
            communicationPreferenceRepository.save(this)
        }

    private fun incrementMarketDefaultNotFoundCounter(market: String) {
        Counter.builder(CP_MARKET_DEFAULT_NOT_FOUND)
            .tag("market", market)
            .register(meterRegistry)
            .increment()
    }

    private fun getCommunicationPreferenceByScope(
        scopeType: CommunicationPreferenceTypeEnum,
        scopeValue: String
    ): CommunicationPreference = when (scopeType) {
        MARKET ->
            communicationPreferenceRepository.findByMarketIgnoreCase(scopeValue)

        DC ->
            communicationPreferenceRepository.findByDcCodeIgnoreCase(scopeValue)

        SUPPLIER ->
            communicationPreferenceRepository.findBySupplierId(UUID.fromString(scopeValue))

        ALL -> throw IllegalArgumentException("Scope type $scopeType is not supported for this operation")
    }.orElseThrow { CommunicationPreferenceNotFoundException(scopeType.value, scopeValue) }

    companion object {
        private const val CP_MARKET_DEFAULT_NOT_FOUND = "communication_preference_market_default_not_found"
    }
}

fun CommunicationPreferenceEnum.toResponse(): CommunicationPreferenceEnumResponse =
    when (this) {
        CommunicationPreferenceEnum.EMAIL -> CommunicationPreferenceEnumResponse.EMAIL
        CommunicationPreferenceEnum.E2OPEN -> CommunicationPreferenceEnumResponse.E2_OPEN
        CommunicationPreferenceEnum.EMAIL_AND_E2OPEN -> CommunicationPreferenceEnumResponse.EMAIL_AND_E2_OPEN
    }

fun CommunicationPreferenceEnumResponse.toModel(): CommunicationPreferenceEnum =
    when (this) {
        CommunicationPreferenceEnumResponse.EMAIL -> CommunicationPreferenceEnum.EMAIL
        CommunicationPreferenceEnumResponse.E2_OPEN -> CommunicationPreferenceEnum.E2OPEN
        CommunicationPreferenceEnumResponse.EMAIL_AND_E2_OPEN -> CommunicationPreferenceEnum.EMAIL_AND_E2OPEN
    }
