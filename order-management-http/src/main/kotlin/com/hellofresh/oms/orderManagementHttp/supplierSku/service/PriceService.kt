package com.hellofresh.oms.orderManagementHttp.supplierSku.service

import com.hellofresh.oms.model.SupplierSkuPrice
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.PriceRepository
import java.time.LocalDate
import java.util.UUID
import org.springframework.stereotype.Service

@Service
class PriceService(val priceRepository: PriceRepository) {

    fun getActivePrices(
        dcCode: String,
        deliveryDate: LocalDate,
        supplierSkuIds: List<UUID>
    ): Map<UUID, SupplierSkuPrice?> =
        priceRepository
            .findAllByDcCodesAndSupplierSkuIdIn(dcCode, supplierSkuIds)
            .groupBy { it.supplierSkuId }
            .mapValues { resolveTheMostRecentActivePrice(it.value, deliveryDate) }
            .filter { it.value != null }

    private fun resolveTheMostRecentActivePrice(prices: List<SupplierSkuPrice>, deliveryDate: LocalDate) =
        prices.filter {
            deliveryDate >= it.startDate.toLocalDate() &&
                deliveryDate <= it.endDate.toLocalDate()
        }.maxByOrNull { it.updatedAt }
}
