package com.hellofresh.oms.orderManagementHttp.configuration

import com.statsig.sdk.Statsig
import com.statsig.sdk.StatsigOptions
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.core.annotation.Order

@Configuration
@Profile(value = ["staging", "live"])
@Order(1)
class StatsigConfiguration(
    @Value("\${statsig.api-key}") apiKey: String,
    @Value("\${statsig.environment}") environment: String
) {
    init {
        Statsig.initializeAsync(apiKey, options = StatsigOptions().apply { this.setTier(environment) }).get()
    }
}

@Configuration
@Profile(value = ["local", "test", "integration"])
@Order(0)
class LocalStatsigConfiguration {
    init {
        Statsig.initializeAsync("any-secret-key", options = StatsigOptions(localMode = true)).get()
    }
}
