package com.hellofresh.oms.orderManagementHttp.uom

import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.UnitOfMeasure
import com.hellofresh.oms.orderManagement.generated.api.model.UnitsOfMeasureApiResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UnitsOfMeasureApiResponse.EnumValue
import com.hellofresh.oms.orderManagement.generated.api.model.UnitsOfMeasureListApiResponse

fun List<UnitOfMeasure>.mapToDto() =
    this.map {
        UnitOfMeasureDto(
            uuid = it.uuid,
            name = it.name,
            market = it.market,
            type = it.type,
            enumValue = it.enumValue,
        )
    }

fun List<UnitOfMeasureDto>.toApiResponse() =
    UnitsOfMeasureListApiResponse(
        this.map {
            UnitsOfMeasureApiResponse(
                id = it.uuid,
                name = it.name,
                type = it.type,
                enumValue = it.enumValue.toApiResponse(),
            )
        },
    )

fun UOM.toApiResponse() =
    when (this) {
        UOM.UNIT -> EnumValue.UNIT
        UOM.KG -> EnumValue.KG
        UOM.L -> EnumValue.L
        UOM.LBS -> EnumValue.LBS
        UOM.GAL -> EnumValue.GAL
        UOM.OZ -> EnumValue.OZ
    }
