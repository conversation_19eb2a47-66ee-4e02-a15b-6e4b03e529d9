package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.orderManagement.generated.api.model.ServiceCreatePurchaseOrderRequest
import com.hellofresh.oms.orderManagement.generated.api.model.ServiceCreatePurchaseOrderResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.ServicePurchaseOrderApi
import com.hellofresh.oms.orderManagementHttp.authentication.getIssuer
import com.hellofresh.oms.orderManagementHttp.configuration.OriginIssuersProperties
import com.hellofresh.oms.orderManagementHttp.order.service.CreatePurchaseOrderRequestDto
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderCreateService
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class ServicePurchaseOrderController(
    private val purchaseOrderCreateService: PurchaseOrderCreateService,
    private val originIssuersProperties: OriginIssuersProperties,
) : ServicePurchaseOrderApi {
    override fun serviceCreateOrder(
        serviceCreatePurchaseOrderRequest: ServiceCreatePurchaseOrderRequest,
        sendImmediately: Boolean,
    ): ResponseEntity<ServiceCreatePurchaseOrderResponse> {
        val purchaseOrder = purchaseOrderCreateService.processPurchaseOrder(
            createPurchaseOrderRequest = serviceCreatePurchaseOrderRequest.toDto(getIssuer()),
            sendImmediately = sendImmediately,
        )
        return ResponseEntity.status(HttpStatus.CREATED.value()).body(
            ServiceCreatePurchaseOrderResponse(
                id = purchaseOrder.id,
                poNumber = purchaseOrder.poNumber,
            ),
        )
    }

    private fun ServiceCreatePurchaseOrderRequest.toDto(issuer: String?): CreatePurchaseOrderRequestDto =
        CreatePurchaseOrderRequestDto(
            purchaseOrderIdentifier = this.purchaseOrderIdentifier,
            userId = this.userId,
            userEmail = this.userEmail,
            supplierId = this.supplierId,
            dcCode = this.dcCode,
            shippingMethod = this.shippingMethod,
            deliveryWindow = this.deliveryWindow,
            comment = this.comment,
            orderItems = this.orderItems,
            dcWeek = this.dcWeek,
            emergencyReasonId = this.emergencyReasonId,
            origin = getOriginBasedOnIssuer(issuer),
        )

    private fun getOriginBasedOnIssuer(issuer: String?): Origin =
        if (originIssuersProperties.planned.contains(issuer)) {
            Origin.PLANNED
        } else {
            Origin.OTHER
        }
}
