package com.hellofresh.oms.orderManagementHttp.order.service.domain

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import java.math.BigDecimal
import java.math.RoundingMode

open class MoneyDomain<T : MoneyDomainPrecision> internal constructor(
    val amount: BigDecimal,
    val currency: String,
    val precision: T
) {
    fun getScaledAmount(): BigDecimal = amount

    fun isZeroOrGreater(): Boolean = amount >= BigDecimal.ZERO.setScale(precision.value, RoundingMode.HALF_EVEN)

    fun toMoney(): Money = Money(amount.setScale(precision.value, RoundingMode.HALF_EVEN), currency)

    companion object {
        val PRECISION = MoneyDomainPrecision.Precision4

        fun <T : MoneyDomainPrecision> from(
            orderMoneyDto: MoneyDto,
            precision: T,
        ): MoneyDomain<T> =
            MoneyDomain(
                orderMoneyDto.amount.toBigDecimal().setScale(precision.value, RoundingMode.HALF_EVEN),
                orderMoneyDto.currency,
                precision,
            )

        fun <T : MoneyDomainPrecision> add(
            a: MoneyDomain<T>,
            b: MoneyDomain<T>,
        ): MoneyDomain<T> =
            MoneyDomain(
                a.amount.setScale(a.precision.value, RoundingMode.HALF_EVEN).add(
                    b.amount.setScale(a.precision.value, RoundingMode.HALF_EVEN),
                ),
                a.currency,
                a.precision,
            )
    }
}

@Suppress("MagicNumber") // Defined constants for precision
sealed class MoneyDomainPrecision(val value: Int) {
    data object Precision4 : MoneyDomainPrecision(4)
}
