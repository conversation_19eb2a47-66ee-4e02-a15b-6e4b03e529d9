package com.hellofresh.oms.orderManagementHttp.uom

import com.hellofresh.oms.orderManagement.generated.api.model.UnitsOfMeasureListApiResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.UnitsOfMeasureApi
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class UnitOfMeasureController(
    private val unitOfMeasureService: UnitOfMeasureService
) : UnitsOfMeasureApi {
    override fun getUnitsOfMeasure(market: String): ResponseEntity<UnitsOfMeasureListApiResponse> {
        val apiResponse = unitOfMeasureService.getUnitOfMeasureByMarket(market).toApiResponse()

        return ResponseEntity.ok(apiResponse)
    }
}
