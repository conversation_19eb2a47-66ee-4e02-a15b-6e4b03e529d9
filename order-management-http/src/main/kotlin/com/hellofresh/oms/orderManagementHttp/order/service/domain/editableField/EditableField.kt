package com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField

import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest

sealed interface EditableField {
    companion object {
        fun getListOfChanges(
            editPurchaseOrderRequest: EditPurchaseOrderRequest,
            persistedPurchaseOrder: PurchaseOrder,
        ): List<EditableField> {
            val editedFields = listOfNotNull(
                ShippingMethodField.from(editPurchaseOrderRequest),
                ExpectedTimeField.from(editPurchaseOrderRequest),
                EmergencyReasonField.from(editPurchaseOrderRequest),
                CommentField.from(editPurchaseOrderRequest),
                OrderItemsField.from(editPurchaseOrderRequest, persistedPurchaseOrder),
            )

            return prepareEditableFields(editedFields, persistedPurchaseOrder)
        }

        private fun prepareEditableFields(editedFields: List<EditableField>, persistedPurchaseOrder: PurchaseOrder) =
            editedFields.filterNot {
                when (it) {
                    is CommentField -> it.isEqual(persistedPurchaseOrder.comment)
                    is EmergencyReasonField -> it.isEqual(persistedPurchaseOrder.emergencyReasonUuid)
                    is ExpectedTimeField -> it.isEqual(
                        persistedPurchaseOrder.expectedStartTime,
                        persistedPurchaseOrder.expectedEndTime,
                        persistedPurchaseOrder.deliveryDateChangeReasonId,
                    )

                    is OrderItemsField -> it.isEqual(
                        persistedPurchaseOrder.totalPrice,
                        persistedPurchaseOrder.orderItems,
                    )

                    is ShippingMethodField -> {
                        it.isEqual(persistedPurchaseOrder.shippingMethod)
                    }
                }
            }
    }
}
