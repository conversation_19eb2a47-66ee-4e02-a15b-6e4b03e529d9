package com.hellofresh.oms.orderManagementHttp.order.service.domain

import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum.MINUS_CREATED_AT
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum.MINUS_DELIVERY_WINDOW_START
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum.MINUS_PO_NUMBER
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum.PLUS_CREATED_AT
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum.PLUS_DELIVERY_WINDOW_START
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum.PLUS_PO_NUMBER
import kotlin.reflect.KClass
import org.springframework.data.domain.Sort

private const val CREATED_AT_PROPERTY_NAME = "created_at"
private const val DELIVERY_WINDOW_START_PROPERTY_NAME = "expected_start_time"
private const val PO_NUMBER_PROPERTY_NAME = "po_number"

private val orderToEnumMap = ListPurchaseOrdersSortEnum.entries.associateBy { it.toSortOrder() }

fun KClass<ListPurchaseOrdersSortEnum>.fromSortOrder(sortOrder: Sort.Order): ListPurchaseOrdersSortEnum =
    orderToEnumMap[sortOrder] ?: throw IllegalArgumentException(
        "ListPurchaseOrdersSortEnum does not contain a value for: $sortOrder",
    )

fun ListPurchaseOrdersSortEnum.toSortOrder(): Sort.Order {
    fun ListPurchaseOrdersSortEnum.direction() = when (this) {
        PLUS_CREATED_AT,
        PLUS_DELIVERY_WINDOW_START,
        PLUS_PO_NUMBER -> Sort.Direction.ASC

        MINUS_CREATED_AT,
        MINUS_DELIVERY_WINDOW_START,
        MINUS_PO_NUMBER -> Sort.Direction.DESC
    }

    fun ListPurchaseOrdersSortEnum.property() = when (this) {
        PLUS_CREATED_AT, MINUS_CREATED_AT -> CREATED_AT_PROPERTY_NAME
        PLUS_DELIVERY_WINDOW_START, MINUS_DELIVERY_WINDOW_START -> DELIVERY_WINDOW_START_PROPERTY_NAME
        PLUS_PO_NUMBER, MINUS_PO_NUMBER -> PO_NUMBER_PROPERTY_NAME
    }

    return Sort.Order(this.direction(), this.property())
}
