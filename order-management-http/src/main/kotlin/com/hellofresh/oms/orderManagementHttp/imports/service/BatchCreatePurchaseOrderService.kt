package com.hellofresh.oms.orderManagementHttp.imports.service

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.getDeliveryAddress
import com.hellofresh.oms.model.importHistory.ImportHistory
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.imports.IncorrectImportFileException
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.BatchCreatePurchaseOrderAggregator
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.PurchaseOrderDto
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchCreatePurchaseOrderParser
import com.hellofresh.oms.orderManagementHttp.imports.validator.BatchCreatePurchaseOrderValidator
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderService
import com.hellofresh.oms.orderManagementHttp.order.service.domain.PackagingDomain
import com.hellofresh.oms.orderManagementHttp.workerAction.SyncBatchOrderScheduler
import java.io.InputStream
import java.time.LocalDateTime
import java.util.UUID
import org.springframework.stereotype.Service

@Service
class BatchCreatePurchaseOrderService(
    private val parser: BatchCreatePurchaseOrderParser,
    private val validator: BatchCreatePurchaseOrderValidator,
    private val aggregator: BatchCreatePurchaseOrderAggregator,
    private val purchaseOrderService: PurchaseOrderService,
    private val syncBatchOrderScheduler: SyncBatchOrderScheduler,
) {
    fun processBatchImport(csv: InputStream, allowedMarkets: List<String>): List<PurchaseOrderDto> {
        val parsedResponse = parser.parse(csv)
        if (parsedResponse.lineErrors.isNotEmpty()) {
            throw IncorrectImportFileException(parsedResponse.lineErrors)
        }

        val validatedResponse = validator.validate(parsedResponse.parsedLines, allowedMarkets)
        if (validatedResponse.lineErrors.isNotEmpty()) {
            throw IncorrectImportFileException(validatedResponse.lineErrors)
        }

        return aggregator.aggregate(validatedResponse.validLines)
    }

    fun persistPurchaseOrder(purchaseOrder: PurchaseOrderDto, user: LoggedInUserInfo) =
        purchaseOrderService.savePurchaseOrder(
            purchaseOrder.toModel(
                user.userId,
                user.userEmail,
            ),
        ).let {
            purchaseOrder.copy(id = it.id, poNumber = it.poNumber)
        }

    fun queueCreatePurchaseOrders(purchaseOrderNumbers: List<String>, importHistory: ImportHistory) =
        purchaseOrderNumbers.forEach { poNumber ->
            syncBatchOrderScheduler.queueSyncBatchOrder(poNumber, importHistory)
        }

    private fun PurchaseOrderDto.toModel(
        userId: UUID,
        userEmail: String
    ): PurchaseOrder {
        val poNumber = purchaseOrderService.generatePoNumber(
            this.yearWeek,
            this.distributionCenter.code,
        )
        val now = LocalDateTime.now()
        val purchaseOrderId = UUID.randomUUID()
        val orderItems = this.orderItems.map { orderItem ->
            val packagingDomain = PackagingDomain.from(orderItem, this.supplier.currency)
            OrderItem.createOrderItem(
                poId = purchaseOrderId,
                skuId = orderItem.sku.uuid,
                price = packagingDomain.getPricePerItem().toMoney(),
                buffer = Permyriad.fromPercent(orderItem.bufferValue),
                packaging = packagingDomain.toPackaging(),
                currentTime = now,
                changeReasonId = null,
                casesPerPallet = packagingDomain.getCasesPerPalletOrNull(),
                totalNumberOfUnits = packagingDomain.getTotalNumberOfUnits(),
                totalPrice = packagingDomain.getTotalPrice().toMoney(),
            )
        }
        val totalPrice = Money(orderItems.sumOf { it.totalPrice.amount }, orderItems.first().totalPrice.currency)
        return PurchaseOrder.createPurchaseOrder(
            poId = purchaseOrderId,
            poNumber = poNumber,
            yearWeek = this.yearWeek,
            userId = userId,
            userEmail = userEmail,
            supplierId = this.supplier.id,
            supplierCode = this.supplier.code.toString(),
            dcCode = this.distributionCenter.code,
            shippingMethod = this.shipMethod,
            shippingAddress = this.distributionCenter.getDeliveryAddress()!!.let { address ->
                ShippingAddress(
                    locationName = this.distributionCenter.name,
                    streetAddress = address.address,
                    city = address.city,
                    region = address.countryCode,
                    postalCode = address.zip,
                    countryCode = address.countryCode,
                )
            },
            expectedStartTime = this.deliveryWindow.start,
            expectedEndTime = this.deliveryWindow.end,
            orderItems = orderItems.toSet(),
            emergencyReasonUuid = this.emergencyReason.uuid,
            totalPrice = totalPrice,
            comment = this.comments,
            currentTime = now,
            origin = Origin.MANUAL,
        )
    }
}
