package com.hellofresh.oms.orderManagementHttp.supplier

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum.CROSSDOCK
import com.hellofresh.oms.model.ShipMethodEnum.FREIGHT_ON_BOARD
import com.hellofresh.oms.model.ShipMethodEnum.OTHER
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.supplier.SupplierExtended

private val DEFAULT_SHIP_METHOD = Method.VENDOR

fun SupplierExtended.toSupplierDto(
    dcCodesToFilter: List<String>,
    supplierShippingMethods: List<ShipMethod>
) = SupplierDto(
    id = this.id,
    name = this.name,
    code = this.code.toString(),
    market = this.market,
    defaultShippingMethods = this.dcCodes.intersect(dcCodesToFilter.toSet()).map {
        ShippingMethodDto(
            dcCode = it,
            method = supplierShippingMethods.find { method -> method.dcCode == it }
                ?.toMethod()
                ?: DEFAULT_SHIP_METHOD,
        )
    },
    address = AddressDto(
        country = this.supplierAddress.country,
        city = this.supplierAddress.city,
        zipcode = this.supplierAddress.postCode,
        address = this.supplierAddress.address,
        number = this.supplierAddress.number,
    ),
    currency = this.currency,
)

private fun ShipMethod.toMethod() = when (this.method) {
    CROSSDOCK -> Method.CROSSDOCK
    OTHER -> Method.OTHER
    VENDOR -> Method.VENDOR
    FREIGHT_ON_BOARD -> Method.FREIGHT_ON_BOARD
}
