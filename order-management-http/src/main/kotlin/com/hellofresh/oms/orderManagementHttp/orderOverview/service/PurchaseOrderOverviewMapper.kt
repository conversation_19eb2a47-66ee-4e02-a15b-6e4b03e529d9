@file:Suppress("TooManyFunctions")

package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.acknowledgement.UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_CASE
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewShipMethodEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrdersOverviewResponseInner.RiskAssessment
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomain
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.PurchaseOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.PurchaseOrderOverviewModel
import com.hellofresh.oms.util.Calculator
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import org.apache.commons.lang3.StringUtils.SPACE

const val HIGH_RISK_SHIPPING_STATE = "SHIPPING_STATE_PARTIALLY_SHIPPED"

@Suppress("LongMethod")
fun PurchaseOrderOverviewModel.mapToPurchaseOrderOverviewModelToApiResponse(): PurchaseOrdersOverviewResponseInner =
    PurchaseOrdersOverviewResponseInner(
        poId = this.poId,
        poNumber = this.poNumber,
        poVersion = this.poVersion,
        supplierName = this.supplierName,
        supplierCode = this.supplierCode,
        skuName = this.skuName,
        skuCode = this.skuCode,
        scheduledDeliveryDate = this.scheduledDeliveryDate,
        orderSize = this.orderSize,
        totalPrice = this.totalPrice.toMoneyDto(this.totalPriceCurrency),
        emergencyReason = this.emergencyReason,
        creatorEmail = this.creatorEmail,
        week = this.week,
        dcCode = this.dcCode,
        brand = this.brand,
        category = this.category,
        status = this.status ?: PoOverviewStatusEnum.UNKNOWN,
        shippingMethod = PoOverviewShipMethodEnum.forValue(this.shippingMethod),
        casePrice = this.casePrice.toMoneyDto(this.casePriceCurrency),
        caseSize = this.caseSize,
        orderUnit = UomEnum.forValue(this.orderUnit.uppercase()),
        purchasingUnit = UomEnum.forValue(this.purchasingUnit.uppercase()),
        quantityOrdered = this.quantityOrdered,
        proposedQuantityCases = this.proposedQuantityCases,
        proposedQuantityUnits = this.proposedQuantityUnits,
        proposedUnitsPerCase = this.proposedUnitsPerCase,
        proposedDeliveryDate = this.proposedDeliveryDate,
        asnShipmentDate = this.asnShipmentDate,
        asnPlannedDeliveryTime = this.asnPlannedDeliveryTime,
        asnShippedQuantityCases = this.asnShippedQuantityCases,
        asnUnitsOfMeasure = this.asnUnitsOfMeasure,
        asnCaseSize = this.asnCaseSize,
        asnShippedQuantityUnits = this.asnShippedQuantityUnits,
        loadNumber = this.loadNumber,
        appointmentTime = this.appointmentTime,
        carrierName = this.carrierName,
        locality = this.locality,
        palletCount = this.palletCount,
        hasIcsTickets = this.hasIcsTickets,
        quantityReceived = this.quantityReceived,
        casesReceived = this.casesReceived,
        caseSizeReceived = if (this.quantityReceived != null && this.casesReceived != null && this.casesReceived > 0) {
            this.quantityReceived.divide(BigDecimal(this.casesReceived), 2, RoundingMode.HALF_UP)
        } else {
            null
        },
        riskAssessment = this.calculateRiskAssessment(),
        assignedBuyer = this.assignedBuyer,
        phfDeliveryPercentOfForecast = this.phfDeliveryPercentOfForecast,
    )

private fun PurchaseOrderOverviewModel.calculateRiskAssessment(): RiskAssessment {
    val hasPlannedDelivery = asnPlannedDeliveryTime != null
    val isHighRisk = isHighRisk(asnPlannedDeliveryTime, scheduledDeliveryDate, asnShippedQuantityUnits, quantityOrdered)

    return when {
        isHighRisk -> RiskAssessment.HIGH
        hasPlannedDelivery &&
            !isHighRisk &&
            (
                asnCaseSize != null && asnCaseSize.compareTo(caseSize) != 0 ||
                    asnPlannedDeliveryTime.toLocalDate() < scheduledDeliveryDate.toLocalDate() ||
                    this.asnShippingState == HIGH_RISK_SHIPPING_STATE
                ) -> RiskAssessment.MEDIUM

        else -> RiskAssessment.NONE
    }
}

private fun isHighRisk(
    asnPlannedDeliveryTime: LocalDateTime?,
    scheduledDeliveryDate: LocalDateTime?,
    asnShippedQuantityUnits: BigDecimal?,
    quantityOrdered: BigDecimal?,
): Boolean = asnPlannedDeliveryTime != null &&
    (
        asnShippedQuantityUnits != null && asnShippedQuantityUnits.compareTo(quantityOrdered) != 0 ||
            asnPlannedDeliveryTime.toLocalDate() > scheduledDeliveryDate?.toLocalDate()
        )

@Suppress("LongMethod")
fun PurchaseOrderStatusDetails.mapToPurchaseOrderOverviewModel(brand: String): PurchaseOrderOverviewModel =
    PurchaseOrderOverviewModel(
        poId = this.poId,
        poNumber = this.poNumber,
        poVersion = this.version,
        supplierName = this.supplierName ?: "",
        supplierCode = this.supplierCode ?: "",
        skuName = this.skuName ?: "",
        skuCode = this.skuCode ?: "",
        brand = brand,
        scheduledDeliveryDate = expectedStartTime.toLocalDateTime(),
        orderSize = calculateOrderSize(),
        emergencyReason = this.emergencyReason ?: "",
        creatorEmail = this.creatorEmail,
        totalPrice = this.totalPrice.toBigDecimal(),
        totalPriceCurrency = this.totalPriceCurrency,
        week = this.week,
        dcCode = this.dcCode,
        category = this.category ?: "",
        status = calculateHjStatus(
            PoStatusParams(
                isVoided = this.isVoided,
                supplierName = this.supplierName,
                hjStatus = this.hjStatus,
                hjOverrideQuantity = this.hjOverrideQuantity?.let { BigDecimal(it) },
                hjReceiptClosedQuantity = this.exportReceiptQuantityReceived,
                quantityOrdered = this.totalQuantity,
                isPastDue = isPastDue(calculateProposedDeliveryDate(this.ackPromisedTime)),
                isBlankReceipt = this.isBlankReceipt,
            ),
        ),
        shippingMethod = this.shippingMethod,
        casePrice = this.priceAmount.toBigDecimal(),
        casePriceCurrency = this.priceCurrency,
        caseSize = this.caseSize ?: BigDecimal.ZERO,
        orderUnit = this.unitOfMeasure,
        purchasingUnit = this.skuUom ?: "UNIT",
        quantityOrdered = this.totalQuantity,
        proposedQuantityCases = if (this.ackUom == UNIT_OF_MEASURE_CASE.toString()) {
            this.ackNumberOfUnits
        } else {
            null
        },
        proposedUnitsPerCase = if (this.ackUom == UNIT_OF_MEASURE_CASE.toString()) {
            this.ackUnitsPerCase
        } else {
            null
        },
        proposedQuantityUnits = calculateProposedQuantityUnits(),
        proposedDeliveryDate = calculateProposedDeliveryDate(this.ackPromisedTime),
        asnShipmentDate = this.asnShipmentTime?.toLocalDateTime(),
        asnPlannedDeliveryTime = this.asnPlannedDeliveryTime?.toLocalDateTime(),
        asnShippedQuantityCases = this.asnShippedOrderSize?.toBigDecimal(),
        asnUnitsOfMeasure = this.asnShippedUnitType,
        asnCaseSize = this.asnShippedSize?.toBigDecimal(),
        asnShippedQuantityUnits = calculateAsnShippedQuantity(),
        loadNumber = this.loadNumber,
        appointmentTime = this.appointmentTime?.toLocalDateTime(),
        carrierName = this.carrierName,
        locality = this.locality,
        palletCount = this.palletCount,
        hasIcsTickets = this.hasIcsTickets,
        quantityReceived = this.exportReceiptQuantityReceived,
        casesReceived = this.receiptOverrideCasesReceived ?: this.exportReceiptCasesReceived,
        assignedBuyer = listOfNotNull(this.assignedBuyerFirstName, this.assignedBuyerLastName)
            .joinToString(SPACE)
            .ifEmpty { null },
        phfDeliveryPercentOfForecast = calculatePhfDeliveryPercentOfForecast()
    )

@Suppress("MagicNumber")
private fun PurchaseOrderStatusDetails.calculatePhfDeliveryPercentOfForecast() =
    if (this.weeklyForecastedDemand == null || this.weeklyForecastedDemand.compareTo(BigDecimal.ZERO) == 0) {
        null
    } else {
        listOfNotNull(this.totalQuantity, this.exportReceiptQuantityReceived).maxOrNull()
            ?.divide(this.weeklyForecastedDemand, 4, RoundingMode.HALF_UP)
            ?.multiply(BigDecimal(HUNDRED))
            ?.setScale(2, RoundingMode.HALF_UP)
    }

private fun PurchaseOrderStatusDetails.calculateOrderSize(): BigDecimal = Calculator.calculateTotalCaseQuantity(
    this.totalQuantity,
    Packaging(
        packagingType = PackagingType.valueOf(this.packagingType),
        caseSize = this.caseSize,
        unitOfMeasure = UOM.valueOf(this.unitOfMeasure),
    ),
)

private fun PurchaseOrderStatusDetails.calculateAsnShippedQuantity(): BigDecimal? = when (this.asnShippedUnitType) {
    "UNIT_OF_MEASURE_UNIT" -> this.asnShippedOrderSize?.toBigDecimal()
    null -> null
    else -> if (this.asnShippedOrderSize != null && this.asnShippedSize != null) {
        (this.asnShippedOrderSize * this.asnShippedSize).toBigDecimal()
    } else {
        null
    }
}

private fun PurchaseOrderStatusDetails.calculateProposedQuantityUnits(): BigDecimal? =
    if (this.ackUom == UNIT_OF_MEASURE_CASE.toString()) {
        if (this.ackNumberOfUnits != null && this.ackUnitsPerCase != null) {
            this.ackNumberOfUnits.multiply(this.ackUnitsPerCase)
        } else {
            null
        }
    } else {
        this.ackNumberOfUnits
    }

private fun BigDecimal.toMoneyDto(currency: String) = MoneyDto(
    amount = this.setScale(MoneyDomain.PRECISION.value, RoundingMode.HALF_EVEN).toPlainString(),
    currency = currency,
)

private fun isPastDue(scheduledDeliveryDate: LocalDateTime?): Boolean =
    scheduledDeliveryDate?.isBefore(LocalDateTime.now(ZoneId.of("UTC"))) ?: false

private fun calculateProposedDeliveryDate(ackPromisedTime: Instant?): LocalDateTime? =
    ackPromisedTime?.let {
        LocalDateTime.ofInstant(
            ackPromisedTime,
            ZoneId.of("UTC"),
        )
    }

const val HUNDRED = 100
