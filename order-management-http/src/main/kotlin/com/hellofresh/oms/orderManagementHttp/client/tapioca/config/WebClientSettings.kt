package com.hellofresh.oms.orderManagementHttp.client.tapioca.config

import java.time.Duration
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "webclient")
data class WebClientSettings(
    val connectionTimeout: Duration,
    val responseTimeout: Duration,
    val maxIdleTime: Duration,
    val maxLifeTime: Duration,
    val pendingAcquireTimeout: Duration,
    val evictInBackground: Duration,
)
