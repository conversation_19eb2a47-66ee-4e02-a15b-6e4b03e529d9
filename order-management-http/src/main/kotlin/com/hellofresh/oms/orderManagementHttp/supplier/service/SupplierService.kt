package com.hellofresh.oms.orderManagementHttp.supplier.service

import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum.PLUS_NAME
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierShippingMethodRepository
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierSimpleRepositoryImpl
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierSimpleRepositoryImpl.SupplierSimpleFilter
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.SupplierListDto
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.SupplierShippingMethodDto
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.toSortOrder
import io.micrometer.core.annotation.Timed
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.server.ResponseStatusException

@Service
class SupplierService(
    private val supplierSimpleRepositoryImpl: SupplierSimpleRepositoryImpl,
    private val supplierRepository: SupplierRepository,
    private val shipMethodRepository: SupplierShippingMethodRepository
) {
    @Timed
    @Throws(SupplierException::class)
    @Suppress("LongParameterList")
    fun getSuppliers(
        market: String,
        search: String?,
        pageNumber: Int,
        pageSize: Int,
        sort: ListSuppliersSortEnum?,
    ): SupplierListDto {
        val suppliersResult = supplierSimpleRepositoryImpl.findAllSuppliersSimplePaginated(
            SupplierSimpleFilter(
                market = market,
                search = search,
                page = pageNumber,
                size = pageSize,
                sortOrder = (sort ?: PLUS_NAME).toSortOrder(),
            ),
        )

        logger.debug(
            "Suppliers found: " +
                "market=$market, search=$search" +
                "totalElements=${suppliersResult.totalElements}, size=${suppliersResult.size}",
        )

        return SupplierListDto.from(suppliersResult)
    }

    @Timed
    @Throws(SupplierException::class)
    fun getSuppliersByIds(ids: List<UUID>): List<SupplierExtended> = supplierRepository.findAllById(ids)

    @Timed
    @Throws(SupplierException::class)
    fun getSupplierShippingMethods(supplierId: UUID): List<SupplierShippingMethodDto> {
        val shipMethod = shipMethodRepository.findAllBySupplierId(supplierId)

        return shipMethod.map { SupplierShippingMethodDto.from(it) }
    }

    @Timed
    @Throws(SupplierException::class)
    fun getSupplierById(supplierId: UUID): SupplierExtended = supplierRepository.findById(supplierId)
        .orElseThrow { SupplierException("Supplier not found with id: $supplierId") }

    companion object {
        private val logger = LoggerFactory.getLogger(SupplierService::class.java)
    }
}

@ResponseStatus(INTERNAL_SERVER_ERROR)
class SupplierException(
    override val message: String? = null,
    override val cause: Exception? = null,
) : ResponseStatusException(INTERNAL_SERVER_ERROR, message, cause)
