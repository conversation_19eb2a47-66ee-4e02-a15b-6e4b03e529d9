package com.hellofresh.oms.orderManagementHttp.orderOverview.service.model

import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewStatusEnum
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class PurchaseOrderOverviewModel(
    val poId: UUID,
    val poNumber: String,
    val poVersion: Int,
    val status: PoOverviewStatusEnum?,
    val supplierName: String,
    val supplierCode: String,
    val skuName: String,
    val skuCode: String,
    val scheduledDeliveryDate: LocalDateTime,
    val orderSize: BigDecimal,
    val totalPrice: BigDecimal,
    val totalPriceCurrency: String,
    val emergencyReason: String,
    val shippingMethod: String,
    val creatorEmail: String,
    val week: String,
    val dcCode: String,
    val brand: String,
    val category: String,
    val casePrice: BigDecimal,
    val casePriceCurrency: String,
    val caseSize: BigDecimal,
    val orderUnit: String,
    val purchasingUnit: String,
    val quantityOrdered: BigDecimal,
    val proposedQuantityCases: BigDecimal? = null,
    val proposedUnitsPerCase: BigDecimal? = null,
    val proposedQuantityUnits: BigDecimal? = null,
    val proposedDeliveryDate: LocalDateTime? = null,
    val asnShipmentDate: LocalDateTime? = null,
    val asnPlannedDeliveryTime: LocalDateTime? = null,
    val asnShippedQuantityCases: BigDecimal? = null,
    val asnUnitsOfMeasure: String? = null,
    val asnCaseSize: BigDecimal? = null,
    val asnShippingState: String? = null,
    val asnShippedQuantityUnits: BigDecimal? = null,
    val loadNumber: String?,
    val appointmentTime: LocalDateTime?,
    val carrierName: String?,
    val locality: String?,
    val palletCount: Int?,
    val hasIcsTickets: Boolean,
    val quantityReceived: BigDecimal?,
    val casesReceived: Int?,
    val assignedBuyer: String?,
    val phfDeliveryPercentOfForecast: BigDecimal?,
    val receiptOverrideQuantity: Int?,
    val receiptOverrideCasesReceived: Int?,
    val receiptOverrideDeliveryDate: LocalDateTime?,
    val receiptOverrideDetails: String?,
    val receiptOverrideCreatedAt: LocalDateTime?,
    val receiptOverrideUpdatedBy: String?,
)
