package com.hellofresh.oms.orderManagementHttp.icsTicket

import com.hellofresh.oms.model.icsTicket.IcsTicket
import io.micrometer.core.annotation.Timed
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

fun interface IcsTicketBulkRepository {
    fun upsertAll(tickets: List<IcsTicket>)
}

@Repository
class IcsTicketBulkRepositoryImpl(
    private val jt: NamedParameterJdbcTemplate,
) : IcsTicketBulkRepository {

    @Timed
    @Transactional
    @Suppress("LongMethod")
    override fun upsertAll(tickets: List<IcsTicket>) {
        if (tickets.isEmpty()) {
            return
        }

        val sql = StringBuilder(
            """
                INSERT INTO ics_ticket (
                    ticket_id, market, bob_code, sku_code, po_number, po_reference, subject, ticket_link, request_type,
                    week, priority, status, type, production_impact, created_at, updated_at
                ) VALUES """,
        )

        val paramValues = MapSqlParameterSource()
        tickets.forEachIndexed { idx, ticket ->
            sql.append(
                """ (
                    :ticketId$idx, :market$idx, :bobCode$idx, :skuCode$idx, :poNumber$idx, :poReference$idx,
                    :subject$idx, :ticketLink$idx, :requestType$idx, :week$idx, :priority$idx, :status$idx,
                    :type$idx, :productionImpact$idx, :createdAt$idx, :updatedAt$idx
                )"""
            )

            if (idx < tickets.size - 1) {
                sql.append(", ")
            }

            paramValues.addValue("ticketId$idx", ticket.ticketId)
            paramValues.addValue("market$idx", ticket.market)
            paramValues.addValue("bobCode$idx", ticket.bobCode)
            paramValues.addValue("skuCode$idx", ticket.skuCode)
            paramValues.addValue("poNumber$idx", ticket.poNumber)
            paramValues.addValue("poReference$idx", ticket.poReference)
            paramValues.addValue("subject$idx", ticket.subject)
            paramValues.addValue("ticketLink$idx", ticket.ticketLink)
            paramValues.addValue("requestType$idx", ticket.requestType)
            paramValues.addValue("week$idx", ticket.week)
            paramValues.addValue("priority$idx", ticket.priority?.name)
            paramValues.addValue("status$idx", ticket.status?.name)
            paramValues.addValue("type$idx", ticket.type)
            paramValues.addValue("productionImpact$idx", ticket.productionImpact)
            paramValues.addValue("createdAt$idx", ticket.createdAt)
            paramValues.addValue("updatedAt$idx", ticket.updatedAt)
        }

        sql.append(
            """
                ON CONFLICT (ticket_id) DO UPDATE SET
                    market = excluded.market,
                    bob_code = excluded.bob_code,
                    sku_code = excluded.sku_code,
                    po_number = excluded.po_number,
                    po_reference = excluded.po_reference,
                    subject = excluded.subject,
                    ticket_link = excluded.ticket_link,
                    request_type = excluded.request_type,
                    week = excluded.week,
                    priority = excluded.priority,
                    status = excluded.status,
                    type = excluded.type,
                    production_impact = excluded.production_impact,
                    created_at = excluded.created_at,
                    updated_at = excluded.updated_at
            """
        )

        jt.batchUpdate(sql.toString(), arrayOf(paramValues))
    }
}
