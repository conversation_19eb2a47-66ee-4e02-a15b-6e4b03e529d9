package com.hellofresh.oms.orderManagementHttp.communicationPreference.intake

import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceDetailed
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceDetailedResponse
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceEnum.E2_OPEN
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceEnum.EMAIL
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceEnum.EMAIL_AND_E2_OPEN
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceResponse
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.DC
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.MARKET
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.SUPPLIER
import com.hellofresh.oms.orderManagement.generated.api.model.DistributionCenter
import com.hellofresh.oms.orderManagement.generated.api.model.Market
import com.hellofresh.oms.orderManagement.generated.api.model.Supplier
import org.springframework.core.convert.converter.Converter
import org.springframework.stereotype.Component

@Component
class CommunicationPreferenceResponseConverter :
    Converter<CommunicationPreferenceEnum, CommunicationPreferenceResponse> {
    override fun convert(source: CommunicationPreferenceEnum): CommunicationPreferenceResponse =
        CommunicationPreferenceResponse(source.toResponse())
}

@Component
class CommunicationPreferenceDetailedResponseConverter :
    Converter<CommunicationPreferenceDetailed, CommunicationPreferenceDetailedResponse> {

    override fun convert(source: CommunicationPreferenceDetailed): CommunicationPreferenceDetailedResponse =
        CommunicationPreferenceDetailedResponse(
            type = when {
                source.market != null -> MARKET
                source.dcCode != null -> DC
                else -> SUPPLIER
            },
            market = getMarket(source),
            distributionCenter = getDistributionCenter(source),
            supplier = getSupplier(source),
            preference = source.preference.toResponse(),
        )

    private fun getSupplier(communicationPreference: CommunicationPreferenceDetailed) =
        communicationPreference.supplierId?.let { id ->
            Supplier(
                id = id,
                code = communicationPreference.supplierCode!!,
                name = communicationPreference.supplierName!!,
            )
        }

    private fun getDistributionCenter(communicationPreference: CommunicationPreferenceDetailed) =
        communicationPreference.dcCode?.let { code ->
            DistributionCenter(
                code = code,
                name = communicationPreference.dcName!!,
            )
        }

    private fun getMarket(communicationPreference: CommunicationPreferenceDetailed) =
        communicationPreference.market?.let {
            Market(code = it)
        }
}

private fun CommunicationPreferenceEnum.toResponse() = when (this) {
    CommunicationPreferenceEnum.EMAIL -> EMAIL
    CommunicationPreferenceEnum.EMAIL_AND_E2OPEN -> EMAIL_AND_E2_OPEN
    CommunicationPreferenceEnum.E2OPEN -> E2_OPEN
}
