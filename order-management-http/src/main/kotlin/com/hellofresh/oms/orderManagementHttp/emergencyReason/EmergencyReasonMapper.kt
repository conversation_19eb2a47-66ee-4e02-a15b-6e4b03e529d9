package com.hellofresh.oms.orderManagementHttp.emergencyReason

import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.orderManagement.generated.api.model.EmergencyReasonApiResponse

fun List<EmergencyReason>.mapToDto() =
    this.map {
        EmergencyReasonDto(
            uuid = it.uuid,
            name = it.name,
            market = it.market,
        )
    }

fun List<EmergencyReasonDto>.mapToApiResponse() =
    this.map {
        EmergencyReasonApiResponse(
            id = it.uuid,
            name = it.name,
            market = it.market,
        )
    }
