package com.hellofresh.oms.orderManagementHttp.workerAction

import com.hellofresh.oms.model.OutboxItemStatus.PENDING
import com.hellofresh.oms.model.PurchaseOrderStatus.DELETED
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData
import com.hellofresh.oms.orderManagement.generated.api.model.SendBulkOrdersRequest
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderEventType
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.outbox.service.OutboxService
import java.time.Clock
import java.time.LocalDateTime
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class SendOrderScheduler(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val workerActionRepository: WorkerActionRepository,
    private val outboxService: OutboxService,
    private val purchaseOrderProducer: PurchaseOrderProducer,
    private val clock: Clock,
) {
    // Asynchronous sending of POs
    fun queueBulkOrders(
        sendBulkOrdersRequest: SendBulkOrdersRequest,
        loggedInUser: LoggedInUserInfo,
    ) {
        val bulkId = UUID.randomUUID()

        logger.info(
            """Sending bulk orders bulkId: $bulkId, orders:
                |[${sendBulkOrdersRequest.purchaseOrderNumbers.distinct().joinToString(",")}]
            """.trimMargin(),
        )

        val purchaseOrders = sendBulkOrdersRequest.purchaseOrderNumbers.distinct()
            .map { poNumber ->
                val purchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(poNumber)
                requireNotNull(purchaseOrder) {
                    "Purchase order not found: $poNumber"
                }
                require(purchaseOrder.status != DELETED) {
                    "Purchase order is deleted: $poNumber"
                }
                require(purchaseOrder.isSynced) {
                    "Purchase order is not synced with OT: $poNumber"
                }
                purchaseOrder
            }
        val workerActions = purchaseOrders
            .map { po ->
                val outboxItem = outboxService.persistOutboxItemFor(po, loggedInUser, PENDING)
                WorkerAction.createWorkerAction(
                    WorkerActionData.SendPurchaseOrder(
                        purchaseOrderNumber = po.poNumber,
                        bulkId = bulkId,
                        outboxItemId = outboxItem.id,
                    ),
                    userEmail = loggedInUser.userEmail,
                    userId = loggedInUser.userId,
                )
            }

        workerActionRepository.saveAll(workerActions)

        purchaseOrders.forEach { po ->
            val persistedPo = purchaseOrderRepository.save(
                po.copy(
                    sendTime = LocalDateTime.now(clock),
                    isSynced = false,
                )
            )
            purchaseOrderProducer.publishPurchaseOrderEvent(persistedPo, PurchaseOrderEventType.STATUS_UPDATED)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
