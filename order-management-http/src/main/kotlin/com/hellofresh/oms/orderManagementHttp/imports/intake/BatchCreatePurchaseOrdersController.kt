package com.hellofresh.oms.orderManagementHttp.imports.intake

import com.hellofresh.oms.orderManagement.generated.api.model.BatchCreatePurchaseOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.routes.BatchCreatePurchaseOrdersApi
import com.hellofresh.oms.orderManagementHttp.authentication.getAllowedMarkets
import com.hellofresh.oms.orderManagementHttp.authentication.getLoggedInUser
import com.hellofresh.oms.orderManagementHttp.imports.intake.BatchCreatePurchaseOrdersController.ProcessType.DRY_RUN
import com.hellofresh.oms.orderManagementHttp.imports.intake.BatchCreatePurchaseOrdersController.ProcessType.PERSIST
import com.hellofresh.oms.orderManagementHttp.imports.service.BatchCreatePurchaseOrderService
import com.hellofresh.oms.orderManagementHttp.imports.service.ImportHistoryService
import io.micrometer.core.instrument.DistributionSummary
import io.micrometer.core.instrument.MeterRegistry
import java.time.Clock
import java.time.LocalDateTime
import org.springframework.core.io.Resource
import org.springframework.http.HttpStatus.CREATED
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@RestController
class BatchCreatePurchaseOrdersController(
    private val service: BatchCreatePurchaseOrderService,
    private val responseConverter: BatchCreatePurchaseOrdersResponseConverter,
    private val importHistoryService: ImportHistoryService,
    private val meterRegistry: MeterRegistry,
    private val clock: Clock,
) : BatchCreatePurchaseOrdersApi {
    override fun batchCreatePurchaseOrders(
        dryRun: Boolean,
        file: MultipartFile?,
    ): ResponseEntity<BatchCreatePurchaseOrdersResponse> {
        requireNotNull(file) { "File cannot be null" }
        requireNotNull(file.originalFilename) { """filename is required in the "multipart/form-data"""" }

        return if (dryRun) {
            recordProcessingTime(DRY_RUN) {
                ResponseEntity.ok(validate(file.resource))
            }
        } else {
            recordProcessingTime(PERSIST) {
                ResponseEntity.status(CREATED).body(validateAndPersist(file.resource))
            }
        }
    }

    private fun validateAndPersist(file: Resource): BatchCreatePurchaseOrdersResponse {
        val filename = file.filename!!
        val user = getLoggedInUser()
        val userAllowedMarkets = user.getAllowedMarkets()

        val purchaseOrders = service.processBatchImport(file.inputStream, userAllowedMarkets)
            .map { service.persistPurchaseOrder(it, user) }

        val purchaseOrderNumbers = purchaseOrders.mapNotNull { it.poNumber }
        val marketCodes = purchaseOrders.map { it.distributionCenter.market }.distinct()
        val importHistory = importHistoryService.createImportHistory(
            filename = filename,
            user = user,
            purchaseOrderNumbers = purchaseOrderNumbers,
            marketCodes = marketCodes,
        )
        service.queueCreatePurchaseOrders(purchaseOrderNumbers, importHistory)

        return responseConverter.convert(purchaseOrders)
    }

    private fun validate(file: Resource): BatchCreatePurchaseOrdersResponse {
        val user = getLoggedInUser()

        val purchaseOrders = service.processBatchImport(file.inputStream, user.getAllowedMarkets())
        return responseConverter.convert(purchaseOrders)
    }

    private fun <R> recordProcessingTime(type: ProcessType, block: () -> R): R {
        val start = LocalDateTime.now(clock)
        try {
            return block()
        } finally {
            DistributionSummary
                .builder("batch_purchase_orders_processing_time")
                .tag("processType", type.name)
                .description(
                    "Records the time taken to process the batch purchase orders, either in dry run or persist mode",
                )
                .register(meterRegistry)
                .record(timeDifferenceInMillis(start, LocalDateTime.now(clock)))
        }
    }

    private fun timeDifferenceInMillis(
        initialDateTime: LocalDateTime,
        finalDateTime: LocalDateTime,
    ) = (
        finalDateTime.toInstant(clock.zone.rules.getOffset(finalDateTime)).toEpochMilli() -
            initialDateTime.toInstant(clock.zone.rules.getOffset(initialDateTime)).toEpochMilli()
        ).toDouble()

    enum class ProcessType {
        DRY_RUN,
        PERSIST
    }
}
