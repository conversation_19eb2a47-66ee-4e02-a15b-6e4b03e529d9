package com.hellofresh.oms.orderManagementHttp.distributionCenters

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.orderManagement.generated.api.model.DcAddressDto
import com.hellofresh.oms.orderManagement.generated.api.model.DistributionCenterDetailedDto

fun List<DistributionCenter>.mapToDto() = this.map {
    DistributionCenterDetailedDto(
        code = it.code,
        name = it.name,
        market = it.market,
        addresses = it.addresses.map { address ->
            DcAddressDto(
                countryCode = address.countryCode,
                address = address.address,
                number = address.number,
                zip = address.zip,
                city = address.city,
                state = address.state,
                type = address.type.name,
                company = address.company,
            )
        }
    )
}
