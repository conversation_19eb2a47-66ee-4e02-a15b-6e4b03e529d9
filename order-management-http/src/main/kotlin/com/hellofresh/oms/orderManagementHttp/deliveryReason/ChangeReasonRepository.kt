package com.hellofresh.oms.orderManagementHttp.deliveryReason

import com.hellofresh.oms.model.ChangeReason
import com.hellofresh.oms.model.ChangeReasonType
import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ChangeReasonRepository : JpaRepository<ChangeReason, UUID> {

    fun findAllByReasonType(reasonType: ChangeReasonType): List<ChangeReason>

    @Query(
        """
            SELECT *
            FROM change_reason
            WHERE (
                UPPER(:allowedMarkets) = ANY (allowed_markets)
                    OR LOWER(:allowedMarkets) = ANY (allowed_markets)
                )
              AND reason_type = :reasonType
        """,
        nativeQuery = true,
    )
    fun findAllByReasonTypeAndAllowedMarkets(reasonType: String, allowedMarkets: String): List<ChangeReason>

    @Query(
        """
            SELECT *
            FROM change_reason
            WHERE id IN :ids
              AND (
                UPPER(:allowedMarket) = ANY (allowed_markets)
                    OR LOWER(:allowedMarket) = ANY (allowed_markets)
                )
        """,
        nativeQuery = true,
    )
    fun findAllByIdsAndAllowedMarket(ids: List<UUID>, allowedMarket: String): List<ChangeReason>
}
