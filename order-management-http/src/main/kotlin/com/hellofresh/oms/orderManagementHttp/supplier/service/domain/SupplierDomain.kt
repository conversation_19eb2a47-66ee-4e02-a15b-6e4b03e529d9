package com.hellofresh.oms.orderManagementHttp.supplier.service.domain

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.supplier.Supplier
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierStatus
import java.time.LocalDateTime
import java.util.UUID
import org.springframework.data.domain.Page
import org.springframework.data.domain.Sort

data class SupplierListDto(
    val pageNumber: Int,
    val pageSize: Int,
    val totalElements: Long,
    val totalPages: Int,
    val sort: Sort,
    val suppliers: List<SupplierDto>,
) {
    companion object {
        fun from(supplierResults: Page<Supplier>) = SupplierListDto(
            pageNumber = supplierResults.number,
            pageSize = supplierResults.size,
            totalElements = supplierResults.totalElements,
            totalPages = supplierResults.totalPages,
            sort = supplierResults.sort,
            suppliers = supplierResults.content.map { supplier -> SupplierDto.from(supplier) }.toList(),
        )
    }
}

data class SupplierDto(
    val id: UUID,
    val code: Int,
    val address: SupplierAddressDto,
    val currency: String,
    val dcCodes: List<String>,
    val market: String,
    val name: String,
    val parentId: UUID,
    val status: SupplierStatus,
    val type: String,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?
) {
    companion object {
        fun from(supplier: Supplier) = SupplierDto(
            id = supplier.id,
            code = supplier.code,
            address = SupplierAddressDto.from(supplier.supplierAddress),
            currency = supplier.currency,
            dcCodes = supplier.dcCodes,
            market = supplier.market,
            name = supplier.name,
            parentId = supplier.parentId,
            status = supplier.status,
            type = supplier.type,
            createdAt = supplier.createdAt,
            updatedAt = supplier.updatedAt,
        )
    }
}

data class SupplierAddressDto(
    val address: String,
    val city: String,
    val countryCode: String,
    val number: String,
    val postalCode: String,
    val region: String,
) {
    companion object {
        fun from(supplierAddress: SupplierAddress) = SupplierAddressDto(
            address = supplierAddress.address,
            city = supplierAddress.city,
            countryCode = supplierAddress.country,
            number = supplierAddress.number,
            postalCode = supplierAddress.postCode,
            region = supplierAddress.state,
        )
    }
}

data class SupplierShippingMethodDto(
    val id: UUID,
    val dcCode: String,
    val supplierId: UUID,
    val market: String,
    val shippingMethod: ShipMethodEnum
) {
    companion object {
        fun from(shipMethod: ShipMethod) = SupplierShippingMethodDto(
            id = shipMethod.uuid,
            dcCode = shipMethod.dcCode,
            supplierId = shipMethod.supplierId,
            market = shipMethod.market,
            shippingMethod = shipMethod.method
        )
    }
}
