package com.hellofresh.oms.orderManagementHttp.workerAction

import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class CreateAndSendOrderScheduler(
    private val workerActionRepository: WorkerActionRepository,
) {
    fun queueCreateAndSendOrder(
        purchaseOrder: PurchaseOrder
    ) {
        workerActionRepository.save(
            WorkerAction.createWorkerAction(
                WorkerActionData.CreateAndSendPurchaseOrder(
                    purchaseOrderId = purchaseOrder.id,
                ),
                userEmail = purchaseOrder.userEmail,
                userId = purchaseOrder.userId,
            ),
        )
        logger.info(
            "CREATE_AND_SEND_ORDER worker action scheduled. [poNumber={}, queueId={}]",
            purchaseOrder.poNumber,
            purchaseOrder.id,
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
