package com.hellofresh.oms.orderManagementHttp.imports.service

import com.hellofresh.oms.model.importHistory.ImportHistory
import com.hellofresh.oms.model.importHistory.ImportHistorySummary.BatchPoCreation
import com.hellofresh.oms.model.importHistory.PurchaseOrderHistory
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.imports.out.ImportHistoryRepository
import com.hellofresh.oms.orderManagementHttp.imports.out.PurchaseOrderHistoryRepository
import java.time.Clock
import java.time.LocalDateTime
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class ImportHistoryService(
    private val importHistoryRepository: ImportHistoryRepository,
    private val purchaseOrderHistoryRepository: PurchaseOrderHistoryRepository,
    private val clock: Clock,
) {
    fun createImportHistory(
        filename: String,
        user: LoggedInUserInfo,
        purchaseOrderNumbers: List<String>,
        marketCodes: List<String>,
    ): ImportHistory = importHistoryRepository.save(
        ImportHistory.createImportHistory(
            filename = filename,
            userId = user.userId,
            userEmail = user.userEmail,
            createdAt = LocalDateTime.now(clock),
            summary = BatchPoCreation(
                purchaseOrders = purchaseOrderNumbers,
                markets = marketCodes,
            ),
        ),
    )

    fun getPurchaseOrdersByNumbers(poNumbers: List<String>): List<PurchaseOrderHistory> =
        purchaseOrderHistoryRepository.findByPoNumberIn(poNumbers)

    fun getImportHistoryListSortedByCreatedAtDescending(market: String, page: Int, size: Int): Page<ImportHistory> =
        importHistoryRepository.findBatchPoCreateByMarketPaginated(
            market = market,
            pageable = PageRequest.of(page, size, Sort.by("created_at").descending()),
        )
}
