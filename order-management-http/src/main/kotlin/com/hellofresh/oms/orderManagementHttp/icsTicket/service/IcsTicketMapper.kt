package com.hellofresh.oms.orderManagementHttp.icsTicket.service

import com.hellofresh.oms.model.icsTicket.IcsTicket
import com.hellofresh.oms.model.icsTicket.TicketPriority
import com.hellofresh.oms.model.icsTicket.TicketStatus
import com.hellofresh.oms.orderManagement.generated.api.model.IcsTicketResponseInner
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketDto

fun IcsTicketDto.toEntity() = IcsTicket(
    ticketId = id,
    market = "US",
    week = customFields.week,
    bobCode = customFields.site,
    skuCode = customFields.skuCode,
    poNumber = customFields.po!!.split('_').first().trim(),
    poReference = customFields.po,
    subject = subject,
    ticketLink = customFields.ticketLink,
    priority = TicketPriority.fromValue(priority),
    requestType = customFields.requestType?.split('-')?.getOrNull(1)?.trim(),
    productionImpact = customFields.productionImpact,
    status = TicketStatus.fromValue(status),
    type = type,
    createdAt = createdAt,
    updatedAt = updatedAt,
)

fun IcsTicket.toResponse() = IcsTicketResponseInner(
    week = week,
    bobCode = bobCode,
    subject = subject,
    poNumber = poNumber,
    skuCode = skuCode,
    ticketLink = ticketLink,
    requestType = requestType,
    ticketId = ticketId,
    status = status.toString(),
    productionImpact = productionImpact,
    createdAt = createdAt,
    updatedAt = updatedAt,
)
