package com.hellofresh.oms.orderManagementHttp.receiptOverride.service

import com.hellofresh.oms.model.imt.recipeOverride.ReceiptOverride
import com.hellofresh.oms.orderManagement.generated.api.model.ReceiptOverrideResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UpsertReceiptOverrideRequest
import java.time.LocalDateTime
import java.util.UUID

fun UpsertReceiptOverrideRequest.toEntity(id: UUID) =
    ReceiptOverride(
        id = id,
        poNumber = poNumber,
        skuCode = skuCode,
        quantity = quantityReceived,
        cases = casesReceived,
        deliveryDate = deliveryDate,
        details = details,
        updatedAt = LocalDateTime.now(),
        createdAt = LocalDateTime.now(),
    )

fun ReceiptOverride.toResponse() =
    ReceiptOverrideResponse(
        poNumber = poNumber,
        skuCode = skuCode,
        quantityReceived = quantity,
        casesReceived = cases,
        deliveryDate = deliveryDate,
        details = details,
    )
