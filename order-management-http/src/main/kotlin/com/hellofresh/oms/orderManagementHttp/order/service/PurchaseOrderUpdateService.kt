package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.exception.PurchaseOrderNotFoundException
import com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.out.GoodsReceivedNoteRepository
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderEventType
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.CommentField
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.EditableField
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.EmergencyReasonField
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.ExpectedTimeField
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.OrderItemsField
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.ShippingMethodField
import io.micrometer.core.annotation.Timed
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import java.time.LocalDateTime
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class PurchaseOrderUpdateService(
    private val purchaseOrderService: PurchaseOrderService,
    private val purchaseOrderValidatorService: PurchaseOrderValidatorService,
    private val meterRegistry: MeterRegistry,
    private val goodsReceiptRepository: GoodsReceivedNoteRepository,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val purchaseOrderProducer: PurchaseOrderProducer,
) {
    @Timed
    fun processEditPurchaseOrder(
        poNumber: String,
        editPurchaseOrderRequest: EditPurchaseOrderRequest,
        loggedInUser: LoggedInUserInfo
    ): UUID {
        logger.info("Editing Purchase Order: poNumber = $poNumber, request = $editPurchaseOrderRequest")
        purchaseOrderValidatorService.validateEditRequest(editPurchaseOrderRequest)

        val persistedPurchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(poNumber)
            ?: throw PurchaseOrderNotFoundException(poNumber)

        val editedFields = EditableField.getListOfChanges(
            editPurchaseOrderRequest,
            persistedPurchaseOrder,
        )
        if (editedFields.isEmpty()) {
            return persistedPurchaseOrder.id
        }

        val market = purchaseOrderValidatorService.validateSupplier(persistedPurchaseOrder.supplierId).market

        purchaseOrderValidatorService.validateEditableFields(
            editedFields,
            market.lowercase(),
            persistedPurchaseOrder.totalPrice.currency
        )

        val updatedPurchaseOrder = createNewRevisionWithEditedFields(
            persistedPurchaseOrder = persistedPurchaseOrder,
            userId = loggedInUser.userId,
            userEmail = loggedInUser.userEmail,
            currentTime = LocalDateTime.now(),
            editedFields = editedFields,
        )

        updatedPurchaseOrder.emergencyReasonUuid?.let {
            purchaseOrderValidatorService.validateEmergencyReason(it)
        }

        val purchaseOrder = purchaseOrderService.saveAndQueuePurchaseOrder(updatedPurchaseOrder, false)

        goodsReceiptRepository.findByPoNumber(poNumber)?.let {
            logger.info("Edited a Purchase Order with an existing GRN. poNumber = $poNumber, GRN = ${it.id}")
            incrementEditedPoWithExistingGrn(persistedPurchaseOrder.status.name, market)
        }

        purchaseOrderProducer.publishPurchaseOrderEvent(purchaseOrder, PurchaseOrderEventType.MODIFIED)

        return purchaseOrder.id
    }

    private fun createNewRevisionWithEditedFields(
        persistedPurchaseOrder: PurchaseOrder,
        userId: UUID,
        userEmail: String,
        currentTime: LocalDateTime,
        editedFields: List<EditableField>,
    ): PurchaseOrder {
        val newRevision = PurchaseOrder.createNewRevisionFrom(
            persistedPurchaseOrder,
            userId,
            userEmail,
            currentTime,
        )

        return editedFields.fold(newRevision) { purchaseOrder, editField ->
            when (editField) {
                is CommentField -> purchaseOrder.copy(comment = editField.comment)
                is ExpectedTimeField -> purchaseOrder.copy(
                    expectedStartTime = editField.expectedStartTime,
                    expectedEndTime = editField.expectedEndTime,
                    deliveryDateChangeReasonId = editField.deliveryDateChangeReasonId,
                )

                is OrderItemsField -> purchaseOrder.copy(
                    totalPrice = editField.totalPrice,
                    orderItems = editField.toOrderItemEntities(purchaseOrder.id, currentTime),
                )

                is ShippingMethodField -> purchaseOrder.copy(shippingMethod = editField.shipMethod)
                is EmergencyReasonField -> purchaseOrder.copy(
                    emergencyReasonUuid = editField.emergencyReasonId,
                )
            }
        }
    }

    private fun incrementEditedPoWithExistingGrn(poStatus: String, market: String) {
        Counter.builder(EDITED_PO_WITH_EXISTING_GRN)
            .tag("po_status", poStatus)
            .tag("market", market)
            .register(meterRegistry)
            .increment()
    }

    companion object {
        private const val EDITED_PO_WITH_EXISTING_GRN = "edited_po_with_existing_grn"
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
