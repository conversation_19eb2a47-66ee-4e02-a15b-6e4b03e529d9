package com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField

import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest

data class CommentField(val comment: String?) : EditableField {
    fun isEqual(comment: String?): Boolean = this.comment == comment

    companion object {
        fun from(
            editPurchaseOrderRequest: EditPurchaseOrderRequest
        ): CommentField? = editPurchaseOrderRequest.comment?.let {
            CommentField(
                it.ifEmpty { null },
            )
        }
    }
}
