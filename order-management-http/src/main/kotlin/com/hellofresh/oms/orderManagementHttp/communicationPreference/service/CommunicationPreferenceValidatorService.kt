package com.hellofresh.oms.orderManagementHttp.communicationPreference.service

import com.hellofresh.oms.model.communicationPreference.CommunicationPreference
import com.hellofresh.oms.orderManagement.generated.api.model.CreateCommunicationPreferenceRequest
import com.hellofresh.oms.orderManagementHttp.communicationPreference.out.CommunicationPreferenceRepository
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.exception.CommunicationPreferenceAlreadyExistException
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import java.util.UUID
import org.springframework.stereotype.Service

@Service
class CommunicationPreferenceValidatorService(
    private val distributionCentersRepository: DistributionCentersRepository,
    private val supplierRepository: SupplierRepository,
    private val communicationPreferenceRepository: CommunicationPreferenceRepository,
) {
    fun validate(createRequest: CreateCommunicationPreferenceRequest): CommunicationPreference = when {
        createRequest.market != null -> validateMarket(createRequest)
        createRequest.distributionCenter != null -> validateDistributionCenter(createRequest)
        createRequest.supplier != null -> validateSupplier(createRequest)
        else -> throw IllegalArgumentException(
            "At least one of market, distribution center or supplier must be present",
        )
    }

    private fun validateMarket(
        createRequest: CreateCommunicationPreferenceRequest
    ): CommunicationPreference {
        val market = createRequest.market!!
        if (!communicationPreferenceRepository.findByMarketIgnoreCase(market.code).isEmpty) {
            throw CommunicationPreferenceAlreadyExistException(
                "A communication preference for the given market already exists",
            )
        }
        return CommunicationPreference(
            id = UUID.randomUUID(),
            market = createRequest.market.code,
            preference = createRequest.preference.toModel(),
        )
    }

    private fun validateDistributionCenter(
        createRequest: CreateCommunicationPreferenceRequest
    ): CommunicationPreference {
        val dc = createRequest.distributionCenter!!
        require(distributionCentersRepository.findByCode(dc.code) != null) {
            "A distribution center with the given code does not exist"
        }

        if (!communicationPreferenceRepository.findByDcCodeIgnoreCase(dc.code).isEmpty) {
            throw CommunicationPreferenceAlreadyExistException(
                "A communication preference for the given distribution center already exists",
            )
        }
        return CommunicationPreference(
            id = UUID.randomUUID(),
            dcCode = dc.code,
            preference = createRequest.preference.toModel(),
        )
    }

    private fun validateSupplier(
        createRequest: CreateCommunicationPreferenceRequest
    ): CommunicationPreference {
        val supplier = createRequest.supplier!!
        require(supplierRepository.findById(supplier.id).isPresent) { "A supplier with the given id does not exist" }

        if (!communicationPreferenceRepository.findBySupplierId(supplier.id).isEmpty) {
            throw CommunicationPreferenceAlreadyExistException(
                "A communication preference for the given supplier already exists",
            )
        }

        return CommunicationPreference(
            id = UUID.randomUUID(),
            supplierId = supplier.id,
            preference = createRequest.preference.toModel(),
        )
    }
}
