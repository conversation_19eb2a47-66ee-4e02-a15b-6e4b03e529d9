package com.hellofresh.oms.orderManagementHttp.order.service.domain

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.orderManagement.generated.api.model.CasePackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PalletPackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.UnitPackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.PurchaseOrderItemDto
import com.hellofresh.oms.orderManagementHttp.order.intake.toDto
import com.hellofresh.oms.util.Calculator
import java.math.BigDecimal
import java.math.RoundingMode

private const val PACKAGING_TYPE_NOT_SUPPORTED = "Packaging type not supported"

sealed class PackagingDomain {
    abstract val packagingType: PackagingType
    abstract val unitOfMeasure: UOM
    fun getTotalNumberOfUnits(): BigDecimal = when (this) {
        is CasePackagingDomain -> this.numberOfCases.toBigDecimal().times(this.unitsPerCase)
        is PalletPackagingDomain -> this.numberOfPallets.toBigDecimal().times(this.casesPerPallet.toBigDecimal())
            .times(this.unitsPerCase)

        is UnitPackagingDomain -> this.numberOfUnits.toBigDecimal()
    }.setScale(Calculator.PRECISION, RoundingMode.HALF_EVEN)

    fun getCasesPerPalletOrNull(): Int? = if (this is PalletPackagingDomain) this.casesPerPallet else null

    fun toPackaging(): Packaging = Packaging(
        packagingType = this.packagingType,
        caseSize = when (this) {
            is CasePackagingDomain -> this.unitsPerCase
            is PalletPackagingDomain -> this.unitsPerCase
            is UnitPackagingDomain -> null
        },
        unitOfMeasure = this.unitOfMeasure,
    )

    fun getPricePerItem(): MoneyDomain<MoneyDomainPrecision.Precision4> = when (this) {
        is CasePackagingDomain -> this.pricePerCase
        is PalletPackagingDomain -> this.pricePerCase
        is UnitPackagingDomain -> this.pricePerUnit
    }

    fun getNumberOfItemsToPurchase(): Int = when (this) {
        is CasePackagingDomain -> this.numberOfCases
        is PalletPackagingDomain -> this.numberOfPallets * this.casesPerPallet
        is UnitPackagingDomain -> this.numberOfUnits
    }

    fun getTotalPrice(): MoneyDomain<MoneyDomainPrecision.Precision4> = TotalPrice.from(
        numberOfItemsToPurchase = this.getNumberOfItemsToPurchase(),
        pricePerItem = this.getPricePerItem().amount,
        currency = this.getPricePerItem().currency,
        MoneyDomainPrecision.Precision4,
    )

    private data class CasePackagingDomain(
        val numberOfCases: Int,
        val unitsPerCase: BigDecimal,
        val pricePerCase: MoneyDomain<MoneyDomainPrecision.Precision4>,
        override val packagingType: PackagingType,
        override val unitOfMeasure: UOM,
    ) : PackagingDomain()

    private data class PalletPackagingDomain(
        val numberOfPallets: Int,
        val casesPerPallet: Int,
        val unitsPerCase: BigDecimal,
        val pricePerCase: MoneyDomain<MoneyDomainPrecision.Precision4>,
        override val packagingType: PackagingType,
        override val unitOfMeasure: UOM,
    ) : PackagingDomain()

    private data class UnitPackagingDomain(
        val numberOfUnits: Int,
        val pricePerUnit: MoneyDomain<MoneyDomainPrecision.Precision4>,
        override val packagingType: PackagingType,
        override val unitOfMeasure: UOM,
    ) : PackagingDomain()

    companion object {
        fun from(packaging: PackagingRequest): PackagingDomain = when (packaging) {
            is CasePackagingRequest -> CasePackagingDomain(
                numberOfCases = packaging.numberOfCases,
                unitsPerCase = packaging.unitsPerCase,
                pricePerCase = MoneyDomain.from(packaging.pricePerCase, MoneyDomainPrecision.Precision4),
                packagingType = packaging.packagingType.toModel(),
                unitOfMeasure = packaging.uom.toDomain(),
            )

            is PalletPackagingRequest -> PalletPackagingDomain(
                numberOfPallets = packaging.numberOfPallets,
                casesPerPallet = packaging.casesPerPallet,
                unitsPerCase = packaging.unitsPerCase,
                pricePerCase = MoneyDomain.from(packaging.pricePerCase, MoneyDomainPrecision.Precision4),
                packagingType = packaging.packagingType.toModel(),
                unitOfMeasure = packaging.uom.toDomain(),
            )

            is UnitPackagingRequest -> UnitPackagingDomain(
                numberOfUnits = packaging.numberOfUnits,
                pricePerUnit = MoneyDomain.from(packaging.pricePerUnit, MoneyDomainPrecision.Precision4),
                packagingType = packaging.packagingType.toModel(),
                unitOfMeasure = UOM.UNIT,
            )

            else -> throw IllegalArgumentException(PACKAGING_TYPE_NOT_SUPPORTED)
        }

        fun from(orderItem: PurchaseOrderItemDto, currency: String): PackagingDomain = when (orderItem.packagingType) {
            PackagingType.UNIT_TYPE -> UnitPackagingDomain(
                numberOfUnits = orderItem.orderSize.toInt(),
                pricePerUnit = MoneyDomain.from(
                    Money(
                        amount = orderItem.price,
                        currency = currency,
                    ).toDto(),
                    MoneyDomainPrecision.Precision4,
                ),
                packagingType = PackagingType.UNIT_TYPE,
                unitOfMeasure = UOM.UNIT,
            )

            PackagingType.CASE_TYPE -> CasePackagingDomain(
                numberOfCases = orderItem.orderSize.toInt(),
                unitsPerCase = orderItem.caseSize,
                pricePerCase = MoneyDomain.from(
                    Money(
                        amount = orderItem.price,
                        currency = currency,
                    ).toDto(),
                    MoneyDomainPrecision.Precision4,
                ),
                packagingType = PackagingType.CASE_TYPE,
                unitOfMeasure = orderItem.uom,
            )

            else -> throw IllegalArgumentException(PACKAGING_TYPE_NOT_SUPPORTED)
        }

        private fun PackagingTypeEnum.toModel(): PackagingType = when (this) {
            PackagingTypeEnum.CASE -> PackagingType.CASE_TYPE
            PackagingTypeEnum.PALLET -> PackagingType.PALLET_TYPE
            PackagingTypeEnum.UNIT -> PackagingType.UNIT_TYPE
        }

        private fun UomEnum.toDomain(): UOM = when (this) {
            UomEnum.UNIT -> UOM.UNIT
            UomEnum.KG -> UOM.KG
            UomEnum.L -> UOM.L
            UomEnum.LBS -> UOM.LBS
            UomEnum.GAL -> UOM.GAL
            UomEnum.OZ -> UOM.OZ
        }
    }
}
