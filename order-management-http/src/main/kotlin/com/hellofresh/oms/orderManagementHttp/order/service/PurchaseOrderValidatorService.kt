package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.orderManagement.generated.api.model.CreateOrderItemRequest
import com.hellofresh.oms.orderManagement.generated.api.model.EditOrderItemRequest
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import com.hellofresh.oms.orderManagement.generated.api.model.OrderItemDto
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderIdentifier
import com.hellofresh.oms.orderManagementHttp.deliveryReason.ChangeReasonRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.exception.OrderCreationException
import com.hellofresh.oms.orderManagementHttp.exception.PurchaseOrderAlreadyExistException
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.order.service.domain.PackagingDomain
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.EditableField
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.EmergencyReasonField
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.ExpectedTimeField
import com.hellofresh.oms.orderManagementHttp.order.service.domain.editableField.OrderItemsField
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import com.hellofresh.oms.orderManagementHttp.uom.UnitOfMeasureRepository
import java.time.LocalDateTime
import java.time.Month
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
@Suppress("TooManyFunctions")
class PurchaseOrderValidatorService(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val changeReasonRepository: ChangeReasonRepository,
    private val skuRepository: SkuRepository,
    private val unitOfMeasureRepository: UnitOfMeasureRepository,
    private val emergencyReasonRepository: EmergencyReasonRepository,
    private val supplierRepository: SupplierRepository,
) {
    fun validateEditableFields(editedFields: List<EditableField>, market: String, persistedCurrency: String) {
        editedFields.forEach { editableField ->
            when (editableField) {
                is ExpectedTimeField -> {
                    require(editableField.deliveryDateChangeReasonId != null) {
                        "Delivery date change reason id is required when changing delivery date"
                    }
                    changeReasonRepository.findById(editableField.deliveryDateChangeReasonId).orElseThrow {
                        OrderCreationException(
                            "Delivery date change reason not found id: ${editableField.deliveryDateChangeReasonId}",
                        )
                    }
                }

                is EmergencyReasonField -> validateEmergencyReason(
                    editableField.emergencyReasonId,
                )

                is OrderItemsField -> {
                    validateUoms(editableField.getUomsForChangedItems(), market = market)

                    validateSkus(editableField.getSkuIds())

                    // validate change reason ids
                    val changeReasonIdForChangedItems: List<UUID?> = editableField.getChangeReasonIdsForChangedItems()

                    val reasonIds = changeReasonIdForChangedItems.filterNotNull().distinct()
                    val existingReasons = changeReasonRepository.findAllByIdsAndAllowedMarket(reasonIds, market)
                    require(reasonIds.size == existingReasons.size) {
                        throw OrderCreationException(
                            "Provided change reason ids not found: " +
                                "${reasonIds.toSet() - existingReasons.map { it.id }.toSet()}",
                        )
                    }

                    // validate currency for changed items
                    val changedItems = editableField.getPricesForChangedItems()
                    changedItems.map { validateCurrency(it.currency, persistedCurrency) }
                }

                else -> Unit
            }
        }
    }

    fun validateEmergencyReason(emergencyReasonId: UUID): EmergencyReason {
        val emergencyReason =
            emergencyReasonRepository.findById(emergencyReasonId).orElseThrow {
                OrderCreationException("Emergency Reason not found id: $emergencyReasonId")
            }

        require(!emergencyReason.disabled) {
            throw OrderCreationException("Provided Emergency Reason is disabled: $emergencyReason")
        }

        return emergencyReason
    }

    fun validateSupplier(supplierId: UUID): SupplierExtended =
        supplierRepository.findById(supplierId).orElseThrow {
            OrderCreationException("Supplier not found id: $supplierId")
        }

    @Throws(OrderCreationException::class)
    fun validateUoms(uoms: List<UOM>, market: String) {
        val persistedUoms = unitOfMeasureRepository.findAllByMarketIgnoreCase(market)
        val missingUoms = uoms.toSet() - persistedUoms.map { it.enumValue }.toSet()

        if (missingUoms.isNotEmpty()) {
            throw OrderCreationException("Provided UOMs not found: $missingUoms")
        }
    }

    @Throws(OrderCreationException::class)
    fun validateSkus(requestedSkuIds: List<UUID>): Set<Sku> {
        val existingSkus = skuRepository.findAllById(requestedSkuIds).toSet()
        require(existingSkus.size == requestedSkuIds.size) {
            val notFoundSkuIds = requestedSkuIds.toSet() - existingSkus.map { it.uuid }.toSet()
            throw OrderCreationException("Provided SKUs not found: $notFoundSkuIds")
        }
        return existingSkus
    }

    fun validateCreateRequest(request: CreatePurchaseOrderRequestDto) {
        validateDeliveryDateRange(
            start = request.deliveryWindow.start,
            end = request.deliveryWindow.end,
        )
        validateOrderItems(request.orderItems.map { it.toOrderItemRequestDto() })
    }

    fun validateEditRequest(request: EditPurchaseOrderRequest) {
        if (request.orderItems != null) {
            requireNotNull(request.orderItems) { "Order Items must be defined if total price is defined." }
            validateOrderItems(request.orderItems.map { it.toOrderItemRequestDto() })
        }

        if (request.deliveryWindow != null) {
            validateDeliveryDateRange(
                start = request.deliveryWindow.start,
                end = request.deliveryWindow.end,
            )
        }
    }

    @Throws(PurchaseOrderAlreadyExistException::class)
    fun validatePurchaseOrderIdentifier(purchaseOrderIdentifier: PurchaseOrderIdentifier) {
        val purchaseOrderExists = purchaseOrderRepository.existsByIdOrPoNumber(
            purchaseOrderIdentifier.id,
            purchaseOrderIdentifier.poNumber,
        )
        if (purchaseOrderExists) {
            logger.warn(
                "Purchase Order already exist for the given id and number. " +
                    "[id=${purchaseOrderIdentifier.id}, poNumber=${purchaseOrderIdentifier.poNumber}]",
            )
            throw PurchaseOrderAlreadyExistException(
                "Purchase Order already exist for the given id and number. " +
                    "[id=${purchaseOrderIdentifier.id}, poNumber=${purchaseOrderIdentifier.poNumber}]",
            )
        }
    }

    private fun validateDeliveryDateRange(start: LocalDateTime, end: LocalDateTime) {
        require(start.isBefore(end) || start == end) { "Delivery start must be before delivery end" }
        require(start.isAfter(HELLO_FRESH_EPOCH) && end.isAfter(HELLO_FRESH_EPOCH)) {
            "Delivery start and end must be after foundation of HelloFresh"
        }
        require(start.second == 0 && end.second == 0) {
            "Delivery date timestamps should not contain seconds"
        }
    }

    private fun validateOrderItems(orderItems: List<OrderItemDto>) {
        require(orderItems.isNotEmpty()) { "Order items list cannot be empty" }
        require(
            orderItems.distinctBy { it.skuId }.size == orderItems.size,
        ) { "Order items must not have repeated SKUs" }

        orderItems.forEach {
            require(PackagingDomain.from(it.packaging).getPricePerItem().isZeroOrGreater()) {
                "Item price amount must be greater or equal to 0. Item: ${it.skuId}"
            }
        }
    }

    private fun CreateOrderItemRequest.toOrderItemRequestDto() = OrderItemDto(
        skuId = this.skuId,
        bufferPercent = this.bufferPercent,
        packaging = this.packaging,
    )

    private fun EditOrderItemRequest.toOrderItemRequestDto() = OrderItemDto(
        skuId = this.skuId,
        bufferPercent = this.bufferPercent,
        packaging = this.packaging,
    )

    private fun validateCurrency(currency: String, supplierCurrency: String) {
        require(currency == supplierCurrency) {
            throw OrderCreationException("Currency mismatch: $currency != $supplierCurrency")
        }
    }

    companion object {
        private const val HF_YEAR = 2011
        private val HELLO_FRESH_EPOCH = LocalDateTime.of(HF_YEAR, Month.OCTOBER, 1, 0, 0)

        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
