package com.hellofresh.oms.orderManagementHttp.emergencyReason

import com.hellofresh.oms.model.EmergencyReason
import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface EmergencyReasonRepository : JpaRepository<EmergencyReason, UUID> {
    fun findAllByMarketIgnoreCaseAndDisabledIsFalse(market: String): List<EmergencyReason>
    fun findByName(reason: String): List<EmergencyReason>
}
