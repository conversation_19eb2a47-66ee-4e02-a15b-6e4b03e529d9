package com.hellofresh.oms.orderManagementHttp.comments.service

import com.hellofresh.oms.model.Comment
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.comments.out.CommentRepository
import com.hellofresh.oms.orderManagementHttp.exception.CommentNotFoundException
import java.time.LocalDateTime
import java.util.UUID
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CommentService(
    private val commentRepository: CommentRepository
) {
    fun getComments(
        filter: GetCommentsFilter
    ) = commentRepository.findAllByDomainInAndResourceTypeInAndYearWeekInAndDcIn(
        domain = filter.domains,
        dcs = filter.dcs,
        resourceTypes = filter.resourceTypes,
        weeks = filter.weeks,
    )

    @Transactional
    fun createComment(
        command: CreateCommentCommand
    ): Comment {
        val now = LocalDateTime.now()
        val newComment = Comment(
            id = UUID.randomUUID(),
            sourceId = null,
            resourceType = command.resourceType,
            resourceId = command.resourceId,
            domain = command.domain,
            dc = command.dc,
            brand = command.brand,
            yearWeek = command.yearWeek,
            comment = command.comment,
            createdBy = command.createdBy,
            createdAt = now,
            updatedBy = command.createdBy,
            updatedAt = now,
        )
        return commentRepository.save(newComment)
    }

    @Transactional
    fun updateComment(id: UUID, newComment: String, updatedBy: String): Comment {
        val existing = commentRepository.findById(id).orElseThrow {
            CommentNotFoundException(id = id)
        }

        val updated = existing.copy(
            comment = newComment,
            updatedBy = updatedBy,
            updatedAt = LocalDateTime.now()
        )

        return commentRepository.save(updated)
    }

    data class GetCommentsFilter(
        val domains: List<String>,
        val resourceTypes: List<String>,
        val weeks: List<YearWeek>,
        val dcs: List<String>,
        val brands: List<String>,
    )

    data class CreateCommentCommand(
        val domain: String,
        val brand: String,
        val dc: String,
        val yearWeek: YearWeek,
        val resourceType: String,
        val resourceId: String,
        val comment: String,
        val createdBy: String,
    )
}
