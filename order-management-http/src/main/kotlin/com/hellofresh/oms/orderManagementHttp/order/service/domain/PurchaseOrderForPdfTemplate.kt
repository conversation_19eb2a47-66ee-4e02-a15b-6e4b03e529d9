package com.hellofresh.oms.orderManagementHttp.order.service.domain

import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.PackagingType.CASE_TYPE
import com.hellofresh.oms.model.PackagingType.PALLET_TYPE
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.CROSSDOCK
import com.hellofresh.oms.model.ShipMethodEnum.FREIGHT_ON_BOARD
import com.hellofresh.oms.model.ShipMethodEnum.OTHER
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.util.Calculator
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.NumberFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle.SHORT
import java.util.Locale
import org.jetbrains.kotlin.utils.addToStdlib.applyIf

data class PurchaseOrderForPdfTemplate(
    val poNumber: String,
    val version: String,
    val updatedAt: String,
    val yearWeek: String,
    val orderedDate: String?,
    val deliveryDate: String,
    val deliveryStartTime: String,
    val deliveryEndTime: String,
    val comment: String?,
    val shippingMethod: String,
    val totalPrice: String,
    val currency: String,
    val from: Address,
    val supplier: Address,
    val shipTo: Address,
    val items: List<OrderItem>,
    val disclaimers: List<String>,
) {

    data class Address(
        val companyName: String? = "",
        val address: String,
        val city: String,
        val state: String,
        val zip: String,
        val country: String,
    )

    data class OrderItem(
        val code: String,
        val name: String,
        val orderSize: String,
        val orderUnit: String,
        val agreedPrice: String,
        val caseSize: String,
        val caseUnit: String,
        val quantity: String,
        val totalAmount: String,
    )

    companion object {
        const val MONEY_PRECISION = 2
        private val humanReadable: DateTimeFormatter = DateTimeFormatter.ofPattern("MMM d, yyyy, HH:mm")
        private val humanReadableWithWeekDay: DateTimeFormatter = DateTimeFormatter.ofPattern("EEE, MMM d, yyyy, HH:mm")
        private val hoursAndMinutes: DateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm")
        private val shortDate = DateTimeFormatter.ofLocalizedDate(SHORT)

        @Suppress("LongParameterList", "LongMethod")
        fun from(
            po: PurchaseOrder,
            supplier: SupplierExtended,
            dcBillingAddress: DistributionCenterAddress,
            disclaimers: List<String>,
            skus: List<Sku>,
            locale: Locale
        ): PurchaseOrderForPdfTemplate {
            val decimalFormat =
                { bd: BigDecimal ->
                    DecimalFormat(
                        "#,##0.##",
                        DecimalFormatSymbols.getInstance(locale)
                    ).format(bd.applyScale())
                }

            val currencyFormat: (BigDecimal) -> String = { bd ->
                NumberFormat.getCurrencyInstance(locale)
                    .apply {
                        minimumFractionDigits = 0
                        maximumFractionDigits = 2
                    }
                    .format(bd.applyScale())
            }

            return PurchaseOrderForPdfTemplate(
                poNumber = po.poNumber,
                version = po.version.toString(),
                updatedAt = po.updatedAt!!.toFormattedString(humanReadableWithWeekDay).plus(" UTC"),
                yearWeek = po.yearWeek.toString(),
                orderedDate = po.sendTime?.toFormattedString(humanReadable)?.plus(" UTC") ?: "UNSENT",
                deliveryDate = po.expectedStartTime.toFormattedString(shortDate.withLocale(locale)),
                deliveryStartTime = po.expectedStartTime.toFormattedString(hoursAndMinutes),
                deliveryEndTime = po.expectedEndTime.toFormattedString(hoursAndMinutes).applyIf(
                    po.expectedStartTime.plusDays(1).dayOfYear == po.expectedEndTime.dayOfYear
                ) {
                    "$this (+1)"
                },
                comment = po.comment,
                shippingMethod = po.shippingMethod.toPlainString(),
                totalPrice = currencyFormat(po.totalPrice.amount),
                currency = po.totalPrice.currency,
                from = Address(
                    companyName = dcBillingAddress.company,
                    address = listOfNotNull(
                        dcBillingAddress.number.takeIf { it.isNotBlank() },
                        dcBillingAddress.address
                    ).joinToString(" "),
                    city = dcBillingAddress.city,
                    state = dcBillingAddress.state,
                    zip = dcBillingAddress.zip,
                    country = dcBillingAddress.countryCode,
                ),
                supplier = Address(
                    companyName = supplier.name,
                    address = supplier.supplierAddress.address,
                    city = supplier.supplierAddress.city,
                    state = supplier.supplierAddress.state,
                    zip = supplier.supplierAddress.postCode,
                    country = supplier.supplierAddress.country,
                ),
                shipTo = Address(
                    companyName = po.shippingAddress.locationName,
                    address = po.shippingAddress.streetAddress,
                    city = po.shippingAddress.city,
                    state = po.shippingAddress.region,
                    zip = po.shippingAddress.postalCode,
                    country = po.shippingAddress.countryCode,
                ),
                items = po.orderItems.map { item ->
                    val sku = skus.find { sku -> sku.uuid == item.skuId }
                        ?: throw IllegalArgumentException("Sku not found. ID: ${item.skuId}")
                    OrderItem(
                        code = sku.code,
                        name = sku.name,
                        orderSize = decimalFormat(
                            Calculator.calculateTotalCaseQuantity(
                                item.totalQty,
                                item.packaging
                            )
                        ),
                        orderUnit = item.packaging.packagingType.toPlainString(),
                        agreedPrice = currencyFormat(item.price.amount),
                        caseSize = item.packaging.caseSize?.let(decimalFormat) ?: "-",
                        caseUnit = item.packaging.unitOfMeasure.name,
                        quantity = decimalFormat(item.totalQty.setScale(2, RoundingMode.HALF_EVEN)),
                        totalAmount = currencyFormat(item.totalPrice.amount),
                    )
                }.sortedBy { it.name },
                disclaimers = disclaimers,
            )
        }
    }
}

private fun LocalDateTime.toFormattedString(format: DateTimeFormatter) =
    format(format)

private fun ShipMethodEnum.toPlainString() = when (this) {
    VENDOR -> "Vendor Delivered"
    FREIGHT_ON_BOARD -> "Freight on Board"
    CROSSDOCK -> "Crossdock"
    OTHER -> "Other"
}

private fun PackagingType.toPlainString() = when (this) {
    UNIT_TYPE -> "unit"
    CASE_TYPE -> "case"
    PALLET_TYPE -> "pallet"
}

private fun BigDecimal.applyScale() = setScale(
    PurchaseOrderForPdfTemplate.MONEY_PRECISION,
    RoundingMode.HALF_EVEN,
)
