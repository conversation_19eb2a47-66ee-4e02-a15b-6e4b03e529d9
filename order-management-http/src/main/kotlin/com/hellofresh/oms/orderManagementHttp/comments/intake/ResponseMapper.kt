package com.hellofresh.oms.orderManagementHttp.comments.intake

import com.hellofresh.oms.model.Comment
import com.hellofresh.oms.orderManagement.generated.api.model.Comment as CommentDto
import com.hellofresh.oms.orderManagement.generated.api.model.CommentListResponse as CommentResponseDto
import org.springframework.stereotype.Service

@Service
class ResponseMapper {
    fun toListResponse(model: List<Comment>) = CommentResponseDto(
        items = model.map {
            CommentDto(
                id = it.id,
                sourceId = it.sourceId,
                resourceType = it.resourceType,
                resourceId = it.resourceId,
                domain = it.domain,
                dc = it.dc,
                comment = it.comment,
                createdBy = it.createdBy,
                updatedAt = it.updatedAt,
                week = it.yearWeek.toString(),
                brand = it.brand
            )
        },
    )
}
