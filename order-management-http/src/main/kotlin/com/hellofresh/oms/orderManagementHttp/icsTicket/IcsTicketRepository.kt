package com.hellofresh.oms.orderManagementHttp.icsTicket

import com.hellofresh.oms.model.icsTicket.IcsTicket
import java.time.LocalDateTime
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface IcsTicketRepository : JpaRepository<IcsTicket, Int>, IcsTicketBulkRepository {

    fun findByPoNumber(poNumber: String): List<IcsTicket>

    @Query("SELECT MAX(t.updatedAt) FROM IcsTicket t")
    fun findLatestUpdatedAt(): LocalDateTime?
}
