package com.hellofresh.oms.orderManagementHttp.orderOverview.intake

import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagement.generated.api.routes.PurchaseOrderStatusApi
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.PurchaseOrderOverviewService
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.mapToPurchaseOrderOverviewModelToApiResponse
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class PurchaseOrderOverviewController(
    private val purchaseOrderOverviewService: PurchaseOrderOverviewService,
) : PurchaseOrderStatusApi {

    override fun poStatusOverview(
        dcWeeks: List<String>,
        brand: String,
        dcCode: String?
    ): ResponseEntity<List<PurchaseOrdersOverviewResponseInner>> {
        val overviewModelList = purchaseOrderOverviewService.getOverview(
            dcCode = dcCode,
            brand = brand,
            dcWeeks = dcWeeks,
        )
        return ResponseEntity.ok(
            overviewModelList.map { it.mapToPurchaseOrderOverviewModelToApiResponse() }
        )
    }
}
