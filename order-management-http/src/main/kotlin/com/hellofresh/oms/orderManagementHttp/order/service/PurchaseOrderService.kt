package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.workerAction.CreateAndSendOrderScheduler
import com.hellofresh.oms.orderManagementHttp.workerAction.CreateOrderScheduler
import io.micrometer.core.annotation.Timed
import java.time.Year
import java.time.format.DateTimeFormatter
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class PurchaseOrderService(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val createOrderScheduler: CreateOrderScheduler,
    private val createAndSendOrderScheduler: CreateAndSendOrderScheduler,
) {
    @Timed
    fun findLatestPurchaseOrderByPoNumber(poNumber: String) =
        purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(poNumber)

    fun saveAndQueuePurchaseOrder(
        purchaseOrder: PurchaseOrder,
        sendImmediately: Boolean,
    ): PurchaseOrder {
        val purchaseOrderEntity = savePurchaseOrder(purchaseOrder)

        if (sendImmediately) {
            createAndSendOrderScheduler.queueCreateAndSendOrder(purchaseOrderEntity)
        } else {
            createOrderScheduler.queueCreateOrder(purchaseOrderEntity)
        }

        return purchaseOrderEntity
    }

    fun savePurchaseOrder(purchaseOrder: PurchaseOrder): PurchaseOrder {
        val savedPurchaseOrder = purchaseOrderRepository.save(purchaseOrder)
        logger.info(
            "Purchase Order created. [poNumber={}, poId={}]",
            savedPurchaseOrder.poNumber,
            savedPurchaseOrder.id,
        )
        return savedPurchaseOrder
    }

    fun generatePoNumber(
        yearWeek: YearWeek,
        dcCode: String,
    ) = Year.of(yearWeek.getYear()).format(DateTimeFormatter.ofPattern("yy")) +
        "%02d".format(yearWeek.getWeek()) + dcCode +
        // This will generate a sequence number within the range of 700,000 to 899,999
        (START_PO_NR_SEQ + purchaseOrderRepository.getNextPoNrSeq() % PO_NR_SEQ_RANGE)

    companion object {
        private const val START_PO_NR_SEQ = 700_000
        private const val PO_NR_SEQ_RANGE = 200_000
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
