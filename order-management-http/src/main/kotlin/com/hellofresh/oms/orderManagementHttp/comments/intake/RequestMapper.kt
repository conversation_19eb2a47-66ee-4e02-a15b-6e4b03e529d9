package com.hellofresh.oms.orderManagementHttp.comments.intake

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.comments.service.CommentService
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import org.springframework.stereotype.Service

@Service
class RequestMapper(
    private val poOverviewConfigs: PoOverviewConfigs,
) {
    fun toGetCommentsFilter(
        dcs: List<String>?,
        resourceTypes: List<String>,
        domains: List<String>,
        weeks: List<YearWeek>,
        brands: List<String>?
    ): CommentService.GetCommentsFilter {
        // When all the DCs for the given brand are requested, there will be no DCs in the request
        // In this case, we can map the DCs based on brands.
        val mappedDcs = if (dcs == null && brands != null) {
            brands.flatMap { brand: String ->
                poOverviewConfigs.brandDcConfig
                    .filter { it.key == brand }
                    .flatMap { it.value.dcs }
            }
        } else {
            dcs
        }

        return CommentService.GetCommentsFilter(
            domains = domains,
            resourceTypes = resourceTypes,
            weeks = weeks,
            dcs = mappedDcs ?: emptyList(),
            brands = brands ?: emptyList(),
        )
    }
}
