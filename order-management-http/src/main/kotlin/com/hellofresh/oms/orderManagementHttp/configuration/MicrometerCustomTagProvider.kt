package com.hellofresh.oms.orderManagementHttp.configuration

import io.micrometer.common.KeyValue
import io.micrometer.common.KeyValues
import org.springframework.context.annotation.Configuration
import org.springframework.http.server.observation.DefaultServerRequestObservationConvention
import org.springframework.http.server.observation.ServerRequestObservationContext

@Configuration
class MicrometerCustomTagProvider : DefaultServerRequestObservationConvention() {
    override fun getLowCardinalityKeyValues(context: ServerRequestObservationContext): KeyValues =
        super.getLowCardinalityKeyValues(context).and(contentType(context)).and(acceptType(context))

    private fun acceptType(context: ServerRequestObservationContext): KeyValue =
        KeyValue.of("accept_header", context.carrier.getHeader("accept") ?: "NOT_SET")

    private fun contentType(context: ServerRequestObservationContext): KeyValue =
        KeyValue.of("content_type", context.carrier.getHeader("content-type")?.removeBoundary() ?: "NOT_SET")

    private fun String.removeBoundary() = this.substringBefore("; boundary")
}
