---
topics:
  purchase-order: public.supply.procurement.purchase-order.v2

server:
  http2:
    enabled: true
  max-http-request-header-size: 1MB
  shutdown: graceful

spring:
  logging:
    level: "DEBUG"

  application:
    name: order-management-service
  datasource:
    driverClassName: org.postgresql.Driver
    url: **************************************************************************************
    username: root
    password: JjORn6khyl7Is6c0hqfM
    hikari:
      connection-timeout: 30000 # 30 seconds
      idle-timeout: 300000 # 5 minutes
      maximum-pool-size: 10
      minimum-idle: 2
      connection-test-query: "SELECT 1"
      validation-timeout: 5000 # 5 second
      max-lifetime: 1800000 # 30 minutes
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    hibernate.ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  lifecycle:
    timeout-per-shutdown-phase: 30s
  servlet:
    multipart:
      max-file-size: 250KB
      max-request-size: 250KB
  security:
    oauth2:
      resourceserver:
        jwt:
          secret-key: ${AUTH_SERVICE_JWT_SECRET_KEY}
          issuer-uri: https://login.microsoftonline.com/${AZURE_TENANT_ID}/v2.0
          jwk-set-uri: https://login.microsoftonline.com/${AZURE_TENANT_ID}/discovery/v2.0/keys
  cloud:
    function:
      definition: purchase-order
    stream:
      bindings:
        purchase-order-out-0:
          destination: ${topics.purchase-order}
          producer:
            use-native-encoding: true
      kafka:
        binder:
          brokers: ${application.brokerAddress}
          autoCreateTopics: false
          consumer-properties:
            key.deserializer: org.apache.kafka.common.serialization.StringDeserializer
          producer-properties:
            key.serializer: org.apache.kafka.common.serialization.StringSerializer
        bindings:
          purchase-order-out-0:
            producer:
              configuration:
                value.serializer: com.hellofresh.oms.orderManagementHttp.configuration.kafka.PurchaseOrderProtoSerializer

springdoc:
  swagger-ui:
    disable-swagger-default-url: true
    config-url: "/docs-oms/v3/api-docs/swagger-config"
    url: "/docs-oms/v3/api-docs"

management:
  endpoint:
    health:
      show-details: "ALWAYS"
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState,binders,diskSpace,db,ping
        liveness:
          include: livenessState,binders,diskSpace,db,ping
  endpoints:
    access:
      default: read_only
    web:
      exposure:
        include: "*"
  server:
    port: 8081
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  info:
    git:
      mode: full
      enabled: true
  tracing:
    propagation:
      type: B3,W3C
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://${OTLP_EXPORTER_HOST:localhost}:9411/api/v2/spans

logging:
  pattern:
    level: "%5p [%MDC{traceId},%MDC{spanId}]"

tapioca:
  base-url: ${TAPIOCA_BASE_URL}
  po-send-url: /purchase-orders/{poId}/send
  po-delete-url: /orders/{poId}

auth-service:
  base-url: ${AUTH_SERVICE_BASE_URL}
  client-id: ${AUTH_SERVICE_CLIENT_ID}
  client-secret: ${AUTH_SERVICE_CLIENT_SECRET}

security:
  allowed-issuers: ${API_ALLOWED_ISSUERS}

webclient:
  connection-timeout: 5s
  response-timeout: 5s
  max-idle-time: 20s
  max-life-time: 60s
  pending-acquire-timeout: 60s
  evict-in-background: 120s

statsig:
  api-key: ${STATSIG_API_KEY}
  environment: ${STATSIG_ENVIRONMENT}
  gates:
    force-email-communication-preference: order_management_-_force_email_communication-preference
    enable-po-topic-v2: order_management_-_enable_po_topic_v2

batch-imports:
  epo-max-lines: 600

po-overview:
  brand-dc-config:
    HF:
      name: "HelloFresh"
      dcs: AZ,KY,NJ,NW,TI
    GC:
      name: "Green Chef"
      dcs: CO,EO,SW
    EP:
      name: "EveryPlate"
      dcs: AZ,SW,TX
    FJ:
      name: "Factor"
      dcs: AC,AG,AU,BO,BR,EO,FK,JO,LV,LZ,PJ,PP,UB
    # This is not a brand, but we use the Warehouse selection in the brand section and named it WH
    WH:
      name: "Warehouse"
      dcs: LZ,HN,LX,LB,RW,JO,YF,FJ,NF,AF,FT,DF,FZ,ZD,FC

gg:
  jte:
    template-location: src/main/jte
    template-suffix: .kte

ics:
  tickets:
    enabled: true
    url: ${ICS_TICKETS_URL}
    username: ${ICS_TICKETS_USERNAME}
    page_size: 100
    update_offset: 30d

pdf:
  disclaimer-per-market:
    au:
      - "For any inquiry or issue, please contact our HelloFresh <NAME_EMAIL>"
    ca:
      - "Send all <NAME_EMAIL>"
      - "Please quote PO# on all invoices to ensure prompt payment. For all payment inquiries, contact <EMAIL>"
      - " \u200E "
      - "TO MAKE INBOUNDING APPOINTMENTS PLEASE EMAIL -"
      - "VersaCold Deliveries: EMAIL <EMAIL> (Subject line must include the site name & PO#: \"Inbound Delivery - Versa Location (Milton/Valley/Edmonton), PO #\")"
      - "18Wheels Ontario Deliveries: EMAIL <EMAIL>"
      - "18Wheels British Colombia Deliveries: EMAIL <EMAIL>"
      - " \u200E "
      - "For Deliveries at HelloFresh DCs, Please book an appointment at https://booking.datadocks.com/sessions/new"
    de:
      - "Hinweis: Bestellnummer bitte unbedingt auf Lieferschein und Rechnung vermerken!"
      - "USt-ID: DE301496358"
    es:
      - "By accepting this purchase order, Supplier agrees to the following terms:"
      - "For HF-managed loads, Supplier shall enter relevant information using BluJay upon PO receipt."
      - "For delivered loads, Supplier shall schedule delivery of the products using BluJay at least twenty-four (24) hours prior to delivery."
    eu:
      - "Please book delivery appointments a minimum of 48 hours in advance with the contact listed below. All delivery notes must be accompanied by a purchase order number. Failure to comply may"
      - "result in your delivery being rejected or delayed unloading."
      - "Deliveries into Hamburg: <EMAIL>"
      - "Deliveries into Birmingham: <EMAIL>"
    gb:
      - "Please remember to book in <NAME_EMAIL> - all delivery notes must be accompanied by a purchase order number."
      - "Failure to comply may result in your delivery being rejected."
    it:
      - "Upon receipt of the Purchase Order, the Supplier must respond in writing, confirming it or requesting a revision, within a period that cannot cause any delay to Hello Fresh production, and in any"
      - "case no later than one (1) working day. For anything not expressly specified in this Purchase Order, refer to the contract between the parties. The contact <NAME_EMAIL>."
      - "VAT *************"
    us:
      - "By accepting this purchase order, Supplier agrees to the following terms:"
      - "For HF-managed loads, Supplier shall enter relevant information using BluJay upon PO receipt."
      - "For delivered loads, Supplier shall schedule delivery of the products using BluJay at least twenty-four (24) hours prior to delivery."
  locale-per-market:
    au: "en-AU"
    beneluxfr: "fr-FR"
    ca: "en-CA"
    dach: "de-DE"
    dkse: "sv-SE"
    es: "es-ES"
    eu: "de-DE"
    fr: "fr-FR"
    gb: "en-GB"
    ie: "en-IE"
    it: "it-IT"
    nl: "nl-NL"
    nz: "en-NZ"
    se: "sv-SE"
    us: "en-US"

resilience4j:
  retry:
    instances:
      publishPurchaseOrder:
        maxAttempts: 3
        waitDuration: 2s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2
        retryExceptions:
          - com.hellofresh.oms.orderManagementHttp.order.out.RetryableEmissionException
        ignoreExceptions:
          - reactor.core.publisher.Sinks.EmissionException

origin-issuers:
  # This is the list of issuers that should have their POs labeled as with "PLANNED" origin
  planned: ${PLANNED_ORIGIN_ISSUERS}
