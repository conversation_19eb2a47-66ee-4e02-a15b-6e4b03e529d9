openapi: 3.0.1
info:
  title: Order Management Service
  description: API Documentation for Order Management Service
  version: 1.0.0
servers:
  - url: /
tags:
  - name: Distribution Centers
    description: APIs intended for fetching distribution centers
  - name: Supplier
    description: Provide Suppliers management APIs.
  - name: Emergency Reasons
    description: Emergency reasons are specified as a part of each order.
  - name: Stock Keeping Units
    description: API intended for fetching list of Stock Keeping Units (SKU)
  - name: Supplier SKU
    description: API intended for fetching List of Stock Keeping Units (SKUs) supported by the supplier
  - name: Purchase order
    description: Purchase order API
  - name: Purchase order status
    description: |
      Purchase order aggregated view which contains information about purchase order status, ASN,
      GRN, acknowledgement, transport management and more
  - name: View State
    description: |
      API that allows users to store, update, read or delete configurations for PO Status View
      (or any other grid or tabular view).
  - name: Comments
    description: |
      API for commenting row or column level information on PO Status View
      (or any other grid or tabular view).
paths:
  /distribution-centers:
    get:
      tags:
        - Distribution Centers
      description: Returns lists of distribution centers
      operationId: getDcs
      parameters:
        - name: market
          description: The market code that corresponds to country
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DistributionCentersResponseDto'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        default:
          $ref: './components/responses/error.yaml#/UnexpectedError'
  /emergency-reasons:
    get:
      tags:
        - Emergency Reasons
      description: >
        Returns the list of enabled emergency methods per market.
      operationId: getEmergencyReasons
      parameters:
        - name: market
          in: query
          description: The code that represent HF market. (ex. US or DACH)
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: '#/components/schemas/EmergencyReasonListApiResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /suppliers:
    get:
      tags:
        - Supplier
      description: >
        Returns supplier info list base on the provided criteria. The result is
        paginated and list of suppliers can be an empty array if search criteria
        is not matched with any suppliers
      operationId: getSuppliers
      parameters:
        - name: market
          in: query
          description: The code that represent HF market. (ex. US or DACH)
          required: true
          schema:
            type: string
        - name: search
          in: query
          description: Name or code of the supplier. (ex. '19th Jan'), it's going to be used in the search query
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: >
            The page number when looking through pages of result.
            This value is zero-based page index and must not be negative.
          required: true
          schema:
            type: integer
        - name: size
          in: query
          description: >
            The size of the page to be returned, must be greater than 0
          required: true
          schema:
            type: integer
        - name: sort
          in: query
          schema:
            $ref: './components/schemas/supplier.yaml#/ListSuppliersSortEnum'
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/supplier.yaml#/SupplierListApiResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /suppliers/{supplier_id}/shipping-methods:
    get:
      tags:
        - Supplier
      description: >
        Returns defaults shipping methods for the supplier and dc_code
      operationId: getSupplierShippingMethods
      parameters:
        - name: supplier_id
          in: path
          description: Supplier ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/supplier.yaml#/SupplierShippingMethodsApiResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /supplier/{supplier_id}/sku:
    get:
      tags:
        - Supplier SKU
      description: Returns list of Stock Keeping Units (SKUs) supported by the supplier
      operationId: getSupplierSkus
      parameters:
        - name: delivery_date
          in: query
          description: >
            Order Delivery Date
          required: true
          schema:
            type: string
          example: "2022-07-28"
        - name: market
          in: query
          description: The code that represents HF market (e.g. US or DACH)
          required: true
          schema:
            type: string
        - name: include_all
          in: query
          description: When true returns both supplier associated and unassociated skus otherwise associated only.
          required: true
          schema:
            default: false
            type: boolean
        - name: dc_code
          in: query
          description: The code that represents Distribution Center (e.g. NJ)
          required: true
          schema:
            type: string
        - name: supplier_id
          in: path
          description: The UUID that represents Supplier
          required: true
          schema:
            type: string
            format: uuid
        - name: search
          in: query
          description: Search pattern to match SKU name or SKU code
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: >
            The page number when looking through pages of result.
            This value is zero-based page index and must not be negative
          required: true
          schema:
            type: integer
        - name: size
          in: query
          description: >
            The size of the page to be returned, must be greater than 0
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: '#/components/schemas/SupplierSkusListApiResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /ics-tickets:
    get:
      tags:
        - ICS Tickets
      description: Returns list of ICS Tickets for specific poNumber and skuCode
      operationId: getIcsTickets
      parameters:
        - name: po_number
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/ics-ticket.yaml#/IcsTicketResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /orders:
    get:
      tags:
        - Purchase order
      description: Returns list of purchase orders
      operationId: getOrders
      parameters:
        - name: dc_weeks
          required: true
          in: query
          schema:
            type: array
            minItems: 1
            items:
              type: string
              pattern: ^20\d{2}-W\d{2}$
          example: "2023-W15"
        - name: dc_codes
          in: query
          required: true
          description: "Distribution center bobcodes"
          schema:
            type: array
            minItems: 1
            items:
              type: string
              example: "BD"
        - name: page
          in: query
          description: >
            The page number when looking through pages of result.
            This value is zero-based page index and must not be negative.
          required: true
          schema:
            type: integer
        - name: size
          in: query
          description: >
            The size of the page to be returned, must be greater than 0
          required: true
          schema:
            type: integer
        - name: delivery_window_start_from
          in: query
          description: |
            Filter orders by delivery window start date lower bound inclusive.
          schema:
            type: string
            format: date-time
          example: "2024-02-02T00:00:00"
        - name: delivery_window_start_to
          in: query
          description: |
            Filter orders by delivery window start date upper bound exclusive.
          schema:
            type: string
            format: date-time
          example: "2024-02-02T23:59:59"
        - name: sort
          in: query
          description: |
            The sorting to be applied on the orders query. |
            To indicate sorting direction, field may be prefixed with + (ascending) or - (descending)
          schema:
            $ref: './components/schemas/enum.yaml#/ListPurchaseOrdersSortEnum'
        - name: shipping_method
          in: query
          description: |
            Filter orders by shipping method.
          schema:
            $ref: './components/schemas/enum.yaml#/ShippingMethodEnum'
        - name: categories
          in: query
          description: |
            Filter orders by categories.
          schema:
            type: array
            minItems: 1
            items:
              type: string
              example: "BAK,BEV,BKD"
        - name: supplier_ids
          in: query
          description: |
            Filter orders by supplier Ids.
          schema:
            type: array
            minItems: 1
            items:
              type: string
              format: uuid
              example: "f7b3f3e3-3e3e-4e3e-3e3e-3e3e3e3e3e3e"
        - name: po_number
          in: query
          description: |
            Filter orders by PO number.
          schema:
            type: string
            example: "2411EC700026"
        - name: status
          in: query
          description: |
            Filter orders by status.
          schema:
            $ref: './components/schemas/enum.yaml#/PurchaseOrderStatusEnum'
        - name: type
          in: query
          description: |
            Filter orders by type.
          schema:
            $ref: './components/schemas/enum.yaml#/PurchaseOrderTypeEnum'
        - name: user_email
          in: query
          description: |
            Filter orders by user email.
          schema:
            type: string
            example: "<EMAIL>"
        - name: sku
          in: query
          description: |
            Filter orders by SKU name or code.
          schema:
            type: string
        - name: send_status
          in: query
          description: Filter orders by the send status.
          schema:
            $ref: './components/schemas/enum.yaml#/PurchaseOrderSendStatusFilterEnum'
        - name: grn_status
          in: query
          description: Filter orders by the GRN status.
          schema:
            $ref: './components/schemas/enum.yaml#/PurchaseOrderGrnStatusFilterEnum'
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/purchase-order.yaml#/ListPurchaseOrdersResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    post:
      tags:
        - Purchase order
      description: Creates purchase order
      operationId: createOrder
      requestBody:
        description: Create Purchase order body
        content:
          'application/json':
            schema:
              $ref: './components/schemas/purchase-order.yaml#/CreatePurchaseOrderRequest'
        required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/purchase-order.yaml#/CreatePurchaseOrderResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /orders/{po_number}:
    get:
      tags:
        - Purchase order
      description: Returns the latest version of a purchase order by the po number
      operationId: getOrder
      parameters:
        - name: po_number
          in: path
          description: Purchase order number
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/purchase-order.yaml#/PurchaseOrderResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    delete:
      tags:
        - Purchase order
      description: Deletes purchase order
      operationId: deleteOrder
      parameters:
        - name: po_number
          in: path
          description: Purchase order number
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Purchase order was deleted
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    patch:
      tags:
        - Purchase order
      description: Updates purchase order
      operationId: updateOrder
      # factor out parameters
      parameters:
        - name: po_number
          in: path
          description: Purchase order number
          required: true
          schema:
            type: string
      requestBody:
        description: Update Purchase order body
        content:
          'application/json':
            schema:
              $ref: './components/schemas/purchase-order.yaml#/EditPurchaseOrderRequest'
        required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/purchase-order.yaml#/CreatePurchaseOrderResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '404':
          $ref: './components/responses/error.yaml#/NotFound'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /orders/{po_number}/items:
    get:
      tags:
        - Purchase order item
      description: Returns list of order items for the latest version of a purchase order
      operationId: getOrderItems
      parameters:
        - name: po_number
          in: path
          description: Purchase order number
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/order-item.yaml#/ListPurchaseOrderItemsResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /orders/{po_number}/send-log:
    get:
      tags:
        - SendLog
      description: Returns list of send log items for a purchase order
      operationId: getSendLog
      parameters:
        - name: po_number
          in: path
          description: Purchase order number
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/send-log.yaml#/SendLogResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /orders/{po_number}/revisions:
    get:
      tags:
        - Purchase order revision
      description: Returns list of revisions for a purchase order
      operationId: getRevisions
      parameters:
        - name: po_number
          in: path
          description: Purchase order number
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/purchase-order.yaml#/ListPurchaseOrderRevisionsResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /orders/{po_number}/reports/{po_uuid}:
    get:
      tags:
        - Purchase order report
      description: Returns a PDF for a purchase order revision
      operationId: generatePdfForPurchaseOrder
      parameters:
        - name: po_number
          in: path
          required: true
          description: Purchase Order number
          schema:
            type: string
        - name: po_uuid
          in: path
          required: true
          description: Purchase Order ID
          schema:
            type: string
      responses:
        '200':
          description: A PDF file representing the Purchase Order revision
          content:
            application/pdf:
              schema:
                type: string
                format: byte
              example: '<Base64-encoded PDF content>'
  /orders/{po_number}/revisions/{po_uuid}/send:
    post:
      parameters:
        - name: po_number
          in: path
          description: The purchase order number.
          required: true
          schema:
            type: string
        - name: po_uuid
          in: path
          description: The purchase order revision uuid. Only the latest purchase order can be sent.
          required: true
          schema:
            type: string
            format: uuid
      tags:
        - Purchase order
      description: Adds a purchase order to the sent outbox and processes it.
      operationId: sendOrders
      responses:
        '200':
          description: The purchase order has been sent successfully.
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /orders/overview:
    get:
      parameters:
        - name: dc_code
          in: query
          description: The distribution center code
          required: false
          schema:
            type: string
        - name: dc_weeks
          required: true
          in: query
          schema:
            type: array
            minItems: 1
            items:
              type: string
              pattern: ^20\d{2}-W\d{2}$
          example: "2023-W15"
        - name: brand
          in: query
          required: true
          schema:
            type: string
      tags:
        - Purchase order status
      description: Provide an overview of the purchase order and aggregate data from various services.
      operationId: poStatusOverview
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/po-status.yaml#/PurchaseOrdersOverviewResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /reasons/{reason_type}:
    get:
      tags:
        - Change Reasons
      parameters:
        - name: market
          in: query
          required: false
          schema:
            type: string
        - name: reason_type
          in: path
          required: true
          schema:
            type: string
            enum:
              - delivery_date
              - order_item_change
      description: >
        Returns a list of change reasons for delivery date, order change
      operationId: getChangeReasons
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: '#/components/schemas/ChangeReasonListApiResponse'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /categories:
    get:
      tags:
        - Categories
      parameters:
        - name: market
          in: query
          required: true
          schema:
            type: string
          example: us
      description: >
        Returns a list of categories by market
      operationId: getCategories
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: '#/components/schemas/CategoryListApiResponse'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /outbox:
    post:
      requestBody:
        description: List of purchase order numbers to be sent
        content:
          'application/json':
            schema:
              properties:
                purchase_order_numbers:
                  type: array
                  minItems: 1
                  items:
                    type: string
              required:
                - purchase_order_numbers
        required: true
      tags:
        - Purchase order
      description: Adds purchase orders to the sent outbox and processes them.
      operationId: sendBulkOrders
      responses:
        '200':
          description: The purchase orders have been successfully added to the outbox.
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /communication-preference/{market}/{dc_code}/{supplier_id}:
    get:
      tags:
        - Communication Preference
      description: >
        Returns communication preferences for a given combination of
        market, distribution center, and supplier ID.
        This endpoint is (to be) used by SPI and OT to determine the
        communication preferences.
      operationId: getCommunicationPreference
      parameters:
        - name: market
          in: path
          description: Market code
          required: true
          schema:
            type: string
        - name: dc_code
          in: path
          description: Distribution center code
          required: true
          schema:
            type: string
        - name: supplier_id
          in: path
          description: Supplier ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/communication-preference.yaml#/CommunicationPreferenceResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /communication-preferences/{scope_type}/{scope_value}:
    patch:
      tags:
        - Communication Preference
      description: >
        Updates communication preferences for a given market, distribution center or supplier
      operationId: updateCommunicationPreferences
      parameters:
        - name: scope_type
          in: path
          required: true
          description: >
            Scope type.
            Possible values are MARKET, DC or SUPPLIER.
          schema:
            $ref: './components/schemas/communication-preference.yaml#/CommunicationPreferenceTypeEnum'
        - name: scope_value
          in: path
          required: true
          description: >
            Scope value.
            If scope type is MARKET, this should be the market code.
            If scope type is DC, this should be the distribution center code.
            If scope type is SUPPLIER, this should be the supplier ID.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './components/schemas/communication-preference.yaml#/UpdateCommunicationPreferenceRequest'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/communication-preference.yaml#/CommunicationPreferenceResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    delete:
      tags:
        - Communication Preference
      description: >
        Deletes communication preferences for a given market, distribution center or supplier
      operationId: deleteCommunicationPreferences
      parameters:
        - name: scope_type
          in: path
          required: true
          description: >
            Scope type.
            Possible values are MARKET, DC or SUPPLIER.
          schema:
            $ref: './components/schemas/communication-preference.yaml#/CommunicationPreferenceTypeEnum'
        - name: scope_value
          in: path
          required: true
          description: >
            Scope value.
            If scope type is MARKET, this should be the market code.
            If scope type is DC, this should be the distribution center code.
            If scope type is SUPPLIER, this should be the supplier ID.
          schema:
            type: string
      responses:
        '204':
          description: Success
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /communication-preferences:
    get:
      tags:
        - Communication Preference
      description: >
        Returns communication preferences for a given market, dc or supplier
      operationId: getCommunicationPreferences
      parameters:
        - name: type
          in: query
          required: true
          description: >
            Communication preference type.
            If not provided, all communication preferences types will be considered.
          schema:
            $ref: './components/schemas/communication-preference.yaml#/CommunicationPreferenceTypeEnum'
        - name: market
          in: query
          description: >
            Market code to be used as a filter of the communication preferences.
            If not provided, all markets will be considered.
            If provided type is MARKET, a single item will be returned.
            If provided type is DC or SUPPLIER, multiple items will be returned filtering by the given market.
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/communication-preference.yaml#/CommunicationPreferencesResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
    post:
      tags:
        - Communication Preference
      description: >
        Creates communication preferences for a given market, dc or supplier
      operationId: createCommunicationPreferences
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './components/schemas/communication-preference.yaml#/CreateCommunicationPreferenceRequest'
      responses:
        '201':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/communication-preference.yaml#/CreateCommunicationPreferenceResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /grns/{po_number}:
    get:
      tags:
        - Goods Received Notes
      description: Returns the GRN for a purchase order number
      operationId: getGrn
      parameters:
        - name: po_number
          in: path
          description: Purchase order number
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/grn.yaml#/GrnResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /units-of-measure:
    get:
      tags:
        - Units of Measure
      description: >
        Returns a list of Units of Measure per market.
      operationId: getUnitsOfMeasure
      parameters:
        - name: market
          in: query
          description: The code that represent HF market. (ex. 'us' or 'dach')
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/uom.yaml#/UnitsOfMeasureListApiResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /imports/orders:
    post:
      tags:
        - Batch create purchase orders
      operationId: batchCreatePurchaseOrders
      parameters:
        - name: dry_run
          in: query
          description: If true, the request will be validated but no purchase orders will be created
          required: false
          schema:
            type: boolean
            default: false
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  format: binary
        required: true
      responses:
        '200':
          description: Success
          content:
            'application/json':
              schema:
                $ref: './components/schemas/imports.yaml#/BatchCreatePurchaseOrdersResponse'
        '201':
          description: Created
          content:
            'application/json':
              schema:
                $ref: './components/schemas/imports.yaml#/BatchCreatePurchaseOrdersResponse'
        '400':
          description: Bad Request
          content:
            'application/json':
              schema:
                $ref: './components/schemas/imports.yaml#/IncorrectImportFileResponse'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '415':
          $ref: './components/responses/error.yaml#/UnsupportedMediaType'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    get:
      tags:
        - Batch import orders history
      description: Returns a list of imported orders history
      operationId: getBatchHistory
      parameters:
        - name: market
          in: query
          description: The code that represents HF market (e.g. US or DACH)
          required: true
          schema:
            type: string
        - name: page
          in: query
          description: >
            The page number when looking through pages of result.
            This value is zero-based page index and must not be negative.
          required: true
          schema:
            type: integer
        - name: size
          in: query
          description: >
            The size of the page to be returned, must be greater than 0
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/imports.yaml#/ListBatchImportOrdersResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        default:
          $ref: './components/responses/error.yaml#/UnexpectedError'
  /static/templates/batch_create_po.csv:
    get:
      tags:
        - Templates
      operationId: getBatchCreatePoTemplate
      responses:
        '200':
          description: Success
          content:
            'text/csv':
              schema:
                type: string
                format: binary
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /service/orders:
    post:
      tags:
        - Service Purchase order
      description: Creates purchase order
      operationId: serviceCreateOrder
      parameters:
        - name: send_immediately
          in: query
          schema:
            type: boolean
            default: false
            description: Indicates whether the purchase order should be sent immediately after creation
      requestBody:
        description: Create Purchase order body
        content:
          'application/json':
            schema:
              $ref: './components/schemas/purchase-order.yaml#/ServiceCreatePurchaseOrderRequest'
        required: true
      responses:
        '201':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/purchase-order.yaml#/ServiceCreatePurchaseOrderResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '409':
          $ref: './components/responses/error.yaml#/Conflict'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'

  /view-states:
    post:
      tags:
        - View State
      description: Creates a new view state configuration for a resource for the current user
      operationId: createViewState
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './components/schemas/view-state.yaml#/CreateViewStateRequest'
      responses:
        '201':
          description: View state was successfully created
          content:
            application/json:
              schema:
                $ref: './components/schemas/view-state.yaml#/CreateViewStateResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    get:
      tags:
        - View State
      description: Returns the list of all view states for the given resource
      operationId: listViewStates
      parameters:
        - name: resource
          in: query
          required: true
          description: >
            String identifier for a resource in the global procurement platform (ops. portal).
            Should be human readable.
          schema:
            type: string
          example: "om-po-status-view"
      responses:
        '200':
          description: Returns the list of states for selected view for current user without pagination
          content:
            application/json:
              schema:
                $ref: './components/schemas/view-state.yaml#/ListViewStateResponse'
  /view-states/{state_id}:
    get:
      tags:
        - View State
      description: Retrieves the view state configuration for a specific user and state ID
      operationId: getViewState
      parameters:
        - name: state_id
          in: path
          description: Unique identifier for the view state
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/view-state.yaml#/ViewStateResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '404':
          $ref: './components/responses/error.yaml#/NotFound'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    patch:
      tags:
        - View State
      description: Patches an existing state
      operationId: updateViewState
      parameters:
        - name: state_id
          in: path
          description: Unique identifier for the view state
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './components/schemas/view-state.yaml#/UpdateViewStateRequest'
      responses:
        '201':
          description: View state was successfully updated
          content:
            application/json:
              schema:
                $ref: './components/schemas/view-state.yaml#/CreateViewStateResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    delete:
      tags:
        - View State
      description: Deletes an existing state
      operationId: deleteViewState
      parameters:
        - name: state_id
          in: path
          description: Unique identifier for the view state
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/view-state.yaml#/CreateViewStateResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '404':
          $ref: './components/responses/error.yaml#/NotFound'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /comments:
    get:
      tags:
        - Comments
      description: Returns list of comments for specified resources
      operationId: getComments
      parameters:
        - name: dcs
          in: query
          description: List of distribution center codes
          required: false
          schema:
            type: array
            items:
              type: string
        - name: resource_type
          in: query
          description: List of resource types (e.g. sku, po, etc.)
          required: true
          schema:
            type: array
            items:
              type: string
        - name: domains
          in: query
          description: List of domains (imt, gpp-po-status)
          required: true
          schema:
            type: array
            items:
              type: string
        - name: weeks
          in: query
          description: List of weeks in format YYYY-Www
          required: true
          schema:
            type: array
            items:
              type: string
              pattern: ^20\d{2}-W\d{2}$
        - name: brands
          in: query
          description: Comma separated list of brands
          required: false
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: './components/schemas/comment.yaml#/CommentListResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
    post:
      tags:
        - Comments
      description: Creates a new comment
      operationId: createComment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './components/schemas/comment.yaml#/CreateCommentRequest'
      responses:
        '201':
          description: Comment created successfully
          content:
            application/json:
              schema:
                $ref: './components/schemas/comment.yaml#/CreateCommentResponse'
        '400':
          $ref: './components/responses/error.yaml#/BadRequest'
        '401':
          $ref: './components/responses/error.yaml#/Unauthorized'
        '403':
          $ref: './components/responses/error.yaml#/Forbidden'
        '500':
          $ref: './components/responses/error.yaml#/InternalServerError'
  /comments/{id}:
    patch:
      tags:
        - Comments
      summary: Update the comment text
      operationId: updateComment
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: './components/schemas/comment.yaml#/UpdateCommentRequest'
      responses:
        '200':
          description: The updated comment
          content:
            application/json:
              schema:
                $ref: './components/schemas/comment.yaml#/CreateCommentResponse'
        '404':
          description: Comment not found

components:
  schemas:
    DistributionCentersResponseDto:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/DistributionCenterDetailedDto'
    DistributionCenterDetailedDto:
      required:
        - code
        - name
        - market
        - addresses
      type: object
      properties:
        code:
          type: string
          example: NJ
        name:
          type: string
          example: New Jersey
        market:
          type: string
          example: US
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/DcAddressDto'
    DcAddressDto:
      required:
        - address
        - number
        - zip
        - city
        - state
        - type
        - country_code
      type: object
      properties:
        country_code:
          type: string
          example: US
        address:
          type: string
          example: Hauptstrasse
        number:
          type: string
          example: '132'
        zip:
          type: string
          example: '10435'
        city:
          type: string
          example: Berlin
        state:
          type: string
          example: Berlin
        company:
          type: string
          example: Company
        type:
          type: string
          example: BILLING
    PriceMoneyDto:
      required:
        - amount
        - currency
        - type
      type: object
      properties:
        amount:
          type: string
          example: '50.42'
        currency:
          type: string
          example: EUR
        type:
          type: string
          example: case
    EmergencyReasonListApiResponse:
      type: object
      properties:
        items:
          type: array
          minItems: 0
          items:
            $ref: '#/components/schemas/EmergencyReasonApiResponse'
      required:
        - items
    EmergencyReasonApiResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier for Emergency Reasons
        name:
          type: string
          example: Ordering Error, Repackaging
          description: A short string describing the Emergency Reason
        market:
          type: string
          example: US
      required:
        - id
        - name
        - market
    ChangeReasonListApiResponse:
      type: object
      properties:
        type:
          type: string
        items:
          type: array
          minItems: 0
          items:
            $ref: '#/components/schemas/ChangeReasonApiResponse'
      required:
        - type
        - items
    ChangeReasonApiResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier for Delivery Reasons
        name:
          type: string
          example: "Buyer corrections: Peak management"
          description: A short string describing the Delivery Date Change Reason
        allowed_markets:
          type: array
          minItems: 0
          items:
            type: string
      required:
        - id
        - name
        - allowed_markets
    SortApiResponse:
      type: object
      properties:
        direction:
          type: string
          example: ASC
          description: Shows the sorting of the list
          enum:
            - ASC
            - DESC
        property:
          type: string
          description: The sorting is based on which property
      required:
        - direction
        - property
    PageResultApiResponse:
      type: object
      properties:
        number:
          type: integer
          description: The requested page number
        page_size:
          type: integer
          description: The requested page size
        total_elements:
          type: number
          description: >
            The total number of items available once the search criteria applied
        total_pages:
          type: integer
          description: >
            The total number of items available once the search criteria applied
        sort:
          type: array
          items:
            $ref: '#/components/schemas/SortApiResponse'
          minItems: 1
      required:
        - number
        - page_size
        - total_elements
        - total_pages
        - sort
    SupplierSkusListApiResponse:
      type: object
      properties:
        page_result:
          $ref: '#/components/schemas/PageResultApiResponse'
        items:
          type: array
          minItems: 0
          items:
            $ref: '#/components/schemas/SupplierSkuApiResponse'
      required:
        - items
        - page_result
    SupplierSkuApiResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: The unique identifier for SKU
        code:
          type: string
          example: PHF-10-10065-4
        status:
          type: string
          example: ACTIVE
          description: "Status of the supplier SKU. Null when the SKU does not belong to the selected supplier"
        skuCategory:
          type: string
          example: "PHF"
        name:
          type: string
          example: Carrot - 3 Ounce (oz)
        case_size:
          type: integer
          example: 32
          deprecated: true
        units_per_case:
          type: string
          example: "4.52"
        cases_per_pallet:
          type: integer
          example: 10
          description: "The number of cases per pallet."
        price:
          $ref: '#/components/schemas/PriceMoneyDto'
        buffer:
          type: number
          example: 10.50
          description: "A decimal representing the percentage buffer. Eg 10 -> 10% buffer."
        uom:
          type: string
          example: 'KG'
          enum:
            - UNIT
            - KG
            - L
            - LBS
            - GAL
            - OZ
        supplier_uuid:
          type: string
          format: uuid
          description: "When supplier id is null - SKU does not belong to the selected supplier"
      required:
        - id
        - code
        - name
    CategoryListApiResponse:
      type: object
      properties:
        items:
          type: array
          minItems: 0
          items:
            $ref: '#/components/schemas/CategoryResponse'
      required:
        - items
    CategoryResponse:
      type: object
      properties:
        code:
          type: string
          description: The unique identifier for Category
      required:
        - id

  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - Bearer: [ ]
