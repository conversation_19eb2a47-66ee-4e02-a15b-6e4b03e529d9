GrnResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: "Unique identifier which represent the GRN"
    summarized_delivery_lines:
      type: array
      items:
        $ref: '#/GrnDeliveryLineDto'
    status:
      $ref: './enum.yaml#/PurchaseOrderGrnStatusEnum'

GrnDeliveryLineDto:
  type: object
  properties:
    sku_code:
      type: string
      description: "SKU code"
    sku_name:
      type: string
      description: "SKU name"
    received_quantity:
      type: number
      description: "Quantity received"
    sku_uom:
      $ref: './enum.yaml#/GrnDeliveryLineUnitOfMeasureEnum'

