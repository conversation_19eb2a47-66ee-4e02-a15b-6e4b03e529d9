CreateViewStateRequest:
  type: object
  required:
    - state
    - resource
    - name
  properties:
    resource:
      type: string
      description: "A string representing view name"
    name:
      description: "A user provided name for a state in the specified view"
      type: string
    state:
      $ref: '#/ViewState'

UpdateViewStateRequest:
  type: object
  properties:
    resource:
      type: string
      description: "A string representing view name"
    name:
      description: "A user provided name for a state in the specified view"
      type: string
    state:
      $ref: '#/ViewState'

CreateViewStateResponse:
  type: object
  required:
    - id
  properties:
    id:
      type: integer
      format: int64
      description: "ID of the view state"

ViewStateResponse:
  type: object
  required:
    - id
    - state
    - name
    - resource
  properties:
    id:
      type: integer
      format: int64
      description: "ID of the view state"
    resource:
      type: string
      description: "A string representing view name"
    name:
      description: "A user provided name for the state for this view"
      type: string
    state:
      $ref: '#/ViewState'

ListViewStateResponse:
  type: object
  required:
    - items
  properties:
    items:
      type: array
      items:
        $ref: '#/ViewStateListItem'

ViewStateListItem:
  type: object
  required:
    - id
    - name
  properties:
    id:
      type: integer
      format: int64
      description: "ID of the view state"
    name:
      description: "A user provided name for the state for this view"
      type: string

ViewState:
  type: object
  description: "State of a view described by a collection of column and filter configurations"
  additionalProperties: true
