CreateOrderItemRequest:
  type: object
  allOf:
    - $ref: "#/OrderItemDto"

OrderItemDto:
  type: object
  required:
    - sku_id
    - buffer_percent
    - packaging
  properties:
    sku_id:
      type: string
      format: uuid
      description: The SKU uuid. Must be unique within the order.
    buffer_percent:
      description: "Represents the buffer in percent. Eg 10 -> 10% buffer -> 0.1 buffer"
      type: number
      minimum: 0
    packaging:
      $ref: '#/PackagingRequest'

ChangedOrderItemRequestDto:
  allOf:
    - $ref: "#/OrderItemDto"
    - type: object
      properties:
        change_reason_id:
          type: string
          format: uuid
          description: |
            UUID which represent the reason for changing the order item's properties.
            Required when order item is changed. Must be of type `order_item_change`.
EditOrderItemRequest:
  type: object
  allOf:
    - $ref: "#/ChangedOrderItemRequestDto"

ListPurchaseOrderItemsResponse:
  type: object
  required:
    - order_items
  properties:
    order_items:
      type: array
      items:
        $ref: "#/OrderItemResponse"

OrderItemResponse:
  type: object
  required:
    - id
    - sku
  allOf:
    - $ref: '#/OrderItemDto'
  properties:
    id:
      type: string
      format: uuid
    correction_reason:
      type: string
    change_reason_id:
      type: string
      description: "Unique identifier which represent the reason for changing the order item's properties. Required when order item is changed."
      format: uuid
    created_at:
      type: string
      format: date-time
    updated_at:
      type: string
      format: date-time
    sku:
      $ref: '#/SkuResponse'

SkuResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: The sku identifier
    name:
      type: string
      example: Carrot - 3 Ounce (oz)
    code:
      type: string
      example: PHF-10-10065-4
    market:
      type: string
      example: us
    status:
      type: string
      example: ACTIVE
    brands:
      type: array
      items:
        type: string
      example: [ "brand1", "brand2" ]
    category:
      type: string
      example: "BAK"
    uom:
      $ref: './enum.yaml#/UomEnum'
  required:
    - id
    - name
    - code
    - market
    - status
    - brands
    - category

################ Packaging - Start ################
PackagingRequest:
  type: object
  properties:
    packaging_type:
      $ref: './enum.yaml#/PackagingTypeEnum'
  required:
    - packaging_type
  discriminator:
    propertyName: packaging_type
    mapping:
      'UNIT': '#/UnitPackagingRequest'
      'CASE': '#/CasePackagingRequest'
      'PALLET': '#/PalletPackagingRequest'

UnitPackagingRequest:
  required:
    - price_per_unit
    - number_of_units
  allOf:
    - $ref: "#/PackagingRequest"
    - type: object
      properties:
        price_per_unit:
          $ref: './money.yaml#/MoneyDto'
          description: "Price of each unit."
        number_of_units:
          type: integer
          example: 10
          minimum: 1
          description: "Number of units after buffer is applied."

CasePackagingRequest:
  allOf:
    - $ref: "#/PackagingRequest"
    - type: object
      required:
        - number_of_cases
        - units_per_case
        - uom
        - price_per_case
      properties:
        number_of_cases:
          minimum: 1
          type: integer
          example: 10
          description: "Number of cases after buffer is applied."
        units_per_case:
          minimum: 0.0001
          type: number
          example: 32.2
          description: "The number of units in each case. (eg. 32.2 liters per case)"
        price_per_case:
          $ref: './money.yaml#/MoneyDto'
          description: "Price of each case"
        uom:
          $ref: './enum.yaml#/UomEnum'

PalletPackagingRequest:
  required: [ number_of_pallets, cases_per_pallet, units_per_case, price_per_case, uom ]
  allOf:
    - $ref: "#/PackagingRequest"
    - type: object
      properties:
        number_of_pallets:
          minimum: 1
          type: integer
          example: 10
          description: "Number of pallets after buffer is applied."
        cases_per_pallet:
          minimum: 1
          type: integer
          example: 10
          description: "Number of cases in each pallet"
        units_per_case:
          minimum: 0.0001
          type: number
          example: 32.2
          description: "The number of units in each case. (eg. 32.2 liters per case)"
        price_per_case:
          $ref: './money.yaml#/MoneyDto'
          description: "Price of each case. Note: there's no price per pallet."
        uom:
          $ref: './enum.yaml#/UomEnum'
################ Packaging - End ################
