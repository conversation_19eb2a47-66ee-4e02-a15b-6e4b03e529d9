PurchaseOrdersOverviewResponse:
  type: array
  items:
    required:
      - po_id
      - po_number
      - po_version
      - status
      - supplier_name
      - supplier_code
      - sku_name
      - sku_code
      - scheduled_delivery_date
      - emergency_reason
      - creator_email
      - week
      - dc_code
      - brand
      - category
      - order_size
      - case_price
      - case_size
      - order_unit
      - purchasing_unit
      - total_price
      - quantity_ordered
      - shipping_method
      - risk_assessment
      - receipt_override
    type: object
    properties:
      po_id:
        type: string
        format: uuid
      po_number:
        type: string
        example: "2318NJ021004"
        displayName: "Purchase Order Number"
      po_version:
        type: integer
        description: "Version of the PO"
      status:
        $ref: './enum.yaml#/PoOverviewStatusEnum'
      supplier_name:
        type: string
        example: "Seneca Foods"
      supplier_code:
        type: string
      sku_name:
        type: string
        example: "Corn Kernels - 1 Unit"
      sku_code:
        type: string
        example: "DRY-10-10483-1"
      scheduled_delivery_date:
        type: string
        format: date-time
        example: "2025-04-16T00:00:00Z"
      emergency_reason:
        type: string
      creator_email:
        type: string
        description: "Email of the user who created the PO"
      week:
        type: string
        example: "2025-W18"
      dc_code:
        type: string
        example: "NJ"
      brand:
        type: string
        example: "HF"
      category:
        type: string
        example: "Grocery"
      order_size:
        type: number
        format: integer
      case_price:
        $ref: './money.yaml#/MoneyDto'
        description: "The price per case in the currency of the PO"
      case_size:
        type: number
        format: double
      order_unit:
        $ref: './enum.yaml#/UomEnum'
      purchasing_unit:
        $ref: './enum.yaml#/UomEnum'
      total_price:
        $ref: './money.yaml#/MoneyDto'
        description: "The total price of the PO item in the currency of the PO"
      quantity_ordered:
        type: number
        format: double
      load_number:
        type: string
        example: "2318NJ021001"
      appointment_time:
        type: string
        format: date-time
        example: "2025-05-03T10:00:00Z"
      carrier_name:
        type: string
        example: "GLOBAL LOGISTICS"
      locality:
        type: string
        example: "HARRISON, NJ"
      pallet_count:
        type: integer
      shipping_method:
        $ref: './enum.yaml#/PoOverviewShipMethodEnum'
      proposed_quantity_cases:
        type: number
        format: double
        description: |
          The quantity proposed by the supplier in cases. This information is communicated via the Acknowledgement message.
          IF unit-of-measure = CASE THEN size
          IF unit-of-measure = UNITS THEN blank
      proposed_quantity_units:
        type: number
        format: double
        description: |
          The quantity proposed by the supplier in units. This information is communicated via
          the Acknowledgement message.
          IF unit-of-measure = CASE then (size * packing-size)
          IF unit-of-measure = UNITS then size
      proposed_units_per_case:
        type: number
        format: double
        description: |
          The unit per case proposed by the supplier. This information is communicated via the Acknowledgement message.
          IF unit-of-measure = CASE then packing-size
          IF unit-of-measure = UNIT then blank
      proposed_delivery_date:
        type: string
        format: date-time
        description: |
          The promised delivery date proposed by the supplier. This information is communicated via
          the Acknowledgement message.
      asn_shipment_date:
        type: string
        format: date-time
        description: |
          The shipment date proposed by the supplier. This information is communicated via the ASN message.
      asn_planned_delivery_time:
        type: string
        format: date-time
        description: |
          The planned delivery time proposed by the supplier. This information is communicated via the ASN message.
      asn_shipped_quantity_cases:
        type: number
        format: double
        description: |
          The shipped quantity in cases proposed by the supplier. This information is communicated via the ASN message.
      asn_units_of_measure:
        type: string
        description: |
          The unit of measure proposed by the supplier. This information is communicated via the ASN message.
      asn_case_size:
        type: number
        format: double
        description: |
          The case size proposed by the supplier. This information is communicated via the ASN message.
      asn_shipped_quantity_units:
        type: number
        format: double
        description: |
          The shipped quantity in units proposed by the supplier. This information is communicated via the ASN message.
      has_ics_tickets:
        type: boolean
      quantity_received:
        type: number
        format: double
      cases_received:
        type: integer
        format: integer
      case_size_received:
        type: number
        format: double
      risk_assessment:
        type: string
        enum:
          - LOW
          - MEDIUM
          - HIGH
          - NONE
      assigned_buyer:
        type: string
        example: "Alice Johnson"
      phf_delivery_percent_of_forecast:
        type: number
        format: double
      receipt_override:
        type: object
        properties:
          quantity:
            type: integer
          cases_received:
            type: integer
          delivery_date:
            type: string
            format: date-time
          details:
            type: string
          created_at:
            type: string
            format: date-time
          updated_by:
            type: string
