DeliveryWindowResponseDto:
  type: object
  allOf:
    - $ref: '#/DeliveryWindowDto'
    - properties:
        change_reason_id:
          type: string
          format: uuid
          description: "Unique identifier which represent the change reason"

DeliveryWindowDto:
  type: object
  required:
    - start
    - end
  properties:
    start:
      type: string
      format: date-time
      description: "Delivery window start date and time"
    end:
      type: string
      format: date-time
      description: "Delivery window end date and time"

EditDeliveryWindowDto:
    type: object
    allOf:
        - $ref: '#/DeliveryWindowResponseDto'
