SupplierListApiResponse:
  type: object
  properties:
    suppliers:
      type: array
      minItems: 0
      items:
        $ref: '#/SupplierResponse'
    page_result:
      required:
        - sort
      type: object
      allOf:
        - $ref: './page-result.yaml#/PageResultDto'
      properties:
        sort:
          type: array
          items:
            $ref: '#/ListSuppliersSortEnum'
  required:
    - page_result
    - suppliers

SupplierShippingMethodsApiResponse:
  type: object
  properties:
      default_shipping_method:
          $ref: './enum.yaml#/ShipMethodEnum'
      shipping_methods:
          type: array
          items:
              $ref: '#/ShippingMethodResponse'
  required:
    - shipping_methods

ShippingMethodResponse:
  type: object
  properties:
    id:
        type: string
        format: uuid
        description: "UUID of the supplier shipping method"
    dc_code:
        type: string
        description: "Distribution center code"
    supplier_id:
        type: string
        format: uuid
        description: "Supplier UUID"
    market:
        type: string
        description: "Market code"
        example: "dkse"
    shipping_method:
      $ref: './enum.yaml#/ShipMethodEnum'


#At the moment, the codebase is only sorting by name ascending (hardcoded)
# see com.hellofresh.oms.orderManagementHttp.supplier.SupplierService#getSuppliers
ListSuppliersSortEnum:
  type: string
  enum:
    - "+name"
    - "-name"

SupplierResponse:
  required:
    - id
    - code
    - address
    - currency
    - market
    - name
    - parent_id
    - status
    - type
#    - created_at
#    - updated_at
  type: object
  properties:
    id:
        type: string
        format: uuid
        description: "Unique identifier of the supplier"
    code:
        type: integer
        description: "Code of the supplier"
    address:
        $ref: './address.yaml#/SupplierAddressDto'
    currency:
        type: string
        format: iso-4217
        description: "Three letter ISO currency code"
    market:
        type: string
        format: iso-3166-alpha-2
        description: "Two letter ISO country code"
    name:
        type: string
        description: "Name of the supplier"
    parent_id:
        type: string
        format: uuid
        description: "Uuid of the facility"
    status:
        $ref: '#/SupplierStatusEnum'
    type:
        type: string
        description: "Type of the supplier"
    created_at:
        type: string
        format: date-time
        description: "Date and time of the creation of the supplier"
    updated_at:
        type: string
        format: date-time
        description: "Date and time of the last update of the supplier"

SupplierStatusEnum:
    type: string
    enum:
      - ACTIVE
      - ONBOARDING
      - INACTIVE
      - ARCHIVED
      - OFFBOARDING
    description: "Status of the supplier"
