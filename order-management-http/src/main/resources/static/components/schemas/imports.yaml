BatchCreatePurchaseOrdersResponse:
  required:
    - purchase_orders
  type: object
  properties:
    purchase_orders:
      type: array
      items:
        $ref: "#/PurchaseOrderPreviewDto"

IncorrectImportFileResponse:
  type: object
  properties:
    line_errors:
      type: array
      minItems: 0
      items:
        $ref: "#/LineError"
  required:
    - line_errors

ListBatchImportOrdersResponse:
  required:
    - items
    - page_result
  type: object
  properties:
    items:
      type: array
      items:
        $ref: "#/BatchImportOrdersResponse"
    page_result:
      required:
        - sort
      type: object
      allOf:
        - $ref: './page-result.yaml#/PageResultDto'

BatchImportOrdersResponse:
  required:
    - id
    - filename
    - uploaded_by
    - uploaded_at
    - distribution_centers
    - weeks
    - purchase_orders
  type: object
  properties:
    id:
      type: string
      format: uuid
    filename:
      type: string
      example: "file.csv"
    uploaded_by:
      type: string
      example: "<EMAIL>"
    uploaded_at:
      type: string
      format: date-time
    distribution_centers:
      type: array
      items:
        type: string
      example: ['NJ']
    weeks:
      type: array
      items:
        type: string
      example: ['2025-W01', '2025-W02']
    purchase_orders:
      type: array
      items:
        type: string
      example: ['202502SF755226']

LineError:
  type: object
  properties:
    line_number:
      type: integer
      example: 1
    error_message:
      type: string
      example: some error message
  required:
    - line_number
    - error_message

PurchaseOrderPreviewDto:
  type: object
  required:
    - week
    - distribution_center
    - supplier
    - shipping_method
    - emergency_reason
    - delivery_window
    - order_items
  properties:
    id:
      type: string
      format: uuid
    po_number:
      type: string
    week:
      type: string
      pattern: '^20\d{2}-W\d{2}$'
      example: "2023-W15"
    distribution_center:
      $ref: '#/DistributionCenterDto'
    supplier:
      $ref: '#/SupplierDto'
    shipping_method:
      $ref: './enum.yaml#/ShipMethodEnum'
    emergency_reason:
      $ref: '#/EmergencyReasonDto'
    delivery_window:
      $ref: './delivery-window.yaml#/DeliveryWindowDto'
    comments:
      type: string
    order_items:
      type: array
      items:
        $ref: '#/BatchOrderItemDto'

DistributionCenterDto:
  type: object
  properties:
    code:
      type: string
    name:
      type: string

SupplierDto:
  type: object
  properties:
    id:
      type: string
      format: uuid
    code:
      type: string
    name:
      type: string

EmergencyReasonDto:
  type: object
  properties:
    id:
      type: string
      format: uuid
    name:
      type: string

BatchOrderItemDto:
  type: object
  properties:
    sku:
      $ref: '#/SkuDto'
    buffer_percent:
      description: "Represents the buffer in percent. Eg 10 -> 10% buffer -> 0.1 buffer"
      type: number
      minimum: 0
    packaging:
      $ref: '#/PackagingResponse'

SkuDto:
  type: object
  properties:
    id:
      type: string
      format: uuid
    code:
      type: string
    name:
      type: string

PackagingResponse:
  type: object
  properties:
    packaging_type:
      $ref: './enum.yaml#/PackagingTypeEnum'
  required:
    - packaging_type
  discriminator:
    propertyName: packaging_type
    mapping:
      'UNIT': '#/UnitPackagingResponse'
      'CASE': '#/CasePackagingResponse'
      # 'PALLET' is currently not supported for bulk imports

UnitPackagingResponse:
  required:
    - price_per_unit
    - number_of_units
  allOf:
    - $ref: "#/PackagingResponse"
    - type: object
      properties:
        price_per_unit:
          $ref: './money.yaml#/MoneyDto'
          description: "Price of each unit."
        number_of_units:
          type: integer
          example: 10
          minimum: 1
          description: "Number of units."

CasePackagingResponse:
  allOf:
    - $ref: "#/PackagingResponse"
    - type: object
      required:
        - number_of_cases
        - units_per_case
        - uom
        - price_per_case
      properties:
        number_of_cases:
          minimum: 1
          type: integer
          example: 10
          description: "Number of cases"
        units_per_case:
          minimum: 0.0001
          type: number
          example: 32.2
          description: "The number of units in each case. (eg. 32.2 liters per case)"
        price_per_case:
          $ref: './money.yaml#/MoneyDto'
          description: "Price of each case"
        uom:
          $ref: './enum.yaml#/UomEnum'
