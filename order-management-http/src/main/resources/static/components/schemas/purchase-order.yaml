##### REQUESTS #####
EditPurchaseOrderRequest:
  allOf:
    - $ref: "#/PurchaseOrderDetailsDto"
  properties:
    delivery_window:
      $ref: './delivery-window.yaml#/EditDeliveryWindowDto'
    order_items:
      type: array
      minItems: 1
      items:
        $ref: './order-item.yaml#/EditOrderItemRequest'

CreatePurchaseOrderRequest:
  required:
    - delivery_window
    - order_items
    - shipping_method
    - emergency_reason_id
    - delivery_address
  allOf:
    - $ref: "#/BasePurchaseOrderDto"
    - $ref: "#/PurchaseOrderDetailsDto"
  properties:
    delivery_window:
      $ref: './delivery-window.yaml#/DeliveryWindowDto'
    order_items:
      type: array
      minItems: 1
      items:
        $ref: './order-item.yaml#/CreateOrderItemRequest'
    delivery_address:
      $ref: './address.yaml#/AddressDto'

ServiceCreatePurchaseOrderRequest:
  required:
    - delivery_window
    - order_items
    - shipping_method
    - emergency_reason_id
    - user_id
    - user_email
  allOf:
    - $ref: "#/BasePurchaseOrderDto"
    - $ref: "#/PurchaseOrderDetailsDto"
  properties:
    purchase_order_identifier:
      $ref: '#/PurchaseOrderIdentifier'
    delivery_window:
      $ref: './delivery-window.yaml#/DeliveryWindowDto'
    order_items:
      type: array
      minItems: 1
      items:
        $ref: './order-item.yaml#/CreateOrderItemRequest'
    user_id:
      type: string
      format: uuid
      description: The user ID of the user creating the purchase order
    user_email:
      type: string
      description: The email of the user creating the purchase order

PurchaseOrderIdentifier:
  required:
    - id
    - po_number
  properties:
    id:
      type: string
      format: uuid
      description: The purchase order ID
    po_number:
      type: string
      pattern: '^\d{4}[A-Za-z]{2}\d{6}$'

##### RESPONSES #####
CreatePurchaseOrderResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid

ServiceCreatePurchaseOrderResponse:
  type: object
  required:
    - id
    - po_number
  properties:
    id:
      type: string
      format: uuid
    po_number:
      type: string
      example: "2318NJ021004"

ListPurchaseOrdersResponse:
  required:
    - purchase_orders
    - total
    - page_result
  type: object
  properties:
    purchase_orders:
      type: array
      items:
        $ref: "#/PurchaseOrderResponse"
    page_result:
      required:
        - sort
      type: object
      allOf:
        - $ref: './page-result.yaml#/PageResultDto'
      properties:
        sort:
          type: array
          items:
            $ref: './enum.yaml#/ListPurchaseOrdersSortEnum'

ListPurchaseOrderRevisionsResponse:
  required:
    - revisions
  type: object
  properties:
    revisions:
      type: array
      minItems: 1
      items:
        $ref: "#/PurchaseOrderResponse"

PurchaseOrderResponse:
  type: object
  required:
    - id # BaseRevisionDto
    - po_number
    - status
    - po_type
    - user_email
    - version # BaseRevisionDto
    - created_at # BaseRevisionDto
    - is_synced # Deprecated field, pls use new field `sync_status`
    - send_status
    - country_code
    - delivery_window # (changed delivery window)
    - emergency_reason # PurchaseOrderDetailsDto
    - shipping_method # PurchaseOrderDetailsDto
    - comment # PurchaseOrderDetailsDto
    - shipping_address # BasePurchaseOrderDto
    - dc_code # BasePurchaseOrderDto
    - dc_week # BasePurchaseOrderDto
    - supplier_id # BasePurchaseOrderDto
    - delivery_address
  allOf:
    - $ref: "#/PurchaseOrderDetailsDto"
    - $ref: "#/BasePurchaseOrderDto"
    - $ref: "#/BaseRevisionDto"
    - properties:
        po_number:
          type: string
          description: "Purchase order number"
        status:
          $ref: './enum.yaml#/PurchaseOrderStatusEnum'
        po_type:
          $ref: './enum.yaml#/PurchaseOrderTypeEnum'
        user_email:
          type: string
          description: "User email"
        is_synced:
          type: boolean
          description: "Indicates whether the purchase order is synced with the supplier"
        send_status:
          $ref: './enum.yaml#/PurchaseOrderSendStatusEnum'
        country_code:
          type: string
          description: "Country code"
        delivery_window:
          $ref: './delivery-window.yaml#/DeliveryWindowResponseDto'
        supplier:
          $ref: './supplier.yaml#/SupplierResponse'
        delivery_address:
          $ref: './address.yaml#/DeliveryAddressDto'
        grn_status:
          $ref: './enum.yaml#/PurchaseOrderGrnStatusEnum'

##### DTOs #####
BaseRevisionDto:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: "Unique identifier which represent the revision"
    version:
      type: integer
      format: int32
      description: "Version of the purchase order"
    send_time:
      type: string
      format: date-time
      description: "Date and time when the purchase order was sent to the supplier"
    created_at:
      type: string
      format: date-time
      description: "Date and time when the purchase order was created"
    updated_at:
      type: string
      format: date-time
      description: "Date and time when the purchase order was updated"

PurchaseOrderDetailsDto:
  type: object
  properties:
    comment:
      type: string
      description: A comment that is visible to buyers and suppliers.
    shipping_method:
      $ref: './enum.yaml#/ShipMethodEnum'
    emergency_reason_id:
      type: string
      format: uuid
      description: "Unique identifier which represent emergency reason"

BasePurchaseOrderDto:
  required:
    - dc_code
    - dc_week
    - supplier_id
  type: object
  properties:
    dc_code:
      type: string
      pattern: '^[A-Z]{2}$'
      example: "NJ"
    dc_week:
      type: string
      pattern: '^20\d{2}-W\d{2}$'
      example: "2023-W15"
    supplier_id:
      type: string
      format: uuid
      description: "Unique identifier which represent the supplier"
