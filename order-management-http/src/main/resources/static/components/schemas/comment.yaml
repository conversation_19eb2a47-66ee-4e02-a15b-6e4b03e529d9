CommentListResponse:
  type: object
  properties:
    items:
      type: array
      items:
        $ref: '#/Comment'

Comment:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: The unique identifier for the comment
    source_id:
      type: string
      description: The original identifier from the source system
      example: "12345"
    resource_type:
      type: string
      description: The type of resource this comment is associated with (e.g., sku, po)
      example: "sku"
    resource_id:
      type: string
      description: The identifier of the resource this comment is associated with
      example: "DAI-10-10088-4"
    domain:
      type: string
      description: The domain this comment belongs to (e.g., imt, gpp-po-status)
      example: "imt"
    dc:
      type: string
      description: The distribution center code
      example: "NJ"
    brand:
      type: string
      description: The brand code
      example: "HF"
    week:
      type: string
      description: The week in format YYYY-Www
      pattern: ^20\d{2}-W\d{2}$
      example: "2025-W12"
    comment:
      type: string
      description: The comment text
      example: "This is a comment"
    created_by:
      type: string
      format: email
      description: The email of the user who created the comment
      example: "<EMAIL>"
    updated_at:
      type: string
      format: date-time
      description: The timestamp when the comment was last updated
  required:
    - id
    - resource_type
    - resource_id
    - domain
    - dc
    - brand
    - week
    - comment
    - created_by
    - updated_at

CreateCommentRequest:
  type: object
  required:
    - domain
    - resource_type
    - resource_id
    - comment
    - dc
    - week
    - brand
  properties:
    domain:
      type: string
      description: The domain where the comment belongs (e.g. imt, gpp-po-status)
    resource_type:
      type: string
      description: The type of resource being commented on (e.g. sku, po)
    resource_id:
      type: string
      description: The identifier of the resource being commented on
    comment:
      type: string
      description: The comment text
    dc:
      type: string
      description: The distribution center code
      example: "NJ"
    brand:
      $ref: '#/ProcurementBrands'
    week:
      type: string
      description: The week in format YYYY-Www
      pattern: ^20\d{2}-W\d{2}$
      example: "2025-W12"

CreateCommentResponse:
  type: object
  required:
    - id
  properties:
    id:
      type: string
      format: uuid
      description: The unique identifier for the comment

UpdateCommentRequest:
  type: object
  required:
    - comment
  properties:
    comment:
      type: string
      description: The new comment text
      example: "Updated comment"

ProcurementBrands:
  type: string
  description: A two letter abbreviation of HelloFresh Group brands.
  example: "HF"
  enum:
    - "HF"
    - "GC"
    - "EP"
    - "FJ"
