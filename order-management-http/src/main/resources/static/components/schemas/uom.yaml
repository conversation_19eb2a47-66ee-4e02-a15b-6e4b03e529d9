UnitsOfMeasureListApiResponse:
  type: object
  properties:
    items:
      type: array
      minItems: 0
      items:
        $ref: '#/UnitsOfMeasureApiResponse'
  required:
    - items

UnitsOfMeasureApiResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: The unique identifier for Units of Measure
    name:
      type: string
      example: lbs
      description: A short string describing the Unit of Measure
    type:
      type: string
      example: bulk
    enumValue:
      type: string
      enum:
        - UNIT
        - KG
        - L
        - LBS
        - GAL
        - OZ
  required:
    - id
    - name
    - type
    - enumValue
