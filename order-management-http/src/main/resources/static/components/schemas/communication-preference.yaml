CommunicationPreferenceResponse:
  type: object
  properties:
    preference:
      $ref: '#/CommunicationPreferenceEnum'

CommunicationPreferencesResponse:
  type: object
  properties:
    preferences:
      type: array
      items:
        $ref: '#/CommunicationPreferenceDetailedResponse'

CommunicationPreferenceDetailedResponse:
  type: object
  required:
    - type
    - preference
  properties:
    type:
      $ref: '#/CommunicationPreferenceTypeEnum'
    preference:
      $ref: '#/CommunicationPreferenceEnum'
    distribution_center:
      $ref: '#/DistributionCenter'
    market:
      $ref: '#/Market'
    supplier:
      $ref: '#/Supplier'

CreateCommunicationPreferenceRequest:
  type: object
  required:
    - preference
  properties:
    preference:
      $ref: '#/CommunicationPreferenceEnum'
    distribution_center:
      $ref: '#/DistributionCenter'
    market:
      $ref: '#/Market'
    supplier:
      $ref: '#/Supplier'

CreateCommunicationPreferenceResponse:
  type: object
  properties:
    id:
      type: string
      format: uuid

UpdateCommunicationPreferenceRequest:
  type: object
  required:
    - preference
  properties:
    preference:
      $ref: '#/CommunicationPreferenceEnum'

DistributionCenter:
  type: object
  required:
    - code
  properties:
    code:
      type: string
      description: "Two letter bobcode of the distribution center"
    name:
      type: string
      description: "Name of the distribution center"

Market:
  type: object
  required:
    - code
  properties:
    code:
      type: string
      description: "Two letter ISO country code"

Supplier:
  type: object
  required:
    - id
  properties:
    id:
      type: string
      format: uuid
      description: "Unique identifier of the supplier"
    code:
      type: integer
      description: "Code of the supplier"
    name:
      type: string
      description: "Name of the supplier"

CommunicationPreferenceEnum:
  type: string
  enum:
    - EMAIL
    - E2OPEN
    - EMAIL_AND_E2OPEN

CommunicationPreferenceTypeEnum:
  type: string
  enum:
    - ALL
    - DC
    - MARKET
    - SUPPLIER
  default: ALL
