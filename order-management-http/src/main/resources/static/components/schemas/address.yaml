AddressDto:
  required:
    - address
    - city
    - country_code
    - region
    - postal_code
  type: object
  properties:
    address:
      type: string
      example: Hauptstrasse
    city:
      type: string
      example: Berlin
    country_code:
      type: string
      example: US
    region:
      type: string
      example: Berlin
    postal_code:
      type: string
      example: '10435'

SupplierAddressDto:
  required:
    - number
  type: object
  allOf:
    - $ref: "#/AddressDto"
  properties:
    number:
      type: string
      example: '132'

DeliveryAddressDto:
  required:
    - location_name
  type: object
  allOf:
    - $ref: "#/AddressDto"
  properties:
    location_name:
      type: string
      example: 'Verden DC'
