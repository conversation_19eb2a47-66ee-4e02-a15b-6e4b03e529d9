SendLogResponse:
  type: object
  properties:
    send_log_items:
      type: array
      minItems: 1
      items:
        $ref: '#/SendLogItem'
  required:
    - send_log_items

SendLogItem:
  type: object
  properties:
    id:
      type: string
      format: uuid
      description: "Unique identifier for the send log item"
    po_id:
      type: string
      format: uuid
      description: "Unique identifier for the purchase order"
    po_number:
      type: string
      description: "Purchase order number"
    user_email:
      type: string
      description: "Email of the user who sent the purchase order"
    user_id:
      type: string
      format: uuid
      description: "Unique identifier for the user who sent the purchase order"
    last_status_change_at:
      type: string
      format: date-time
      description: "Date and time of the last status change"
    created_at:
      type: string
      format: date-time
      description: "Date and time of the send log item creation"
    version:
      type: integer
      description: "Version of the purchase order"
  required:
    - id
    - po_id
    - po_number
    - user_email
    - user_id
    - last_status_change_at
    - created_at
    - version


