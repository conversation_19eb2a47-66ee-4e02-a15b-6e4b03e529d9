PageResultDto:
  type: object
  properties:
    number:
      type: integer
      description: The requested page number
    page_size:
      type: integer
      description: The requested page size
    total_elements:
      type: integer
      description: >
        The total number of items available once the search criteria applied
    total_pages:
      type: integer
      description: >
        The total number of items available once the search criteria applied
  required:
    - number
    - page_size
    - total_elements
    - total_pages

PaginatedResponseDto:
  type: object
  properties:
    page_result:
      $ref: '#/PageResultDto'
  required:
    - page_result
