---
application:
  brokerAddress: ${KAFKA_BROKER_HOST:localhost}:29092

management:
  endpoints:
    access:
      default: read_only
    web:
      exposure:
        include: "*"
  server:
    port: 8081
  endpoint:
    health:
      show-details: "ALWAYS"
      probes:
        enabled: true
  tracing:
    enabled: false

spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:9092
          jwk-set-uri: http://localhost:9092/.well-known/jwks.json
  cloud:
    stream:
      kafka:
        binder:
          autoCreateTopics: true

tapioca:
  base-url: "http://localhost:1919"

auth-service:
  base-url: "http://localhost:1919"
  client-id: "client_id"
  client-secret: "client_secret"

springdoc:
  swagger-ui:
    disable-swagger-default-url: false
    config-url: "/v3/api-docs/swagger-config"
    url: "/v3/api-docs"

logging:
  level:
    com.hellofresh.oms.orderManagementHttp: DEBUG

ics:
  tickets:
    enabled: false
