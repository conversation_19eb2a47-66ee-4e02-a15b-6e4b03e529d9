<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>HelloFresh - Purchase Order</title>
    <link href="../resources/static/vendor/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <style type="text/css">

        @page {
            size: A4 landscape;
            margin: 1cm;
        }

        /* Landscape orientation */
        body {
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
        }

        table .grey-column {
            background-color: #f5f5f5;
        }

        .purchase-order .col-xs-8 {
            padding: 0 0 0 5px;
            margin: 0;
        }

        .purchase-order .row {
            padding: 5px 0;
            margin: 0;
        }

        .purchase-order-title h1 {
            margin-right: 16px;
        }

        .logo {
            padding: 20px;
        }

        .card-header {
            padding-top: 3px;
            padding-bottom: 3px;
        }

        .card-text, .card-title {
            margin-bottom: 0;
            line-clamp: 4;
        }

        .card-body {
            padding-top: 6px;
            padding-bottom: 6px;
        }

        thead {
            font-size: 12px;
        }

        tbody {
            font-size: 11px;
        }

        .disclaimer {
            font-size: 10px;
        }

    </style>
</head>
<body>
<div class="container-fluid purchase-order">
    <div class="row mb-1 purchase-order-title">
        <div class="col-8">
            <div class="d-inline-flex align-items-baseline mb-2">
                <h1>Purchase Order</h1>
                <h3 class="text-secondary">2510NJ701454</h2>
            </div>


            <h5 class="fw-normal text-secondary">Last edited on: Fri, Mar 7, 2025 11:03am</h5>


        </div>
        <div class="col-4 text-end">
            <img class="logo" width="200px" src="../resources/static/logo-hf.png" alt="Hello Fresh Logo"/>
        </div>
    </div>
    <div class="row p-0 mb-2">
        <div class="col-4">
            <div class="card p-0">
                <div class="card-header">
                    From
                </div>
                <div class="card-body">
                        <h6 class="card-title">HelloFresh</h5>
                        <p class="card-text">28 Liberty St, 10th Floor</p>
                        <p class="card-text">New York , New York , 10005</p>
                        <p class="card-text">USA</p>
                </div>
            </div>
        </div>
        <div class="col-4">
            <div class="card p-0">
                <div class="card-header">
                    Supplier
                </div>
                <div class="card-body">
                    <h6 class="card-title">Braga Family Fresh Farms</h5>
                    <p class="card-text">PO Box 93960 CA</p>
                    <p class="card-text">Soledad, CA , 93960</p>
                    <p class="card-text">USA</p>
                </div>
            </div>
        </div>

        <div class="col-4">
            <div class="card p-0">
                <div class="card-header">
                    Ship to
                </div>
                <div class="card-body">
                    <h6 class="card-title">HelloFresh United States - NJ</h5>
                    <p class="card-text">60 Lister Avenue</p>
                    <p class="card-text">Newark, NJ , 07105</p>
                    <p class="card-text">USA</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card p-0">
                <div class="card-header">
                    Details
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-5">
                            <p class="card-text">Week: 2025-W10</p>
                            <p class="card-text">Order Date: Mar 7, 2025, 1:43 PM</p>
                            <p class="card-text">Ship Method: Vendor Delivered</p>
                            <p class="card-text">Delivery date : 6/1/25, 9:00 AM - 12:00 PM</p>
                        </div>
                        <div class="col-7">
                            <p class="card-text small">
                                Additional comments: "This is a test comment to meet the requested
                                character count. It should be exactly 240 characters long, ensuring
                                accuracy. Let’s add more words to reach the limit while maintaining
                                readability and structure. Almost there! Just a few more w
                            </p>
                        </div>
                    </div>
                 </div>
            </div>
        </div>
    </div>

    <!-- Table for items -->
    <table class="table table-bordered">
        <thead>
        <tr class="align-middle text-center">
            <th scope="col">#</th>
            <th scope="col">SKU</th>
            <th scope="col">Description</th>
            <th scope="col" class="grey-column">Order Size</th>
            <th scope="col" class="grey-column">Order Unit</th>
            <th scope="col" class="grey-column">Agreed Price</th>
            <th scope="col">Case Size</th>
            <th scope="col">Case Unit</th>
            <th scope="col">Order Quantity</th>
            <th scope="col" class="grey-column">Amount</th>
        </tr>
        </thead>
        <tbody class="text-center">
        <tr class="text-center">
            <th scope="row">1</th>
            <td>DRY-10-10980-1</td>
            <td>Almonds, Sliced - 0.5 Ounce (oz)</td>
            <td class="grey-column">3,865</td>
            <td class="grey-column">unit</td>
            <td class="grey-column">$10000</td>
            <td>-</td>
            <td>unit</td>
            <td>3,865</td>
            <td class="grey-column">$38,650,000</td>
        </tr>
        <tr>
            <th scope="row">2</th>
            <td>DRY-10-10980-1</td>
            <td>Dark Chocolate Chunk Oatmeal Cup -
                1.76 Ounce (oz)</td>
            <td class="grey-column">100</td>
            <td class="grey-column">case</td>
            <td class="grey-column">$1.25</td>
            <td>12</td>
            <td>oz</td>
            <td>1,200</td>
            <td class="grey-column">$125</td>
        </tr>
        </tbody>
    </table>
    <div class="row">
        <div class="col-12 text-end">
            <p><strong>Order Value : $38,650,000 USD</strong></p>
        </div>
    </div>

    <!-- Disclaimer section -->
    <div class="col-11 disclaimer">By accepting this purchase order, Supplier agrees to the following terms:<br/>
        For HF-managed loads, Supplier shall enter relevant information using BluJay upon PO receipt. For delivered loads, Supplier shall schedule delivery of
        the products using BluJay at least twenty-four
        (24) hours prior to delivery.
    </div>

</div>
</body>
</html>
