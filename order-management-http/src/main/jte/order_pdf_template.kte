@import com.hellofresh.oms.orderManagementHttp.order.service.domain.PurchaseOrderForPdfTemplate
@import gg.jte.support.ForSupport

@param order: PurchaseOrderForPdfTemplate


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
    <head>
        <meta charset="UTF-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <title>HelloFresh - Purchase Order</title>
        <link rel="stylesheet" type="text/css" href="static/vendor/bootstrap3.min.css" media="all"/>

        <style type="text/css">
            @page {
                size: A4 landscape;
                margin: 1cm;
                margin-top: 30mm;


                @top-center {
                    content: element(header);
                }

                @bottom-center {
                    content: counter(page) "/" counter(pages);
                    font-family: "Source Sans Pro", sans-serif;
                    font-size: 11px;
                }
            }

            @media print {
                table.order-line-table td.grey-column,
                table.order-line-table th.grey-column {
                    background-color: #f5f5f5 !important;
                }

                .purchase-order .panel .panel-heading {
                    background-color: #f5f5f5 !important;
                }

                .purchase-order .title .order-number,
                .purchase-order .title .order-version,
                .purchase-order .title h2 {
                    color: #8c8c8c !important;
                }

                .purchase-order .disclaimer p {
                    color: #8c8c8c !important;
                    font-style: italic !important;
                }
            }

            #header {
                position: running(header)
            }

            body,
            html {
                margin: 0;
                padding: 0;
                width: 100vw;
                height: 100vh;
            }

            body,
            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                font-family: "Source Sans Pro", sans-serif;
            }

            h1 {
                font-size: 30px;
                margin-bottom: 0px;
                font-weight: 400;
            }

            h1 span.order-number {
                margin-left: 15px;
                font-size: 24px;
                color: #8c8c8c;
                font-weight: 400;
            }

            h1 span.order-version {
                margin-left: 8px;
                font-size: 18px;
                color: #8c8c8c;
                font-weight: 400;
            }

            h2 {
                font-size: 18px;
                color: #8c8c8c;
                font-weight: 400;
            }

            h4 {
                font-size: 15px;
                padding: 0;
                margin: 0;
            }

            .panel-body > p {
                font-size: 13px;
            }

            .order-line-table {
                -fs-table-paginate: paginate;
            }

            .order-line-table th,
            .order-line-table td {
                text-align: center;
                vertical-align: middle !important;
            }

            .order-line-table th {
                font-size: 11px;
            }

            .order-line-table td {
                font-size: 11px;
            }

            .purchase-order .col-xs-8 {
                padding: 0 0 0 5px;
                margin: 0;
            }

            .purchase-order .row {
                padding: 5px 0;
                margin: 0;
            }

            .logo {
                padding: 25px;
            }

            thead {
                font-size: 12px;
            }

            .panel-body p {
                font-size: 13px;
                margin: 0;
            }

            .disclaimer {
                margin-top: 15px;
                font-style: italic;
                font-size: 11px;
                line-height: 0.5;
                margin-bottom: 0;
                color: #8c8c8c;
            }

            .order-line {
                font-size: 11px;
            }

            .purchase-order .panel-heading,
            .purchase-order .panel-body {
                padding: 2px 10px;
                margin: 0;
            }

            .purchase-order .panel-heading {
                font-size: 15px;
            }

            .purchase-order .row {
                padding: 1px 0;
                margin: 0;
            }

            .grey-column {
                background-color: #f5f5f5;
            }

            .page-break {
                page-break-inside: avoid;
            }

        </style>
    </head>
    <body>
        <div class="container-fluid purchase-order">
            <div id="header" class="row">
                <div class="col-xs-8 title">
                    <h1>Purchase Order <span class="order-number">${order.poNumber}</span><span class="order-version">(v${order.version})</span></h1>
                    <h2>Last edited on: ${order.updatedAt}</h2>
                </div>
                <div class="col-xs-4 text-right">
                        <img class="logo" width="150px" src="static/logo-hf.png" alt="Hello Fresh Logo" />
                </div>
            </div>
            <div class="row">
                <div class="col-xs-4">
                    <div class="panel panel-default">
                        <div class="panel-heading">From</div>
                        <div class="panel-body">
                            <p>${order.from.companyName}</p>
                            <p>${order.from.address}</p>
                            <p>${order.from.city}, ${order.from.state}, ${order.from.zip}</p>
                            <p>${order.from.country}</p>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="panel panel-default">
                        <div class="panel-heading">Supplier</div>
                        <div class="panel-body">
                            <p>${order.supplier.companyName}</p>
                            <p>${order.supplier.address}</p>
                            <p>${order.supplier.city}, ${order.supplier.state}, ${order.supplier.zip}</p>
                            <p>${order.supplier.country}</p>
                        </div>
                    </div>
                </div>

                <div class="col-xs-4">
                    <div class="panel panel-default">
                        <div class="panel-heading">Ship to</div>
                        <div class="panel-body">
                            <p>${order.shipTo.companyName}</p>
                            <p>${order.shipTo.address}</p>
                            <p>${order.shipTo.city}, ${order.shipTo.state}, ${order.shipTo.zip}</p>
                            <p>${order.shipTo.country}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">Details</div>
                        <div class="panel-body">
                            <div class="col-xs-5">
                                <p>Week: ${order.yearWeek}</p>
                                <p>Order Date: ${order.orderedDate}</p>
                                <p>Ship Method: ${order.shippingMethod}</p>
                                <p>Delivery date: ${order.deliveryDate}, ${order.deliveryStartTime} - ${order.deliveryEndTime}</p>
                            </div>
                            <div class="col-xs-7">
                                @if(!order.comment.isNullOrBlank())
                                <p class="comment small">Additional comments: ${order.comment}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <table class="table table-bordered order-line-table">
                    <thead>
                        <tr class="page-break">
                            <th scope="col" class="text-center" width="20">#</th>
                            <th scope="col" class="text-center" width="100">SKU</th>
                            <th scope="col" width="120">Description</th>
                            <th scope="col" class="grey-column" width="40">Order Size</th>
                            <th scope="col" class="grey-column" width="40">Order Unit</th>
                            <th scope="col" class="grey-column" width="40">Agreed Price</th>
                            <th scope="col" width="40">Case Size</th>
                            <th scope="col" width="40">Case Unit</th>
                            <th scope="col" width="20">Order Quantity</th>
                            <th scope="col" class="grey-column price" width="40">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for(i in 0..order.items.lastIndex)
                        <tr class="order-line page-break">
                            <th scope="row">${i+1}</th>
                            <td>${order.items[i].code}</td>
                            <td>${order.items[i].name}</td>
                            <td class="grey-column">${order.items[i].orderSize}</td>
                            <td class="grey-column">${order.items[i].orderUnit}</td>
                            <td class="grey-column">${order.items[i].agreedPrice}</td>
                            <td>${order.items[i].caseSize}</td>
                            <td>${order.items[i].caseUnit}</td>
                            <td>${order.items[i].quantity}</td>
                            <td class="grey-column">${order.items[i].totalAmount}</td>
                        </tr>
                        @endfor
                        @if(order.items.isEmpty())
                            <tr>
                                <td colspan="4">No SKUs found.</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <div class="row">
                <div class="col-xs-12 text-right">
                    <p><strong>Order Value: ${order.totalPrice} ${order.currency}</strong></p>
                </div>
            </div>

            <div class="row disclaimer">
                @for(i in 0..order.disclaimers.lastIndex)
                    <p>${order.disclaimers[i]}</p>
                @endfor
            </div>
        </div>
    </body>
</html>
