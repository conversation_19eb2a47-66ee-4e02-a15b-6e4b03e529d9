package com.hellofresh.oms.orderManagementHttp.imports.intake

import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.CasePackagingResponse
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum as ShipMethodEnumResponse
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.PurchaseOrderDto
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.PurchaseOrderItemDto
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierSimpleEntity
import java.math.BigDecimal
import java.time.LocalDateTime
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.DynamicTest.dynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory

class BatchCreatePurchaseOrdersCategoryResponseConverterTest {
    @Test
    fun `should throw and exception when packaging type is PALLET_TYPE`() {
        val batchCreatePurchaseOrdersResponseConverter = BatchCreatePurchaseOrdersResponseConverter()
        val purchaseOrderItemDto = PurchaseOrderItemDto(
            sku = getSkuEntity(),
            bufferValue = BigDecimal.ZERO,
            packagingType = PackagingType.PALLET_TYPE,
            orderSize = BigDecimal.ZERO,
            caseSize = BigDecimal.ZERO,
            price = BigDecimal.ZERO,
            uom = UOM.UNIT,
        )

        val exception = assertThrows(UnsupportedOperationException::class.java) {
            batchCreatePurchaseOrdersResponseConverter.convert(
                listOf(
                    PurchaseOrderDto(
                        yearWeek = YearWeek(2022, 1),
                        distributionCenter = getDistributionCenterEntity(),
                        shipMethod = ShipMethodEnum.VENDOR,
                        supplier = getSupplierSimpleEntity(),
                        deliveryWindow = DeliveryWindowDto(
                            start = LocalDateTime.now(),
                            end = LocalDateTime.now(),
                        ),
                        emergencyReason = getEmergencyReasonEntity(),
                        orderItems = listOf(purchaseOrderItemDto),
                    ),
                ),
            )
        }

        assertThat(
            exception.message,
            equalTo("PALLET_TYPE is not supported at the moment in OT and will not be supported for now."),
        )
    }

    @TestFactory
    fun `should map unit of measures to proper response types`() = listOf(
        UOM.KG to UomEnum.KG,
        UOM.LBS to UomEnum.LBS,
        UOM.L to UomEnum.L,
        UOM.GAL to UomEnum.GAL,
        UOM.OZ to UomEnum.OZ,
        UOM.UNIT to UomEnum.UNIT,
    ).map { (givenUom, expectedUomResponse) ->
        dynamicTest("should map $givenUom to $expectedUomResponse") {
            val batchCreatePurchaseOrdersResponseConverter = BatchCreatePurchaseOrdersResponseConverter()
            val purchaseOrderItemDto = PurchaseOrderItemDto(
                sku = getSkuEntity(),
                bufferValue = BigDecimal.ZERO,
                packagingType = PackagingType.CASE_TYPE,
                orderSize = BigDecimal.ZERO,
                caseSize = BigDecimal.ZERO,
                price = BigDecimal.ZERO,
                uom = givenUom,
            )

            val response = batchCreatePurchaseOrdersResponseConverter.convert(
                listOf(
                    PurchaseOrderDto(
                        yearWeek = YearWeek(2022, 1),
                        distributionCenter = getDistributionCenterEntity(),
                        shipMethod = ShipMethodEnum.VENDOR,
                        supplier = getSupplierSimpleEntity(),
                        deliveryWindow = DeliveryWindowDto(
                            start = LocalDateTime.now(),
                            end = LocalDateTime.now(),
                        ),
                        emergencyReason = getEmergencyReasonEntity(),
                        orderItems = listOf(purchaseOrderItemDto),
                    ),
                ),
            )

            assertThat(
                response.purchaseOrders.first().orderItems?.first()?.packaging,
                equalTo(
                    CasePackagingResponse(
                        packagingType = PackagingTypeEnum.CASE,
                        numberOfCases = 0,
                        unitsPerCase = BigDecimal.ZERO,
                        pricePerCase = MoneyDto(
                            amount = "0.0000",
                            currency = "EUR",
                        ),
                        uom = expectedUomResponse,
                    )
                )
            )
        }
    }

    @TestFactory
    fun `should map ship method to proper response types`() = listOf(
        ShipMethodEnum.VENDOR to ShipMethodEnumResponse.VENDOR,
        ShipMethodEnum.CROSSDOCK to ShipMethodEnumResponse.CROSSDOCK,
        ShipMethodEnum.FREIGHT_ON_BOARD to ShipMethodEnumResponse.FREIGHT_ON_BOARD,
        ShipMethodEnum.OTHER to ShipMethodEnumResponse.OTHER,
    ).map { (givenShipMethod, expectedShipMethodResponse) ->
        dynamicTest("should map $givenShipMethod to $expectedShipMethodResponse") {
            val batchCreatePurchaseOrdersResponseConverter = BatchCreatePurchaseOrdersResponseConverter()
            val purchaseOrderItemDto = PurchaseOrderItemDto(
                sku = getSkuEntity(),
                bufferValue = BigDecimal.ZERO,
                packagingType = PackagingType.CASE_TYPE,
                orderSize = BigDecimal.ZERO,
                caseSize = BigDecimal.ZERO,
                price = BigDecimal.ZERO,
                uom = UOM.KG,
            )

            val response = batchCreatePurchaseOrdersResponseConverter.convert(
                listOf(
                    PurchaseOrderDto(
                        yearWeek = YearWeek(2022, 1),
                        distributionCenter = getDistributionCenterEntity(),
                        shipMethod = givenShipMethod,
                        supplier = getSupplierSimpleEntity(),
                        deliveryWindow = DeliveryWindowDto(
                            start = LocalDateTime.now(),
                            end = LocalDateTime.now(),
                        ),
                        emergencyReason = getEmergencyReasonEntity(),
                        orderItems = listOf(purchaseOrderItemDto),
                    ),
                ),
            )

            assertThat(response.purchaseOrders.first().shippingMethod, equalTo(expectedShipMethodResponse))
        }
    }
}
