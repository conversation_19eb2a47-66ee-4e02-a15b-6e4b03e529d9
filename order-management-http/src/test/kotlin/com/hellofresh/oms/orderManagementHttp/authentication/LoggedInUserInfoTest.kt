package com.hellofresh.oms.orderManagementHttp.authentication

import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test

class LoggedInUserInfoTest {
    @Test
    fun `should respond with allowed markets`() {
        // given
        val givenRoles = listOf(
            "purchasing.ca.all.manager",
            "purchasing.au.all.manager",
            "purchasing.it.all.manager",
            "purchasing.nl.all.manager",
            "purchasing.fr.all.manager",
            "purchasing.de.all.manager",
            "purchasing.nz.all.manager",
            "purchasing.se.all.manager",
            "purchasing.us.all.manager",
            "procurement.gb.all.manager",
            "3p_assembly_planning.au.all.manager",
            "culinary.gb.all.manager",
            "culinary.de.all.manager",
            "culinary.de.all.viewer",
            "scm_operation.access",
            "users.admin",
            "purchasing.all.communication_preferences.manager",
        )

        val user = LoggedInUserInfo(
            userId = UUID.randomUUID(),
            userEmail = "<EMAIL>",
            userName = "name",
            roles = givenRoles,
            issuer = UUID.randomUUID().toString(),
        )

        // when
        assertThat(
            user.getAllowedMarkets().toSet(),
            equalTo(
                setOf(
                    "ca",
                    "au",
                    "it",
                    "beneluxfr",
                    "fr",
                    "dach",
                    "nz",
                    "dkse",
                    "us",
                ),
            ),
        )
    }
}
