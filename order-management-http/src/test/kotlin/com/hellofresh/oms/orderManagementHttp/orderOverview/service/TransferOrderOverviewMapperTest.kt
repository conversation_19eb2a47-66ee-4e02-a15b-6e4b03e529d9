package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID

class TransferOrderOverviewMapperTest {

    @Test
    fun `should map TransferOrderOverviewModel to API response correctly`() {
        val transferOrderId = UUID.randomUUID()
        val breakdownItem = TransferOrderBreakdownItem(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC001",
            week = YearWeek("2024-W01"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            totalQuantity = BigDecimal("10.0"),
            casePrice = "EUR 25.50",
            reasonText = "Stock transfer",
            shippingMethod = "Truck"
        )

        val overviewModel = TransferOrderOverviewModel(
            skuCode = "SKU-001",
            sourceDcCode = "DC002",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            packagingType = "case",
            totalOrderedQuantity = BigDecimal("10.0"),
            totalReceivedQuantity = BigDecimal("10.0"),
            totalCasesReceived = 5,
            totalPriceOrdered = "EUR 25.50",
            totalPriceReceived = "EUR 25.50",
            weightedAvgCasePrice = "EUR 25.50",
            weightedAvgCaseSize = BigDecimal("2.0"),
            weightedAvgCaseSizeReceived = BigDecimal("2.0"),
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe",
            transferOrderBreakdown = listOf(breakdownItem)
        )

        val result = overviewModel.mapToTransferOrderOverviewModelToApiResponse()

        assertEquals("SKU-001", result.skuCode)
        assertEquals("DC002", result.sourceDcCode)
        assertEquals("Test Product", result.skuName)
        assertEquals(TransferOrdersOverviewResponseInner.PackagingType.CASE, result.packagingType)
        assertEquals(BigDecimal("10.0"), result.totalOrderedQuantity)
        assertEquals(1, result.transferOrderBreakdown.size)
        assertEquals(transferOrderId, result.transferOrderBreakdown[0].transferOrderId)
    }

    @Test
    fun `should map TransferOrderStatusDetails to breakdown item correctly`() {
        val transferOrderId = UUID.randomUUID()
        val statusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-003",
            skuCode = "SKU-003",
            status = "STATE_IN_TRANSIT",
            destinationDcCode = "DC005",
            sourceDcCode = "DC006",
            skuName = "Transit Product",
            category = "Dairy",
            skuUom = "liter",
            week = YearWeek("2024-W03"),
            quantityReceived = BigDecimal("30.0"),
            casesReceived = 15,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 75.00",
            totalQuantity = BigDecimal("30.0"),
            reasonText = "Regular transfer",
            shippingMethod = "Standard",
            assignedBuyerFirstName = "Bob",
            assignedBuyerLastName = "Johnson"
        )

        val result = statusDetails.mapToTransferOrderBreakdownItem()

        assertEquals(transferOrderId, result.transferOrderId)
        assertEquals("TO-2024-003", result.transferOrderNumber)
        assertEquals("STATE_IN_TRANSIT", result.status)
        assertEquals("DC005", result.destinationDcCode)
        assertEquals(YearWeek("2024-W03"), result.week)
    }

    @Test
    fun `should handle null values in breakdown item correctly`() {
        val transferOrderId = UUID.randomUUID()
        val statusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-004",
            skuCode = "SKU-004",
            status = "STATE_OPEN",
            destinationDcCode = "DC007",
            sourceDcCode = "DC008",
            skuName = "Minimal Product",
            category = "Other",
            skuUom = null,
            week = YearWeek("2024-W04"),
            quantityReceived = null,
            casesReceived = null,
            caseSize = null,
            packagingType = "unit",
            priceAmount = "EUR 0.00",
            totalQuantity = BigDecimal("0.0"),
            reasonText = null,
            shippingMethod = null,
            assignedBuyerFirstName = null,
            assignedBuyerLastName = null
        )

        val result = statusDetails.mapToTransferOrderBreakdownItem()

        assertEquals(transferOrderId, result.transferOrderId)
        assertNull(result.quantityReceived)
        assertNull(result.casesReceived)
        assertNull(result.caseSize)
        assertNull(result.reasonText)
        assertNull(result.shippingMethod)
    }
}
