package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.TransferOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.mapToTransferOrderBreakdownItem
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.mapToTransferOrderOverviewModelToApiResponse
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID

class TransferOrderOverviewMapperTest {

    @Test
    fun `should map TransferOrderOverviewModel to API response correctly`() {
        val transferOrderId = UUID.randomUUID()
        val breakdownItem = TransferOrderBreakdownItem(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC001",
            week = YearWeek("2024-W01"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            totalQuantity = BigDecimal("10.0"),
            casePrice = "EUR 25.50",
            reasonText = "Stock transfer",
            shippingMethod = "Truck"
        )

        val overviewModel = TransferOrderOverviewModel(
            skuCode = "SKU-001",
            sourceDcCode = "DC002",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            packagingType = "case",
            totalOrderedQuantity = BigDecimal("10.0"),
            totalReceivedQuantity = BigDecimal("10.0"),
            totalCasesReceived = 5,
            totalPriceOrdered = "EUR 25.50",
            totalPriceReceived = "EUR 25.50",
            weightedAvgCasePrice = "EUR 25.50",
            weightedAvgCaseSize = BigDecimal("2.0"),
            weightedAvgCaseSizeReceived = BigDecimal("2.0"),
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe",
            transferOrderBreakdown = listOf(breakdownItem)
        )

        val result = overviewModel.mapToTransferOrderOverviewModelToApiResponse()

        assertEquals("SKU-001", result.skuCode)
        assertEquals("DC002", result.sourceDcCode)
        assertEquals("Test Product", result.skuName)
        assertEquals(TransferOrdersOverviewResponseInner.PackagingType.CASE, result.packagingType)
        assertEquals(BigDecimal("10.0"), result.totalOrderedQuantity)
        assertEquals(BigDecimal("10.0"), result.totalReceivedQuantity)
        assertEquals(5, result.totalCasesReceived)
        assertEquals("EUR 25.50", result.totalPriceOrdered)
        assertEquals("EUR 25.50", result.totalPriceReceived)
        assertEquals("EUR 25.50", result.weightedAvgCasePrice)
        assertEquals(BigDecimal("2.0"), result.weightedAvgCaseSize)
        assertEquals(BigDecimal("2.0"), result.weightedAvgCaseSizeReceived)
        assertEquals("John", result.assignedBuyerFirstName)
        assertEquals("Doe", result.assignedBuyerLastName)
        assertEquals(1, result.transferOrderBreakdown.size)
        assertEquals(transferOrderId, result.transferOrderBreakdown[0].transferOrderId)
        assertEquals("TO-2024-001", result.transferOrderBreakdown[0].transferOrderNumber)
        assertEquals("STATE_OPEN", result.transferOrderBreakdown[0].status)
        assertEquals("DC001", result.transferOrderBreakdown[0].destinationDcCode)
        assertEquals("2024-W01", result.transferOrderBreakdown[0].week)
        assertEquals(BigDecimal("10.0"), result.transferOrderBreakdown[0].quantityReceived)
        assertEquals(5, result.transferOrderBreakdown[0].casesReceived)
        assertEquals(BigDecimal("2.0"), result.transferOrderBreakdown[0].caseSize)
        assertEquals(BigDecimal("10.0"), result.transferOrderBreakdown[0].totalQuantity)
        assertEquals("EUR 25.50", result.transferOrderBreakdown[0].casePrice)
        assertEquals("Stock transfer", result.transferOrderBreakdown[0].reasonText)
        assertEquals("Truck", result.transferOrderBreakdown[0].shippingMethod)
    }

    @Test
    fun `should map unit packaging type correctly`() {
        val breakdownItem = TransferOrderBreakdownItem(
            transferOrderId = UUID.randomUUID(),
            transferOrderNumber = "TO-2024-002",
            status = "STATE_DELIVERED",
            destinationDcCode = "DC003",
            week = YearWeek("2024-W02"),
            quantityReceived = BigDecimal("5.0"),
            casesReceived = null,
            caseSize = null,
            totalQuantity = BigDecimal("5.0"),
            casePrice = "EUR 12.50",
            reasonText = null,
            shippingMethod = null
        )

        val overviewModel = TransferOrderOverviewModel(
            skuCode = "SKU-002",
            sourceDcCode = "DC004",
            skuName = "Unit Product",
            category = "Packaged",
            skuUom = "piece",
            packagingType = "unit",
            totalOrderedQuantity = BigDecimal("5.0"),
            totalReceivedQuantity = BigDecimal("5.0"),
            totalCasesReceived = null,
            totalPriceOrdered = "EUR 12.50",
            totalPriceReceived = "EUR 12.50",
            weightedAvgCasePrice = null,
            weightedAvgCaseSize = null,
            weightedAvgCaseSizeReceived = null,
            assignedBuyerFirstName = null,
            assignedBuyerLastName = null,
            transferOrderBreakdown = listOf(breakdownItem)
        )

        val result = overviewModel.mapToTransferOrderOverviewModelToApiResponse()

        assertEquals(TransferOrdersOverviewResponseInner.PackagingType.UNIT, result.packagingType)
        assertEquals("piece", result.skuUom)
        assertNull(result.totalCasesReceived)
        assertNull(result.weightedAvgCasePrice)
        assertNull(result.weightedAvgCaseSize)
        assertNull(result.weightedAvgCaseSizeReceived)
        assertNull(result.assignedBuyerFirstName)
        assertNull(result.assignedBuyerLastName)
    }

    @Test
    fun `should map TransferOrderStatusDetails to breakdown item correctly`() {
        val transferOrderId = UUID.randomUUID()
        val statusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-003",
            skuCode = "SKU-003",
            status = "STATE_IN_TRANSIT",
            destinationDcCode = "DC005",
            sourceDcCode = "DC006",
            skuName = "Transit Product",
            category = "Dairy",
            skuUom = "liter",
            week = YearWeek("2024-W03"),
            quantityReceived = BigDecimal("30.0"),
            casesReceived = 15,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 75.00",
            totalQuantity = BigDecimal("30.0"),
            reasonText = "Regular transfer",
            shippingMethod = "Standard",
            assignedBuyerFirstName = "Bob",
            assignedBuyerLastName = "Johnson"
        )

        val result = statusDetails.mapToTransferOrderBreakdownItem()

        assertEquals(transferOrderId, result.transferOrderId)
        assertEquals("TO-2024-003", result.transferOrderNumber)
        assertEquals("STATE_IN_TRANSIT", result.status)
        assertEquals("DC005", result.destinationDcCode)
        assertEquals(YearWeek("2024-W03"), result.week)
        assertEquals(BigDecimal("30.0"), result.quantityReceived)
        assertEquals(15, result.casesReceived)
        assertEquals(BigDecimal("2.0"), result.caseSize)
        assertEquals(BigDecimal("30.0"), result.totalQuantity)
        assertEquals("EUR 75.00", result.casePrice)
        assertEquals("Regular transfer", result.reasonText)
        assertEquals("Standard", result.shippingMethod)
    }

        val result = statusDetails.mapToTransferOrderBreakdownItem()

        assertEquals(transferOrderId, result.transferOrderId)
        assertEquals("TO-2024-003", result.transferOrderNumber)
        assertEquals("STATE_IN_TRANSIT", result.status)
        assertEquals("DC005", result.destinationDcCode)
        assertEquals(YearWeek("2024-W03"), result.week)
    }

    @Test
    fun `should handle null values in breakdown item correctly`() {
        val transferOrderId = UUID.randomUUID()
        val statusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-004",
            skuCode = "SKU-004",
            status = "STATE_OPEN",
            destinationDcCode = "DC007",
            sourceDcCode = "DC008",
            skuName = "Minimal Product",
            category = "Other",
            skuUom = null,
            week = YearWeek("2024-W04"),
            quantityReceived = null,
            casesReceived = null,
            caseSize = null,
            packagingType = "unit",
            priceAmount = "EUR 0.00",
            totalQuantity = BigDecimal("0.0"),
            reasonText = null,
            shippingMethod = null,
            assignedBuyerFirstName = null,
            assignedBuyerLastName = null
        )

        val result = statusDetails.mapToTransferOrderBreakdownItem()

        assertEquals(transferOrderId, result.transferOrderId)
        assertEquals("TO-2024-004", result.transferOrderNumber)
        assertEquals("STATE_OPEN", result.status)
        assertEquals("DC007", result.destinationDcCode)
        assertEquals(YearWeek("2024-W04"), result.week)
        assertNull(result.quantityReceived)
        assertNull(result.casesReceived)
        assertNull(result.caseSize)
        assertEquals(BigDecimal("0.0"), result.totalQuantity)
        assertEquals("EUR 0.00", result.casePrice)
        assertNull(result.reasonText)
        assertNull(result.shippingMethod)
    }

    @Test
    fun `should map list of TransferOrderStatusDetails to overview model correctly`() {
        val transferOrderId1 = UUID.randomUUID()
        val transferOrderId2 = UUID.randomUUID()

        val statusDetails1 = TransferOrderStatusDetails(
            transferOrderId = transferOrderId1,
            transferOrderNumber = "TO-2024-005",
            skuCode = "SKU-005",
            status = "STATE_OPEN",
            destinationDcCode = "DC009",
            sourceDcCode = "DC010",
            skuName = "Aggregated Product",
            category = "Fresh",
            skuUom = "kg",
            week = YearWeek("2024-W05"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 25.00",
            totalQuantity = BigDecimal("10.0"),
            reasonText = "Transfer 1",
            shippingMethod = "Truck",
            assignedBuyerFirstName = "Alice",
            assignedBuyerLastName = "Brown"
        )

        val statusDetails2 = TransferOrderStatusDetails(
            transferOrderId = transferOrderId2,
            transferOrderNumber = "TO-2024-006",
            skuCode = "SKU-005",
            status = "STATE_DELIVERED",
            destinationDcCode = "DC011",
            sourceDcCode = "DC010",
            skuName = "Aggregated Product",
            category = "Fresh",
            skuUom = "kg",
            week = YearWeek("2024-W05"),
            quantityReceived = BigDecimal("15.0"),
            casesReceived = 7,
            caseSize = BigDecimal("2.5"),
            packagingType = "case",
            priceAmount = "EUR 40.00",
            totalQuantity = BigDecimal("15.0"),
            reasonText = "Transfer 2",
            shippingMethod = "Standard",
            assignedBuyerFirstName = "Alice",
            assignedBuyerLastName = "Brown"
        )

        val statusDetailsList = listOf(statusDetails1, statusDetails2)
        val result = statusDetailsList.mapToTransferOrderOverviewModel()

        assertEquals("SKU-005", result.skuCode)
        assertEquals("DC010", result.sourceDcCode)
        assertEquals("Aggregated Product", result.skuName)
        assertEquals("Fresh", result.category)
        assertEquals("kg", result.skuUom)
        assertEquals("case", result.packagingType)
        assertEquals(BigDecimal("25.0"), result.totalOrderedQuantity) // 10 + 15
        assertEquals(BigDecimal("25.0"), result.totalReceivedQuantity) // 10 + 15
        assertEquals(12, result.totalCasesReceived) // 5 + 7
        assertEquals("Alice", result.assignedBuyerFirstName)
        assertEquals("Brown", result.assignedBuyerLastName)
        assertEquals(2, result.transferOrderBreakdown.size)
    }

    @Test
    fun `should calculate weighted averages correctly`() {
        val statusDetails1 = TransferOrderStatusDetails(
            transferOrderId = UUID.randomUUID(),
            transferOrderNumber = "TO-2024-007",
            skuCode = "SKU-006",
            status = "STATE_OPEN",
            destinationDcCode = "DC012",
            sourceDcCode = "DC013",
            skuName = "Weighted Product",
            category = "Dairy",
            skuUom = "liter",
            week = YearWeek("2024-W06"),
            quantityReceived = BigDecimal("20.0"),
            casesReceived = 10,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 50.00",
            totalQuantity = BigDecimal("20.0"),
            reasonText = "Weighted transfer 1",
            shippingMethod = "Express",
            assignedBuyerFirstName = "Charlie",
            assignedBuyerLastName = "Davis"
        )

        val statusDetails2 = TransferOrderStatusDetails(
            transferOrderId = UUID.randomUUID(),
            transferOrderNumber = "TO-2024-008",
            skuCode = "SKU-006",
            status = "STATE_DELIVERED",
            destinationDcCode = "DC014",
            sourceDcCode = "DC013",
            skuName = "Weighted Product",
            category = "Dairy",
            skuUom = "liter",
            week = YearWeek("2024-W06"),
            quantityReceived = BigDecimal("30.0"),
            casesReceived = 15,
            caseSize = BigDecimal("3.0"),
            packagingType = "case",
            priceAmount = "EUR 90.00",
            totalQuantity = BigDecimal("30.0"),
            reasonText = "Weighted transfer 2",
            shippingMethod = "Standard",
            assignedBuyerFirstName = "Charlie",
            assignedBuyerLastName = "Davis"
        )

        val statusDetailsList = listOf(statusDetails1, statusDetails2)
        val result = statusDetailsList.mapToTransferOrderOverviewModel()

        // Weighted average case size: (2.0 * 20 + 3.0 * 30) / (20 + 30) = (40 + 90) / 50 = 2.6
        assertEquals(BigDecimal("2.60"), result.weightedAvgCaseSize)
        // Weighted average case size received: (2.0 * 20 + 3.0 * 30) / (20 + 30) = 2.6
        assertEquals(BigDecimal("2.60"), result.weightedAvgCaseSizeReceived)
    }
}

        val result = statusDetails.mapToTransferOrderBreakdownItem()

        assertEquals(transferOrderId, result.transferOrderId)
        assertNull(result.quantityReceived)
        assertNull(result.casesReceived)
        assertNull(result.caseSize)
        assertNull(result.reasonText)
        assertNull(result.shippingMethod)
    }
}
