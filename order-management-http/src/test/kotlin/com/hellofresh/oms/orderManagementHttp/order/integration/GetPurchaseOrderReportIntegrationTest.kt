package com.hellofresh.oms.orderManagementHttp.order.integration

import java.util.UUID
import org.junit.jupiter.api.Test
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.get

class GetPurchaseOrderReportIntegrationTest : PurchaseOrderAbstractIntegrationTest() {

    @Test
    fun `should return 404 when P<PERSON> was not found`() {
        // given
        val givenPoNumber = "PO-123"
        val givenPoId = UUID.randomUUID()

        // when
        mockMvc.get("/orders/{po_number}/revisions/{po_id}", givenPoNumber, givenPoId)
            .andExpect {
                status { isNotFound() }
            }
    }

    @Test
    @Sql(
        scripts = [
            "/data/purchaseOrder.sql",
            "/data/orderItem.sql",
            "/data/suppliers.sql",
            "/data/dc.sql",
            "/data/dc_billing_address.sql",
        ],
    )
    fun `should return PDF with correct file name when P<PERSON> exists`() {
        // given
        val poId = "e3fa59aa-91c1-4c78-bfa5-000000000001"
        val poNumber = "2318NJ021004"

        // when
        mockMvc.get("/orders/{po_number}/reports/{po_uuid}", poNumber, poId)
            .andExpect {
                status { isOk() }
                header { string("Content-Disposition", "attachment; filename=\"purchase-order-$poNumber.pdf\"") }
                header { string("Content-Type", "application/pdf") }
            }
    }
}
