package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.client.tapioca.TapiocaClient
import com.hellofresh.oms.orderManagementHttp.client.tapioca.exception.TapiocaClientException
import com.hellofresh.oms.orderManagementHttp.exception.OrderingToolException
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.workerAction.CreateAndSendOrderScheduler
import com.hellofresh.oms.orderManagementHttp.workerAction.CreateOrderScheduler
import java.util.UUID.randomUUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.times
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class PurchaseOrderDeleteServiceTest {
    private val purchaseOrderRepositoryMock = mock(PurchaseOrderRepository::class.java)
    private val tapiocaClientMock = mock(TapiocaClient::class.java)
    private val createOrderSchedulerMock = mock(CreateOrderScheduler::class.java)
    private val createAndSendOrderSchedulerMock = mock(CreateAndSendOrderScheduler::class.java)
    private val purchaseOrderProducer = mock(PurchaseOrderProducer::class.java)

    private val purchaseOrderServiceMock = PurchaseOrderService(
        purchaseOrderRepositoryMock,
        createOrderSchedulerMock,
        createAndSendOrderSchedulerMock,
    )

    private val subject = PurchaseOrderDeleteService(
        purchaseOrderServiceMock,
        purchaseOrderRepositoryMock,
        tapiocaClientMock,
        purchaseOrderProducer,
    )

    @Test
    fun `when deleting should throw an exception when tapioca client fails`() {
        // given
        val poNumber = "po_number"
        val exceptionMessage = "some exception"
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(poNumber))
            .thenReturn(getPurchaseOrderEntity())
        // throw exception when tapioca client fails
        whenever(
            tapiocaClientMock.deletePurchaseOrder(any(), any()),
        ).thenThrow(TapiocaClientException(exceptionMessage))

        // when
        val err = assertThrows<OrderingToolException> {
            subject.processDeletePurchaseOrder(poNumber, user)
        }

        // then
        assertEquals("""OT responded with error: $exceptionMessage""", err.message)
    }

    @Test
    fun `when deleting should mark as deleted`() {
        // given
        val poNumber = "po_number"
        val purchaseOrderEntity = getPurchaseOrderEntity()
        whenever(purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(poNumber))
            .thenReturn(purchaseOrderEntity)
        whenever(purchaseOrderRepositoryMock.save(any())).thenReturn(purchaseOrderEntity)
        // when
        subject.processDeletePurchaseOrder(poNumber, user)
        // then
        verify(purchaseOrderRepositoryMock, times(1)).save(any())
    }

    companion object {
        val user = LoggedInUserInfo(
            randomUUID(),
            "<EMAIL>",
            "userName",
            emptyList(),
            randomUUID().toString(),
        )
    }
}
