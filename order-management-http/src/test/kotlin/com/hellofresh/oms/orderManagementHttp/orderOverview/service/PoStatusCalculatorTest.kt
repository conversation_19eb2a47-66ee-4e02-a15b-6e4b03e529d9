package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewStatusEnum
import java.math.BigDecimal
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class PoStatusCalculatorTest {

    @Nested
    @DisplayName("calculateHjStatus")
    inner class CalculateHjStatusTest {

        @Test
        fun `should return VOIDED when isVoided is true`() {
            val params = PoStatusParams(
                isVoided = true,
                supplierName = null,
                hjStatus = null,
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.VOIDED)
        }

        @Test
        fun `should return RECEIVED_ACCURATE when supplier is Autobagger and status is Closed`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Autobagger",
                hjStatus = "Closed",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_ACCURATE)
        }

        @Test
        fun `should return RECEIVED_ACCURATE when supplier is autobagger (case insensitive) and status is Closed`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "autobagger",
                hjStatus = "Closed",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_ACCURATE)
        }

        @Test
        fun `should return RECEIVED_ACCURATE when supplier is Autobagger and status is Rejected`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Autobagger",
                hjStatus = "Rejected",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_ACCURATE)
        }

        @Test
        fun `should return DELIVERY_REJECTED when status is Rejected`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Rejected",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.DELIVERY_REJECTED)
        }

        @Test
        fun `should return RECEIPT_CANCELLED when status is Cancelled`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Cancelled",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIPT_CANCELLED)
        }

        @Test
        fun `should return RECEIVED_PARTIAL_REJECTION when status is Closed - Partial Rejection`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Closed - Partial Rejection",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_PARTIAL_REJECTION)
        }

        @Test
        fun `should return NOT_DELIVERED_PAST_DUE when status is empty and isPastDue is true`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = true,
                isBlankReceipt = true,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.NOT_DELIVERED_PAST_DUE)
        }

        @Test
        fun `should return IN_PROGRESS_HJ when status is empty and isPastDue is false`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.IN_PROGRESS_HJ)
        }

        @Test
        fun `should return RECEIVED_OVER when status is Closed and received quantity is greater than ordered`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Closed",
                hjOverrideQuantity = BigDecimal("10.0"),
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal("5.0"),
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_OVER)
        }

        @Test
        fun `should return RECEIVED_ACCURATE when status is Closed and received quantity equals ordered`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Closed",
                hjOverrideQuantity = BigDecimal("10.0"),
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal("10.0"),
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_ACCURATE)
        }

        @Test
        fun `should return RECEIVED_UNDER when status is Closed and received quantity is less than ordered`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Closed",
                hjOverrideQuantity = BigDecimal("5.0"),
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal("10.0"),
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_UNDER)
        }

        @Test
        fun `should prefer hjOverrideQuantity over hjReceiptClosedQuantity when status is Closed`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Closed",
                hjOverrideQuantity = BigDecimal("5.0"),
                hjReceiptClosedQuantity = BigDecimal("10.0"),
                quantityOrdered = BigDecimal("10.0"),
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_UNDER)
        }

        @Test
        fun `should use hjReceiptClosedQuantity when hjOverrideQuantity is null and status is Closed`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Closed",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = BigDecimal("10.0"),
                quantityOrdered = BigDecimal("10.0"),
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_ACCURATE)
        }

        @Test
        fun `should default to ZERO when both hjOverrideQuantity and hjReceiptClosedQuantity are null`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Closed",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal("10.0"),
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.RECEIVED_UNDER)
        }

        @Test
        fun `should return IN_PROGRESS_HJ for unknown status`() {
            val params = PoStatusParams(
                isVoided = false,
                supplierName = "Other",
                hjStatus = "Unknown",
                hjOverrideQuantity = null,
                hjReceiptClosedQuantity = null,
                quantityOrdered = BigDecimal.ZERO,
                isPastDue = false,
                isBlankReceipt = false,
            )

            val result = calculateHjStatus(params)

            assertThat(result).isEqualTo(PoOverviewStatusEnum.IN_PROGRESS_HJ)
        }
    }
}
