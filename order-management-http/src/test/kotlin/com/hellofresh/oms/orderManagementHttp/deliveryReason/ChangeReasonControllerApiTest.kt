package com.hellofresh.oms.orderManagementHttp.deliveryReason

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.hamcrest.CoreMatchers.equalTo
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(scripts = ["/data/changeReasons.sql"])
class ChangeReasonControllerApiTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Test
    fun `should return order change reasons for order_change_reasons, and no markets`() {
        mockMvc.get(ORDER_CHANGE_REASONS_PATH) {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(3))

            jsonPath("$.items[0].id", equalTo("ee9b378b-0e9a-4491-a5fe-addb2cb8dbec"))
            jsonPath("$.items[0].name", equalTo("order change reason 1"))

            jsonPath("$.items[1].id", equalTo("961c8c64-0858-476c-9f1b-fb80e59e87ee"))
            jsonPath("$.items[1].name", equalTo("order change reason 2"))
        }
    }

    @Test
    fun `should return no order change reasons for market with no order_change_reasons`() {
        mockMvc.get(ORDER_CHANGE_REASONS_PATH) {
            param(MARKET, MARKET_ZZ)
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(0))
        }
    }

    @Test
    fun `should return 2 delivery date reasons when delivery date reasons, no markets provided`() {
        mockMvc.get(DELIVERY_DATE_REASONS_PATH) {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(2))

            jsonPath("$.items[0].id", equalTo("511fd932-2da7-4070-b564-fc5588760b24"))
            jsonPath("$.items[0].name", equalTo("delivery date reason 1"))

            jsonPath("$.items[1].id", equalTo("37c2e641-ac77-4fec-8b17-83091d4a9b60"))
            jsonPath("$.items[1].name", equalTo("delivery date reason 2"))
        }
    }

    @Test
    fun `should return multiple delivery date reasons for delivery_date_reasons and market`() {
        mockMvc.get(DELIVERY_DATE_REASONS_PATH) {
            param(MARKET, MARKET_US)
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(2))

            jsonPath("$.items[0].id", equalTo("511fd932-2da7-4070-b564-fc5588760b24"))
            jsonPath("$.items[0].name", equalTo("delivery date reason 1"))

            jsonPath("$.items[1].id", equalTo("37c2e641-ac77-4fec-8b17-83091d4a9b60"))
            jsonPath("$.items[1].name", equalTo("delivery date reason 2"))
        }
    }

    @Test
    fun `should return multiple delivery date reasons for delivery_date_reasons and lowercase market`() {
        mockMvc.get(DELIVERY_DATE_REASONS_PATH) {
            param(MARKET, MARKET_US_LOWER)
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(2))

            jsonPath("$.items[0].id", equalTo("511fd932-2da7-4070-b564-fc5588760b24"))
            jsonPath("$.items[0].name", equalTo("delivery date reason 1"))

            jsonPath("$.items[1].id", equalTo("37c2e641-ac77-4fec-8b17-83091d4a9b60"))
            jsonPath("$.items[1].name", equalTo("delivery date reason 2"))
        }
    }

    @Test
    fun `should return a single delivery date reason for delivery_date_reasons and market`() {
        mockMvc.get(DELIVERY_DATE_REASONS_PATH) {
            param(MARKET, MARKET_CA)
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(1))

            jsonPath("$.items[0].id", equalTo("37c2e641-ac77-4fec-8b17-83091d4a9b60"))
            jsonPath("$.items[0].name", equalTo("delivery date reason 2"))
        }
    }

    @Test
    fun `should return delivery date reasons for delivery_date_reasons when no market is provided`() {
        mockMvc.get(DELIVERY_DATE_REASONS_PATH) {
            param(MARKET, "")
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(2))

            jsonPath("$.items[0].id", equalTo("511fd932-2da7-4070-b564-fc5588760b24"))
            jsonPath("$.items[0].name", equalTo("delivery date reason 1"))

            jsonPath("$.items[1].id", equalTo("37c2e641-ac77-4fec-8b17-83091d4a9b60"))
            jsonPath("$.items[1].name", equalTo("delivery date reason 2"))
        }
    }

    companion object {
        const val ORDER_CHANGE_REASONS_PATH: String = "/reasons/order_item_change"
        const val DELIVERY_DATE_REASONS_PATH: String = "/reasons/delivery_date"
        const val MARKET: String = "market"
        const val MARKET_US: String = "US"
        const val MARKET_US_LOWER: String = "us"
        const val MARKET_ZZ: String = "ZZ"
        const val MARKET_CA: String = "CA"
    }
}
