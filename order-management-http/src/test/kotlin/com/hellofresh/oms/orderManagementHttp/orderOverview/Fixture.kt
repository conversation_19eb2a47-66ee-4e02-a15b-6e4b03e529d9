package com.hellofresh.oms.orderManagementHttp.orderOverview

import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.acknowledgement.UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_CASE
import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewStatusEnum
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.PurchaseOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.PurchaseOrderOverviewModel
import java.math.BigDecimal
import java.sql.Timestamp
import java.time.Instant
import java.util.UUID

object Fixture {
    fun getPurchaseOrderOverviewModel() =
        PurchaseOrderOverviewModel(
            poId = UUID.randomUUID(),
            poNumber = "PO123456",
            poVersion = 1,
            status = PoOverviewStatusEnum.RECEIVED_ACCURATE,
            supplierName = "Supplier Name",
            supplierCode = "SUP123",
            skuName = "SKU Name",
            skuCode = "SKU123",
            brand = "HF",
            scheduledDeliveryDate = java.time.LocalDateTime.now(),
            orderSize = BigDecimal("100.00"),
            totalPrice = BigDecimal("1000.00"),
            totalPriceCurrency = "USD",
            emergencyReason = "Emergency Reason",
            shippingMethod = "CROSSDOCK",
            creatorEmail = "<EMAIL>",
            week = "2023-W40",
            dcCode = "DC123",
            category = "Category",
            casePrice = BigDecimal("17.5"),
            casePriceCurrency = "USD",
            caseSize = BigDecimal("10.00"),
            orderUnit = "UNIT",
            purchasingUnit = "UNIT",
            quantityOrdered = BigDecimal("1000.00"),
            loadNumber = "2318NJ021001",
            appointmentTime = java.time.LocalDateTime.now(),
            carrierName = "GLOBAL LOGISTICS",
            locality = "HARRISON, NJ",
            palletCount = 8,
            hasIcsTickets = true,
            quantityReceived = BigDecimal("10.00"),
            casesReceived = 2,
            assignedBuyer = "Alice Johnson",
            phfDeliveryPercentOfForecast = BigDecimal("100.00"),
            receiptOverrideQuantity = 200,
            receiptOverrideCasesReceived = 20,
            receiptOverrideDeliveryDate = java.time.LocalDateTime.now(),
            receiptOverrideDetails = "Incorrectly closed PO",
            receiptOverrideCreatedAt = java.time.LocalDateTime.now(),
            receiptOverrideUpdatedBy = "<EMAIL>",
        )

    fun createPurchaseOrderStatusDetails() =
        PurchaseOrderStatusDetails(
            poId = UUID.randomUUID(),
            poNumber = "PO123456",
            version = 1,
            status = PoOverviewStatusEnum.RECEIVED_ACCURATE.toString(),
            supplierName = "supplierName",
            supplierCode = "supplierCode",
            skuName = "skuName",
            skuCode = "skuCode",
            emergencyReason = "emergencyReason",
            creatorEmail = "creatorEmail",
            week = "week",
            dcCode = "dcCode",
            category = "category",
            shippingMethod = ShipMethodEnum.CROSSDOCK.toString(),
            caseSize = BigDecimal.TEN,
            expectedStartTime = Timestamp.valueOf("2025-05-01 00:00:00"),
            unitOfMeasure = UOM.UNIT.toString(),
            skuUom = UOM.UNIT.toString(),
            packagingType = PackagingType.UNIT_TYPE.toString(),
            priceAmount = "17.5",
            priceCurrency = "USD",
            totalPrice = "10.000",
            totalPriceCurrency = "USD",
            totalQuantity = BigDecimal.ONE,
            ackUom = UNIT_OF_MEASURE_CASE.toString(),
            ackUnitsPerCase = BigDecimal.TWO,
            ackNumberOfUnits = BigDecimal.TEN,
            ackPromisedTime = Instant.now(),
            asnShipmentTime = Timestamp.valueOf("2025-04-29 12:00:00"),
            asnPlannedDeliveryTime = Timestamp.valueOf("2025-04-30 12:00:00"),
            asnShippedOrderSize = 100.0,
            asnShippedUnitType = "UNIT_OF_MEASURE_UNIT",
            asnShippedSize = 100.0,
            loadNumber = "loadNumber",
            appointmentTime = Timestamp.valueOf("2025-05-03 10:00:00"),
            carrierName = "carrierName",
            locality = "locality",
            palletCount = 8,
            hasIcsTickets = true,
            exportReceiptQuantityReceived = BigDecimal(10),
            exportReceiptCasesReceived = 2,
            receiptOverrideCasesReceived = 3,
            hjStatus = "Closed",
            assignedBuyerFirstName = "Alice",
            assignedBuyerLastName = "Johnson",
            weeklyForecastedDemand = BigDecimal(30),
            receiptOverrideQuantity = 1,
            receiptOverrideDeliveryDate = Timestamp.valueOf("2025-07-10 12:00:00"),
            receiptOverrideDetails = "Incorrectly closed PO",
            receiptOverrideCreatedAt = Timestamp.valueOf("2025-07-07 10:00:00"),
            receiptOverrideUpdatedBy = "<EMAIL>",
        )
}
