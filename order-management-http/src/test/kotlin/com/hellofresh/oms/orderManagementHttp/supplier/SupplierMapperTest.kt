package com.hellofresh.oms.orderManagementHttp.supplier

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.CROSSDOCK
import com.hellofresh.oms.model.ShipMethodEnum.FREIGHT_ON_BOARD
import com.hellofresh.oms.model.ShipMethodEnum.OTHER
import java.util.UUID.randomUUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class SupplierMapperTest {

    @Test
    fun `should map empty ship method list when user dc-input is empty`() {
        // given
        val shipMethods = getShipMethodEntity(DC_NJ to OTHER)
        val supplierEntityExtended = Fixture.supplierEntityExtended.copy(
            dcCodes = listOf(DC_NJ),
            shipMethods = shipMethods,
        )

        // when
        val actualSupplierDto = supplierEntityExtended.toSupplierDto(listOf(), shipMethods)

        // then
        val actualShippingMethods = actualSupplierDto.defaultShippingMethods
        assertArrayEquals(emptyArray<ShippingMethodDto>(), actualShippingMethods.toTypedArray())
    }

    @Test
    fun `should map empty ship method list when incoming dc does not match any of supplier dcs`() {
        // given
        val shipMethods = getShipMethodEntity(DC_NJ to OTHER)

        val supplierEntityExtended = Fixture.supplierEntityExtended.copy(
            dcCodes = listOf(DC_NJ),
            shipMethods = shipMethods,
        )

        // when
        val actualSupplierDto = supplierEntityExtended.toSupplierDto(listOf("XX"), shipMethods)

        // then
        val actualShippingMethods = actualSupplierDto.defaultShippingMethods
        assertArrayEquals(emptyArray<ShippingMethodDto>(), actualShippingMethods.toTypedArray())
    }

    @Test
    fun `should map multiple ship methods for one supplier`() {
        // given
        val shipMethods = getShipMethodEntity(
            DC_NJ to OTHER,
            DC_CY to CROSSDOCK,
            "XX" to FREIGHT_ON_BOARD,
        )

        val supplierEntityExtended = Fixture.supplierEntityExtended.copy(
            dcCodes = listOf(DC_NJ, DC_CY),
            shipMethods = shipMethods,
        )

        // when
        val actualSupplierDto = supplierEntityExtended.toSupplierDto(listOf(DC_NJ, DC_CY), shipMethods)

        // then
        val actualShippingMethods = actualSupplierDto.defaultShippingMethods
        assertArrayEquals(
            arrayOf(Method.OTHER, Method.CROSSDOCK),
            actualShippingMethods.map { it.method }.toTypedArray()
        )
    }

    @Test
    fun `should default to vendor if no ship method stored for supplier-dc`() {
        // given
        val shipMethods = getShipMethodEntity(DC_NJ to OTHER)
        val supplierEntity = Fixture.supplierEntityExtended.copy(
            dcCodes = listOf(DC_NJ, DC_CY),
            shipMethods = shipMethods,
        )

        // when
        val actualSupplierDto = supplierEntity.toSupplierDto(listOf(DC_NJ, DC_CY), shipMethods)

        // then
        val actualShippingMethods = actualSupplierDto.defaultShippingMethods
        assertArrayEquals(arrayOf(Method.OTHER, Method.VENDOR), actualShippingMethods.map { it.method }.toTypedArray())
    }

    @ParameterizedTest
    @ValueSource(
        strings = [
            "CROSSDOCK",
            "OTHER",
            "VENDOR",
            "FREIGHT_ON_BOARD",
        ],
    )
    fun `should map entity enum to DTO enum`(enumValue: String) {
        // given
        val shipMethods = getShipMethodEntity(DC_NJ to ShipMethodEnum.valueOf(enumValue))
        val supplierEntityExtended = Fixture.supplierEntityExtended.copy(
            dcCodes = listOf(DC_NJ),
            shipMethods = shipMethods,
        )

        // when
        val actualSupplierDto = supplierEntityExtended.toSupplierDto(listOf(DC_NJ), shipMethods)

        // then
        val actualShippingMethods = actualSupplierDto.defaultShippingMethods
        assertArrayEquals(arrayOf(Method.valueOf(enumValue)), actualShippingMethods.map { it.method }.toTypedArray())
    }

    @Test
    fun `should map Supplier to SupplierDto`() {
        // given
        val supplierEntity = Fixture.supplierEntityExtended

        // when
        val supplierDto = supplierEntity.toSupplierDto(listOf("AA", "BB"), Fixture.supplierEntityExtended.shipMethods)

        // then
        assertAll(
            { assertEquals(supplierEntity.id, supplierDto.id) },
            { assertEquals("Supplier 1", supplierDto.name) },
            { assertEquals("11111", supplierDto.code) },
            { assertEquals("us", supplierDto.market) },
            { assertNotNull(supplierDto.address) },
        )
    }

    companion object {
        const val DC_NJ = "NJ"
        const val DC_CY = "CY"

        fun getShipMethodEntity(vararg dcMethods: Pair<String, ShipMethodEnum>) =
            dcMethods.map { pair ->
                ShipMethod(
                    uuid = randomUUID(),
                    pair.first,
                    randomUUID(),
                    market = "us",
                    method = pair.second,
                )
            }
    }
}
