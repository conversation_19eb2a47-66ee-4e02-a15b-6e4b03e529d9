package com.hellofresh.oms.orderManagementHttp.icsTicket.service

import com.hellofresh.oms.model.icsTicket.IcsTicket
import com.hellofresh.oms.model.icsTicket.TicketPriority
import com.hellofresh.oms.model.icsTicket.TicketStatus
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketCustomFields
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketDto
import java.time.LocalDateTime
import kotlin.random.Random
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class IcsTicketMapperTest {

    @Test
    fun `should map IcsTicketDto service response to IcsTicket entity`() {
        // Given
        val ticketId = Random.nextInt()
        val subject = "Test Ticket"
        val priority = 2
        val status = 1
        val type = "Issue"
        val createdAt = LocalDateTime.now()
        val updatedAt = LocalDateTime.now()
        val week = "202501"
        val site = "SiteA"
        val skuCode = "SKU123"
        val ticketLink = "http://example.com/ticket/1"
        val requestType = "1 - TypeA"
        val productionImpact = "High"
        val po = "2501NJ123456_E1"

        val icsTicketDto = IcsTicketDto(
            id = ticketId,
            subject = subject,
            priority = priority,
            status = status,
            type = type,
            createdAt = createdAt,
            updatedAt = updatedAt,
            customFields = IcsTicketCustomFields(
                week = week,
                site = site,
                skuCode = skuCode,
                ticketLink = ticketLink,
                requestType = requestType,
                productionImpact = productionImpact,
                po = po,
            ),
        )

        // When
        val actual = icsTicketDto.toEntity()

        // Then
        assertEquals(ticketId, actual.ticketId)
        assertEquals(subject, actual.subject)
        assertEquals(TicketPriority.fromValue(priority), actual.priority)
        assertEquals(TicketStatus.fromValue(status), actual.status)
        assertEquals(type, actual.type)
        assertEquals(createdAt, actual.createdAt)
        assertEquals(updatedAt, actual.updatedAt)
        assertEquals(week, actual.week)
        assertEquals(site, actual.bobCode)
        assertEquals(skuCode, actual.skuCode)
        assertEquals(po.split('_').first().trim(), actual.poNumber)
        assertEquals(po, actual.poReference)
        assertEquals(ticketLink, actual.ticketLink)
        assertEquals(requestType.split('-').getOrNull(1)?.trim(), actual.requestType)
        assertEquals(productionImpact, actual.productionImpact)
        assertEquals("US", actual.market)
    }

    @Test
    fun `should map IcsTicket entity to api response`() {
        // Given
        val ticketId = Random.nextInt()
        val market = "US"
        val week = "202501"
        val bobCode = "SiteA"
        val skuCode = "SKU123"
        val poNumber = "2501NJ123456"
        val poReference = "2501NJ123456_E1"
        val subject = "Test Ticket"
        val ticketLink = "http://example.com/ticket/1"
        val priority = TicketPriority.MEDIUM
        val requestType = "TypeA"
        val productionImpact = "High"
        val status = TicketStatus.OPEN
        val type = "Issue"
        val createdAt = LocalDateTime.now()
        val updatedAt = LocalDateTime.now()

        val entity = IcsTicket(
            ticketId = ticketId,
            market = market,
            week = week,
            bobCode = bobCode,
            skuCode = skuCode,
            poNumber = poNumber,
            poReference = poReference,
            subject = subject,
            ticketLink = ticketLink,
            priority = priority,
            requestType = requestType,
            productionImpact = productionImpact,
            status = status,
            type = type,
            createdAt = createdAt,
            updatedAt = updatedAt
        )

        // When
        val actual = entity.toResponse()

        // Then
        assertEquals(ticketId, actual.ticketId)
        assertEquals(week, actual.week)
        assertEquals(bobCode, actual.bobCode)
        assertEquals(subject, actual.subject)
        assertEquals(poNumber, actual.poNumber)
        assertEquals(skuCode, actual.skuCode)
        assertEquals(ticketLink, actual.ticketLink)
        assertEquals(requestType, actual.requestType)
        assertEquals(status.toString(), actual.status)
        assertEquals(productionImpact, actual.productionImpact)
        assertEquals(createdAt, actual.createdAt)
        assertEquals(updatedAt, actual.updatedAt)
    }
}
