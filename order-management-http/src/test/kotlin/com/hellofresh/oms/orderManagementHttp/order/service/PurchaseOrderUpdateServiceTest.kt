package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.orderManagement.generated.api.model.EditOrderItemRequest
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum
import com.hellofresh.oms.orderManagement.generated.api.model.UnitPackagingRequest
import com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.out.GoodsReceivedNoteRepository
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.order.getTestUser
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import io.micrometer.core.instrument.MeterRegistry
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.whenever

@Suppress("UnusedPrivateProperty")
@ExtendWith(MockitoExtension::class)
class PurchaseOrderUpdateServiceTest {

    @Mock
    private lateinit var purchaseOrderService: PurchaseOrderService

    @Mock
    private lateinit var purchaseOrderValidatorService: PurchaseOrderValidatorService

    @Mock
    private lateinit var meterRegistry: MeterRegistry

    @Mock
    private lateinit var goodsReceiptRepository: GoodsReceivedNoteRepository

    @Mock
    private lateinit var purchaseOrderRepository: PurchaseOrderRepository

    @Mock
    private lateinit var purchaseOrderProducer: PurchaseOrderProducer

    @InjectMocks
    private lateinit var subject: PurchaseOrderUpdateService

    @Test
    fun `should keep origin from persisted PO`() {
        // given
        val givenPoNumber = "PO12345"
        val supplierId = UUID.randomUUID()
        val givenRequest = EditPurchaseOrderRequest(
            comment = "Updated comment",
            orderItems = listOf(
                EditOrderItemRequest(
                    skuId = UUID.randomUUID(),
                    packaging = UnitPackagingRequest(
                        packagingType = PackagingTypeEnum.UNIT,
                        numberOfUnits = 10,
                        pricePerUnit = MoneyDto(
                            amount = "1.0",
                            currency = "USD",
                        ),
                    ),
                    bufferPercent = 0.0.toBigDecimal(),
                ),
            ),
            shippingMethod = ShipMethodEnum.CROSSDOCK,
        )
        val persistedPurchaseOrder = getPurchaseOrderEntity(
            poNumber = givenPoNumber,
            origin = Origin.MANUAL,
            supplierId = supplierId,
        )
        whenever(purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(eq(givenPoNumber)))
            .thenReturn(
                persistedPurchaseOrder,
            )
        whenever(purchaseOrderValidatorService.validateSupplier(eq(supplierId)))
            .thenReturn(
                getSupplierEntity(market = "us"),
            )
        val argumentCaptor = argumentCaptor<PurchaseOrder>()
        whenever(purchaseOrderService.saveAndQueuePurchaseOrder(argumentCaptor.capture(), eq(false)))
            .thenReturn(persistedPurchaseOrder)

        // when
        subject.processEditPurchaseOrder(
            poNumber = givenPoNumber,
            editPurchaseOrderRequest = givenRequest,
            loggedInUser = getTestUser(),
        )

        // then
        val updatedPurchaseOrder = argumentCaptor.firstValue
        assertThat(updatedPurchaseOrder.origin, equalTo(persistedPurchaseOrder.origin))
    }
}
