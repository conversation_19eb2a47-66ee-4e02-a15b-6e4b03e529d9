package com.hellofresh.oms.orderManagementHttp.order

import com.hellofresh.oms.model.ChangeReason
import com.hellofresh.oms.model.ChangeReasonType
import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterAddressType
import com.hellofresh.oms.model.DistributionCenterStatus
import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.OutboxItemStatus.SENT
import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.POType.EMERGENCY
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType.CASE_TYPE
import com.hellofresh.oms.model.PackagingType.PALLET_TYPE
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.PurchaseOrderStatus.INITIATED
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.SkuStatus
import com.hellofresh.oms.model.SkuStatus.ARCHIVED
import com.hellofresh.oms.model.SupplierContact
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.UOM.KG
import com.hellofresh.oms.model.UnitOfMeasure
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.grn.GrnStateEnum
import com.hellofresh.oms.model.supplier.Supplier
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.model.supplier.SupplierStatus.ACTIVE
import com.hellofresh.oms.orderManagement.generated.api.model.AddressDto
import com.hellofresh.oms.orderManagement.generated.api.model.CasePackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.CreateOrderItemRequest
import com.hellofresh.oms.orderManagement.generated.api.model.CreatePurchaseOrderRequest
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagement.generated.api.model.EditDeliveryWindowDto
import com.hellofresh.oms.orderManagement.generated.api.model.EditOrderItemRequest
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersResponsePageResult
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum.MINUS_CREATED_AT
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum.CASE
import com.hellofresh.oms.orderManagement.generated.api.model.PalletPackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusFilterEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderSendStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.UnitPackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum.UNIT
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.order.intake.toDto
import com.hellofresh.oms.orderManagementHttp.order.service.CreatePurchaseOrderRequestDto
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomain
import com.hellofresh.oms.orderManagementHttp.uom.toApiResponse
import com.hellofresh.oms.util.Calculator
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDateTime.now
import java.util.UUID
import java.util.UUID.randomUUID
import org.intellij.lang.annotations.Language
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort

// TODO: this test UUID is shared between this and the emergency reason uuid. Change to make it unique.
fun getDeliveryDateChangeReasonId(): UUID = UUID.fromString("37c2e641-ac77-4fec-8b17-83091d4a9b60")

val supplier = SupplierExtended(
    id = randomUUID(),
    parentId = randomUUID(),
    market = "",
    code = 0,
    name = "",
    status = ACTIVE,
    currency = "USD",
    type = "",
    createdAt = null,
    updatedAt = null,
    supplierAddress = SupplierAddress(
        city = "",
        country = "",
        state = "",
        address = "",
        number = "",
        postCode = "",
    ),
    contacts = setOf(),
    dcCodes = listOf(),
    shipMethods = listOf(),
)

fun getEmergencyReasonEntity(
    uuid: UUID = UUID.fromString("511FD932-2DA7-4070-B564-FC5588760B24"),
    name: String = "Ordering Error",
    market: String = "US",
    disabled: Boolean = false
): EmergencyReason = EmergencyReason(
    uuid = uuid,
    name = name,
    market = market,
    disabled = disabled,
)

val emergencyReason = EmergencyReason(
    uuid = randomUUID(),
    name = "",
    market = "",
    disabled = false,
)

val deliveryAddressEntity = DistributionCenterAddress(
    id = randomUUID(),
    dcCode = "",
    number = "",
    address = "",
    zip = "",
    city = "Berlin",
    state = "",
    company = null,
    type = DistributionCenterAddressType.DELIVERY,
    createdAt = now(),
    updatedAt = now(),
    countryCode = "",
)

val billingAddress = DistributionCenterAddress(
    id = randomUUID(),
    dcCode = "",
    number = "",
    address = "",
    zip = "",
    city = "London",
    state = "",
    company = null,
    type = DistributionCenterAddressType.BILLING,
    createdAt = now(),
    updatedAt = now(),
    countryCode = "",
)

@Suppress("LongParameterList")
fun getDistributionCenterEntity(
    code: String = "NJ",
    status: DistributionCenterStatus = DistributionCenterStatus.ACTIVE,
    name: String = "HelloFresh United States - NJ",
    market: String = "US",
    addresses: List<DistributionCenterAddress> = listOf(
        DistributionCenterAddress(
            id = randomUUID(),
            dcCode = "NJ",
            number = "60",
            address = "Lister Avenue",
            zip = "07105",
            city = "Newark",
            state = "NJ",
            company = "HelloFresh United States - NJ",
            type = DistributionCenterAddressType.BILLING,
            createdAt = now(),
            updatedAt = now(),
            countryCode = "US",
        ),
        DistributionCenterAddress(
            id = randomUUID(),
            dcCode = "NJ",
            number = "60",
            address = "60 Lister Avenue",
            zip = "07105",
            city = "Newark",
            state = "NJ",
            company = "HelloFresh United States - NJ",
            type = DistributionCenterAddressType.DELIVERY,
            createdAt = now(),
            updatedAt = now(),
            countryCode = "US",
        )
    ),
    createdAt: LocalDateTime = now(),
    updatedAt: LocalDateTime = now(),
): DistributionCenter = DistributionCenter(
    code = code,
    status = status,
    name = name,
    market = market,
    addresses = addresses,
    createdAt = createdAt,
    updatedAt = updatedAt,
)

val distributionCenter = DistributionCenter(
    code = "NJ",
    status = DistributionCenterStatus.ACTIVE,
    name = "",
    market = "",
    addresses = listOf(deliveryAddressEntity, billingAddress),
    createdAt = now(),
    updatedAt = now(),
)

fun getUomEntity(id: UUID = UUID.fromString("022fa317-1df8-4e91-9254-2c1768202004"), uom: UOM = KG) = UnitOfMeasure(
    id,
    "units",
    "US",
    uom,
    "bulk",
)

fun getCurrentTime(): LocalDateTime = LocalDateTime.parse("2022-09-27T15:13:31.000000")

fun getTestUser(
    userId: UUID = UUID.fromString("3c0a5325-0492-4dc1-bf29-cc7d44630a5b"),
    userEmail: String = "<EMAIL>"
): LoggedInUserInfo =
    LoggedInUserInfo(
        userId = userId,
        userEmail = userEmail,
        userName = "name",
        roles = emptyList(),
        issuer = UUID.randomUUID().toString(),
    )

@Suppress("LongParameterList")
fun getOutboxItem(
    id: UUID = randomUUID(),
    poId: UUID = getPurchaseOrderEntity().id,
    poNumber: String = getPurchaseOrderEntity().poNumber,
    userEmail: String = getTestUser().userEmail,
    userId: UUID = getTestUser().userId,
    status: OutboxItemStatus = SENT,
    version: Int = getPurchaseOrderEntity().version,
    createdAt: LocalDateTime = getCurrentTime(),
): OutboxItem =
    OutboxItem(
        id = id,
        poId = poId,
        poNumber = poNumber,
        status = status,
        userEmail = userEmail,
        userId = userId,
        lastStatusChangeAt = getCurrentTime(),
        createdAt = createdAt,
        version = version,
    )

@Suppress("LongParameterList")
fun getPurchaseOrderEntity(
    poNumber: String = "2318NJ021004",
    id: UUID = UUID.fromString("e3fa59aa-91c1-4c78-bfa5-000000000002"),
    yearWeek: YearWeek = YearWeek("2023-W18"),
    status: PurchaseOrderStatus = INITIATED,
    dcCode: String = "NJ",
    supplierCode: String = "113200",
    type: POType = EMERGENCY,
    bufferPermyriad: Permyriad = Permyriad(300),
    createdAt: LocalDateTime = LocalDateTime.parse("2022-09-27T15:13:31.000000"),
    updatedAt: LocalDateTime = createdAt,
    shipMethod: ShipMethodEnum = VENDOR,
    expectedStartTime: LocalDateTime = LocalDateTime.parse("2023-04-29T10:00:00.000000"),
    expectedEndTime: LocalDateTime = LocalDateTime.parse("2023-04-29T11:00:00.000000"),
    version: Int = 2,
    shippingAddress: ShippingAddress = ShippingAddress(
        locationName = "HelloFresh United States - NJ",
        streetAddress = "60 Lister Avenue",
        city = "Newark",
        region = "NJ",
        postalCode = "07105",
        countryCode = "US",
    ),
    orderItems: Set<OrderItem> = setOf(getOrderItem(id, bufferPermyriad)),
    sendTime: LocalDateTime? = LocalDateTime.parse("2023-04-29T10:00:00.000000"),
    deliveryDateChangeReasonId: UUID? = null,
    supplierId: UUID = UUID.fromString("35661b1f-edd1-4ce2-ad33-f7c982086b2a"),
    totalPrice: Money = Money(
        currency = "USD",
        amount = BigDecimal(190).setScale(MoneyDomain.PRECISION.value),
    ),
    comment: String? = null,
    isSynced: Boolean = false,
    origin: Origin = Origin.MANUAL,
    emergencyReasonId: UUID = UUID.fromString("511FD932-2DA7-4070-B564-FC5588760B24"),
) = PurchaseOrder(
    poNumber = poNumber,
    id = id,
    version = version,
    type = type,
    yearWeek = yearWeek,
    userId = getTestUser().userId,
    userEmail = getTestUser().userEmail,
    status = status,
    sendTime = sendTime,
    supplierId = supplierId,
    supplierCode = supplierCode,
    dcCode = dcCode,
    shippingMethod = shipMethod,
    shippingAddress = shippingAddress,
    expectedStartTime = expectedStartTime,
    expectedEndTime = expectedEndTime,
    emergencyReasonUuid = emergencyReasonId,
    createdAt = createdAt,
    updatedAt = updatedAt,
    totalPrice = totalPrice,
    comment = comment,
    orderItems = orderItems,
    isSynced = isSynced,
    deliveryDateChangeReasonId = deliveryDateChangeReasonId,
    origin = origin,
)

fun getOrderItemChangeReasonEntity(
    id: UUID = UUID.fromString("ee9b378b-0e9a-4491-a5fe-addb2cb8dbec"),
    name: String = "order change reason 1",
    allowedMarkets: List<String> = listOf("US"),
) =
    ChangeReason(
        id = id,
        name = name,
        allowedMarkets = allowedMarkets,
        reasonType = ChangeReasonType.ORDER_ITEM_CHANGE,
    )

@Suppress("LongParameterList")
fun getSkuEntity(
    uuid: UUID = randomUUID(),
    market: String = "us",
    name: String = "DO NOT USE - X3B- Bun, Brioche (1pc)",
    code: String = "BAK-10-004062-1",
    status: SkuStatus = ARCHIVED,
    brands: List<String> = listOf("{HelloFresh}"),
    category: String = "BAK",
    uom: UOM? = KG
): Sku = Sku(
    uuid = uuid,
    market = market,
    name = name,
    code = code,
    status = status,
    brands = brands,
    category = category,
    uom = uom,
)

@Suppress("LongParameterList")
fun getOrderItem(
    id: UUID = UUID.fromString("26e074d4-0141-4033-bffd-000d2a9d3308"),
    buffer: Permyriad = Permyriad(300),
    packaging: Packaging = Packaging(
        packagingType = CASE_TYPE,
        unitOfMeasure = KG,
        caseSize = BigDecimal(12).setScale(Calculator.PRECISION),
    ),
    skuId: UUID = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
    casesPerPallet: Int? = null,
    totalQty: BigDecimal = BigDecimal(456).setScale(Calculator.PRECISION),
) = OrderItem(
    id = id,
    skuId = skuId,
    buffer = buffer,
    correctionReason = "correction reason",
    packaging = packaging,
    poId = UUID.fromString("e3fa59aa-91c1-4c78-bfa5-000000000002"),
    createdAt = LocalDateTime.parse("2022-09-27T15:13:31.000000"),
    updatedAt = LocalDateTime.parse("2022-09-27T15:13:31.000000"),
    price = Money(
        currency = "USD",
        amount = BigDecimal(5).setScale(MoneyDomain.PRECISION.value),
    ),
    totalPrice = Money(
        currency = "USD",
        amount = BigDecimal(190).setScale(MoneyDomain.PRECISION.value),
    ),
    totalQty = totalQty,
    rawQty = BigDecimal(432).setScale(Calculator.PRECISION),
    changeReasonId = null,
    casesPerPallet = casesPerPallet,
)

@Suppress("LongParameterList")
fun getSupplierEntity(
    supplierId: UUID = randomUUID(),
    name: String = "Supplier Name",
    contacts: Set<SupplierContact> = setOf(
        SupplierContact(
            supplierId = supplierId,
            email = "<EMAIL>",
        ),
    ),
    code: Int = 1234,
    market: String = "dach",
    supplierAddress: SupplierAddress = SupplierAddress(
        "Berlin",
        "Germany",
        state = "Berlin",
        address = "HelloFresh Str. 1",
        number = "1234",
        postCode = "1234",
    )
) = SupplierExtended(
    id = supplierId,
    parentId = randomUUID(),
    market = market,
    code = code,
    name = name,
    status = ACTIVE,
    currency = "EUR",
    type = "Manufacturer",
    createdAt = LocalDateTime.parse("2023-04-05T18:46:00"),
    updatedAt = LocalDateTime.parse("2023-04-05T18:46:00"),
    dcCodes = listOf("NJ"),
    supplierAddress = supplierAddress,
    contacts = contacts,
    shipMethods = emptyList(),
)

fun getSupplierSimpleEntity(
    supplierId: UUID = randomUUID(),
    name: String = "Supplier Name",
    code: Int = 1234,
    market: String = "dach",
) = Supplier(
    id = supplierId,
    parentId = randomUUID(),
    name = name,
    code = code,
    market = market,
    status = ACTIVE,
    createdAt = LocalDateTime.parse("2023-04-05T18:46:00"),
    updatedAt = LocalDateTime.parse("2023-04-05T18:46:00"),
    currency = "EUR",
    type = "Manufacturer",
    dcCodes = listOf("NJ"),
    supplierAddress = SupplierAddress(
        "Berlin",
        "Germany",
        state = "Berlin",
        address = "HelloFresh Str. 1",
        number = "1234",
        postCode = "1234",
    ),
)

val deliveryAddress = AddressDto(
    address = "60 Lister Avenue",
    postalCode = "07105",
    city = "Newark",
    region = "NJ",
    countryCode = "US",
)

val casePackaging = CasePackagingRequest(
    numberOfCases = 1,
    unitsPerCase = BigDecimal(10),
    pricePerCase = MoneyDto("10", "USD"),
    uom = UNIT,
    packagingType = CASE,
)

val unitPackaging = UnitPackagingRequest(
    pricePerUnit = MoneyDto("10", "USD"),
    numberOfUnits = 1,
    packagingType = PackagingTypeEnum.UNIT,
)

val palletPackaging = PalletPackagingRequest(
    numberOfPallets = 1,
    casesPerPallet = 1,
    unitsPerCase = BigDecimal(10),
    pricePerCase = MoneyDto("10", "USD"),
    uom = UNIT,
    packagingType = PackagingTypeEnum.PALLET,
)

val palletItem = CreateOrderItemRequest(
    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
    bufferPercent = BigDecimal(1),
    packaging = palletPackaging,
)

val unitItem = CreateOrderItemRequest(
    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
    bufferPercent = BigDecimal(1),
    packaging = unitPackaging,
)

val caseItem = CreateOrderItemRequest(
    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
    bufferPercent = BigDecimal(1),
    packaging = casePackaging,
)

val poRequestBody = CreatePurchaseOrderRequest(
    deliveryWindow = DeliveryWindowDto(
        start = LocalDateTime.parse("2023-04-29T10:00:00.000000"),
        end = LocalDateTime.parse("2023-04-29T11:00:00.000000"),
    ),
    orderItems = listOf(),
    dcCode = "NJ",
    dcWeek = "2022-W10",
    deliveryAddress = deliveryAddress,
    shippingMethod = com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.VENDOR,
    supplierId = randomUUID(),
    comment = "Test comment",
    emergencyReasonId = randomUUID(),
)

val createPoRequestDto = CreatePurchaseOrderRequestDto(
    deliveryWindow = DeliveryWindowDto(
        start = LocalDateTime.parse("2023-04-29T10:00:00.000000"),
        end = LocalDateTime.parse("2023-04-29T10:00:00.000000"),
    ),
    orderItems = listOf(),
    dcCode = "NJ",
    dcWeek = "2022-W10",
    deliveryAddress = deliveryAddress,
    shippingMethod = com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.VENDOR,
    supplierId = randomUUID(),
    comment = "Test comment",
    emergencyReasonId = randomUUID(),
    userId = getTestUser().userId,
    userEmail = getTestUser().userEmail,
    origin = Origin.MANUAL,
)

@Suppress("LongParameterList")
fun getCreateOrderItemRequest(
    skuId: UUID = randomUUID(),
    bufferPercent: BigDecimal = BigDecimal(1),
    packaging: PackagingRequest,
): CreateOrderItemRequest = CreateOrderItemRequest(
    skuId = skuId,
    bufferPercent = bufferPercent,
    packaging = packaging,
)

@Suppress("LongParameterList", "MaxLineLength")
fun getPurchaseOrderEditRequest(
    shippingMethod: com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum = com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.VENDOR,
    deliveryWindowDto: EditDeliveryWindowDto? = null,
    emergencyReason: UUID? = UUID.fromString("511FD932-2DA7-4070-B564-FC5588760B24"),
    orderItems: List<EditOrderItemRequest>? = listOf(getEditOrderItemRequest(changeReasonId = null)),
    comment: String? = null,
) = EditPurchaseOrderRequest(
    deliveryWindow = deliveryWindowDto,
    orderItems = orderItems,
    comment = comment,
    shippingMethod = shippingMethod,
    emergencyReasonId = emergencyReason,
)

@Suppress("LongParameterList")
fun getEditOrderItemRequest(
    skuId: UUID = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
    bufferPercent: BigDecimal = BigDecimal(1),
    packaging: PackagingRequest = casePackaging,
    changeReasonId: UUID? = UUID.fromString("511fd932-2da7-4070-b564-fc5588760b24"),
) = EditOrderItemRequest(
    skuId = skuId,
    bufferPercent = bufferPercent,
    packaging = packaging,
    changeReasonId = changeReasonId,
)

@Suppress("LongMethod")
@Language("json")
fun getJsonPoRequestBody() =
    """
        {
          "delivery_window": {
            "start": "2023-04-05T18:46:00",
            "end": "2023-04-06T18:46:00"
          },
          "order_items": [
            {
              "sku_id": "7a291c6d-688a-41f3-b0ec-7ff760aa7a6a",
              "buffer_percent": 1,
              "packaging": {
                "packaging_type": "CASE",
                "number_of_cases": 11,
                "units_per_case": 10,
                "price_per_case": {
                  "amount": "10",
                  "currency": "EUR"
                },
                "uom": "KG"
              }
            },
            {
              "sku_id": "7a291c6d-688a-41f3-b0ec-000000000000",
              "buffer_percent": 1,
              "packaging": {
                "packaging_type": "PALLET",
                "units_per_case": 1,
                "cases_per_pallet": 10,
                "number_of_pallets": 1,
                "price_per_case": {
                  "amount": "10",
                  "currency": "EUR"
                },
                "uom": "KG"
              }
            },
            {
              "sku_id": "7a291c6d-688a-41f3-b0ec-111111111111",
              "buffer_percent": 1,
              "packaging": {
                "packaging_type": "UNIT",
                "number_of_units": 1,
                "price_per_unit": {
                  "amount": "10",
                  "currency": "EUR"
                }
              }
            }
          ],
          "dc_code": "NJ",
          "dc_week": "2022-W35",
          "delivery_address": {
               "address": "Hauptstrasse",
               "postal_code": "10435",
               "city": "Berlin",
               "region": "Berlin",
               "type": "DELIVERY",
               "country_code": "us"
          },
          "supplier_id": "5f79dd0f-b3fa-40b9-9868-0959a5860fdd",
          "shipping_method": "OTHER",
          "emergency_reason_id": "37c2e641-ac77-4fec-8b17-83091d4a9b60",
          "comment": "test comment"
        }

    """.trimIndent()

@Suppress("LongMethod")
@Language("json")
fun getJsonWithZeroPricePoRequestBody() =
    """
        {
          "delivery_window": {
            "start": "2023-04-05T18:46:00",
            "end": "2023-04-06T18:46:00"
          },
          "order_items": [
            {
              "sku_id": "7a291c6d-688a-41f3-b0ec-7ff760aa7a6a",
              "buffer_percent": 1,
              "packaging": {
                "packaging_type": "CASE",
                "number_of_cases": 11,
                "units_per_case": 10,
                "price_per_case": {
                  "amount": "0",
                  "currency": "EUR"
                },
                "uom": "KG"
              }
            },
            {
              "sku_id": "7a291c6d-688a-41f3-b0ec-000000000000",
              "buffer_percent": 1,
              "packaging": {
                "packaging_type": "PALLET",
                "units_per_case": 1,
                "cases_per_pallet": 10,
                "number_of_pallets": 1,
                "price_per_case": {
                  "amount": "0",
                  "currency": "EUR"
                },
                "uom": "KG"
              }
            },
            {
              "sku_id": "7a291c6d-688a-41f3-b0ec-111111111111",
              "buffer_percent": 1,
              "packaging": {
                "packaging_type": "UNIT",
                "number_of_units": 1,
                "price_per_unit": {
                  "amount": "0",
                  "currency": "EUR"
                }
              }
            }
          ],
          "dc_code": "NJ",
          "dc_week": "2022-W35",
          "delivery_address": {
               "address": "Hauptstrasse",
               "postal_code": "10435",
               "city": "Berlin",
               "region": "Berlin",
               "type": "DELIVERY",
               "country_code": "us"
          },
          "supplier_id": "5f79dd0f-b3fa-40b9-9868-0959a5860fdd",
          "shipping_method": "OTHER",
          "emergency_reason_id": "37c2e641-ac77-4fec-8b17-83091d4a9b60",
          "comment": "test comment"
        }

    """.trimIndent()

fun getModifiedEditPurchaseOrderRequest() = getPurchaseOrderEditRequest(
    shippingMethod = com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.CROSSDOCK,
    deliveryWindowDto = EditDeliveryWindowDto(
        start = LocalDateTime.parse("2023-04-30T10:00:00.000000"),
        end = LocalDateTime.parse("2023-04-30T11:00:00.000000"),
        changeReasonId = getDeliveryDateChangeReasonId(),
    ),
    emergencyReason = UUID.fromString("37C2E641-AC77-4FEC-8B17-83091D4A9B60"),
    orderItems = listOf(
        getEditOrderItemRequest(
            skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-000000000000"),
            changeReasonId = getOrderItemChangeReasonEntity().id,
        ),
    ),
    comment = "This is a comment.",
)

fun getPageResultDto() = ListPurchaseOrdersResponsePageResult(
    number = 0,
    pageSize = 10,
    totalPages = 1,
    sort = listOf(MINUS_CREATED_AT),
    totalElements = 1,
)

fun <T> getPageOf(listOf: List<T>): Page<T> = PageImpl(listOf, getPageable(), 1)

fun getPageable(sorting: Sort = Sort.by(Sort.Direction.DESC, "created_at")) = PageRequest.of(0, 10, sorting)

fun PurchaseOrder.toResponseMap(
    supplier: SupplierExtended,
    sendStatus: PurchaseOrderSendStatusEnum = PurchaseOrderSendStatusEnum.NOT_SENT,
    grnStatus: PurchaseOrderGrnStatusFilterEnum? = null,
) = mapOf(
    "id" to id,
    "version" to version,
    "po_number" to poNumber,
    "dc_code" to dcCode,
    "dc_week" to yearWeek.value,
    "supplier_id" to supplier.id,
    "created_at" to createdAt,
    "updated_at" to updatedAt,
    "status" to status.name,
    "po_type" to type.name,
    "user_email" to userEmail,
    "is_synced" to isSynced,
    "send_status" to sendStatus,
    "country_code" to shippingAddress.countryCode,
    "supplier" to mapOf(
        "id" to supplier.id,
        "code" to supplier.code,
        "name" to supplier.name,
        "address" to mapOf(
            "number" to supplier.supplierAddress.number,
            "address" to supplier.supplierAddress.address,
            "city" to supplier.supplierAddress.city,
            "country_code" to supplier.supplierAddress.country,
            "region" to supplier.supplierAddress.state,
            "postal_code" to supplier.supplierAddress.postCode,
        ),
        "currency" to supplier.currency,
        "market" to supplier.market,
        "parent_id" to supplier.parentId,
        "status" to supplier.status.name,
        "type" to supplier.type,
        "created_at" to supplier.createdAt,
        "updated_at" to supplier.updatedAt,
    ),
    "comment" to comment,
    "shipping_method" to shippingMethod.name,
    "delivery_window" to mapOf(
        "start" to expectedStartTime,
        "end" to expectedEndTime,
        "change_reason_id" to deliveryDateChangeReasonId,
    ),
    "delivery_address" to mapOf(
        "location_name" to shippingAddress.locationName,
        "address" to shippingAddress.streetAddress,
        "city" to shippingAddress.city,
        "country_code" to shippingAddress.countryCode,
        "region" to shippingAddress.region,
        "postal_code" to shippingAddress.postalCode,
    ),
    "emergency_reason_id" to emergencyReasonUuid,
    "send_time" to sendTime,
    "grn_status" to grnStatus,
)

fun OrderItem.toResponseMap(sku: Sku) = mapOf(
    "id" to id,
    "sku" to sku.toResponseMap(),
    "sku_id" to skuId,
    "buffer_percent" to buffer.toPercent().toDouble(),
    "packaging" to packaging.toResponseMap(this),
    "correction_reason" to correctionReason,
    "change_reason_id" to changeReasonId,
    "created_at" to createdAt,
    "updated_at" to updatedAt,
)

fun Sku.toResponseMap() = mapOf(
    "id" to uuid,
    "name" to name,
    "code" to code,
    "market" to market,
    "status" to status,
    "brands" to brands,
    "category" to category,
    "uom" to uom?.name,
)

fun Packaging.toResponseMap(orderItem: OrderItem) = when (packagingType) {
    CASE_TYPE -> mapOf(
        "number_of_cases" to orderItem.totalQty.toInt() / caseSize!!.toInt(),
        "units_per_case" to caseSize!!.toInt(),
        "price_per_case" to orderItem.price.toDto(),
        "uom" to unitOfMeasure,
        "packaging_type" to PackagingTypeEnum.CASE,
    )

    PALLET_TYPE -> mapOf(
        "number_of_pallets" to orderItem.totalQty.toInt() / (caseSize!!.toInt() * orderItem.casesPerPallet!!),
        "cases_per_pallet" to orderItem.casesPerPallet,
        "units_per_case" to caseSize!!.toInt(),
        "price_per_case" to orderItem.price.toDto(),
        "uom" to unitOfMeasure.toApiResponse(),
        "packaging_type" to PackagingTypeEnum.PALLET,
    )

    UNIT_TYPE -> mapOf(
        "price_per_unit" to orderItem.price.toDto(),
        "number_of_units" to orderItem.totalQty.toInt(),
        "packaging_type" to PackagingTypeEnum.UNIT,
    )
}

fun GrnStateEnum.toDto() = when (this) {
    GrnStateEnum.STATE_OPEN -> PurchaseOrderGrnStatusEnum.OPENED
    GrnStateEnum.STATE_CLOSE -> PurchaseOrderGrnStatusEnum.CLOSED
    GrnStateEnum.STATE_UNSPECIFIED -> PurchaseOrderGrnStatusEnum.UNSPECIFIED
}
