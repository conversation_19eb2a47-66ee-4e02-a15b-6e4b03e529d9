package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderSendStatusEnum.NOT_SENT
import com.hellofresh.oms.orderManagementHttp.order.toResponseMap
import java.util.UUID
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.get
import uk.org.webcompere.modelassert.json.JsonAssertions.json

class GetPurchaseOrderRevisionIntegrationTest : PurchaseOrderAbstractIntegrationTest() {
    @Test
    fun `should return all revisions for the given purchase order number`() {
        // given
        val givenPoNumber = "PO-123"
        val givenSupplierId = UUID.randomUUID()
        val givenSupplier = saveSupplier(givenSupplierId)
        val poV1 = savePurchaseOrderRevision(
            poNumber = givenPoNumber,
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
        )
        val poV2 = savePurchaseOrderRevision(
            poNumber = givenPoNumber,
            version = 2,
            supplierId = givenSupplierId,
            comment = "second version",
        )
        val poV3 = savePurchaseOrderRevision(
            poNumber = givenPoNumber,
            version = 3,
            supplierId = givenSupplierId,
            comment = "third version",
        )
        savePurchaseOrderRevision(
            poNumber = "ANOTHER-PO",
            version = 1,
            supplierId = givenSupplierId,
            comment = "whatever",
        )

        // when
        val expectedResponse = listOf(
            poV1.toResponseMap(givenSupplier, NOT_SENT),
            poV2.toResponseMap(givenSupplier, NOT_SENT),
            poV3.toResponseMap(givenSupplier, NOT_SENT),
        )
        mockMvc.get("/orders/{po_number}/revisions", givenPoNumber)
            .andExpect {
                status { isOk() }
                content {
                    string(
                        json()
                            .at("/revisions")
                            .where()
                            .keysInAnyOrder()
                            .isEqualTo(expectedResponse),
                    )
                }
            }
    }

    @Test
    fun `should return empty response when no purchase order was found`() {
        // given
        val givenPoNumber = "non-existing-po-number"

        // when
        mockMvc.get("/orders/{po_number}/revisions", givenPoNumber)
            .andExpect {
                status { isOk() }
                content {
                    string(
                        json()
                            .at("/revisions")
                            .isEmpty(),
                    )
                }
            }
    }
}
