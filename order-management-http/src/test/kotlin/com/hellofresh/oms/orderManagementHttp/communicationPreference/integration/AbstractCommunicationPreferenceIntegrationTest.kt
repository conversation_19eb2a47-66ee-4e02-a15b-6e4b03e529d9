package com.hellofresh.oms.orderManagementHttp.communicationPreference.integration

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.communicationPreference.CommunicationPreference
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.EMAIL
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.orderManagementHttp.communicationPreference.out.CommunicationPreferenceRepository
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import java.util.UUID
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc

@Tag("integration")
@SpringBootTest
@AutoConfigureMockMvc
@Sql(scripts = ["/data/deleteCommunicationPreferences.sql"])
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
abstract class AbstractCommunicationPreferenceIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var communicationPreferenceRepository: CommunicationPreferenceRepository

    @Autowired
    private lateinit var distributionCentersRepository: DistributionCentersRepository

    @Autowired
    private lateinit var supplierRepository: SupplierRepository

    fun savePreference(
        preference: CommunicationPreferenceEnum = EMAIL,
        market: String? = null,
        distributionCenter: DistributionCenter? = null,
        supplier: SupplierExtended? = null,
    ): CommunicationPreference? {
        distributionCenter?.let { distributionCentersRepository.save(it) }
        supplier?.let { supplierRepository.save(it) }
        return communicationPreferenceRepository.save(
            CommunicationPreference(
                id = UUID.randomUUID(),
                preference = preference,
                market = market,
                dcCode = distributionCenter?.code,
                supplierId = supplier?.id,
            ),
        )
    }

    fun saveDistributionCenter(code: String): DistributionCenter = distributionCentersRepository.save(
        getDistributionCenterEntity(code = code),
    )

    fun saveSupplier(id: UUID): SupplierExtended = supplierRepository.save(
        getSupplierEntity(supplierId = id),
    )
}
