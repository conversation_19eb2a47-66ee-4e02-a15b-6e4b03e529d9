package com.hellofresh.oms.orderManagementHttp.emergencyReason

import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.orderManagementHttp.emergencyReason.Fixture.MARKET_US
import java.util.UUID.randomUUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class EmergencyReasonServiceTest {
    @Mock
    private lateinit var emergencyReasonRepository: EmergencyReasonRepository

    @InjectMocks
    private lateinit var emergencyReasonService: EmergencyReasonService

    @Test
    fun `should get correct emergency reasons when given market`() {
        // given
        val emergencyReasons = listOf(
            EmergencyReason(
                market = MARKET_US,
                name = "Repackaging",
                uuid = randomUUID(),
                disabled = false,
            ),
            EmergencyReason(
                market = MARKET_US,
                name = "Thanksgiving",
                uuid = randomUUID(),
                disabled = false,
            ),
        )
        whenever(
            emergencyReasonRepository.findAllByMarketIgnoreCaseAndDisabledIsFalse(MARKET_US)
        ).thenReturn(emergencyReasons)

        // when
        val actualEmergencyReasons = emergencyReasonService.findEnabledEmergencyReasonByMarket(MARKET_US)

        // then
        verify(emergencyReasonRepository).findAllByMarketIgnoreCaseAndDisabledIsFalse(MARKET_US)
        assertEquals(emergencyReasons.size, actualEmergencyReasons.size)
        for (i in emergencyReasons.indices) {
            assertEquals(emergencyReasons[i].uuid, actualEmergencyReasons[i].uuid)
            assertEquals(emergencyReasons[i].name, actualEmergencyReasons[i].name)
            assertEquals(emergencyReasons[i].market, actualEmergencyReasons[i].market)
        }
    }

    @Test
    fun `should return empty list when there is no emergency reason for given market`() {
        // given
        whenever(
            emergencyReasonRepository.findAllByMarketIgnoreCaseAndDisabledIsFalse(MARKET_US)
        ).thenReturn(emptyList())

        // when
        val actualEmergencyReasons = emergencyReasonService.findEnabledEmergencyReasonByMarket(MARKET_US)

        // then
        verify(emergencyReasonRepository).findAllByMarketIgnoreCaseAndDisabledIsFalse(MARKET_US)
        assertEquals(0, actualEmergencyReasons.size)
    }
}
