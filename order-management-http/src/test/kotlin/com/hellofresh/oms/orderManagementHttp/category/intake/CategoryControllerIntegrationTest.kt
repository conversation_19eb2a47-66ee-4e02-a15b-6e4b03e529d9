package com.hellofresh.oms.orderManagementHttp.category.intake

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(
    executionPhase = BEFORE_TEST_METHOD,
    scripts = [
        "/data/sku.sql",
    ],
)
class CategoryControllerIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Test
    fun `should return correct categories while filtering by markets`() {
        mockMvc.get("/categories?market=us")
            .andExpect {
                status { isOk() }
                content {
                    json(
                        """
                        {
                            "items": [
                                {"code": "OTH"},
                                {"code": "PRO"},
                                {"code": "BAK"},
                                {"code": "PTN"},
                                {"code": "VEG"}
                            ]
                        }
                        """.trimIndent(),
                    )
                }
            }
    }
}
