package com.hellofresh.oms.orderManagementHttp.workerAction

import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.PurchaseOrderStatus.DELETED
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData.SendPurchaseOrder
import com.hellofresh.oms.model.WorkerActionStatus.PENDING
import com.hellofresh.oms.orderManagement.generated.api.model.SendBulkOrdersRequest
import com.hellofresh.oms.orderManagementHttp.order.getOutboxItem
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getTestUser
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.outbox.service.OutboxService
import java.time.Clock
import java.time.Instant
import java.time.ZoneId
import java.util.UUID
import kotlin.test.assertEquals
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class SendOrderSchedulerTest {

    @Mock
    private lateinit var purchaseOrderRepository: PurchaseOrderRepository

    @Mock
    private lateinit var workerActionRepository: WorkerActionRepository

    @Mock
    private lateinit var outboxService: OutboxService

    @Mock
    @Suppress("UnusedPrivateProperty")
    private lateinit var purchaseOrderProducer: PurchaseOrderProducer

    @Mock
    private lateinit var clock: Clock

    @InjectMocks
    private lateinit var subject: SendOrderScheduler

    @Test
    fun `should save one WorkerAction and one OutboxItem for each poNumber`() {
        // given
        val givenPurchaseOrders = listOf("2241FF020162", "2204NJ020170").map { poNumber ->
            val purchaseOrder = getPurchaseOrderEntity(poNumber = poNumber, isSynced = true)
            whenever(purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(poNumber))
                .thenReturn(purchaseOrder)
            purchaseOrder
        }

        val captor = argumentCaptor<List<WorkerAction>>()
        whenever(workerActionRepository.saveAll(captor.capture()))
            .thenReturn(emptyList())

        val expectedOutboxItem = getOutboxItem()
        whenever(outboxService.persistOutboxItemFor(any(), any(), any()))
            .thenReturn(expectedOutboxItem)

        whenever(purchaseOrderRepository.save(any())).thenReturn(givenPurchaseOrders.first())
        whenever(clock.instant()).thenReturn(Instant.parse("2024-12-11T15:00:00Z"))
        whenever(clock.zone).thenReturn(ZoneId.of("UTC"))

        // when
        subject.queueBulkOrders(SendBulkOrdersRequest(givenPurchaseOrders.map { it.poNumber }), ANY_TEST_USER)

        // then
        verify(workerActionRepository).saveAll<WorkerAction>(any())
        assertEquals(listOf(PENDING, PENDING), captor.firstValue.map { it.status })

        givenPurchaseOrders.forEach { po ->
            verify(outboxService).persistOutboxItemFor(eq(po), eq(ANY_TEST_USER), eq(OutboxItemStatus.PENDING))
        }
        val expectedWorkerActions = givenPurchaseOrders
            .map { po ->
                SendPurchaseOrder(po.poNumber, ANY_BULK_ID, expectedOutboxItem.id)
            }.toTypedArray()
        assertThat(captor.firstValue.map { it.getPayload() })
            // since we are using UUID.randomUUID(), we need to ignore the bulkId field
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("bulkId")
            .containsExactlyInAnyOrder(
                *expectedWorkerActions,
            )
    }

    @Test
    fun `should not save any WorkerAction nor OutboxItem when at least one poNumber does not exist`() {
        // given
        val invalidPo = "2204NJ020170"
        val givenPurchaseOrderNumbers = listOf("2241FF020162", invalidPo)
        whenever(purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc("2241FF020162"))
            .thenReturn(getPurchaseOrderEntity(poNumber = "2241FF020162", isSynced = true))
        whenever(purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(invalidPo))
            .thenReturn(null)

        // when
        val exception = assertThrows<IllegalArgumentException> {
            subject.queueBulkOrders(SendBulkOrdersRequest(givenPurchaseOrderNumbers), ANY_TEST_USER)
        }
        assertThat(exception.message).isEqualTo("""Purchase order not found: $invalidPo""")

        // then
        verifyNoInteractions(workerActionRepository)
        verifyNoInteractions(outboxService)
    }

    @Test
    fun `should not save any WorkerAction nor OutboxItem when at leas one purchase order is DELETED`() {
        // given
        val deletedPo = "2204NJ020170"
        val givenPurchaseOrderNumbers = listOf("2241FF020162", deletedPo)
        whenever(purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc("2241FF020162"))
            .thenReturn(getPurchaseOrderEntity(poNumber = "2241FF020162", isSynced = true))
        whenever(purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(deletedPo))
            .thenReturn(getPurchaseOrderEntity(poNumber = deletedPo, status = DELETED, isSynced = true))

        // when
        val exception = assertThrows<IllegalArgumentException> {
            subject.queueBulkOrders(SendBulkOrdersRequest(givenPurchaseOrderNumbers), ANY_TEST_USER)
        }
        assertThat(exception.message).isEqualTo("""Purchase order is deleted: $deletedPo""")

        // then
        verifyNoInteractions(workerActionRepository)
        verifyNoInteractions(outboxService)
    }

    @Test
    fun `should not save any WorkerAction nor OutboxItem when at leas one purchase order is not synced`() {
        // given
        val notSyncedPo = "2204NJ020170"
        val givenPurchaseOrderNumbers = listOf("2241FF020162", notSyncedPo)
        whenever(purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc("2241FF020162"))
            .thenReturn(getPurchaseOrderEntity(poNumber = "2241FF020162", isSynced = true))
        whenever(purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(notSyncedPo))
            .thenReturn(getPurchaseOrderEntity(poNumber = notSyncedPo, isSynced = false))

        // when
        val exception = assertThrows<IllegalArgumentException> {
            subject.queueBulkOrders(SendBulkOrdersRequest(givenPurchaseOrderNumbers), ANY_TEST_USER)
        }
        assertThat(exception.message).isEqualTo("""Purchase order is not synced with OT: $notSyncedPo""")

        // then
        verifyNoInteractions(workerActionRepository)
        verifyNoInteractions(outboxService)
    }

    companion object {
        private val ANY_BULK_ID = UUID.randomUUID()
        private val ANY_TEST_USER = getTestUser()
    }
}
