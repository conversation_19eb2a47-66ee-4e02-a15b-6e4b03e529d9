package com.hellofresh.oms.orderManagementHttp.configuration

import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post

@SpringBootTest(properties = ["security.allowed-issuers=test-client"])
@AutoConfigureMockMvc
@Tag("integration")
class IssuerValidationFilterIntegrationTest : AbstractIntegrationTest() {

    @Autowired
    lateinit var mockMvc: MockMvc

    @Test
    @WithJWTUser(iss = "not-allowed-issuer")
    fun `should return 403 when issuer is not allowed`() {
        // when
        mockMvc.post("/service/orders") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            // then
            status { isForbidden() }
        }
    }

    @Test
    @WithJWTUser(iss = "test-client")
    fun `should not return 403 for any issuer validated path when issuer is allowed`() {
        // when
        mockMvc.get("/service/test-path-that-does-not-exist") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            // then
            status { isNotFound() }
        }
    }

    @Test
    @WithJWTUser(iss = "not-allowed-issuer")
    fun `should not validate issuer when the IssuerValidationFilter is not applied to the path`() {
        // when
        mockMvc.get("/api.yaml") {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            // then
            status { isOk() }
        }
    }
}
