package com.hellofresh.oms.orderManagementHttp.orderOverview

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewModel
import java.math.BigDecimal
import java.util.UUID

object TransferOrderFixture {

    fun getTransferOrderStatusDetails(
        transferOrderId: UUID = UUID.randomUUID(),
        transferOrderNumber: String = "TO-2024-001",
        skuCode: String = "SKU-001",
        status: String = "STATE_OPEN",
        destinationDcCode: String = "DC001",
        sourceDcCode: String = "DC002",
        skuName: String = "Test Product",
        category: String = "Fresh",
        skuUom: String? = "kg",
        week: YearWeek = YearWeek("2024-W01"),
        quantityReceived: BigDecimal? = BigDecimal("10.0"),
        casesReceived: Int? = 5,
        caseSize: BigDecimal? = BigDecimal("2.0"),
        packagingType: String = "case",
        priceAmount: String = "EUR 25.00",
        totalQuantity: BigDecimal = BigDecimal("10.0"),
        reasonText: String? = "Stock transfer",
        shippingMethod: String? = "Truck",
        assignedBuyerFirstName: String? = "John",
        assignedBuyerLastName: String? = "Doe"
    ): TransferOrderStatusDetails {
        return TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = transferOrderNumber,
            skuCode = skuCode,
            status = status,
            destinationDcCode = destinationDcCode,
            sourceDcCode = sourceDcCode,
            skuName = skuName,
            category = category,
            skuUom = skuUom,
            week = week,
            quantityReceived = quantityReceived,
            casesReceived = casesReceived,
            caseSize = caseSize,
            packagingType = packagingType,
            priceAmount = priceAmount,
            totalQuantity = totalQuantity,
            reasonText = reasonText,
            shippingMethod = shippingMethod,
            assignedBuyerFirstName = assignedBuyerFirstName,
            assignedBuyerLastName = assignedBuyerLastName
        )
    }

    fun getTransferOrderBreakdownItem(
        transferOrderId: UUID = UUID.randomUUID(),
        transferOrderNumber: String = "TO-2024-001",
        status: String = "STATE_OPEN",
        destinationDcCode: String = "DC001",
        week: YearWeek = YearWeek("2024-W01"),
        quantityReceived: BigDecimal? = BigDecimal("10.0"),
        casesReceived: Int? = 5,
        caseSize: BigDecimal? = BigDecimal("2.0"),
        totalQuantity: BigDecimal = BigDecimal("10.0"),
        casePrice: String = "EUR 25.00",
        reasonText: String? = "Stock transfer",
        shippingMethod: String? = "Truck"
    ): TransferOrderBreakdownItem {
        return TransferOrderBreakdownItem(
            transferOrderId = transferOrderId,
            transferOrderNumber = transferOrderNumber,
            status = status,
            destinationDcCode = destinationDcCode,
            week = week,
            quantityReceived = quantityReceived,
            casesReceived = casesReceived,
            caseSize = caseSize,
            totalQuantity = totalQuantity,
            casePrice = casePrice,
            reasonText = reasonText,
            shippingMethod = shippingMethod
        )
    }

    fun getTransferOrderOverviewModel(
        skuCode: String = "SKU-001",
        sourceDcCode: String = "DC002",
        skuName: String = "Test Product",
        category: String = "Fresh",
        skuUom: String? = "kg",
        packagingType: String = "case",
        totalOrderedQuantity: BigDecimal = BigDecimal("10.0"),
        totalReceivedQuantity: BigDecimal? = BigDecimal("10.0"),
        totalCasesReceived: Int? = 5,
        totalPriceOrdered: String = "EUR 25.00",
        totalPriceReceived: String? = "EUR 25.00",
        weightedAvgCasePrice: String? = "EUR 25.00",
        weightedAvgCaseSize: BigDecimal? = BigDecimal("2.0"),
        weightedAvgCaseSizeReceived: BigDecimal? = BigDecimal("2.0"),
        assignedBuyerFirstName: String? = "John",
        assignedBuyerLastName: String? = "Doe",
        transferOrderBreakdown: List<TransferOrderBreakdownItem> = listOf(getTransferOrderBreakdownItem())
    ): TransferOrderOverviewModel {
        return TransferOrderOverviewModel(
            skuCode = skuCode,
            sourceDcCode = sourceDcCode,
            skuName = skuName,
            category = category,
            skuUom = skuUom,
            packagingType = packagingType,
            totalOrderedQuantity = totalOrderedQuantity,
            totalReceivedQuantity = totalReceivedQuantity,
            totalCasesReceived = totalCasesReceived,
            totalPriceOrdered = totalPriceOrdered,
            totalPriceReceived = totalPriceReceived,
            weightedAvgCasePrice = weightedAvgCasePrice,
            weightedAvgCaseSize = weightedAvgCaseSize,
            weightedAvgCaseSizeReceived = weightedAvgCaseSizeReceived,
            assignedBuyerFirstName = assignedBuyerFirstName,
            assignedBuyerLastName = assignedBuyerLastName,
            transferOrderBreakdown = transferOrderBreakdown
        )
    }

    fun getMultipleTransferOrderStatusDetails(): List<TransferOrderStatusDetails> {
        return listOf(
            getTransferOrderStatusDetails(
                transferOrderId = UUID.randomUUID(),
                transferOrderNumber = "TO-2024-001",
                skuCode = "SKU-001",
                status = "STATE_OPEN",
                destinationDcCode = "DC001",
                sourceDcCode = "DC002",
                totalQuantity = BigDecimal("10.0"),
                quantityReceived = BigDecimal("10.0"),
                casesReceived = 5,
                caseSize = BigDecimal("2.0"),
                priceAmount = "EUR 25.00"
            ),
            getTransferOrderStatusDetails(
                transferOrderId = UUID.randomUUID(),
                transferOrderNumber = "TO-2024-002",
                skuCode = "SKU-001",
                status = "STATE_DELIVERED",
                destinationDcCode = "DC003",
                sourceDcCode = "DC002",
                totalQuantity = BigDecimal("15.0"),
                quantityReceived = BigDecimal("15.0"),
                casesReceived = 7,
                caseSize = BigDecimal("2.0"),
                priceAmount = "EUR 37.50"
            )
        )
    }

    fun getTransferOrderStatusDetailsWithNulls(): TransferOrderStatusDetails {
        return getTransferOrderStatusDetails(
            transferOrderNumber = "TO-2024-NULL",
            skuCode = "SKU-NULL",
            status = "STATE_OPEN",
            skuUom = null,
            quantityReceived = null,
            casesReceived = null,
            caseSize = null,
            packagingType = "unit",
            priceAmount = "EUR 0.00",
            totalQuantity = BigDecimal("0.0"),
            reasonText = null,
            shippingMethod = null,
            assignedBuyerFirstName = null,
            assignedBuyerLastName = null
        )
    }

    fun getUnitPackagingTransferOrderStatusDetails(): TransferOrderStatusDetails {
        return getTransferOrderStatusDetails(
            transferOrderNumber = "TO-2024-UNIT",
            skuCode = "SKU-UNIT",
            status = "STATE_DELIVERED",
            skuUom = "piece",
            quantityReceived = BigDecimal("5.0"),
            casesReceived = null,
            caseSize = null,
            packagingType = "unit",
            priceAmount = "EUR 12.50",
            totalQuantity = BigDecimal("5.0"),
            category = "Packaged"
        )
    }

    fun getCasePackagingTransferOrderStatusDetails(): TransferOrderStatusDetails {
        return getTransferOrderStatusDetails(
            transferOrderNumber = "TO-2024-CASE",
            skuCode = "SKU-CASE",
            status = "STATE_IN_TRANSIT",
            skuUom = "kg",
            quantityReceived = BigDecimal("20.0"),
            casesReceived = 10,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 50.00",
            totalQuantity = BigDecimal("20.0"),
            category = "Fresh"
        )
    }

    fun getDifferentSourceDcTransferOrderStatusDetails(): List<TransferOrderStatusDetails> {
        return listOf(
            getTransferOrderStatusDetails(
                skuCode = "SKU-001",
                sourceDcCode = "DC001",
                transferOrderNumber = "TO-2024-DC001"
            ),
            getTransferOrderStatusDetails(
                skuCode = "SKU-001",
                sourceDcCode = "DC002",
                transferOrderNumber = "TO-2024-DC002"
            ),
            getTransferOrderStatusDetails(
                skuCode = "SKU-002",
                sourceDcCode = "DC001",
                transferOrderNumber = "TO-2024-SKU002"
            )
        )
    }
}
