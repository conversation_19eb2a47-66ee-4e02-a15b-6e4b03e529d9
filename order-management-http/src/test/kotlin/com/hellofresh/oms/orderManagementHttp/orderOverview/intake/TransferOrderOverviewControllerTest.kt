package com.hellofresh.oms.orderManagementHttp.orderOverview.intake

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.imports.service.ImportHistoryService
import com.hellofresh.oms.orderManagementHttp.orderOverview.intake.transferOrder.TransferOrderOverviewController
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.model.transferOrder.TransferOrderOverviewModel
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderBreakdownItem
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewService
import com.hellofresh.oms.orderManagementHttp.sku.SkuService
import java.math.BigDecimal
import java.util.UUID
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(TransferOrderOverviewController::class)
@Suppress("LongMethod", "UnusedPrivateProperty")
class TransferOrderOverviewControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockitoBean
    private lateinit var transferOrderOverviewServiceMock: TransferOrderOverviewService

    @MockitoBean
    private lateinit var importHistoryService: ImportHistoryService

    @MockitoBean
    private lateinit var skuService: SkuService

    @Test
    @WithJWTUser
    fun `should return transfer order overview when valid parameters provided`() {
        val dcWeek = listOf("2024-W01")
        val brand = "hellofresh"
        val dcCode = "DC001"

        val transferOrderId = UUID.randomUUID()
        val breakdownItem = TransferOrderBreakdownItem(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC002",
            week = YearWeek("2024-W01"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            totalQuantity = BigDecimal("10.0"),
            casePrice = "EUR 25.00",
            reasonText = "Stock transfer",
            shippingMethod = "Truck"
        )

        val overviewModel = TransferOrderOverviewModel(
            skuCode = "SKU-001",
            sourceDcCode = "DC001",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            packagingType = "case",
            totalOrderedQuantity = BigDecimal("10.0"),
            totalReceivedQuantity = BigDecimal("10.0"),
            totalCasesReceived = 5,
            totalPriceOrdered = "EUR 25.00",
            totalPriceReceived = "EUR 25.00",
            weightedAvgCasePrice = "EUR 25.00",
            weightedAvgCaseSize = BigDecimal("2.0"),
            weightedAvgCaseSizeReceived = BigDecimal("2.0"),
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe",
            transferOrderBreakdown = listOf(breakdownItem)
        )

        whenever(
            transferOrderOverviewServiceMock.getOverview(
                dcCode = dcCode,
                brand = brand,
                dcWeeks = dcWeek,
            ),
        ).thenReturn(listOf(overviewModel))

        mockMvc.perform(
            get(TO_OVERVIEW_PATH)
                .param("dc_weeks", dcWeek.joinToString(","))
                .param("brand", brand)
                .param("dc_code", dcCode),
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$[0].sku_code").value("SKU-001"))
            .andExpect(jsonPath("$[0].source_dc_code").value("DC001"))
            .andExpect(jsonPath("$[0].sku_name").value("Test Product"))
            .andExpect(jsonPath("$[0].category").value("Fresh"))
            .andExpect(jsonPath("$[0].packaging_type").value("case"))
            .andExpect(jsonPath("$[0].total_ordered_quantity").value(10.0))
            .andExpect(jsonPath("$[0].transfer_order_breakdown").isArray)
            .andExpect(jsonPath("$[0].transfer_order_breakdown[0].transfer_order_id").value(transferOrderId.toString()))
            .andExpect(jsonPath("$[0].transfer_order_breakdown[0].transfer_order_number").value("TO-2024-001"))
    }

    @Test
    @WithJWTUser
    fun `should return bad request when dc_weeks parameter is missing`() {
        val brand = "hellofresh"

        mockMvc.perform(
            get(TO_OVERVIEW_PATH)
                .param("brand", brand),
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    @WithJWTUser
    fun `should return bad request when brand parameter is missing`() {
        val dcWeek = listOf("2024-W01")

        mockMvc.perform(
            get(TO_OVERVIEW_PATH)
                .param("dc_weeks", dcWeek.joinToString(",")),
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `should return Unauthorized when request does not contain JWT token`() {
        val dcWeek = listOf("2024-W01")
        val brand = "hellofresh"
        val dcCode = "DC001"

        mockMvc.perform(
            get(TO_OVERVIEW_PATH)
                .param("dc_weeks", dcWeek.joinToString(","))
                .param("brand", brand)
                .param("dc_code", dcCode),
        )
            .andExpect(status().isUnauthorized)
    }

    companion object {
        private const val TO_OVERVIEW_PATH = "/transfer-orders/overview"
    }
}
