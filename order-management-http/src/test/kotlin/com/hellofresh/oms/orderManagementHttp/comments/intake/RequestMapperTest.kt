package com.hellofresh.oms.orderManagementHttp.comments.intake

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.comments.service.CommentService
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.BrandDcConfig
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class RequestMapperTest {

    private val mapper = RequestMapper(
        poOverviewConfigs,
    )

    @Test
    fun `should map DCs from brands when DCs are not provided`() {
        // given
        val brands = listOf("HF", "GC")
        RequestMapper(poOverviewConfigs)

        // when
        val result = mapper.toGetCommentsFilter(
            dcs = null,
            resourceTypes = listOf("sku"),
            domains = listOf("test-domain"),
            weeks = listOf(YearWeek("2025-W10")),
            brands = brands,
        )

        // then
        assertEquals(
            CommentService.GetCommentsFilter(
                domains = listOf("test-domain"),
                resourceTypes = listOf("sku"),
                weeks = listOf(YearWeek("2025-W10")),
                dcs = listOf("AZ", "KY", "NJ", "CO", "EO", "SW"),
                brands = brands,
            ),
            result,
        )
    }

    @Test
    fun `should use provided DCs when DCs are not null`() {
        // given
        val dcs = listOf("NJ", "BD")
        val brands = listOf("HF", "GC")

        // when
        val result = mapper.toGetCommentsFilter(
            dcs = dcs,
            resourceTypes = listOf("sku"),
            domains = listOf("test-domain"),
            weeks = listOf(YearWeek("2025-W10")),
            brands = brands,
        )

        // then
        assertEquals(
            CommentService.GetCommentsFilter(
                domains = listOf("test-domain"),
                resourceTypes = listOf("sku"),
                weeks = listOf(YearWeek("2025-W10")),
                dcs = dcs,
                brands = brands,
            ),
            result,
        )
    }

    @Test
    fun `should return empty lists when brands is null`() {
        // when
        val result = mapper.toGetCommentsFilter(
            dcs = null,
            resourceTypes = listOf("sku"),
            domains = listOf("test-domain"),
            weeks = listOf(YearWeek("2025-W10")),
            brands = null,
        )

        // then
        assertEquals(
            CommentService.GetCommentsFilter(
                domains = listOf("test-domain"),
                resourceTypes = listOf("sku"),
                weeks = listOf(YearWeek("2025-W10")),
                dcs = emptyList(),
                brands = emptyList(),
            ),
            result,
        )
    }

    companion object {
        private val poOverviewConfigs = PoOverviewConfigs(
            brandDcConfig = mapOf(
                "HF" to BrandDcConfig(name = "HelloFresh", dcs = listOf("AZ", "KY", "NJ")),
                "GC" to BrandDcConfig(name = "Green Chef", dcs = listOf("CO", "EO", "SW")),
                "WH" to BrandDcConfig(name = "Warehouse", dcs = listOf("LZ", "HN", "LX")),
            ),
        )
    }
}
