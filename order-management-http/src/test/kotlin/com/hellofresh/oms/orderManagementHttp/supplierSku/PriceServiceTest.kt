package com.hellofresh.oms.orderManagementHttp.supplierSku

import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.SupplierSkuPrice
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.PriceRepository
import com.hellofresh.oms.orderManagementHttp.supplierSku.service.PriceService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime.now
import java.util.UUID.randomUUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

class PriceServiceTest {
    private val priceRepositoryMock: PriceRepository = Mockito.mock(PriceRepository::class.java)
    private val subject: PriceService = PriceService(priceRepositoryMock)

    @Test
    fun `should return empty list when no prices found`() {
        // given
        whenever(priceRepositoryMock.findAllByDcCodesAndSupplierSkuIdIn(MARKET, listOf(randomUUID())))
            .thenReturn(emptyList())

        // when
        val result = subject.getActivePrices(DC, LocalDate.now(), listOf())

        // then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `should return correct price when delivery date not in active range`() {
        // given
        val supplierSkuId = SUPPLIER_SKU_ID
        val deliveryDate = LocalDate.now()
        val price = priceEntity.copy(
            supplierSkuId = supplierSkuId,
            startDate = now().plusDays(1),
            endDate = now().plusDays(2)
        )
        whenever(priceRepositoryMock.findAllByDcCodesAndSupplierSkuIdIn(any(), any()))
            .thenReturn(listOf(price))

        // when
        val result = subject.getActivePrices(
            dcCode = DC,
            deliveryDate = deliveryDate,
            supplierSkuIds = listOf(supplierSkuId)
        )

        // then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `should return correct price when there is more then one active for given range`() {
        // given
        val supplierSkuId = SUPPLIER_SKU_ID
        val deliveryDate = LocalDate.now()
        val expectedEvaluatedPrice = 20

        val price1 = priceEntity.copy(
            pricePermyriad = Permyriad(10),
            supplierSkuId = supplierSkuId,
            updatedAt = now().plusHours(1)
        )
        val price2 = priceEntity.copy(
            pricePermyriad = Permyriad(expectedEvaluatedPrice),
            supplierSkuId = supplierSkuId,
            updatedAt = now().plusHours(1).plusSeconds(2)
        )
        val price3 = priceEntity.copy(
            pricePermyriad = Permyriad(30),
            supplierSkuId = supplierSkuId,
            updatedAt = now().plusHours(1).plusSeconds(1)
        )

        whenever(priceRepositoryMock.findAllByDcCodesAndSupplierSkuIdIn(any(), any()))
            .thenReturn(listOf(price1, price2, price3))

        // when
        val result = subject.getActivePrices(
            dcCode = DC,
            deliveryDate = deliveryDate,
            supplierSkuIds = listOf(supplierSkuId)
        )

        // then
        assertEquals(1, result.size)
        assertEquals(expectedEvaluatedPrice, result[SUPPLIER_SKU_ID]!!.pricePermyriad.value)
    }

    companion object {
        const val DC = "NJ"
        const val MARKET = "us"
        val SUPPLIER_SKU_ID = randomUUID()
        val priceEntity = SupplierSkuPrice(
            uuid = randomUUID(),
            supplierSkuId = SUPPLIER_SKU_ID,
            dcCodes = listOf("NJ"),
            startDate = now(),
            endDate = now().plusDays(5),
            enabled = true,
            priceType = "case_type",
            market = "us",
            currency = "EUR",
            pricePermyriad = Permyriad(1),
            bufferPermyriad = Permyriad(1),
            caseSize = 10,
            unitsPerCase = BigDecimal(10),
            casePrice = Permyriad(10),
            createdAt = now(),
            updatedAt = now().minusHours(1)
        )
    }
}
