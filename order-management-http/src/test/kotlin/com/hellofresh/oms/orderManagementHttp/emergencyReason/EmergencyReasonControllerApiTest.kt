package com.hellofresh.oms.orderManagementHttp.emergencyReason

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.emergencyReason.Fixture.EMERGENCY_REASON_PATH
import com.hellofresh.oms.orderManagementHttp.emergencyReason.Fixture.MARKET
import com.hellofresh.oms.orderManagementHttp.emergencyReason.Fixture.MARKET_DE
import com.hellofresh.oms.orderManagementHttp.emergencyReason.Fixture.MARKET_GB
import com.hellofresh.oms.orderManagementHttp.emergencyReason.Fixture.MARKET_US
import com.hellofresh.oms.orderManagementHttp.emergencyReason.Fixture.MARKET_US_UPPERCASE
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.hamcrest.CoreMatchers.equalTo
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(scripts = ["/data/emergencyReasons.sql"])
class EmergencyReasonControllerApiTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Test
    fun `should get only enabled reasons when there is a disabled reason for market`() {
        mockMvc.get(EMERGENCY_REASON_PATH) {
            param(MARKET, MARKET_US_UPPERCASE)
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(2))
            jsonPath("$.items[0].id", equalTo("511fd932-2da7-4070-b564-fc5588760b24"))
            jsonPath("$.items[0].name", equalTo("Ordering Error"))
            jsonPath("$.items[0].market", equalTo(MARKET_US))
            jsonPath("$.items[1].id", equalTo("37c2e641-ac77-4fec-8b17-83091d4a9b60"))
            jsonPath("$.items[1].name", equalTo("Repackaging"))
            jsonPath("$.items[1].market", equalTo(MARKET_US))
        }
    }

    @Test
    fun `should return empty list when there is no emergency reason for given market`() {
        mockMvc.get(EMERGENCY_REASON_PATH) {
            param(MARKET, MARKET_DE)
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(0))
        }
    }

    @Test
    fun `should return emergency reason for correct market when there are emergency reasons for multiple markets`() {
        mockMvc.get(EMERGENCY_REASON_PATH) {
            param(MARKET, MARKET_GB)
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(1))
            jsonPath("$.items[0].id", equalTo("c7d8e9f0-0a0b-0c1d-2d2e-3e3e4e4e5e5e"))
            jsonPath("$.items[0].name", equalTo("Advance Weekly order"))
            jsonPath("$.items[0].market", equalTo(MARKET_GB))
        }
    }

    @Test
    fun `should return 400 when market is not provided`() {
        mockMvc.get(EMERGENCY_REASON_PATH) {
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isBadRequest() }
        }
    }
}
