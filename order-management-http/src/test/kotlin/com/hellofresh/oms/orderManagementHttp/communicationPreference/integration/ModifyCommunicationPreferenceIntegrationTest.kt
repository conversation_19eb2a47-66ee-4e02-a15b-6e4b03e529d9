package com.hellofresh.oms.orderManagementHttp.communicationPreference.integration

import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.E2OPEN
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.EMAIL
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.EMAIL_AND_E2OPEN
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceEnum as CommunicationPreferenceEnumApi
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceEnum.E2_OPEN
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceEnum.EMAIL_AND_E2_OPEN
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum as CommunicationPreferenceTypeEnumApi
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.DC
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.MARKET
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum.SUPPLIER
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import java.util.UUID
import kotlin.random.Random
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.patch
import uk.org.webcompere.modelassert.json.JsonAssertions.json

class ModifyCommunicationPreferenceIntegrationTest : AbstractCommunicationPreferenceIntegrationTest() {
    @ParameterizedTest
    @EnumSource(UpdateByScopeTestCase::class)
    @Sql(scripts = ["/data/communicationPreferences.sql"])
    fun `should update preference when scopeType and scopeValue are given in lowercase`(
        testCase: UpdateByScopeTestCase,
    ) {
        mockMvc.patch(
            "/communication-preferences/{scopeType}/{scopeValue}",
            testCase.givenScopeType.lowercase(),
            testCase.givenScopeValue.lowercase(),
        ) {
            content = """ { "preference": "${testCase.givenPreference}" } """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.preference") { value(testCase.expectedPreference.toString()) }
        }
    }

    @ParameterizedTest
    @EnumSource(UpdateByScopeTestCase::class)
    @Sql(scripts = ["/data/communicationPreferences.sql"])
    fun `should update preference when scopeType and scopeValue are given in uppercase`(
        testCase: UpdateByScopeTestCase,
    ) {
        mockMvc.patch(
            "/communication-preferences/{scopeType}/{scopeValue}",
            testCase.givenScopeType.uppercase(),
            testCase.givenScopeValue.uppercase(),
        ) {
            content = """ { "preference": "${testCase.givenPreference}" } """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.preference") { value(testCase.expectedPreference.toString()) }
        }
    }

    @Test
    fun `should throw bad request when scopeType is not valid`() {
        mockMvc.patch(
            "/communication-preferences/{scopeType}/{scopeValue}",
            "INVALID",
            "us",
        ) {
            content = """ { "preference": "EMAIL_AND_E2OPEN" } """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isBadRequest() }
        }
    }

    @Test
    fun `should throw not found when scopeValue is not found`() {
        mockMvc.patch(
            "/communication-preferences/{scopeType}/{scopeValue}",
            CommunicationPreferenceTypeEnumApi.MARKET.value,
            "XX",
        ) {
            content = """ { "preference": "EMAIL_AND_E2OPEN" } """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNotFound() }
        }
    }

    @Test
    fun `should throw bad request when scopeValue is not valid`() {
        mockMvc.patch(
            "/communication-preferences/{scopeType}/{scopeValue}",
            CommunicationPreferenceTypeEnumApi.SUPPLIER.value,
            "definitely-a-valid-uuid",
        ) {
            content = """ { "preference": "EMAIL_AND_E2OPEN" } """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isBadRequest() }
        }
    }

    @Test
    fun `should throw bad request when preference is not valid`() {
        mockMvc.patch(
            "/communication-preferences/{scopeType}/{scopeValue}",
            CommunicationPreferenceTypeEnumApi.MARKET.value,
            "us",
        ) {
            content = """ { "preference": "INVALID" } """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isBadRequest() }
        }
    }

    @Test
    fun `should delete communication preferences when scopeType and scopeValue are given`() {
        // given
        val givenSupplierId = UUID.randomUUID()
        savePreference(
            preference = E2OPEN,
            supplier = getSupplierEntity(
                supplierId = givenSupplierId,
                code = Random.nextInt(),
                name = "Any supplier name",
                market = "any",
            ),
        )

        // when
        mockMvc.delete(
            "/communication-preferences/{scopeType}/{scopeValue}",
            SUPPLIER.value,
            givenSupplierId,
        ).andExpect {
            status { isNoContent() }
        }

        // then
        mockMvc.get("/communication-preferences") {
            param("type", SUPPLIER.value)
        }.andExpect {
            status { isOk() }
            content {
                string(
                    json()
                        .at("/preferences")
                        .isEmpty(),
                )
            }
        }
    }

    @Test
    fun `should return no content when trying to delete non existing communication preferences`() {
        mockMvc.delete(
            "/communication-preferences/{scopeType}/{scopeValue}",
            CommunicationPreferenceTypeEnumApi.MARKET.value,
            "XX",
        ).andExpect {
            status { isNotFound() }
        }
    }

    enum class UpdateByScopeTestCase(
        val givenScopeType: String,
        val givenScopeValue: String,
        val givenPreference: String,
        val expectedPreference: CommunicationPreferenceEnum,
    ) {
        MARKET_TO_EMAIL(
            givenScopeType = MARKET.value,
            givenScopeValue = "us",
            givenPreference = CommunicationPreferenceEnumApi.EMAIL.value,
            expectedPreference = EMAIL,
        ),
        DC_TO_EMAIL_AND_E2OPEN(
            givenScopeType = DC.value,
            givenScopeValue = "NJ",
            givenPreference = EMAIL_AND_E2_OPEN.value,
            expectedPreference = EMAIL_AND_E2OPEN,
        ),
        SUPPLIER_TO_E2OPEN(
            givenScopeType = SUPPLIER.value,
            givenScopeValue = "00000000-0000-0000-0000-000000000001",
            givenPreference = E2_OPEN.value,
            expectedPreference = E2OPEN,
        ),
    }
}
