package com.hellofresh.oms.orderManagementHttp.imports.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterAddressType
import com.hellofresh.oms.model.WorkerActionData.SyncBatchOrder
import com.hellofresh.oms.model.WorkerActionStatus
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.oms.model.importHistory.ImportHistorySummary.BatchPoCreation
import com.hellofresh.oms.orderManagement.generated.api.model.BatchCreatePurchaseOrdersResponse
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.imports.out.ImportHistoryRepository
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierSimpleEntity
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierSimpleRepository
import com.hellofresh.oms.orderManagementHttp.workerAction.WorkerActionRepository
import java.time.LocalDate
import java.time.LocalDateTime.now
import java.util.UUID.randomUUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.mock.web.MockPart
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.multipart
import uk.org.webcompere.modelassert.json.JsonAssertions
import uk.org.webcompere.modelassert.json.JsonProviders.overrideObjectMapper

@Suppress("MaxLineLength")
@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
class BatchCreatePurchaseOrdersIntegrationTest : AbstractIntegrationTest() {

    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var distributionCentersRepository: DistributionCentersRepository

    @Autowired
    private lateinit var supplierSimpleRepository: SupplierSimpleRepository

    @Autowired
    private lateinit var emergencyReasonRepository: EmergencyReasonRepository

    @Autowired
    private lateinit var purchaseOrderRepository: PurchaseOrderRepository

    @Autowired
    private lateinit var skuRepository: SkuRepository

    @Autowired
    private lateinit var workerActionRepository: WorkerActionRepository

    @Autowired
    private lateinit var importHistoryRepository: ImportHistoryRepository

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @BeforeEach
    fun setup() {
        overrideObjectMapper(objectMapper)
    }

    @AfterEach
    fun tearDown() {
        distributionCentersRepository.deleteAll()
        supplierSimpleRepository.deleteAll()
        emergencyReasonRepository.deleteAll()
        skuRepository.deleteAll()
        importHistoryRepository.deleteAll()
    }

    @Nested
    inner class DryRun {
        @Test
        @Suppress("LongMethod")
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.us.all.manager")
        fun `should respond with 200 with valid es file`() {
            val givenWeek = "2024-W44"
            val givenSupplierCode = "114318"
            val givenDeliveryDate = LocalDate.now().plusDays(1)
            val givenStartTime = "9:00"
            val givenEndTime = "12:00"
            val givenShipMethod = "Vendor Delivered"
            val givenMarket = "us"
            val givenDc = saveDc("SP", givenMarket)
            val givenSupplier = saveSupplier(givenSupplierCode.toInt(), givenMarket)
            val givenReason = saveReason("Multi-Week", givenMarket)
            val firstSku = saveSku("PCK-00-120387-1", givenMarket)
            val secondSku = saveSku("PCK-00-120389-1", givenMarket)
            val thirdSku = saveSku("PCK-00-120391-1", givenMarket)

            val expectedResponse = """
            {
              "purchase_orders": [
                {
                  "id": null,
                  "po_number": null,
                  "week": "$givenWeek",
                  "distribution_center": {
                    "code": "${givenDc.code}",
                    "name": "${givenDc.name}"
                  },
                  "supplier": {
                    "id": "${givenSupplier.id}",
                    "code": "${givenSupplier.code}",
                    "name": "${givenSupplier.name}"
                  },
                  "shipping_method": "VENDOR",
                  "emergency_reason": {
                    "id": "${givenReason.uuid}",
                    "name": "${givenReason.name}"
                  },
                  "delivery_window": {
                    "start": "${givenDeliveryDate}T09:00:00",
                    "end": "${givenDeliveryDate}T12:00:00"
                  },
                  "comments": "test purchase order",
                  "order_items": [
                    {
                      "sku": {
                        "id": "${firstSku.uuid}",
                        "code": "${firstSku.code}",
                        "name": "${firstSku.name}"
                      },
                      "buffer_percent": 0,
                      "packaging": {
                        "packaging_type": "UNIT",
                        "price_per_unit": {
                          "amount": "0.0890",
                          "currency": "${givenSupplier.currency}"
                        },
                        "number_of_units": 533
                      }
                    },
                    {
                      "sku": {
                        "id": "${secondSku.uuid}",
                        "code": "${secondSku.code}",
                        "name": "${secondSku.name}"
                      },
                      "buffer_percent": 0,
                      "packaging": {
                        "packaging_type": "UNIT",
                        "price_per_unit": {
                          "amount": "0.0890",
                          "currency": "${givenSupplier.currency}"
                        },
                        "number_of_units": 495
                      }
                    },
                    {
                      "sku": {
                        "id": "${thirdSku.uuid}",
                        "code": "${thirdSku.code}",
                        "name": "${thirdSku.name}"
                      },
                      "buffer_percent": 0,
                      "packaging": {
                        "packaging_type": "UNIT",
                        "price_per_unit": {
                          "amount": "0.0890",
                          "currency": "${givenSupplier.currency}"
                        },
                        "number_of_units": 471
                      }
                    }
                  ]
                }
              ]
            }
        """

            val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod,comments
            $givenWeek,${givenDc.code},,${givenReason.name},$givenSupplierCode,$givenDeliveryDate,$givenStartTime,$givenEndTime,PCK-00-120387-1,533,UNIT_TYPE,0,,unit,0.0890,$givenShipMethod,test purchase order
            $givenWeek,${givenDc.code},,${givenReason.name},$givenSupplierCode,$givenDeliveryDate,$givenStartTime,$givenEndTime,PCK-00-120389-1,495,UNIT_TYPE,0,,units,0.0890,$givenShipMethod,
            $givenWeek,${givenDc.code},,${givenReason.name},$givenSupplierCode,$givenDeliveryDate,$givenStartTime,$givenEndTime,PCK-00-120391-1,471,UNIT_TYPE,0,,unit(s),0.0890,$givenShipMethod,
            """.trimIndent().toByteArray()

            postBulkCreateFile(validFileContent)
                .andExpect {
                    status { isOk() }
                    content {
                        string(
                            JsonAssertions.json()
                                .where()
                                .keysInAnyOrder()
                                .isEqualTo(expectedResponse),
                        )
                    }
                }
        }

        @Test
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.fr.all.manager")
        fun `should respond with 200 with valid fr file`() {
            saveDc("LI", "fr")
            saveSupplier("10017".toInt(), "fr")
            saveSupplier("115599".toInt(), "fr")
            saveReason("Internal Forecast Change", "fr")
            saveSku("PTN-13-135445-5", "fr")
            saveSku("PTN-13-135443-5", "fr")
            saveSku("PTN-13-132884-4", "fr")
            saveSku("PTN-13-128945-5", "fr")
            val validDeliveryDate = LocalDate.now().plusDays(1)

            val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
            2024-W44,LI,,Internal Forecast Change,10017,$validDeliveryDate,4:00,9:00,PTN-13-135445-5,65,case_type,0,36,units,86.4072,Vendor Delivered
            2024-W44,LI,,Internal Forecast Change,10017,$validDeliveryDate,4:00,9:00,PTN-13-135443-5,26,case_type,0,50,units,82.06,Vendor Delivered
            2024-W44,LI,,Internal Forecast Change,115599,$validDeliveryDate,4:00,9:00,PTN-13-132884-4,2,case_type,0,100,units,240,Vendor Delivered
            2024-W44,LI,,Internal Forecast Change,115599,$validDeliveryDate,4:00,9:00,PTN-13-128945-5,6,case_type,0,30,units,148.08,Vendor Delivered
            """.trimIndent().toByteArray()

            postBulkCreateFile(validFileContent).andExpect { status { isOk() } }
        }

        @Test
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.se.all.manager")
        fun `should respond with 200 with valid se file`() {
            val givenMarket = "dkse"
            val validDeliveryDate = LocalDate.now().plusDays(1)
            saveDc("MO", givenMarket)
            saveSupplier("111764".toInt(), givenMarket)
            saveSupplier("111652".toInt(), givenMarket)
            saveReason("Raw Ingredients (Bulk)", givenMarket)
            saveSku("PTN-33-001813-2", givenMarket)
            saveSku("PTN-33-10002-2", givenMarket)
            saveSku("PTN-33-10001-2", givenMarket)
            saveSku("PTN-33-10017-2", givenMarket)
            saveSku("PTN-33-10018-2", givenMarket)
            saveSku("PTN-33-133085-4", givenMarket)
            saveSku("PTN-33-133086-4", givenMarket)
            saveSku("PTN-33-10021-2", givenMarket)
            saveSku("PTN-33-121160-2", givenMarket)
            saveSku("PTN-33-121159-2", givenMarket)
            saveSku("PTN-33-10022-2", givenMarket)

            val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
            2024-W45,MO,,Raw Ingredients (Bulk),111764,$validDeliveryDate,06:00,08:00,PTN-33-001813-2,470,CASE_TYPE,0,18,unit,732.42,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111764,$validDeliveryDate,06:00,08:00,PTN-33-10002-2,111,CASE_TYPE,0,18,unit,1485.72,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111764,$validDeliveryDate,06:00,08:00,PTN-33-10001-2,155,CASE_TYPE,0,18,unit,745.56,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-10017-2,80,CASE_TYPE,0,24,unit,770.16,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-10018-2,97,CASE_TYPE,0,18,unit,1128.15,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-133085-4,10,CASE_TYPE,0,20,unit,1489.2,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-133086-4,20,CASE_TYPE,0,10,unit,1434.6,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-10021-2,65,CASE_TYPE,0,24,unit,654.12,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-121160-2,35,CASE_TYPE,0,18,unit,780.3,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-121159-2,38,CASE_TYPE,0,24,unit,528,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-10022-2,85,CASE_TYPE,0,18,unit,964.8,Vendor Delivered
            """.trimIndent().toByteArray()

            postBulkCreateFile(validFileContent).andExpect { status { isOk() } }
        }

        @Test
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.ca.all.manager")
        fun `should respond with 200 with valid ca file`() {
            saveDc("BL", "ca")
            saveSupplier("115057".toInt(), "ca")
            saveReason("Internal Transfer", "ca")
            saveSku("PRO-10-119689-7", "ca")
            val validDeliveryDate = LocalDate.now().plusDays(1)

            val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod,
            2024-W41,BL,,Internal Transfer,115057,$validDeliveryDate,9:00,15:00,PRO-10-119689-7,5,CASE_TYPE,0,12,unit,0,Freight On Board,Fine Choice Foods
            """.trimIndent().toByteArray()

            postBulkCreateFile(validFileContent).andExpect { status { isOk() } }
        }

        @Test
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.it.all.manager")
        fun `should respond with 200 with valid it file`() {
            saveDc("IT", "it")
            saveSupplier("113000".toInt(), "it")
            saveReason("Multi-Week", "it")
            saveSku("PCK-00-005297-1", "it")
            saveSku("PCK-00-100200-1", "it")
            saveSku("PCK-00-100201-1", "it")
            saveSku("PCK-00-100202-1", "it")
            saveSku("PCK-00-100203-1", "it")
            saveSku("PCK-00-100205-1", "it")
            saveSku("PCK-00-100204-1", "it")
            saveSku("PCK-00-100206-1", "it")
            saveSku("PCK-00-100207-1", "it")
            saveSku("PCK-00-100208-1", "it")
            saveSku("PCK-00-100210-1", "it")
            saveSku("PCK-00-100209-1", "it")
            saveSku("PCK-00-102339-1", "it")
            saveSku("PCK-00-102432-1", "it")
            saveSku("PCK-00-102434-1", "it")
            saveSku("PCK-00-102433-1", "it")
            saveSku("PCK-00-102435-1", "it")
            saveSku("PCK-00-123024-1", "it")
            saveSku("PCK-00-123025-1", "it")
            saveSku("PCK-00-123026-1", "it")
            saveSku("PCK-00-131910-1", "it")
            saveSku("PCK-00-132081-1", "it")
            val validDeliveryDate = LocalDate.now().plusDays(1)

            val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-005297-1,1200,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100200-1,1333,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100201-1,1363,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100202-1,690,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100203-1,553,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100205-1,422,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100204-1,384,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100206-1,434,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100207-1,304,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100208-1,664,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100210-1,648,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-100209-1,571,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-102339-1,280,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-102432-1,403,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-102434-1,605,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-102433-1,208,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-102435-1,186,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-123024-1,373,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-123025-1,916,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-123026-1,437,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-131910-1,217,UNIT_TYPE,2.5,0,units,0.10220,Vendor Delivered
            2024-W37,IT,,Multi-Week,113000,$validDeliveryDate,06:00,08:00,PCK-00-132081-1,34,UNIT_TYPE,3,0,units,0.10220,Vendor Delivered
            """.trimIndent().toByteArray()

            postBulkCreateFile(validFileContent).andExpect { status { isOk() } }
        }

        @Test
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.nl.all.manager")
        fun `should respond with 400 with invalid nl file`() {
            val givenMarket = "beneluxfr"
            saveDc("QF", givenMarket)
            saveSupplier("115950".toInt(), givenMarket)
            saveReason("Multi-Week", givenMarket)
            saveSku("OTH-40-132658-4", givenMarket)
            saveSku("OTH-40-136028-4", givenMarket)
            saveSku("OTH-40-132680-4", givenMarket)
            saveSku("OTH-40-135470-4", givenMarket)
            saveSku("OTH-40-132689-4", givenMarket)
            saveSku("OTH-40-132671-4", givenMarket)
            saveSku("OTH-40-133176-4", givenMarket)
            saveSku("OTH-40-132657-4", givenMarket)
            saveSku("OTH-40-132670-4", givenMarket)
            saveSku("OTH-40-132676-4", givenMarket)
            saveSku("OTH-40-132677-4", givenMarket)
            saveSku("OTH-40-137315-4", givenMarket)
            saveSku("OTH-40-132682-4", givenMarket)
            saveSku("OTH-40-133165-4", givenMarket)
            saveSku("OTH-40-135242-4", givenMarket)
            saveSku("OTH-40-137008-4", givenMarket)
            saveSku("OTH-40-133128-4", givenMarket)
            saveSku("OTH-40-137314-4", givenMarket)

            val validFileContent = """
            week.value;distributionCenter.value;customOrderNumberCode;reason;supplierCode.value;deliveryDate;startTime;endTime;sku.value;orderSize;orderUnit.value;buffer.value;case.size;case.uom;price;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132658-4;2631;UNIT_TYPE;0;1;unit;3.92;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-136028-4;1949;UNIT_TYPE;0;1;unit;4.15;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132680-4;1962;UNIT_TYPE;0;1;unit;5.01;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-135470-4;2687;UNIT_TYPE;0;1;unit;5.12;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132689-4;1804;UNIT_TYPE;0;1;unit;5.48;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132671-4;1673;UNIT_TYPE;0;1;unit;4.66;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-133176-4;1267;UNIT_TYPE;0;1;unit;4.59;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132657-4;1687;UNIT_TYPE;0;1;unit;5.63;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132670-4;370;UNIT_TYPE;0;1;unit;3.81;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132676-4;368;UNIT_TYPE;0;1;unit;3.92;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132677-4;283;UNIT_TYPE;0;1;unit;4.29;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-137315-4;543;UNIT_TYPE;0;1;unit;6.35;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-132682-4;863;UNIT_TYPE;0;1;unit;4.97;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-133165-4;481;UNIT_TYPE;0;1;unit;3.76;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-135242-4;428;UNIT_TYPE;0;1;unit;5.02;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-137008-4;406;UNIT_TYPE;0;1;unit;4.49;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-133128-4;683;UNIT_TYPE;0;1;unit;4.71;;
            2024-W44;QF;;Multi-Week;115950;2024-10-25;03:00;04:00;OTH-40-137314-4;496;UNIT_TYPE;0;1;unit;4.06;;
            ;;;;;;;;;;;;;;;;
            ;;;;;;;;;;;;;;;;
            ;;;;;;;;;;;;;;;;
            ;;;;;;;;;;;;;;;;
            ;;;;;;;;;;;;;;;;
            ;;;;;;;;;;;;;;;;
            """.trimIndent().toByteArray()

            postBulkCreateFile(validFileContent).andExpect { status { isBadRequest() } }
        }

        @Test
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.se.all.manager")
        fun `should respond with 400 when there are duplicate SKUs`() {
            val givenMarket = "dkse"
            val validDeliveryDate = LocalDate.now().plusDays(1)
            saveDc("MO", givenMarket)
            saveSupplier("111764".toInt(), givenMarket)
            saveSupplier("111652".toInt(), givenMarket)
            saveReason("Raw Ingredients (Bulk)", givenMarket)
            saveSku("PTN-33-001813-2", givenMarket)
            saveSku("PTN-33-10002-2", givenMarket)
            saveSku("PTN-33-10001-2", givenMarket)
            saveSku("PTN-33-10017-2", givenMarket)
            saveSku("PTN-33-10018-2", givenMarket)

            val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
            2024-W45,MO,,Raw Ingredients (Bulk),111764,$validDeliveryDate,06:00,08:00,PTN-33-001813-2,470,CASE_TYPE,0,18,unit,732.42,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111764,$validDeliveryDate,06:00,08:00,PTN-33-10002-2,111,CASE_TYPE,0,18,unit,1485.72,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111764,$validDeliveryDate,06:00,08:00,PTN-33-10001-2,155,CASE_TYPE,0,18,unit,745.56,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111764,$validDeliveryDate,06:00,08:00,PTN-33-10001-2,110,CASE_TYPE,0,13,unit,400.56,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-10017-2,80,CASE_TYPE,0,24,unit,770.16,Vendor Delivered
            2024-W45,MO,,Raw Ingredients (Bulk),111652,$validDeliveryDate,06:00,08:00,PTN-33-10018-2,97,CASE_TYPE,0,18,unit,1128.15,Vendor Delivered
            """.trimIndent().toByteArray()

            postBulkCreateFile(validFileContent).andExpect {
                status { isBadRequest() }
                jsonPath("$.message") { value("Duplicate SKUs found in the file: PTN-33-10001-2") }
            }
        }

        @Test
        @WithJWTUser(
            userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df",
            roleClaim = "purchasing.es.all.manager,purchasing.nl.all.manager,purchasing.us.all.manager"
        )
        fun `should respond with 200 and map comments properly`() {
            saveDc("SP", "es")
            saveDc("LI", "beneluxfr")
            saveDc("NJ", "us")
            saveSupplier("114318".toInt(), "es")
            saveSupplier("300738".toInt(), "beneluxfr")
            saveSupplier("10243".toInt(), "us")
            saveReason("Multi-Week", "es")
            saveReason("Internal Forecast Change", "beneluxfr")
            saveReason("Packaging", "us")
            saveSku("PCK-00-120387-1", "es")
            saveSku("PCK-00-120389-1", "es")
            saveSku("PCK-00-120391-1", "es")
            saveSku("PCK-00-120420-1", "es")
            saveSku("PCK-00-120393-1", "es")
            saveSku("DRY-13-22012-4", "beneluxfr")
            saveSku("PRO-13-121720-1", "beneluxfr")
            saveSku("PRO-13-123503-1", "beneluxfr")
            saveSku("PRO-13-123509-1", "beneluxfr")
            saveSku("PRO-13-123510-1", "beneluxfr")
            saveSku("PRO-13-123512-1", "beneluxfr")
            saveSku("PRO-10-136857-1", "us")
            saveSku("OTH-10-137049-7", "us")
            saveSku("OTH-10-137050-7", "us")
            saveSku("PCK-10-21118-1", "us")
            val validDeliveryDate = LocalDate.now().plusDays(1)

            val validFileContent = """
                week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod,comments
                2024-W41,SP,,Multi-Week,114318,$validDeliveryDate,9:00,12:00,PCK-00-120387-1,340,CASE_TYPE,10,10,OZ,0.0890,Vendor Delivered,First Comment taken ignoring rest
                2024-W41,SP,,Multi-Week,114318,$validDeliveryDate,9:00,12:00,PCK-00-120389-1,150,CASE_TYPE,23,15,L,0.0890,Vendor Delivered,Comment 2
                2024-W41,SP,,Multi-Week,114318,$validDeliveryDate,9:00,12:00,PCK-00-120391-1,1140,UNIT_TYPE,0,,unit,0.0890,Vendor Delivered,Comment 3
                2024-W41,SP,,Multi-Week,114318,$validDeliveryDate,9:00,12:00,PCK-00-120420-1,580,UNIT_TYPE,35,,unit,0.0890,Vendor Delivered,
                2024-W41,SP,,Multi-Week,114318,$validDeliveryDate,9:00,12:00,PCK-00-120393-1,480,UNIT_TYPE,0,,unit,0.0890,Vendor Delivered,
                2024-W45,LI,,Internal Forecast Change,300738,$validDeliveryDate,14:00,17:00,DRY-13-22012-4,11520,UNIT_TYPE,0,1,unit,0.92,Vendor Delivered,First Comment empty
                2024-W45,LI,,Internal Forecast Change,300738,$validDeliveryDate,14:00,17:00,PRO-13-121720-1,14976,UNIT_TYPE,0,1,unit,0.28,Crossdock,
                2024-W45,LI,,Internal Forecast Change,300738,$validDeliveryDate,14:00,17:00,PRO-13-123503-1,3240,CASE_TYPE,0,4,GAL,0.47,Crossdock,
                2024-W45,LI,,Internal Forecast Change,300738,$validDeliveryDate,14:00,17:00,PRO-13-123509-1,3672,UNIT_TYPE,0,1,OZ,0.45,Freight on board,
                2024-W45,LI,,Internal Forecast Change,300738,$validDeliveryDate,14:00,17:00,PRO-13-123510-1,19200,UNIT_TYPE,0,1,unit,0.52,Freight on board,
                2024-W45,LI,,Internal Forecast Change,300738,$validDeliveryDate,14:00,17:00,PRO-13-123512-1,9600,UNIT_TYPE,0,1,unit,0.52,Freight on board,
                2024-W41,NJ,,Packaging,10243,$validDeliveryDate,12:00,11:00,OTH-10-137049-7,150,UNIT_TYPE,0,,unit,0.0890,Vendor Delivered,
                2024-W41,NJ,,Packaging,10243,$validDeliveryDate,9:00,12:00,OTH-10-137050-7,130,UNIT_TYPE,0,,unit,0.0890,Vendor Delivered,
                2024-W41,NJ,,Packaging,10243,$validDeliveryDate,9:00,12:00,PCK-10-21118-1,420,UNIT_TYPE,0,,unit,0.0890,Vendor Delivered,Comment 5
            """.trimIndent().toByteArray()

            postBulkCreateFile(validFileContent).andExpect {
                status { isOk() }
                jsonPath("$.purchase_orders[0].comments") { value("First Comment taken ignoring rest") }
                jsonPath("$.purchase_orders[1].comments") { value("First Comment empty") }
                jsonPath("$.purchase_orders[2].comments") { isEmpty() }
                jsonPath("$.purchase_orders[3].comments") { isEmpty() }
                jsonPath("$.purchase_orders[4].comments") { isEmpty() }
                jsonPath("$.purchase_orders[5].comments") { value("Comment 5") }
            }
        }
    }

    @Nested
    inner class Persist {
        @Test
        @Suppress("LongMethod")
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.us.all.manager")
        fun `should respond with 200 with a valid file and provide created purchase orders ID and Number`() {
            // given
            val givenWeek = "2024-W44"
            val givenSupplierCode = "114318"
            val givenDeliveryDate = LocalDate.now().plusDays(1)
            val givenStartTime = "9:00"
            val givenEndTime = "12:00"
            val givenShipMethod = "Vendor Delivered"
            val givenMarket = "us"
            val givenDc = saveDc(
                "SP",
                givenMarket,
                listOf(
                    DistributionCenterAddress(
                        id = randomUUID(),
                        dcCode = "NJ",
                        number = "60",
                        address = "60 Lister Avenue",
                        zip = "07105",
                        city = "Newark",
                        state = "NJ",
                        company = "HelloFresh United States - NJ",
                        type = DistributionCenterAddressType.DELIVERY,
                        createdAt = now(),
                        updatedAt = now(),
                        countryCode = "US",
                    ),
                ),
            )
            saveSupplier(givenSupplierCode.toInt(), givenMarket)
            val givenReason = saveReason("Multi-Week", givenMarket)
            saveSku("PCK-00-120387-1", givenMarket)
            val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
            $givenWeek,${givenDc.code},,${givenReason.name},$givenSupplierCode,$givenDeliveryDate,$givenStartTime,$givenEndTime,PCK-00-120387-1,533,UNIT_TYPE,0,,unit,0.0890,$givenShipMethod
            """.trimIndent().toByteArray()

            // when
            val response = postBulkCreateFile(validFileContent, "false")
                .andExpect {
                    // then
                    status { isCreated() }
                    jsonPath("$.purchase_orders[0].id") { isNotEmpty() }
                    jsonPath("$.purchase_orders[0].po_number") { isNotEmpty() }
                }.andReturn()

            val previewResponse = objectMapper.readValue(
                response.response.contentAsString,
                BatchCreatePurchaseOrdersResponse::class.java,
            )

            // then assert import history created for given purchase orders
            val importHistories = importHistoryRepository.findAll()
            val expectedSummary = BatchPoCreation(
                previewResponse.purchaseOrders.mapNotNull { it.poNumber },
                listOf(givenMarket)
            )
            assertThat(
                importHistories.map { it.getSummary() },
                equalTo(listOf(expectedSummary)),
            )

            previewResponse.purchaseOrders.map { it.id to it.poNumber }.forEach { idToPoNumber ->
                // then assert purchase orders where created
                val poIds = purchaseOrderRepository.findAllByPoNumber(idToPoNumber.second!!).map { it.id }
                assertThat(poIds, equalTo(listOf(idToPoNumber.first)))

                // then assert worker actions where queued
                val workerActions = workerActionRepository.findAll()
                assertThat(workerActions, hasSize(1))
                assertThat(workerActions[0].actionType, equalTo(WorkerActionType.SYNC_BATCH_ORDER))
                assertThat(workerActions[0].status, equalTo(WorkerActionStatus.PENDING))
                assertThat(
                    (workerActions[0].getPayload() as SyncBatchOrder).purchaseOrderNumber,
                    equalTo(idToPoNumber.second),
                )
            }
        }

        @Test
        @Suppress("LongMethod")
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.us.all.manager")
        fun `should respond with 200 with a valid file and persist with comments`() {
            // given
            val givenWeek = "2024-W44"
            val givenSupplierCode = "114318"
            val givenDeliveryDate = LocalDate.now().plusDays(1)
            val givenStartTime = "9:00"
            val givenEndTime = "12:00"
            val givenShipMethod = "Vendor Delivered"
            val givenMarket = "us"
            val givenDc = saveDc(
                "SP",
                givenMarket,
                listOf(
                    DistributionCenterAddress(
                        id = randomUUID(),
                        dcCode = "NJ",
                        number = "60",
                        address = "60 Lister Avenue",
                        zip = "07105",
                        city = "Newark",
                        state = "NJ",
                        company = "HelloFresh United States - NJ",
                        type = DistributionCenterAddressType.DELIVERY,
                        createdAt = now(),
                        updatedAt = now(),
                        countryCode = "US",
                    ),
                ),
            )
            saveSupplier(givenSupplierCode.toInt(), givenMarket)
            val givenReason = saveReason("Multi-Week", givenMarket)
            saveSku("PCK-00-120387-1", givenMarket)
            val givenComment = "First Comment"
            val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod,comments
            $givenWeek,${givenDc.code},,${givenReason.name},$givenSupplierCode,$givenDeliveryDate,$givenStartTime,$givenEndTime,PCK-00-120387-1,533,UNIT_TYPE,0,,unit,0.0890,$givenShipMethod,$givenComment
            """.trimIndent().toByteArray()

            // when
            val response = postBulkCreateFile(validFileContent, "false")
                .andExpect {
                    // then
                    status { isCreated() }
                    jsonPath("$.purchase_orders[0].id") { isNotEmpty() }
                    jsonPath("$.purchase_orders[0].po_number") { isNotEmpty() }
                }.andReturn()

            val previewResponse = objectMapper.readValue(
                response.response.contentAsString,
                BatchCreatePurchaseOrdersResponse::class.java,
            )

            // then assert import history created for given purchase orders
            val importHistories = importHistoryRepository.findAll()
            val expectedSummary = BatchPoCreation(
                previewResponse.purchaseOrders.mapNotNull { it.poNumber },
                listOf(givenMarket)
            )
            assertThat(
                importHistories.map { it.getSummary() },
                equalTo(listOf(expectedSummary)),
            )

            previewResponse.purchaseOrders.map { it.id to it.poNumber }.forEach { idToPoNumber ->
                // then assert purchase orders where created
                val poIds = purchaseOrderRepository.findAllByPoNumber(idToPoNumber.second!!).map { it.id to it.comment }
                assertThat(poIds, equalTo(listOf(idToPoNumber.first to givenComment)))

                // then assert worker actions where queued
                val workerActions = workerActionRepository.findAll()
                assertThat(workerActions, hasSize(1))
                assertThat(workerActions[0].actionType, equalTo(WorkerActionType.SYNC_BATCH_ORDER))
                assertThat(workerActions[0].status, equalTo(WorkerActionStatus.PENDING))
                assertThat(
                    (workerActions[0].getPayload() as SyncBatchOrder).purchaseOrderNumber,
                    equalTo(idToPoNumber.second),
                )
            }
        }
    }

    private fun postBulkCreateFile(validFileContent: ByteArray, dryRun: String = "true") = mockMvc.multipart(
        "/imports/orders",
    ) {
        param("dry_run", dryRun)
        part(
            MockPart("file", "testFile.csv", validFileContent, MediaType.valueOf("text/csv")),
        )
    }

    private fun saveDc(dcCode: String, market: String, addresses: List<DistributionCenterAddress> = emptyList()) = distributionCentersRepository.save(
        getDistributionCenterEntity(code = dcCode, market = market, addresses = addresses),
    )

    private fun saveSupplier(supplierCode: Int, market: String) =
        supplierSimpleRepository.save(getSupplierSimpleEntity(code = supplierCode, market = market))

    private fun saveReason(reason: String, market: String) = emergencyReasonRepository.save(
        getEmergencyReasonEntity(name = reason, market = market),
    )

    private fun saveSku(code: String, market: String) = skuRepository.save(
        getSkuEntity(code = code, market = market),
    )
}
