package com.hellofresh.oms.orderManagementHttp.comments.integration

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
abstract class AbstractCommentControllerTest : AbstractIntegrationTest() {

    @Autowired
    lateinit var mockMvc: MockMvc

    companion object {
        protected const val COMMENTS_PATH = "/comments"

        @JvmStatic
        protected val APPLICATION_CONTENT = MediaType.parseMediaType("application/json")
    }
}
