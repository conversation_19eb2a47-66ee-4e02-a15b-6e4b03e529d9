package com.hellofresh.oms.orderManagementHttp.uom

import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.UnitOfMeasure
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import java.util.UUID
import org.junit.jupiter.api.DynamicTest.dynamicTest
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace.NONE
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import uk.org.webcompere.modelassert.json.JsonAssertions.json

@SpringBootTest
@AutoConfigureMockMvc
@AutoConfigureTestDatabase(replace = NONE)
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Tag("integration")
class UnitOfMeasureIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var uomRepository: UnitOfMeasureRepository

    @TestFactory
    fun `should get units of measure depending on the market`() =
        listOf("us", "dach", "it")
            .flatMap { market ->
                listOf(
                    Triple(market, UOM.UNIT, "unit"),
                    Triple(market, UOM.KG, "kg"),
                    Triple(market, UOM.L, "l"),
                    Triple(market, UOM.LBS, "lbs"),
                    Triple(market, UOM.GAL, "gal"),
                    Triple(market, UOM.OZ, "oz"),
                )
            }.map { (market, enumValue, name) ->
                dynamicTest("""should retrieve $enumValue for $market market""") {
                    uomRepository.deleteAll()
                    val givenUom =
                        UnitOfMeasure(
                            uuid = UUID.randomUUID(),
                            name = name,
                            market = market,
                            type = "bulk",
                            enumValue = enumValue,
                        )
                    uomRepository.save(givenUom)

                    mockMvc.get("/units-of-measure") {
                        param("market", market)
                    }.andExpect {
                        status().isOk
                        content().string(
                            json()
                                .at("/items")
                                .isEqualTo(
                                    listOf(
                                        mapOf(
                                            "id" to givenUom.uuid,
                                            "name" to name,
                                            "type" to "bulk",
                                            "enumValue" to enumValue.name,
                                        ),
                                    ),
                                ),
                        )
                    }
                }
            }

    @Test
    fun `should get empty units of measure list api response when given market does not exists`() {
        mockMvc.get("/units-of-measure") {
            param("market", "mk")
        }.andExpect {
            status().isOk
            content().string(json().at("/items").isEmpty)
        }
    }
}
