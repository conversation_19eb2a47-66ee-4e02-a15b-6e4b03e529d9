package com.hellofresh.oms.orderManagementHttp.uom

import com.hellofresh.oms.orderManagementHttp.uom.Fixture.getUnitOfMeasureDto
import com.hellofresh.oms.orderManagementHttp.uom.Fixture.getUnitOfMeasureEntities
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll

class UnitOfMeasureMapperTest {
    @Test
    fun `should map list of unit of measure entity to list of dto`() {
        // given
        val unitOfMeasureEntities = getUnitOfMeasureEntities()

        // when
        val unitOfMeasureDto = unitOfMeasureEntities.mapToDto()

        // then
        assertAll(
            { assertEquals(unitOfMeasureEntities[0].uuid, unitOfMeasureDto[0].uuid) },
            { assertEquals(unitOfMeasureEntities[0].name, unitOfMeasureDto[0].name) },
            { assertEquals(unitOfMeasureEntities[0].market, unitOfMeasureDto[0].market) },
            { assertEquals(unitOfMeasureEntities[0].type, unitOfMeasureDto[0].type) },

            { assertEquals(unitOfMeasureEntities[1].uuid, unitOfMeasureDto[1].uuid) },
            { assertEquals(unitOfMeasureEntities[1].name, unitOfMeasureDto[1].name) },
            { assertEquals(unitOfMeasureEntities[1].market, unitOfMeasureDto[1].market) },
            { assertEquals(unitOfMeasureEntities[1].type, unitOfMeasureDto[1].type) },
        )
    }

    @Test
    fun `should map list of unit of measure dto to list of api response`() {
        // given
        val unitOfMeasureDto = getUnitOfMeasureDto()

        // when
        val unitOfMeasureListApiResponse = unitOfMeasureDto.toApiResponse()

        // then
        assertAll(
            { assertEquals(unitOfMeasureDto[0].uuid, unitOfMeasureListApiResponse.items[0].id) },
            { assertEquals(unitOfMeasureDto[0].name, unitOfMeasureListApiResponse.items[0].name) },
            { assertEquals(unitOfMeasureDto[0].type, unitOfMeasureListApiResponse.items[0].type) },

            { assertEquals(unitOfMeasureDto[1].uuid, unitOfMeasureListApiResponse.items[1].id) },
            { assertEquals(unitOfMeasureDto[1].name, unitOfMeasureListApiResponse.items[1].name) },
            { assertEquals(unitOfMeasureDto[1].type, unitOfMeasureListApiResponse.items[1].type) },
        )
    }
}
