package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.PalletPackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum.GAL
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum.KG
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.deliveryReason.ChangeReasonRepository
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.exception.OrderCreationException
import com.hellofresh.oms.orderManagementHttp.order.billingAddress
import com.hellofresh.oms.orderManagementHttp.order.createPoRequestDto
import com.hellofresh.oms.orderManagementHttp.order.distributionCenter
import com.hellofresh.oms.orderManagementHttp.order.emergencyReason
import com.hellofresh.oms.orderManagementHttp.order.getCreateOrderItemRequest
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getUomEntity
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.order.palletPackaging
import com.hellofresh.oms.orderManagementHttp.order.supplier
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import com.hellofresh.oms.orderManagementHttp.uom.UnitOfMeasureRepository
import com.hellofresh.oms.orderManagementHttp.workerAction.CreateAndSendOrderScheduler
import com.hellofresh.oms.orderManagementHttp.workerAction.CreateOrderScheduler
import java.util.Optional.of
import java.util.UUID.randomUUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class PurchaseOrderCreateServiceTest {
    private val purchaseOrderRepositoryMock = mock<PurchaseOrderRepository>()
    private val emergencyOrderRepositoryMock = mock<EmergencyReasonRepository>()
    private val supplierRepositoryMock = mock<SupplierRepository>()
    private val distributionCentersRepositoryMock = mock<DistributionCentersRepository>()
    private val unitOfMeasureRepositoryMock = mock<UnitOfMeasureRepository>()
    private val skuRepositoryMock = mock<SkuRepository>()
    private val changeReasonRepositoryMock = mock<ChangeReasonRepository>()
    private val createOrderSchedulerMock = mock<CreateOrderScheduler>()
    private val createAndSendOrderSchedulerMock = mock<CreateAndSendOrderScheduler>()
    private val purchaseOrderProducerMock = mock<PurchaseOrderProducer>()

    private val purchaseOrderValidatorServiceMock = PurchaseOrderValidatorService(
        purchaseOrderRepositoryMock,
        changeReasonRepositoryMock,
        skuRepositoryMock,
        unitOfMeasureRepositoryMock,
        emergencyOrderRepositoryMock,
        supplierRepositoryMock,
    )

    private val purchaseOrderServiceMock = PurchaseOrderService(
        purchaseOrderRepositoryMock,
        createOrderSchedulerMock,
        createAndSendOrderSchedulerMock,
    )

    private val subject = PurchaseOrderCreateService(
        purchaseOrderServiceMock,
        purchaseOrderValidatorServiceMock,
        distributionCentersRepositoryMock,
        purchaseOrderProducerMock,
    )

    @Test
    fun `should throw an exception when the request has no deliveryAddress and the distribution center has no DELIVERY address`() {
        // given
        val skuId = randomUUID()
        val input = createPoRequestDto.copy(
            deliveryAddress = null,
            orderItems = listOf(
                getCreateOrderItemRequest(
                    skuId = skuId,
                    packaging = palletPackaging.copy(
                        pricePerCase = MoneyDto("0.0001", "EUR"),
                        uom = KG,
                    ),
                ),
            ),
        )

        whenever(purchaseOrderRepositoryMock.getNextPoNrSeq()).thenReturn(1)
        whenever(supplierRepositoryMock.findById(any())).thenReturn(of(supplier))
        whenever(emergencyOrderRepositoryMock.findById(any())).thenReturn(of(emergencyReason))
        whenever(distributionCentersRepositoryMock.findByCode(any()))
            .thenReturn(distributionCenter.copy(addresses = listOf(billingAddress)))
        whenever(unitOfMeasureRepositoryMock.findAllByMarketIgnoreCase(any())).thenReturn(
            listOf(getUomEntity()),
        )
        whenever(skuRepositoryMock.findAllById(any()))
            .thenReturn(listOf(getSkuEntity(uuid = skuId)))

        // when
        val err = assertThrows<OrderCreationException> {
            subject.processPurchaseOrder(input, false)
        }

        assertEquals("Distribution center has no Address of type DELIVERY. ${distributionCenter.code}", err.message)
    }

    @Test
    fun `should throw an exception when UOMs are not found when processing PO`() {
        // given
        val input = createPoRequestDto.copy(
            orderItems = listOf(
                getCreateOrderItemRequest(
                    packaging = palletPackaging.copy(
                        pricePerCase = MoneyDto("0.0001", "EUR"),
                    ),
                ),
                getCreateOrderItemRequest(
                    packaging = palletPackaging.copy(
                        pricePerCase = MoneyDto("0.0001", "EUR"),
                        uom = GAL,
                    ),
                ),
            ),
        )

        whenever(purchaseOrderRepositoryMock.getNextPoNrSeq()).thenReturn(1)
        whenever(supplierRepositoryMock.findById(any())).thenReturn(of(supplier))
        whenever(emergencyOrderRepositoryMock.findById(any())).thenReturn(of(emergencyReason))
        whenever(distributionCentersRepositoryMock.findByCode(any())).thenReturn(distributionCenter)
        whenever(unitOfMeasureRepositoryMock.findAllByMarketIgnoreCase(any())).thenReturn(
            listOf(getUomEntity()),
        )

        // when
        val err = assertThrows<OrderCreationException> {
            subject.processPurchaseOrder(input, false)
        }

        val actualOrderItemUoms = input.orderItems.map { (it.packaging as PalletPackagingRequest).uom }.toSet()
        assertEquals("Provided UOMs not found: $actualOrderItemUoms", err.message)
    }

    companion object {
        val user = LoggedInUserInfo(
            randomUUID(),
            "<EMAIL>",
            "userName",
            emptyList(),
            randomUUID().toString(),
        )
    }
}
