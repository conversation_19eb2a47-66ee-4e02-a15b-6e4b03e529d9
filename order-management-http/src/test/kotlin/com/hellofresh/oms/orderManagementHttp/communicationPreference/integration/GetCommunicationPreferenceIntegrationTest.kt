package com.hellofresh.oms.orderManagementHttp.communicationPreference.integration

import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.E2OPEN
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.EMAIL
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.EMAIL_AND_E2OPEN
import com.hellofresh.oms.orderManagement.generated.api.model.CommunicationPreferenceTypeEnum as CommunicationPreferenceTypeEnumApi
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.statsig.sdk.Statsig
import io.micrometer.core.instrument.MeterRegistry
import java.util.UUID
import kotlin.random.Random
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import uk.org.webcompere.modelassert.json.JsonAssertions.json

class GetCommunicationPreferenceIntegrationTest : AbstractCommunicationPreferenceIntegrationTest() {
    @Autowired
    private lateinit var meterRegistry: MeterRegistry

    @Value("\${statsig.gates.force-email-communication-preference}")
    private lateinit var enableForceIncludeEmailCommunicationPreferenceStatsigGate: String

    @BeforeEach
    fun setUp() {
        Statsig.overrideGate(enableForceIncludeEmailCommunicationPreferenceStatsigGate, false)
    }

    @AfterEach
    fun tearDown() {
        meterRegistry.clear()
    }

    @ParameterizedTest
    @EnumSource(GetSingleTestCase::class)
    @Sql(scripts = ["/data/communicationPreferences.sql"])
    fun `should return preference according to market, dc & supplier`(testCase: GetSingleTestCase) {
        mockMvc.get(
            "/communication-preference/{market}/{dcCode}/{supplierId}",
            testCase.givenMarket,
            testCase.givenDcCode,
            testCase.givenSupplierId,
        ).andExpect {
            if (testCase.expectedPreference != null) {
                status { isOk() }
                jsonPath("$.preference") { value(testCase.expectedPreference.toString()) }
            } else {
                status { isNotFound() }
            }
        }
    }

    @Test
    fun `should return not found when there is no preference for the given po number`() {
        mockMvc.get(
            "/communication-preference/{market}/{dcCode}/{supplierId}",
            "DE",
            "XX",
            "00000000-0000-0000-0000-000000000003",
        )
            .andExpect {
                status { isNotFound() }
            }
    }

    @Test
    fun `should return no content when trying to delete non existing communication preferences`() {
        mockMvc.delete(
            "/communication-preferences/{scopeType}/{scopeValue}",
            CommunicationPreferenceTypeEnumApi.MARKET.value,
            "XX",
        ).andExpect {
            status { isNotFound() }
        }
    }

    @Test
    @Suppress("LongMethod")
    fun `should return filtered preferences when market filter is given`() {
        // given
        val givenMarketCode = "ca"
        val differentMarket = "it"
        val givenDcCode = "ZH"
        val givenDcName = "Zurich Delivery"
        val givenSupplierId = UUID.randomUUID()
        val givenSupplierCode = 489669707
        val givenSupplierName = "Onions 101"

        val expectedResponse = listOf(
            mapOf(
                "type" to "MARKET",
                "preference" to "EMAIL_AND_E2OPEN",
                "market" to mapOf("code" to givenMarketCode),
                "distribution_center" to null,
                "supplier" to null,
            ),
            mapOf(
                "type" to "DC",
                "preference" to "E2OPEN",
                "market" to null,
                "distribution_center" to mapOf(
                    "code" to givenDcCode,
                    "name" to givenDcName,
                ),
                "supplier" to null,
            ),
            mapOf(
                "type" to "SUPPLIER",
                "preference" to "E2OPEN",
                "market" to null,
                "distribution_center" to null,
                "supplier" to mapOf(
                    "id" to givenSupplierId,
                    "code" to givenSupplierCode,
                    "name" to givenSupplierName,
                ),
            ),
        )

        savePreference(preference = EMAIL_AND_E2OPEN, market = givenMarketCode)
        savePreference(preference = EMAIL, market = differentMarket)
        savePreference(
            preference = E2OPEN,
            distributionCenter = getDistributionCenterEntity(
                code = givenDcCode,
                name = givenDcName,
                market = givenMarketCode,
            ),
        )
        savePreference(
            preference = EMAIL,
            distributionCenter = getDistributionCenterEntity(
                code = "FD",
                name = "Fast Distribution",
                market = differentMarket,
            ),
        )
        savePreference(
            preference = E2OPEN,
            supplier = getSupplierEntity(
                supplierId = givenSupplierId,
                code = givenSupplierCode,
                name = givenSupplierName,
                market = givenMarketCode,
            ),
        )
        savePreference(
            preference = EMAIL_AND_E2OPEN,
            supplier = getSupplierEntity(
                supplierId = UUID.randomUUID(),
                code = 999999855,
                name = "TomaToes",
                market = differentMarket,
            ),
        )

        // when
        mockMvc.get("/communication-preferences") {
            param("market", givenMarketCode)
        }.andExpect {
            // then
            status { isOk() }
            content {
                string(
                    json()
                        .at("/preferences")
                        .where()
                        .keysInAnyOrder()
                        .arrayInAnyOrder()
                        .isEqualTo(expectedResponse),
                )
            }
        }
    }

    @Test
    @Suppress("LongMethod")
    fun `should return feature flag overridden preferences`() {
        // given
        val mappings = mapOf(
            EMAIL_AND_E2OPEN to "ca",
            EMAIL to "us",
            E2OPEN to "de",
        )

        val overriddenMappings = mapOf(
            EMAIL_AND_E2OPEN to "ca",
            EMAIL to "us",
            EMAIL_AND_E2OPEN to "de", // <- overridden
        )

        mappings.forEach(::savePreference)

        fun callAndValidate(preference: CommunicationPreferenceEnum, market: String) =
            mockMvc.get("/communication-preference/{market}/{dcCode}/{supplierId}", market, "XX", UUID.randomUUID())
                .andExpect {
                    status { isOk() }
                    content {
                        string(
                            json()
                                .at("/preference")
                                .isText(preference.name),
                        )
                    }
                }

        // when
        mappings.forEach(::callAndValidate)
        Statsig.overrideGate(enableForceIncludeEmailCommunicationPreferenceStatsigGate, true)
        overriddenMappings.forEach(::callAndValidate)
    }

    @ParameterizedTest
    @EnumSource(FilterByTypeTestCase::class)
    fun `should return filtered preferences when type filter is given`(testCase: FilterByTypeTestCase) {
        // given
        savePreference(preference = E2OPEN, market = testCase.givenMarketCode)
        savePreference(
            preference = EMAIL,
            distributionCenter = getDistributionCenterEntity(
                code = testCase.givenDcCode,
                name = testCase.givenDcName,
            ),
        )
        savePreference(
            preference = EMAIL_AND_E2OPEN,
            supplier = getSupplierEntity(
                supplierId = testCase.givenSupplierId,
                name = testCase.givenSupplierName,
                code = testCase.givenSupplierCode,
                market = "dach",
            ),
        )

        // when
        mockMvc.get("/communication-preferences") {
            param("type", testCase.givenType)
        }.andExpect {
            status { isOk() }
            content {
                string(
                    json()
                        .at("/preferences")
                        .where()
                        .keysInAnyOrder()
                        .arrayInAnyOrder()
                        .isEqualTo(testCase.expectedResponse),
                )
            }
        }
    }

    @Test
    fun `should increment the metric for market default not found`() {
        // given
        val nonExistingMarketCode = "NAM"
        val nonExistingDc = "ST"
        val nonExistingSupplier = UUID.randomUUID()

        // when
        mockMvc.get(
            "/communication-preference/{market}/{dcCode}/{supplierId}",
            nonExistingMarketCode,
            nonExistingDc,
            nonExistingSupplier,
        ).andExpect {
            status { isNotFound() }
        }

        // then
        assertThat(
            meterRegistry["communication_preference_market_default_not_found"]
                .tags("market", nonExistingMarketCode)
                .counter().count(),
            equalTo(1.0),
        )
    }

    companion object {
        private val NON_EXISTENT_SUPPLIER = UUID.randomUUID()
        private val EXISTING_SUPPLIER_ID = UUID.fromString("00000000-0000-0000-0000-000000000001")
        private const val EXISTING_DC = "NJ"
        private const val NON_EXISTING_DC = "XX"
        private const val EXISTING_MARKET = "us"
        private const val NON_EXISTING_MARKET = "xx"
    }

    enum class GetSingleTestCase(
        val testCaseName: String,
        val givenSupplierId: UUID,
        val givenDcCode: String,
        val givenMarket: String,
        val expectedPreference: CommunicationPreferenceEnum?,
    ) {
        MARKET(
            testCaseName = "return market preference",
            givenSupplierId = NON_EXISTENT_SUPPLIER,
            givenDcCode = NON_EXISTING_DC,
            givenMarket = EXISTING_MARKET,
            expectedPreference = EMAIL_AND_E2OPEN,
        ),
        DC_WITH_EXISTING_MARKET(
            testCaseName = "return DC preference, with existing market",
            givenSupplierId = NON_EXISTENT_SUPPLIER,
            givenDcCode = EXISTING_DC,
            givenMarket = EXISTING_MARKET,
            expectedPreference = E2OPEN,
        ),
        DC_WITHOUT_EXISTING_MARKET(
            testCaseName = "return DC preference, with non-existing market",
            givenSupplierId = NON_EXISTENT_SUPPLIER,
            givenDcCode = EXISTING_DC,
            givenMarket = NON_EXISTING_MARKET,
            expectedPreference = E2OPEN,
        ),
        SUPPLIER_PREFERENCE_WITHOUT_MARKET_AND_DC(
            testCaseName = "return supplier preference, with non existing market and non existing DC",
            givenSupplierId = EXISTING_SUPPLIER_ID,
            givenDcCode = NON_EXISTING_DC,
            givenMarket = NON_EXISTING_MARKET,
            expectedPreference = EMAIL,
        ),
        SUPPLIER_WITH_MARKET_AND_WITHOUT_DC(
            testCaseName = "return supplier preference, with existing market and non existing DC",
            givenSupplierId = EXISTING_SUPPLIER_ID,
            givenDcCode = NON_EXISTING_DC,
            givenMarket = EXISTING_MARKET,
            expectedPreference = EMAIL,
        ),
        SUPPLIER_WITH_MARKET_AND_DC(
            testCaseName = "return supplier preference, with existing market and existing DC",
            givenSupplierId = EXISTING_SUPPLIER_ID,
            givenDcCode = EXISTING_DC,
            givenMarket = EXISTING_MARKET,
            expectedPreference = EMAIL,
        ),
    }

    @Suppress("LongParameterList")
    enum class FilterByTypeTestCase(
        val givenMarketCode: String = "dach",
        val givenDcCode: String = "OW",
        val givenDcName: String = "Owings Mills",
        val givenSupplierId: UUID = UUID.randomUUID(),
        val givenSupplierCode: Int = Random.nextInt(0, 100000),
        val givenSupplierName: String = "Magic Leafs",
        val givenType: String,
        val expectedResponse: List<Map<String, Any?>>,
    ) {
        SUPPLIER(
            givenSupplierId = UUID.fromString("38cded21-86f8-4fec-ae3d-7884d02f451a"),
            givenSupplierCode = 452112241,
            givenSupplierName = "Magic Leafs",
            givenType = "SUPPLIER",
            expectedResponse = listOf(
                mapOf(
                    "type" to "SUPPLIER",
                    "preference" to "EMAIL_AND_E2OPEN",
                    "market" to null,
                    "distribution_center" to null,
                    "supplier" to mapOf(
                        "id" to "38cded21-86f8-4fec-ae3d-7884d02f451a",
                        "code" to 452112241,
                        "name" to "Magic Leafs",
                    ),
                ),
            ),
        ),
        DC(
            givenDcCode = "OW",
            givenDcName = "Owings Mills",
            givenType = "DC",
            expectedResponse = listOf(
                mapOf(
                    "type" to "DC",
                    "preference" to "EMAIL",
                    "market" to null,
                    "distribution_center" to mapOf(
                        "code" to "OW",
                        "name" to "Owings Mills",
                    ),
                    "supplier" to null,
                ),
            ),
        ),
        MARKET(
            givenMarketCode = "us",
            givenType = "MARKET",
            expectedResponse = listOf(
                mapOf(
                    "type" to "MARKET",
                    "preference" to "E2OPEN",
                    "market" to mapOf("code" to "us"),
                    "distribution_center" to null,
                    "supplier" to null,
                ),
            ),
        ),
        ALL(
            givenMarketCode = "dach",
            givenDcCode = "OW",
            givenDcName = "Owings Mills",
            givenSupplierId = UUID.fromString("38cded21-86f8-4fec-ae3d-7884d02f451a"),
            givenSupplierCode = 452112241,
            givenSupplierName = "Magic Leafs",
            givenType = "ALL",
            expectedResponse = listOf(
                mapOf(
                    "type" to "MARKET",
                    "preference" to "E2OPEN",
                    "market" to mapOf("code" to "dach"),
                    "distribution_center" to null,
                    "supplier" to null,
                ),
                mapOf(
                    "type" to "DC",
                    "preference" to "EMAIL",
                    "market" to null,
                    "distribution_center" to mapOf(
                        "code" to "OW",
                        "name" to "Owings Mills",
                    ),
                    "supplier" to null,
                ),
                mapOf(
                    "type" to "SUPPLIER",
                    "preference" to "EMAIL_AND_E2OPEN",
                    "market" to null,
                    "distribution_center" to null,
                    "supplier" to mapOf(
                        "id" to "38cded21-86f8-4fec-ae3d-7884d02f451a",
                        "code" to 452112241,
                        "name" to "Magic Leafs",
                    ),
                ),
            ),
        )
    }
}
