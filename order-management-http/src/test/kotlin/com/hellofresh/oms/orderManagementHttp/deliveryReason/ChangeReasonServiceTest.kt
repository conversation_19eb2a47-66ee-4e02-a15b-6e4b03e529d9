package com.hellofresh.oms.orderManagementHttp.deliveryReason

import com.hellofresh.oms.model.ChangeReason
import com.hellofresh.oms.model.ChangeReasonType
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class ChangeReasonServiceTest {

    @Mock
    lateinit var repository: ChangeReasonRepository

    @InjectMocks
    lateinit var service: ChangeReasonService

    @Test
    fun `should return all values for the reason type when no allowed markets provided`() {
        val reasonType = ChangeReasonType.DELIVERY_DATE
        val expected = ChangeReason(
            UUID.fromString("c7d8e9f0-0a0b-0c1d-2d2e-3e3e4e4e5e5e"),
            "name",
            listOf("us"),
            ChangeReasonType.DELIVERY_DATE,
        )

        whenever(repository.findAllByReasonType(reasonType)).thenReturn(listOf(expected))

        val result = service.findAllBy(reasonType, null)

        assertEquals(listOf(expected), result)
    }

    @Test
    fun `should return all values for particular market and the reason type when allowed markets provided`() {
        val reasonType = ChangeReasonType.DELIVERY_DATE
        val allowedMarket = "us"
        val expected = ChangeReason(
            UUID.fromString("c7d8e9f0-0a0b-0c1d-2d2e-3e3e4e4e5e5e"),
            "name",
            listOf("us"),
            ChangeReasonType.DELIVERY_DATE,
        )

        whenever(repository.findAllByReasonTypeAndAllowedMarkets(reasonType.name, allowedMarket)).thenReturn(
            listOf(expected)
        )

        val result = service.findAllBy(reasonType, allowedMarket)

        assertEquals(listOf(expected), result)
    }
}
