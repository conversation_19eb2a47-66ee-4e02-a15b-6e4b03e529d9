package com.hellofresh.oms.orderManagementHttp.distributionCenters

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterAddressType
import com.hellofresh.oms.model.DistributionCenterStatus.ACTIVE
import java.time.LocalDateTime
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class DistributionCentersServiceTest {
    @Mock
    lateinit var distributionCentersRepositoryMock: DistributionCentersRepository

    @InjectMocks
    lateinit var distributionCentersService: DistributionCentersService

    @Test
    fun `getDistributionCenters should get correct DCs by market`() {
        // given
        val market = "us"
        val dcAddress = DistributionCenterAddress(
            id = UUID.randomUUID(),
            dcCode = "NJ",
            number = "12",
            address = "Broadway Avenue",
            zip = "77777",
            city = "New Jersey",
            state = "New Jersey State",
            company = "Freeman Sachs",
            type = DistributionCenterAddressType.BILLING,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            countryCode = "US"
        )
        val dc = DistributionCenter(
            code = "NJ",
            status = ACTIVE,
            name = "New Jersey",
            market = market,
            addresses = listOf(dcAddress),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )
        whenever(
            distributionCentersRepositoryMock.findAllByMarketAndStatusAndIsVisibleOrderByName(market, ACTIVE, true)
        )
            .thenReturn(listOf(dc))

        // when
        val result = distributionCentersService.getDistributionCenters(market)

        // then
        assertEquals(listOf(dc), result)
    }
}
