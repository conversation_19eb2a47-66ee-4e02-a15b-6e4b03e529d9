package com.hellofresh.oms.orderManagementHttp.documentation

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
class SwaggerDocsTest : AbstractIntegrationTest() {

    private val path = "/swagger-ui/index.html"

    @Autowired
    lateinit var mockMvc: MockMvc

    @Test
    fun `should successfully return swagger documentation`() {
        mockMvc.get(path).andExpect { status { isOk() } }
    }

    @Test
    fun `should download openapi yaml`() {
        mockMvc.get("/api.yaml").andExpect { status { isOk() } }
    }
}
