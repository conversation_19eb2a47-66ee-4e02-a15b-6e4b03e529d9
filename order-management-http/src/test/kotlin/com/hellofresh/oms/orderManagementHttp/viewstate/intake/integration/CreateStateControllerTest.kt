package com.hellofresh.oms.orderManagementHttp.viewstate.intake.integration

import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.testcontainers.shaded.org.hamcrest.Matchers

class CreateStateControllerTest : AbstractViewStateControllerTest() {
    @Test
    fun `should create a new view state correctly`() {
        val requestBody = """
            {
                "resource": "po-status-view",
                "name": "test-view-state",
                "state": {
                    "colState": [],
                    "filterState": {}
                }
            }
        """.trimIndent()

        mockMvc.post(VIEW_STATES_PATH) {
            contentType = APPLICATION_CONTENT
            content = requestBody
        }.andExpect {
            status { isOk() }
            jsonPath("$.id").exists()
            jsonPath("$.id").isNumber
            jsonPath("$.id", Matchers.greaterThan(0))
        }
    }

    @ParameterizedTest
    @MethodSource("invalidRequestBodyProvider")
    fun `should return 400 when request body is invalid`(
        requestBody: String,
        expectedErrorMessage: String
    ) {
        mockMvc.post(VIEW_STATES_PATH) {
            contentType = APPLICATION_CONTENT
            content = requestBody
        }.andExpect {
            status { isBadRequest() }
            jsonPath("$.message", String::class.java).value(expectedErrorMessage)
        }
    }

    companion object {
        @JvmStatic
        fun invalidRequestBodyProvider() = listOf(
            Arguments.of(
                """
                {
                    "resource": "po-status-view",
                    "name": "test-view-state",
                    "state": {
                        "colState": [],
                        "filterState": {}
                    }
                    not a valid json object
                }
                """.trimIndent(),
                "Invalid JSON format",
            ),
            Arguments.of(
                """
                {
                    "name": "test-view-state",
                    "state": {
                        "colState": [],
                        "filterState": {}
                    }
                }
                """.trimIndent(),
                "Missing required field: resource",
            ),
            Arguments.of(
                """
                {
                    "resource": "po-status-view",
                    "state": {
                        "colState": [],
                        "filterState": {}
                    }
                }
                """.trimIndent(),
                "Missing required field: name",
            ),
        )
    }
}
