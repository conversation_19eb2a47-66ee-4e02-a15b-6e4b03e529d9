package com.hellofresh.oms.orderManagementHttp.icsTicket

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD

@DataJpaTest
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
@Sql(executionPhase = BEFORE_TEST_METHOD, scripts = ["/data/icsTicket.sql"])
@Tag("integration")
class IcsTicketRepositoryTest(
    @Autowired private val repository: IcsTicketRepository
) : AbstractIntegrationTest() {

    @Test
    fun `should select order and have null if doesn't exist`() {
        // when
        val result = repository.findByPoNumber("256NJ5000")

        // then
        assert(result.isEmpty())
    }

    @Test
    fun `should return relevant ICS tickets by PO number`() {
        // when
        val result = repository.findByPoNumber("2318NJ021001")

        // then
        assert(result.isNotEmpty())
        assertEquals(2, result.size)
        result.forEach {
            assertEquals("2318NJ021001", it.poNumber)
        }
    }

    @Test
    fun `should return latest updated at when records available`() {
        // when
        val latestUpdateAt = repository.findLatestUpdatedAt()

        // then
        assert(latestUpdateAt != null)
        assertEquals(2025, latestUpdateAt!!.year)
        assertEquals(5, latestUpdateAt.monthValue)
        assertEquals(2, latestUpdateAt.dayOfMonth)
        assertEquals(12, latestUpdateAt.hour)
    }

    @Test
    @Sql(executionPhase = BEFORE_TEST_METHOD, scripts = ["/data/removeIcsTicket.sql"])
    fun `should return null when no records available for latest updated at`() {
        // when
        val latestUpdateAt = repository.findLatestUpdatedAt()

        // then
        assertNull(latestUpdateAt)
    }

    @Test
    @Sql(executionPhase = BEFORE_TEST_METHOD, scripts = ["/data/removeIcsTicket.sql"])
    fun `should save ICS tickets in batch`() {
        // given
        val tickets = listOf(
            Fixture.createIcsTicket(ticketId = Random.nextInt()),
            Fixture.createIcsTicket(ticketId = Random.nextInt()),
        )

        // when
        repository.upsertAll(tickets)

        // then
        val saveIcsTickets = repository.findAll()

        assert(saveIcsTickets.isNotEmpty())
        assertEquals(2, saveIcsTickets.size)
    }
}
