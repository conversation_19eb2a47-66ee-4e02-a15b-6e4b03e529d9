package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagementHttp.deliveryReason.ChangeReasonRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.order.caseItem
import com.hellofresh.oms.orderManagementHttp.order.casePackaging
import com.hellofresh.oms.orderManagementHttp.order.createPoRequestDto
import com.hellofresh.oms.orderManagementHttp.order.getCreateOrderItemRequest
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.order.palletItem
import com.hellofresh.oms.orderManagementHttp.order.palletPackaging
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderValidatorService
import com.hellofresh.oms.orderManagementHttp.order.unitItem
import com.hellofresh.oms.orderManagementHttp.order.unitPackaging
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import com.hellofresh.oms.orderManagementHttp.uom.UnitOfMeasureRepository
import java.time.LocalDateTime
import java.time.Month
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class PurchaseOrderValidatorTest {

    @Mock
    lateinit var purchaseOrderRepository: PurchaseOrderRepository

    @Mock
    lateinit var changeReasonRepository: ChangeReasonRepository

    @Mock
    lateinit var skuRepository: SkuRepository

    @Mock
    lateinit var unitOfMeasureRepository: UnitOfMeasureRepository

    @Mock
    lateinit var emergencyReasonRepository: EmergencyReasonRepository

    @Mock
    lateinit var supplierRepository: SupplierRepository

    @InjectMocks
    lateinit var purchaseOrderValidator: PurchaseOrderValidatorService

    @Test
    fun `should pass validation without exceptions when body is valid`() {
        // when
        purchaseOrderValidator.validateCreateRequest(createPoRequestDto.copy(orderItems = listOf(caseItem)))
        purchaseOrderValidator.validateCreateRequest(createPoRequestDto.copy(orderItems = listOf(unitItem)))
        purchaseOrderValidator.validateCreateRequest(createPoRequestDto.copy(orderItems = listOf(palletItem)))
    }

    @Test
    fun `should set correct scale on incoming BigDecimals`() {
        // given
        val poBody = createPoRequestDto.copy(
            orderItems = listOf(
                getCreateOrderItemRequest(
                    packaging = palletPackaging.copy(
                        pricePerCase = MoneyDto("0.0001", "EUR"),
                    ),
                ),
            ),
        )

        // when
        purchaseOrderValidator.validateCreateRequest(poBody)
    }

    @Test
    fun `should throw exception when delivery start after end`() {
        // given
        val requestBody = createPoRequestDto.copy(
            deliveryWindow = DeliveryWindowDto(
                start = LocalDateTime.now().plusHours(1),
                end = LocalDateTime.now(),
            ),
        )

        // when
        val exception = assertThrows<IllegalArgumentException> {
            purchaseOrderValidator.validateCreateRequest(requestBody)
        }

        // then
        Assertions.assertEquals("Delivery start must be before delivery end", exception.message)
    }

    @Test
    fun `should throw exception when delivery date start or end contains seconds`() {
        // given
        val requestBody = createPoRequestDto.copy(
            deliveryWindow = DeliveryWindowDto(
                start = LocalDateTime.now().withSecond(10),
                end = LocalDateTime.now().withSecond(10),
            ),
        )

        // when
        val exception = assertThrows<IllegalArgumentException> {
            purchaseOrderValidator.validateCreateRequest(requestBody)
        }

        // then
        Assertions.assertEquals(
            "Delivery date timestamps should not contain seconds",
            exception.message,
        )
    }

    @Test
    fun `should throw exception when items have duplicated SKUs`() {
        // given
        val body = createPoRequestDto.copy(orderItems = listOf(unitItem, unitItem))
        // when
        val exception = assertThrows<IllegalArgumentException> {
            purchaseOrderValidator.validateCreateRequest(body)
        }
        // then
        Assertions.assertEquals(
            "Order items must not have repeated SKUs",
            exception.message,
        )
    }

    @Test
    fun `should throw exception when delivery date start or end is before hf foundation`() {
        // given
        val requestBody = createPoRequestDto.copy(
            deliveryWindow = DeliveryWindowDto(
                start = LocalDateTime.of(2000, Month.OCTOBER, 1, 0, 0),
                end = LocalDateTime.of(2000, Month.OCTOBER, 1, 1, 0),
            ),
        )

        // when
        val exception = assertThrows<IllegalArgumentException> {
            purchaseOrderValidator.validateCreateRequest(requestBody)
        }

        // then
        Assertions.assertEquals(
            "Delivery start and end must be after foundation of HelloFresh",
            exception.message,
        )
    }

    @Test
    fun `should throw exception when pallet price is negative`() {
        val requestBody = createPoRequestDto.copy(
            orderItems = listOf(
                getCreateOrderItemRequest(
                    packaging = palletPackaging.copy(
                        pricePerCase = MoneyDto("-1", "EUR"),
                    ),
                ),
            ),
        )

        val exception = assertThrows<IllegalArgumentException> {
            purchaseOrderValidator.validateCreateRequest(requestBody)
        }

        Assertions.assertEquals(
            "Item price amount must be greater or equal to 0. Item: ${requestBody.orderItems[0].skuId}",
            exception.message,
        )
    }

    @Test
    fun `should throw exception when case price is negative`() {
        val requestBody = createPoRequestDto.copy(
            orderItems = listOf(
                getCreateOrderItemRequest(
                    packaging = casePackaging.copy(
                        pricePerCase = MoneyDto("-1", "EUR"),
                    ),
                ),
            ),
        )

        val exception = assertThrows<IllegalArgumentException> {
            purchaseOrderValidator.validateCreateRequest(requestBody)
        }

        Assertions.assertEquals(
            "Item price amount must be greater or equal to 0. Item: ${requestBody.orderItems[0].skuId}",
            exception.message,
        )
    }

    @Test
    fun `should throw exception when unit price is negative`() {
        val requestBody = createPoRequestDto.copy(
            orderItems = listOf(
                getCreateOrderItemRequest(
                    packaging = unitPackaging.copy(
                        pricePerUnit = MoneyDto("-1", "EUR"),
                    ),
                ),
            ),
        )

        val exception = assertThrows<IllegalArgumentException> {
            purchaseOrderValidator.validateCreateRequest(requestBody)
        }

        Assertions.assertEquals(
            "Item price amount must be greater or equal to 0. Item: ${requestBody.orderItems[0].skuId}",
            exception.message,
        )
    }
}
