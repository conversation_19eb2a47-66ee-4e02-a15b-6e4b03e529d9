package com.hellofresh.oms.orderManagementHttp.comments.intake

import com.hellofresh.oms.model.Comment
import com.hellofresh.oms.model.YearWeek
import java.time.LocalDateTime
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class ResponseMapperTest {

    private val mapper = ResponseMapper()

    @Test
    fun `should map multiple comments to response correctly`() {
        val response = mapper.toListResponse(comments)

        assertEquals(2, response.items?.size ?: 0)
        with(requireNotNull(response.items?.get(0)) { "Must not be empty!" }) {
            assertEquals("11111111-1111-1111-1111-111111111111", id.toString())
            assertEquals(null, sourceId)
            assertEquals("sku", resourceType)
            assertEquals("DAI-10-10088-4", resourceId)
            assertEquals("test-domain", domain)
            assertEquals("NJ", dc)
            assertEquals("HF", brand)
            assertEquals("2025-W10", week)
            assertEquals("First comment", comment)
            assertEquals("<EMAIL>", createdBy)
            assertEquals("2025-01-01T01:01:01", updatedAt.toString())
        }
        with(requireNotNull(response.items?.get(1)) { "Must not be empty!" }) {
            assertEquals("*************-2222-2222-************", id.toString())
            assertEquals("12345", sourceId)
            assertEquals("po", resourceType)
            assertEquals("PO-123", resourceId)
            assertEquals("gpp-po-status", domain)
            assertEquals("VE", dc)
            assertEquals("GC", brand)
            assertEquals("2025-W11", week)
            assertEquals("Second comment", comment)
            assertEquals("<EMAIL>", createdBy)
            assertEquals("2025-01-02T02:02:02", updatedAt.toString())
        }
    }

    @Test
    fun `should handle empty comment list`() {
        // when
        val response = mapper.toListResponse(emptyList())

        // then
        assertEquals(0, response.items?.size)
    }

    companion object {
        private val comments = listOf(
            Comment(
                id = UUID.fromString("11111111-1111-1111-1111-111111111111"),
                sourceId = null,
                resourceType = "sku",
                resourceId = "DAI-10-10088-4",
                domain = "test-domain",
                dc = "NJ",
                brand = "HF",
                yearWeek = YearWeek("2025-W10"),
                comment = "First comment",
                createdBy = "<EMAIL>",
                createdAt = LocalDateTime.parse("2025-01-01T01:01:01"),
                updatedBy = "<EMAIL>",
                updatedAt = LocalDateTime.parse("2025-01-01T01:01:01")
            ),
            Comment(
                id = UUID.fromString("*************-2222-2222-************"),
                sourceId = "12345",
                resourceType = "po",
                resourceId = "PO-123",
                domain = "gpp-po-status",
                dc = "VE",
                brand = "GC",
                yearWeek = YearWeek("2025-W11"),
                comment = "Second comment",
                createdBy = "<EMAIL>",
                createdAt = LocalDateTime.parse("2025-01-02T02:02:02"),
                updatedBy = "<EMAIL>",
                updatedAt = LocalDateTime.parse("2025-01-02T02:02:02")
            )
        )
    }
}
