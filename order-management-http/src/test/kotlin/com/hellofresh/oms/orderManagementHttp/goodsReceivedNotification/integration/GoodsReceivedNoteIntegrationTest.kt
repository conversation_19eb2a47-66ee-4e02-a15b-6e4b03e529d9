package com.hellofresh.oms.orderManagementHttp.goodsReceivedNotification.integration

import com.hellofresh.oms.model.grn.DeliveryLineStateEnum.DELIVERY_LINE_STATE_CLOSED
import com.hellofresh.oms.model.grn.DeliveryStateEnum.DELIVERY_STATE_RECEIVED
import com.hellofresh.oms.model.grn.GrnStateEnum.STATE_OPEN
import com.hellofresh.oms.model.grn.PurchaseOrderDelivery
import com.hellofresh.oms.model.grn.PurchaseOrderDeliveryLine
import com.hellofresh.oms.model.grn.UnitOfMeasureGrnEnum.UNIT_OF_MEASURE_UNIT
import com.hellofresh.oms.orderManagement.generated.api.model.GrnDeliveryLineUnitOfMeasureEnum.UNIT
import com.hellofresh.oms.orderManagementHttp.order.integration.PurchaseOrderAbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.order.toDto
import java.time.LocalDateTime
import java.util.UUID
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.get
import uk.org.webcompere.modelassert.json.JsonAssertions.json

class GoodsReceivedNoteIntegrationTest : PurchaseOrderAbstractIntegrationTest() {
    @Test
    fun `should respond with 404 when the purchase order does not exits`() {
        mockMvc.get(GRNS_PATH, "123").andExpect {
            status { isNotFound() }
        }.andReturn()!!
    }

    @Test
    fun `should respond with 404 when the purchase order exist but there is no GRN for it`() {
        val purchaseOrder = savePurchaseOrderRevision(
            id = UUID.randomUUID(),
            poNumber = "2503NJ${IntRange(100000, 999999).random()}",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "any comment",
        )

        mockMvc.get(GRNS_PATH, purchaseOrder.poNumber).andExpect {
            status { isNotFound() }
        }.andReturn()!!
    }

    @Test
    @Suppress("LongMethod")
    fun `should respond with 200 and correct receivedQuantity for SKUs`() {
        val purchaseOrder = savePurchaseOrderRevision(
            id = UUID.randomUUID(),
            poNumber = "PO-12345",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "any comment",
        )
        val givenMarket = "dach"
        saveSupplier(
            id = purchaseOrder.supplierId,
            market = givenMarket,
        )
        val sku1 = saveSku(
            skuCode = "SKU-001",
            skuName = "Name of Sku",
            market = givenMarket,
        )
        val sku2 = saveSku(
            skuCode = "SKU-002",
            skuName = "Name of Sku",
            market = givenMarket,
        )
        val sku3 = saveSku(
            skuCode = "SKU-003",
            skuName = "Name of Sku",
            market = givenMarket,
        )
        val givenGrnId = UUID.randomUUID()
        val givenDeliveryId = UUID.randomUUID()
        val givenDeliveryId2 = UUID.randomUUID()
        val givenDeliveries = listOf(
            getDeliveryEntity(
                deliveryId = givenDeliveryId,
                grnId = givenGrnId,
                poDeliveryId = "${purchaseOrder.poNumber}_O1-001",
                deliveryLines = listOf(
                    getDeliveryLineEntity(
                        deliveryId = givenDeliveryId,
                        poDeliveryLineId = "${purchaseOrder.poNumber}_O1-001_L1",
                        skuCode = sku1.code,
                        palletizedQuantity = 5.0,
                    ),
                    getDeliveryLineEntity(
                        deliveryId = givenDeliveryId,
                        poDeliveryLineId = "${purchaseOrder.poNumber}_O1-001_L2",
                        skuCode = sku2.code,
                        palletizedQuantity = 6.0,
                    ),
                    getDeliveryLineEntity(
                        deliveryId = givenDeliveryId,
                        poDeliveryLineId = "${purchaseOrder.poNumber}_O1-001_L3",
                        skuCode = sku3.code,
                        palletizedQuantity = 7.0,
                    ),
                ),
            ),
            getDeliveryEntity(
                deliveryId = givenDeliveryId2,
                grnId = givenGrnId,
                poDeliveryId = "${purchaseOrder.poNumber}_O1-001",
                deliveryLines = listOf(
                    getDeliveryLineEntity(
                        deliveryId = givenDeliveryId2,
                        poDeliveryLineId = "${purchaseOrder.poNumber}_O1-002_L1",
                        skuCode = sku1.code,
                        palletizedQuantity = 3.0,
                    ),
                    getDeliveryLineEntity(
                        deliveryId = givenDeliveryId2,
                        poDeliveryLineId = "${purchaseOrder.poNumber}_O1-002_L2",
                        skuCode = sku2.code,
                        palletizedQuantity = 10.0,
                    ),
                    getDeliveryLineEntity(
                        deliveryId = givenDeliveryId,
                        poDeliveryLineId = "${purchaseOrder.poNumber}_O1-002_L3",
                        skuCode = sku3.code,
                        palletizedQuantity = 50.0,
                    ),
                ),
            ),
        )
        val grn = saveGrn(
            grnId = givenGrnId,
            poNumber = purchaseOrder.poNumber,
            grnState = STATE_OPEN,
            deliveries = givenDeliveries,
        )

        mockMvc.get(GRNS_PATH, purchaseOrder.poNumber)
            .andExpect {
                status { isOk() }
                content {
                    string(
                        json()
                            .where()
                            .keysInAnyOrder()
                            .arrayInAnyOrder()
                            .isEqualTo(
                                mapOf(
                                    "id" to grn.id,
                                    "summarized_delivery_lines" to listOf(
                                        mapOf(
                                            "sku_code" to sku1.code,
                                            "sku_name" to sku1.name,
                                            "received_quantity" to 8.0,
                                            "sku_uom" to UNIT.name,
                                        ),
                                        mapOf(
                                            "sku_code" to sku2.code,
                                            "sku_name" to sku2.name,
                                            "received_quantity" to 16.0,
                                            "sku_uom" to UNIT.name,
                                        ),
                                        mapOf(
                                            "sku_code" to sku3.code,
                                            "sku_name" to sku3.name,
                                            "received_quantity" to 57.0,
                                            "sku_uom" to UNIT.name,
                                        )
                                    ),
                                    "status" to grn.state.toDto(),
                                )
                            ),
                    )
                }
            }
    }

    fun getDeliveryEntity(
        deliveryId: UUID,
        grnId: UUID,
        poDeliveryId: String,
        deliveryLines: List<PurchaseOrderDeliveryLine>,
    ) = PurchaseOrderDelivery(
        id = deliveryId,
        grnId = grnId,
        poDeliveryId = poDeliveryId,
        deliveryTime = LocalDateTime.now(),
        state = DELIVERY_STATE_RECEIVED,
        expectedDeliveryStartTime = null,
        expectedDeliveryEndTime = null,
        lines = deliveryLines,
    )

    fun getDeliveryLineEntity(
        deliveryId: UUID,
        poDeliveryLineId: String,
        skuCode: String,
        palletizedQuantity: Double,
    ) = PurchaseOrderDeliveryLine(
        id = UUID.randomUUID(),
        grnPurchaseOrderDeliveryId = deliveryId,
        poDeliveryLineId = poDeliveryLineId,
        skuCode = skuCode,
        unloadedQuantity = 0.0,
        receivedQuantity = 0.0,
        expectedQuantity = 0.0,
        rejectedQuantity = 0.0,
        palletizedQuantity = palletizedQuantity,
        expirationDate = null,
        supplierLotNumber = "LOT-001",
        caseSize = null,
        state = DELIVERY_LINE_STATE_CLOSED,
        skuUom = UNIT_OF_MEASURE_UNIT,
    )

    companion object {
        private const val GRNS_PATH = "/grns/{poNumber}"
    }
}
