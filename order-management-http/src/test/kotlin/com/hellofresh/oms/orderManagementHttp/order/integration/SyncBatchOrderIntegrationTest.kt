package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.orderManagement.generated.api.model.CreatePurchaseOrderResponse
import com.hellofresh.oms.orderManagementHttp.order.getJsonPoRequestBody
import com.hellofresh.oms.orderManagementHttp.order.getJsonWithZeroPricePoRequestBody
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.comparesEqualTo
import org.junit.jupiter.api.Test
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.post

@Sql(
    scripts = [
        "/data/suppliers.sql",
        "/data/emergencyReasons.sql",
        "/data/purchaseOrderSku.sql",
    ],
)
@AutoConfigureWireMock(stubs = ["classpath:/stubs"])
class SyncBatchOrderIntegrationTest : PurchaseOrderAbstractIntegrationTest() {
    @Test
    fun `should create new order with pallet based line item`() {
        // given
        val body = getJsonPoRequestBody()

        // when
        val result = mockMvc.post(ORDERS_PATH) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }.andReturn()!!

        // then
        val purchaseOrderResponse = objectMapper.readValue(
            result.response.contentAsString,
            CreatePurchaseOrderResponse::class.java,
        )
        val purchaseOrder = purchaseOrderRepository.findById(purchaseOrderResponse.id!!)
        assertTrue(purchaseOrder.isPresent)
        assertEquals(
            10,
            purchaseOrder.get().orderItems.first {
                it.packaging.packagingType == PackagingType.PALLET_TYPE
            }.casesPerPallet,
        )
    }

    @Test
    fun `should create new order with zero price`() {
        // given
        val body = getJsonWithZeroPricePoRequestBody()

        // when
        val result = mockMvc.post(ORDERS_PATH) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }.andReturn()!!

        // then
        val purchaseOrderResponse = objectMapper.readValue(
            result.response.contentAsString,
            CreatePurchaseOrderResponse::class.java,
        )
        val purchaseOrder = purchaseOrderRepository.findById(purchaseOrderResponse.id!!)

        assertTrue(purchaseOrder.isPresent)
        assertThat(purchaseOrder.get().totalPrice.amount, comparesEqualTo(BigDecimal.ZERO))
        assertThat(purchaseOrder.get().orderItems.first().price.amount, comparesEqualTo(BigDecimal.ZERO))
    }

    @Test
    fun `should create a new order from API`() {
        // given
        val body = getJsonPoRequestBody()

        // when
        val result = mockMvc.post(ORDERS_PATH) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }.andReturn()!!

        // then
        val purchaseOrderResponse = objectMapper.readValue(
            result.response.contentAsString,
            CreatePurchaseOrderResponse::class.java,
        )
        val purchaseOrder = purchaseOrderRepository.findById(purchaseOrderResponse.id!!)
        assertTrue(purchaseOrder.isPresent)
        assertEquals("test comment", purchaseOrder.get().comment)
    }

    companion object {
        private const val ORDERS_PATH = "/orders"
        private val APPLICATION_CONTENT = MediaType.parseMediaType("application/json")
    }
}
