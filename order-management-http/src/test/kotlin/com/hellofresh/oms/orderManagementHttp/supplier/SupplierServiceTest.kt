package com.hellofresh.oms.orderManagementHttp.supplier

import com.hellofresh.oms.model.ShipMethod
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.supplier.Supplier
import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum.MINUS_NAME
import com.hellofresh.oms.orderManagementHttp.supplier.Fixture.supplierEntity
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierShippingMethodRepository
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierSimpleRepositoryImpl
import com.hellofresh.oms.orderManagementHttp.supplier.service.SupplierService
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl

@ExtendWith(MockitoExtension::class)
class SupplierServiceTest {

    @Mock
    private lateinit var supplierSimpleRepository: SupplierSimpleRepositoryImpl

    @Mock
    @Suppress("UnusedPrivateProperty") // Used for mocking
    private lateinit var supplierRepository: SupplierRepository

    @Mock
    private lateinit var shipMethodRepository: SupplierShippingMethodRepository

    @InjectMocks
    private lateinit var supplierService: SupplierService

    @Test
    fun `should return requested suppliers when supplier exists`() {
        // Given
        val supplierEntityList = mutableListOf<Supplier>()
        repeat(3) { index ->
            supplierEntityList.add(
                supplierEntity.copy(
                    id = UUID.randomUUID(),
                    name = "Supplier $index",
                ),
            )
        }
        whenever(supplierSimpleRepository.findAllSuppliersSimplePaginated(any()))
            .thenReturn(PageImpl(supplierEntityList))

        // When
        val result = supplierService.getSuppliers(
            market = "us",
            search = "",
            pageNumber = 0,
            pageSize = 10,
            sort = MINUS_NAME,
        )

        // Then
        assert(result.suppliers.isNotEmpty())
        assertEquals(result.suppliers.size, 3)
        result.suppliers.forEach { supplier ->
            val filteredSupplier = supplierEntityList.first { it.id == supplier.id }
            assertEquals(supplier.name, filteredSupplier.name)
            assertEquals(supplier.code, filteredSupplier.code)
            assertEquals(supplier.market, filteredSupplier.market)
            assertEquals(supplier.address.countryCode, filteredSupplier.supplierAddress.country)
            assertEquals(supplier.address.city, filteredSupplier.supplierAddress.city)
            assertEquals(supplier.address.postalCode, filteredSupplier.supplierAddress.postCode)
            assertEquals(supplier.address.address, filteredSupplier.supplierAddress.address)
            assertEquals(supplier.address.number, filteredSupplier.supplierAddress.number)
        }
        assertEquals(result.totalElements, 3)
    }

    @Test
    fun `should return empty list when nothing is found`() {
        // Given
        whenever(supplierSimpleRepository.findAllSuppliersSimplePaginated(any()))
            .thenReturn(Page.empty())

        // When
        val result = supplierService.getSuppliers(
            market = "us",
            search = "",
            pageNumber = 0,
            pageSize = 10,
            sort = MINUS_NAME,
        )

        // Then
        assert(result.suppliers.isEmpty())
        assertEquals(result.totalElements, 0)
        assertEquals(result.pageSize, 0)
    }

    @Test
    fun `should return shipping methods`() {
        // Given
        val supplierId = UUID.randomUUID()
        val shipmentMethods: List<ShipMethod> = listOf(
            ShipMethod(
                uuid = UUID.randomUUID(),
                supplierId = UUID.randomUUID(),
                dcCode = "AA",
                method = VENDOR,
                market = "us",
            ),
            ShipMethod(
                uuid = UUID.randomUUID(),
                supplierId = UUID.randomUUID(),
                dcCode = "BB",
                method = VENDOR,
                market = "us",
            ),
        )

        // When
        whenever(shipMethodRepository.findAllBySupplierId(supplierId))
            .thenReturn(shipmentMethods)

        // Then
        val result = supplierService.getSupplierShippingMethods(supplierId)

        assertEquals(2, result.size)
        assertEquals(shipmentMethods[0].uuid, result[0].id)
        assertEquals(shipmentMethods[0].supplierId, result[0].supplierId)
        assertEquals(shipmentMethods[0].dcCode, result[0].dcCode)
        assertEquals(shipmentMethods[0].method, result[0].shippingMethod)
        assertEquals(shipmentMethods[0].market, result[0].market)

        assertEquals(shipmentMethods[1].uuid, result[1].id)
        assertEquals(shipmentMethods[1].supplierId, result[1].supplierId)
        assertEquals(shipmentMethods[1].dcCode, result[1].dcCode)
        assertEquals(shipmentMethods[1].method, result[1].shippingMethod)
        assertEquals(shipmentMethods[1].market, result[1].market)
    }
}
