package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.WorkerActionData.CreatePurchaseOrder
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.oms.orderManagement.generated.api.model.CreatePurchaseOrderResponse
import com.hellofresh.oms.orderManagementHttp.configuration.consumeAtLeast
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.workerAction.WorkerActionRepository
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import com.statsig.sdk.Statsig
import java.time.Duration
import java.util.UUID
import org.apache.kafka.clients.consumer.Consumer
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.post

@SpringBootTest(
    properties = [
        "topics.purchase-order=public.supply.procurement.purchase-order.v2-create-test"
    ]
)
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
class CreatePurchaseOrderIntegrationTest : PurchaseOrderAbstractIntegrationTest() {

    @Autowired
    private lateinit var workerActionRepository: WorkerActionRepository

    @Value("\${topics.purchase-order}")
    private lateinit var purchaseOrderTopicName: String

    @Value("\${statsig.gates.enable-po-topic-v2}")
    private lateinit var enablePublisherForPoTopicStatsigGate: String

    @Autowired
    private lateinit var purchaseOrderConsumer: Consumer<String, PurchaseOrderEvent>

    @BeforeEach
    override fun setUp() {
        super.setUp()
        workerActionRepository.deleteAll()
        Statsig.overrideGate(enablePublisherForPoTopicStatsigGate, true)
        purchaseOrderConsumer.subscribe(listOf(purchaseOrderTopicName))
    }

    @AfterEach
    fun tearDown() {
        purchaseOrderConsumer.unsubscribe()
    }

    @Test
    @Suppress("LongMethod")
    fun `should respond with CREATED given a valid request is provided and schedule a CREATE_ORDER task and publish a Kafka message`() {
        // given
        val givenMarket = "us"
        val supplierId = UUID.randomUUID()
        val skuId = UUID.randomUUID()
        val emergencyReasonId = UUID.randomUUID()
        val dcCode = "HN"

        saveSupplier(id = supplierId, market = givenMarket)
        saveSku(id = skuId, skuCode = "SKU-123", skuName = "Test SKU", market = givenMarket)
        saveEmergencyReason(id = emergencyReasonId, name = "Test reason", market = givenMarket)
        saveDistributionCenter(code = dcCode, market = givenMarket)

        // when
        val response = postCreateOrder(
            content = """
                {
                    "delivery_window": {
                        "start": "2021-10-01T08:00:00Z",
                        "end": "2021-10-01T10:00:00Z"
                    },
                    "order_items": [
                        {
                          "sku_id": "$skuId",
                          "buffer_percent": 1,
                          "packaging": {
                            "packaging_type": "CASE",
                            "number_of_cases": 11,
                            "units_per_case": 12,
                            "price_per_case": {
                              "amount": "10",
                              "currency": "EUR"
                            },
                            "uom": "UNIT"
                          }
                        }
                    ],
                    "delivery_address": {
                        "address": "Hauptstrasse",
                        "number": "132",
                        "postal_code": "10435",
                        "city": "Berlin",
                        "region": "Berlin",
                        "country_code": "DE"
                    },
                    "dc_code": "$dcCode",
                    "dc_week": "2021-W01",
                    "supplier_id": "$supplierId",
                    "shipping_method": "VENDOR",
                    "emergency_reason_id": "$emergencyReasonId",
                    "comment": "test comment"
                    }
            """.trimIndent(),
        ).andExpect {
            // then
            status { isOk() }
        }.andReturn()

        val poId = objectMapper.readValue(
            response.response.contentAsString,
            CreatePurchaseOrderResponse::class.java,
        ).id

        requireNotNull(poId)

        // and then
        assertThat(
            workerActionRepository.findAll().map { it.actionType to it.getPayload() },
            equalTo(
                listOf(
                    WorkerActionType.CREATE_ORDER to CreatePurchaseOrder(poId),
                ),
            ),
        )

        // and then
        val createdPurchaseOrder = purchaseOrderRepository.findByIdOrNull(poId)
        assertThat(
            createdPurchaseOrder?.origin,
            equalTo(Origin.MANUAL),
        )

        // and then
        val messages = purchaseOrderConsumer.consumeAtLeast(1, Duration.ofSeconds(10))
        assertThat(messages.map { it.key() }, equalTo(listOf(createdPurchaseOrder?.poNumber)))
    }

    private fun postCreateOrder(content: String) = mockMvc.post("/orders") {
        contentType = MediaType.APPLICATION_JSON
        this.content = content
    }
}
