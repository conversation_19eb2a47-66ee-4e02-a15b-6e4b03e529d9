package com.hellofresh.oms.orderManagementHttp.viewstate.intake.integration

import org.junit.jupiter.api.Test
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlMergeMode
import org.springframework.test.context.jdbc.SqlMergeMode.MergeMode
import org.springframework.test.json.JsonCompareMode
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath

class GetViewStateControllerTest : AbstractViewStateControllerTest() {

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `should list only view states for current user matching the resource parameter`() {
        mockMvc.get(VIEW_STATES_PATH) {
            param("resource", "orders")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            jsonPath("$.items").isArray
            jsonPath("$.items.length()").value(2)
            content {
                json(
                    """
                    {
                        "items": [
                            {
                                "name": "My Orders View"
                            },
                            {
                                "name": "Completed Orders"
                            }
                        ]
                    }
                    """.trimIndent(),
                    JsonCompareMode.LENIENT,
                )
            }
        }
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `should return empty list when  non-existent resource`() {
        mockMvc.get(VIEW_STATES_PATH) {
            contentType = APPLICATION_CONTENT
            param("resource", "non-existent")
        }.andExpect {
            status { isOk() }
            jsonPath("$.items").isArray
            jsonPath("$.items.length()").value(0)
        }
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `should return view state by id for the current user`() {
        mockMvc.get("$VIEW_STATES_PATH/$MY_VIEW_STATE_ID") {
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            content {
                json(
                    """
                    {
                        "id": 1,
                        "resource": "orders",
                        "name": "My Orders View",
                        "state": {
                            "filterState": {
                                "status": "pending"
                            }
                        }
                    }
                    """.trimIndent(),
                    JsonCompareMode.LENIENT,
                )
            }
        }
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `should return 404 when trying to fetch a view state for another user`() {
        mockMvc.get("$VIEW_STATES_PATH/$OTHER_USER_VIEW_STATE_ID") {
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isNotFound() }
        }
    }
}
