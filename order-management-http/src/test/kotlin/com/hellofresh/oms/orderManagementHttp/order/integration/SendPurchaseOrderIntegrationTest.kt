package com.hellofresh.oms.orderManagementHttp.order.integration

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.put
import com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.orderManagementHttp.configuration.consumeAtLeast
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import com.statsig.sdk.Statsig
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import org.apache.kafka.clients.consumer.Consumer
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.hasItem
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.MediaType.APPLICATION_JSON
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.web.servlet.post
import uk.org.webcompere.modelassert.json.JsonAssertions.json

@SpringBootTest(
    properties = [
        "topics.purchase-order=public.supply.procurement.purchase-order.v2-send-test",
    ],
)
@AutoConfigureWireMock
class SendPurchaseOrderIntegrationTest : PurchaseOrderAbstractIntegrationTest() {

    @Value("\${topics.purchase-order}")
    private lateinit var purchaseOrderTopicName: String

    @Value("\${statsig.gates.enable-po-topic-v2}")
    private lateinit var enablePublisherForPoTopicStatsigGate: String

    @Autowired
    private lateinit var purchaseOrderConsumer: Consumer<String, PurchaseOrderEvent>

    @MockitoBean
    private lateinit var clock: Clock

    @Autowired
    lateinit var wireMock: WireMockServer

    @BeforeEach
    override fun setUp() {
        wireMock.resetAll()

        wireMock.stubFor(
            post("/token")
                .willReturn(
                    aResponse()
                        .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .withBody(
                            """
                            {
                                "token_type": "Bearer",
                                "access_token": "eyJleHAiOjE2Mzg5MDg4NTMsImlhdCI6MTYzODg4NzI1MywiaXNzIjoiMWI1ZWVkMWYtMDNkZC00ZjlkLThkMzAtMmEwOGY5Mzk3NjU1IiwianRpIjoiMWZiYjBmMmEtMmI4ZS00NjJlLTljYTctMWZhNDUzZWYxOTM1In0",
                                "expires_in": 21600
                            }
                            """.trimIndent(),
                        ),
                ),
        )

        Statsig.overrideGate(enablePublisherForPoTopicStatsigGate, true)
        purchaseOrderConsumer.subscribe(listOf(purchaseOrderTopicName))
        whenever(clock.instant()).thenReturn(Instant.parse("2024-12-11T15:00:00Z"))
        whenever(clock.zone).thenReturn(ZoneId.of("UTC"))
    }

    @AfterEach
    fun tearDown() {
        purchaseOrderConsumer.unsubscribe()
    }

    @Test
    fun `should respond with 409 when PO is not synced`() {
        // given
        val purchaseOrder =
            savePurchaseOrderRevision(
                poNumber = "2501GW${IntRange(100000, 999999).random()}",
                version = 1,
                supplierId = UUID.randomUUID(),
                isSynced = false,
                comment = "Not synced PO",
            )

        // when
        mockMvc.post("$ORDERS_PATH/{po_number}/revisions/{po_id}/send", purchaseOrder.poNumber, purchaseOrder.id) {
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isConflict() }
            content {
                string(
                    json()
                        .at("/message")
                        .textContains("Purchase Order is not synced: ${purchaseOrder.id}"),
                )
            }
        }
    }

    @Test
    fun `should respond with 404 when PO does not exist`() {
        // given
        val poNumber = "2501GW${IntRange(100000, 999999).random()}"
        val poUUid = UUID.randomUUID()

        // when
        mockMvc.post("$ORDERS_PATH/{po_number}/revisions/{po_id}/send", poNumber, poUUid) {
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isNotFound() }
            content {
                string(
                    json()
                        .at("/message")
                        .textContains("Purchase Order not found: $poUUid"),
                )
            }
        }
    }

    @Test
    fun `should respond with 409 when OT responds with error`() {
        // given
        val purchaseOrder = savePurchaseOrderRevision(
            poNumber = "2501GW${IntRange(100000, 999999).random()}",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "Normal PO",
            isSynced = true,
        )

        wireMock.stubFor(
            put(urlPathMatching("/purchase-orders/(.*)/send"))
                .willReturn(
                    aResponse()
                        .withStatus(500)
                        .withBody(
                            """{
                            |"status" : 500,
                            |"message" : "The email must not be empty"
                            |}
                            """.trimMargin(),
                        )
                        .withHeader("Content-Type", "application/json"),
                ),
        )

        // when
        mockMvc.post("$ORDERS_PATH/{po_number}/revisions/{po_id}/send", purchaseOrder.poNumber, purchaseOrder.id) {
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isConflict() }
            content {
                string(
                    json()
                        .at("/message")
                        .textContains("OT responded with error: The email must not be empty"),
                )
            }
        }
    }

    @Test
    fun `should send a purchase order`() {
        // given
        val emergencyReason = saveEmergencyReason(name = "Manual PO")

        val supplier = saveSupplier(market = "us")

        val dc = saveDistributionCenter(code = "HW", market = "us")

        val purchaseOrder =
            savePurchaseOrderRevision(
                poNumber = "2501GW${IntRange(100000, 999999).random()}",
                version = 1,
                supplierId = supplier.id,
                comment = "Normal PO",
                isSynced = true,
                emergencyReasonId = emergencyReason.uuid,
                dcCode = dc.code,
            )

        wireMock.stubFor(
            put(urlPathMatching("/purchase-orders/(.*)/send"))
                .willReturn(
                    aResponse().withStatus(200),
                ),
        )

        // when
        mockMvc.post("${ORDERS_PATH}/{po_number}/revisions/{po_id}/send", purchaseOrder.poNumber, purchaseOrder.id) {
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isOk() }
        }

        // then
        val outboxItem = outboxRepository.findAll().first { it.poNumber == purchaseOrder.poNumber }!!

        assertEquals(purchaseOrder.poNumber, outboxItem.poNumber)
        assertEquals(purchaseOrder.id, outboxItem.poId)
        assertEquals(OutboxItemStatus.SENT, outboxItem.status)
        assertEquals(UUID.fromString("3d40b713-b378-40cf-8ed8-dffbd6cfa8df"), outboxItem.userId)
        assertEquals("<EMAIL>", outboxItem.userEmail)
        assert(outboxItem.createdAt == outboxItem.lastStatusChangeAt)

        // and then
        val messages = purchaseOrderConsumer.consumeAtLeast(1, Duration.ofSeconds(10))
        assertThat(
            messages.map { it.key() to it.value().eventType },
            hasItem(purchaseOrder.poNumber to PurchaseOrderEvent.Event.EVENT_STATUS_UPDATED),
        )
        assertThat(
            messages.find { it.key() == purchaseOrder.poNumber }?.value()?.payload?.metadata?.state,
            Matchers.equalTo(PurchaseOrderEvent.PurchaseOrder.State.STATE_SENT),
        )

        // and then
        val persistedPo = purchaseOrderRepository.findById(purchaseOrder.id).getOrNull()
        assertThat(persistedPo?.isSynced, Matchers.equalTo(false))
        assertThat(persistedPo?.sendTime, Matchers.equalTo(LocalDateTime.now(clock)))
    }

    companion object {
        private const val ORDERS_PATH = "/orders"
    }
}
