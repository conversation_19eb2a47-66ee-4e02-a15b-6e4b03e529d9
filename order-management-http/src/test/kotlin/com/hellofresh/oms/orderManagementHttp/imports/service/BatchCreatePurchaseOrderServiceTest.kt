package com.hellofresh.oms.orderManagementHttp.imports.service

import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterAddressType
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagement.generated.api.model.LineError
import com.hellofresh.oms.orderManagementHttp.imports.IncorrectImportFileException
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.BatchCreatePurchaseOrderAggregator
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.PurchaseOrderDto
import com.hellofresh.oms.orderManagementHttp.imports.aggregator.PurchaseOrderItemDto
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchCreatePurchaseOrderParser
import com.hellofresh.oms.orderManagementHttp.imports.parser.BatchCreatePurchaseOrderParserResponse
import com.hellofresh.oms.orderManagementHttp.imports.validator.BatchCreatePurchaseOrderValidator
import com.hellofresh.oms.orderManagementHttp.imports.validator.BatchCreatePurchaseOrderValidatorResponse
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierSimpleEntity
import com.hellofresh.oms.orderManagementHttp.order.getTestUser
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderService
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomainPrecision.Precision4
import com.hellofresh.oms.orderManagementHttp.workerAction.SyncBatchOrderScheduler
import java.io.InputStream
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class BatchCreatePurchaseOrderServiceTest {
    @Mock
    lateinit var parser: BatchCreatePurchaseOrderParser

    @Mock
    lateinit var validator: BatchCreatePurchaseOrderValidator

    @Mock
    lateinit var aggregator: BatchCreatePurchaseOrderAggregator

    @Mock
    lateinit var purchaseOrderService: PurchaseOrderService

    @Mock
    lateinit var syncBatchOrderScheduler: SyncBatchOrderScheduler

    @InjectMocks
    lateinit var subject: BatchCreatePurchaseOrderService

    @Test
    fun `should throw an exception when receiving errors from parser`() {
        // given
        val csv: InputStream = mock()
        whenever(parser.parse(csv))
            .thenReturn(BatchCreatePurchaseOrderParserResponse(emptyList(), listOf(LineError(1, "error"))))

        // then
        assertThrows<IncorrectImportFileException> {
            subject.processBatchImport(csv, emptyList())
        }
    }

    @Test
    fun `should throw an exception when parser has no errors but received errors from validator`() {
        // given
        val csv: InputStream = mock()
        whenever(parser.parse(csv))
            .thenReturn(BatchCreatePurchaseOrderParserResponse(emptyList(), emptyList()))
        whenever(validator.validate(emptyList(), emptyList()))
            .thenReturn(
                BatchCreatePurchaseOrderValidatorResponse(
                    validLines = emptyList(),
                    lineErrors = setOf(LineError(1, "error")),
                ),
            )

        // then
        assertThrows<IncorrectImportFileException> {
            subject.processBatchImport(csv, emptyList())
        }
    }

    @Test
    @Suppress("LongMethod")
    fun `should map correctly from dto to entity when persisting`() {
        // given
        val givenSupplier = getSupplierSimpleEntity()
        val givenFirstItem = PurchaseOrderItemDto(
            sku = getSkuEntity(),
            bufferValue = BigDecimal.valueOf(5),
            packagingType = PackagingType.CASE_TYPE,
            orderSize = BigDecimal.valueOf(10),
            caseSize = BigDecimal.valueOf(100),
            price = BigDecimal.valueOf(22), // 22 * (10 #orderSize#) = 220
            uom = UOM.LBS,
        )
        val givenSecondItem = PurchaseOrderItemDto(
            sku = getSkuEntity(),
            bufferValue = BigDecimal.valueOf(30),
            packagingType = PackagingType.UNIT_TYPE,
            orderSize = BigDecimal.valueOf(100),
            caseSize = BigDecimal.ZERO,
            price = BigDecimal.valueOf(5), // 5 * (100 #orderSize#) = 500
            uom = UOM.UNIT,
        )

        val expectedTotalPriceForOrder = BigDecimal.valueOf(220 + 500)
            .setScale(Precision4.value, RoundingMode.HALF_EVEN)

        val givenPurchaseOrderDto = PurchaseOrderDto(
            yearWeek = YearWeek(2022, 1),
            distributionCenter = getDistributionCenterEntity(
                addresses = listOf(
                    DistributionCenterAddress(
                        id = UUID.randomUUID(),
                        dcCode = "NJ",
                        number = "60",
                        address = "60 Lister Avenue",
                        zip = "07105",
                        city = "Newark",
                        state = "NJ",
                        company = "HelloFresh United States - NJ",
                        type = DistributionCenterAddressType.DELIVERY,
                        createdAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now(),
                        countryCode = "US",
                    ),
                ),
            ),
            shipMethod = ShipMethodEnum.VENDOR,
            supplier = givenSupplier,
            deliveryWindow = DeliveryWindowDto(
                start = LocalDateTime.now(),
                end = LocalDateTime.now(),
            ),
            emergencyReason = getEmergencyReasonEntity(),
            orderItems = listOf(givenFirstItem, givenSecondItem),
        )

        val expectedPoNumber = "PO-1234"
        val expectedPoId = UUID.randomUUID()
        whenever(purchaseOrderService.generatePoNumber(any(), any()))
            .thenReturn(expectedPoNumber)
        whenever(purchaseOrderService.savePurchaseOrder(any()))
            .thenReturn(getPurchaseOrderEntity(id = expectedPoId, poNumber = expectedPoNumber))

        // when
        val actualPurchaseOrder = subject.persistPurchaseOrder(
            purchaseOrder = givenPurchaseOrderDto,
            user = getTestUser(),
        )

        // then
        val saveArgumentCaptor = argumentCaptor<PurchaseOrder>()
        verify(purchaseOrderService).savePurchaseOrder(saveArgumentCaptor.capture())
        val savedPurchaseOrder = saveArgumentCaptor.firstValue
        assertThat(savedPurchaseOrder.poNumber, equalTo(expectedPoNumber))
        assertThat(savedPurchaseOrder.totalPrice.amount, equalTo(expectedTotalPriceForOrder))
        assertThat(actualPurchaseOrder.id, equalTo(expectedPoId))
        assertThat(actualPurchaseOrder.poNumber, equalTo(expectedPoNumber))
    }
}
