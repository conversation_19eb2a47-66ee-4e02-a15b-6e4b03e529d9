package com.hellofresh.oms.orderManagementHttp.orderOverview.intake

import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.imports.service.ImportHistoryService
import com.hellofresh.oms.orderManagementHttp.orderOverview.Fixture
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.PurchaseOrderOverviewService
import com.hellofresh.oms.orderManagementHttp.sku.SkuService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@ExtendWith(SpringExtension::class)
@WebMvcTest(controllers = [PurchaseOrderOverviewController::class])
class PurchaseOrderOverviewControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockitoBean
    private lateinit var purchaseOrderOverviewServiceMock: PurchaseOrderOverviewService

    //    Following are unnecessary for this test, but are required since converters are not defined properly
    @MockitoBean
    @Suppress("UnusedPrivateProperty")
    private lateinit var skuService: SkuService

    @MockitoBean
    @Suppress("UnusedPrivateProperty")
    private lateinit var importHistoryService: ImportHistoryService

    @Test
    @WithJWTUser
    fun `should return 200 OK when request is valid`() {
        val dcWeek = listOf("2023-W01")
        val brand = "BRAND"
        val dcCode = "DC_CODE"

        whenever(
            purchaseOrderOverviewServiceMock.getOverview(
                dcCode = dcCode,
                brand = brand,
                dcWeeks = dcWeek,
            ),
        ).thenReturn(listOf(Fixture.getPurchaseOrderOverviewModel()))

        mockMvc.perform(
            get(PO_OVERVIEW_PATH)
                .param("dc_weeks", dcWeek.joinToString(","))
                .param("brand", brand)
                .param("dc_code", dcCode),
        )
            .andExpect(status().isOk)
    }

    @Test
    @WithJWTUser
    fun `should return Bad Request when request is not passing correct parameters`() {
        whenever(
            purchaseOrderOverviewServiceMock.getOverview(
                any(),
                any(),
                any(),
            ),
        ).thenReturn(listOf(Fixture.getPurchaseOrderOverviewModel()))

        mockMvc.perform(
            get(PO_OVERVIEW_PATH)
                .param("dc_weeks", listOf("2025-W01").joinToString(","))
                .param("dc_code", "DC_CODE"),
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `should return Unauthorized when request does not contain JWT token`() {
        val dcWeek = listOf("2023-W01")
        val brand = "BRAND"
        val dcCode = "DC_CODE"

        whenever(
            purchaseOrderOverviewServiceMock.getOverview(
                dcCode = dcCode,
                brand = brand,
                dcWeeks = dcWeek,
            ),
        ).thenReturn(listOf(Fixture.getPurchaseOrderOverviewModel()))

        mockMvc.perform(
            get(PO_OVERVIEW_PATH)
                .param("dc_weeks", dcWeek.joinToString(","))
                .param("brand", brand)
                .param("dc_code", dcCode),
        )
            .andExpect(status().isUnauthorized)
    }

    companion object {
        private const val PO_OVERVIEW_PATH = "/orders/overview"
    }
}
