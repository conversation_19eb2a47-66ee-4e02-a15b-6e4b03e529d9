package com.hellofresh.oms.orderManagementHttp.deliveryReason

import com.hellofresh.oms.model.ChangeReason
import com.hellofresh.oms.model.ChangeReasonType
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest

@DataJpaTest
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
class ChangeReasonRepositoryTest(
    @Autowired val subject: ChangeReasonRepository
) {

    @Test
    fun `should persist ChangeReason`() {
        // given
        val changeReasonId = UUID.randomUUID()
        val expectedChangeReason = ChangeReason(
            changeReasonId,
            "Test Change Reason",
            listOf("dach", "bach", "mach", "sach"),
            ChangeReasonType.ORDER_ITEM_CHANGE,
        )

        val actualChangeReason = subject.save(expectedChangeReason)
        val retrievedChangeReason = subject.findAllByReasonType(ChangeReasonType.ORDER_ITEM_CHANGE).first()

        assertEquals(expectedChangeReason, actualChangeReason)
        assertEquals(expectedChangeReason, retrievedChangeReason)
    }
}
