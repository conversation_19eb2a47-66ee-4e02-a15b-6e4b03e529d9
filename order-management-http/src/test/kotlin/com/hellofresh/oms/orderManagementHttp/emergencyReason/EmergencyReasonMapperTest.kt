package com.hellofresh.oms.orderManagementHttp.emergencyReason

import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.orderManagementHttp.emergencyReason.Fixture.MARKET_US
import java.util.UUID.randomUUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll

class EmergencyReasonMapperTest {

    @Test
    fun `should map list of emergency reason entity to emergency reason DTO`() {
        // given
        val emergencyReasons = listOf(
            EmergencyReason(
                uuid = randomUUID(),
                name = "Repacking- Service",
                market = MARKET_US,
                disabled = false,
            ),
            EmergencyReason(
                uuid = randomUUID(),
                name = "Thanksgiving",
                market = MARKET_US,
                disabled = false,
            ),
        )

        // when
        val actualEmergencyReasons = emergencyReasons.mapToDto()

        // then
        assertAll(
            { assertEquals(emergencyReasons[0].uuid, actualEmergencyReasons[0].uuid) },
            { assertEquals(emergencyReasons[0].name, actualEmergencyReasons[0].name) },
            { assertEquals(emergencyReasons[0].market, actualEmergencyReasons[0].market) },

            { assertEquals(emergencyReasons[1].uuid, actualEmergencyReasons[1].uuid) },
            { assertEquals(emergencyReasons[1].name, actualEmergencyReasons[1].name) },
            { assertEquals(emergencyReasons[1].market, actualEmergencyReasons[1].market) },
        )
    }

    @Test
    fun `should map list of emergency reason DTO to emergency reason api response`() {
        // given
        val emergencyReasonDTOs = listOf(
            EmergencyReasonDto(
                uuid = randomUUID(),
                name = "Repacking- Service",
                market = MARKET_US,
            ),
            EmergencyReasonDto(
                uuid = randomUUID(),
                name = "Thanksgiving",
                market = MARKET_US,
            ),
        )

        // when
        val actualEmergencyReasonApiResponse = emergencyReasonDTOs.mapToApiResponse()

        // then
        assertAll(
            { assertEquals(emergencyReasonDTOs[0].uuid, actualEmergencyReasonApiResponse[0].id) },
            { assertEquals(emergencyReasonDTOs[0].name, actualEmergencyReasonApiResponse[0].name) },
            { assertEquals(emergencyReasonDTOs[0].market, actualEmergencyReasonApiResponse[0].market) },

            { assertEquals(emergencyReasonDTOs[1].uuid, actualEmergencyReasonApiResponse[1].id) },
            { assertEquals(emergencyReasonDTOs[1].name, actualEmergencyReasonApiResponse[1].name) },
            { assertEquals(emergencyReasonDTOs[1].market, actualEmergencyReasonApiResponse[1].market) },
        )
    }
}
