package com.hellofresh.oms.orderManagementHttp.comments.integration

import org.hamcrest.CoreMatchers
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Test
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlMergeMode
import org.springframework.test.context.jdbc.SqlMergeMode.MergeMode
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath

class GetCommentControllerTest : AbstractCommentControllerTest() {
    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `returns not found when required parameters are missing`() {
        mockMvc.get(COMMENTS_PATH) {
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isBadRequest() }
        }
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/comments.sql"])
    fun `returns comments based on filter criteria`() {
        mockMvc.get(COMMENTS_PATH) {
            param("dcs", "NJ", "VE")
            param("resource_type", "sku")
            param("domains", "test-domain")
            param("weeks", "2025-W10", "2025-W11")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            jsonPath("$.items").isArray
            jsonPath("$.items").value(hasSize<Any>(2))
            jsonPath("$.items[0].id").value("11111111-1111-1111-1111-111111111111")
            jsonPath("$.items[0].sourceId").value(CoreMatchers.nullValue())
            jsonPath("$.items[0].resourceType").value("sku")
            jsonPath("$.items[0].domain").value("test-domain")
        }
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/comments.sql"])
    fun `should map DCs from brands when DCs are not provided`() {
        // when/then
        mockMvc.get(COMMENTS_PATH) {
            param("resource_type", "sku")
            param("domains", "gpp-po-status")
            param("weeks", "2025-W10,2025-W11,2025-W12")
            param("brands", "HF", "GC")
        }.andExpect {
            status { isOk() }
            jsonPath("$.items").isArray
            jsonPath("$.items").value(hasSize<Any>(3))
            jsonPath("$.items[0].id").value("11111111-1111-1111-1111-111111111111")
            jsonPath("$.items[1].id").value("22222222-2222-2222-2222-222222222222")
            jsonPath("$.items[2].id").value("33333333-3333-3333-3333-333333333333")
        }
    }
}
