package com.hellofresh.oms.orderManagementHttp.distributionCenters

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.orderManagement.generated.api.model.DistributionCentersResponseDto
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.TestFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(
    executionPhase = BEFORE_TEST_METHOD,
    scripts = ["/data/distributionCenters.sql"],
)
@AutoConfigureWireMock(stubs = ["classpath:/stubs"])
class DistributionCentersIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @TestFactory
    fun `should filter by given market and active and visible DCs`() =
        listOf(
            Pair("gb", setOf("Z1")),
            Pair("us", setOf("Z5")),
        ).map { (givenMarket, expectedDcs) ->
            DynamicTest.dynamicTest(
                """should respond with [${expectedDcs.joinToString(",")}] for market [$givenMarket] """,
            ) {
                val result =
                    mockMvc.get("/distribution-centers") {
                        param("market", givenMarket)
                        contentType = V2_APPLICATION_CONTENT
                    }.andExpect {
                        status { isOk() }
                    }.andReturn()!!

                val listResponse =
                    objectMapper.readValue(
                        result.response.contentAsString,
                        DistributionCentersResponseDto::class.java,
                    )

                assertEquals(expectedDcs, listResponse.items?.map { it.code }?.toSet())
            }
        }

    companion object {
        private val V2_APPLICATION_CONTENT = MediaType.parseMediaType("application/json")
    }
}
