package com.hellofresh.oms.orderManagementHttp.uom

import com.hellofresh.oms.model.UOM.GAL
import com.hellofresh.oms.model.UOM.LBS
import com.hellofresh.oms.model.UnitOfMeasure
import java.util.UUID

object Fixture {
    fun getUnitOfMeasureEntities() = listOf(
        UnitOfMeasure(
            uuid = UUID.randomUUID(),
            name = "lbs",
            market = "us",
            type = "bulk",
            enumValue = LBS,
        ),
        UnitOfMeasure(
            uuid = UUID.randomUUID(),
            name = "gal",
            market = "us",
            type = "bulk",
            enumValue = GAL,
        ),
    )

    fun getUnitOfMeasureDto() = listOf(
        UnitOfMeasureDto(
            uuid = UUID.randomUUID(),
            name = "lbs",
            market = "us",
            type = "bulk",
            enumValue = LBS,
        ),
        UnitOfMeasureDto(
            uuid = UUID.randomUUID(),
            name = "gal",
            market = "us",
            type = "bulk",
            enumValue = GAL,
        ),
    )
}
