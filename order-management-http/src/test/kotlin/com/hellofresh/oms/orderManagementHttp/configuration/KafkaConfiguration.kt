package com.hellofresh.oms.orderManagementHttp.configuration

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractKafkaIntegrationTest.Companion.getKafkaBootstrapServers
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import java.time.Duration
import java.util.UUID
import org.apache.kafka.clients.consumer.Consumer
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.serialization.Deserializer
import org.apache.kafka.common.serialization.StringDeserializer
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean

private const val POLL_MILLIS = 250L

@TestConfiguration
class KafkaConfiguration {

    @Bean
    fun purchaseOrderConsumer(): Consumer<String, PurchaseOrderEvent> = KafkaConsumer(
        mapOf<String, Any>(
            ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to getKafkaBootstrapServers(),
            ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG to StringDeserializer::class.java,
            ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG to PurchaseOrderDeserializer::class.java,
            ConsumerConfig.CLIENT_ID_CONFIG to UUID.randomUUID().toString(),
            ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
            ConsumerConfig.GROUP_ID_CONFIG to "consumer-purchase-order-group-test",
        ),
    )

    class PurchaseOrderDeserializer : Deserializer<PurchaseOrderEvent> {
        override fun deserialize(topic: String, data: ByteArray): PurchaseOrderEvent =
            PurchaseOrderEvent.parseFrom(data)
    }
}

fun Consumer<String, PurchaseOrderEvent>.consumeAtLeast(numberOfRecords: Int, timeout: Duration):
    List<ConsumerRecord<String, PurchaseOrderEvent>> {
    val records = mutableListOf<ConsumerRecord<String, PurchaseOrderEvent>>()
    var millisLeft = timeout.toMillis()
    do {
        this.poll(Duration.ofMillis(POLL_MILLIS)).iterator().forEachRemaining { records.add(it) }
        millisLeft -= POLL_MILLIS
    } while (millisLeft > 0 && records.size < numberOfRecords)
    return records
}
