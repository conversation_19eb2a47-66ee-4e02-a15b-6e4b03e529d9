package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.orderManagementHttp.orderOverview.Fixture
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.BrandDcConfig
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.PurchaseOrderStatusViewRepository
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

class PurchaseOrderOverviewServiceTest {

    private val purchaseOrderStatusViewRepository = mock(PurchaseOrderStatusViewRepository::class.java)

    private val subject = PurchaseOrderOverviewService(
        poOverviewConfigs = PoOverviewConfigs(
            brandDcConfig = mapOf(
                "HF" to BrandDcConfig(
                    name = "HelloFresh",
                    dcs = listOf("NJ"),
                ),
            ),
        ),
        purchaseOrderStatusViewRepository = purchaseOrderStatusViewRepository
    )

    @Test
    fun `should successfully get purchase order overview`() {
        whenever(purchaseOrderStatusViewRepository.findAllPurchaseOrderDetails(any(), any()))
            .thenReturn(listOf(Fixture.createPurchaseOrderStatusDetails()))
        val overview = subject.getOverview(dcCode = "NJ", brand = "HF", dcWeeks = listOf("2025-W18"))

        assert(overview.isNotEmpty()) { "Expected non-empty overview" }
    }

    @Test
    fun `should use the dc brand config if dc codes are not presented`() {
        whenever(purchaseOrderStatusViewRepository.findAllPurchaseOrderDetails(any(), any()))
            .thenReturn(listOf(Fixture.createPurchaseOrderStatusDetails()))
        val overview = subject.getOverview(dcCode = null, brand = "HF", dcWeeks = listOf("2025-W18"))

        assert(overview.isNotEmpty()) { "Expected non-empty overview" }
    }
}
