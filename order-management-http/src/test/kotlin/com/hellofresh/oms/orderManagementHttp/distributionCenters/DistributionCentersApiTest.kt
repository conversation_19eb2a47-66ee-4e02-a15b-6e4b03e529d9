package com.hellofresh.oms.orderManagementHttp.distributionCenters

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.hamcrest.CoreMatchers
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(
    scripts = ["/data/add_dc.sql"],
)
class DistributionCentersApiTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Test
    fun `should get valid controller response`() {
        mockMvc.get("/distribution-centers") {
            param("market", "xx")
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", CoreMatchers.equalTo(3))
            jsonPath("$.items[0].code", CoreMatchers.equalTo("AA"))
            jsonPath("$.items[0].name", CoreMatchers.equalTo("Test DC 1"))
            jsonPath("$.items[1].name", CoreMatchers.equalTo("Test DC 2"))
            jsonPath("$.items[2].name", CoreMatchers.equalTo("Test DC 3"))
            jsonPath("$.items[0].market", CoreMatchers.equalTo("xx"))
            jsonPath("$.items[0].addresses.length()", CoreMatchers.equalTo(1))
            jsonPath("$.items[0].addresses[0].number", CoreMatchers.equalTo("111"))
            jsonPath("$.items[0].addresses[0].zip", CoreMatchers.equalTo("12345"))
            jsonPath("$.items[0].addresses[0].city", CoreMatchers.equalTo("A city"))
            jsonPath("$.items[0].addresses[0].state", CoreMatchers.equalTo("NJ"))
            jsonPath("$.items[0].addresses[0].type", CoreMatchers.equalTo("DELIVERY"))
            jsonPath("$.items[0].addresses[0].company", CoreMatchers.equalTo(null))
        }
    }

    @Test
    fun `should use lowercase market`() {
        mockMvc.get("/distribution-centers") {
            param("market", "XX")
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", CoreMatchers.equalTo(3))
        }
    }
}
