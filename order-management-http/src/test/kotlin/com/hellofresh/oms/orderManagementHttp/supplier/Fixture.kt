package com.hellofresh.oms.orderManagementHttp.supplier

import com.hellofresh.oms.model.supplier.Supplier
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.model.supplier.SupplierStatus.ACTIVE
import java.time.LocalDateTime
import java.util.UUID

object Fixture {
    val supplierEntityExtended = SupplierExtended(
        id = UUID.randomUUID(),
        parentId = UUID.randomUUID(),
        market = "us",
        code = 11111,
        name = "Supplier 1",
        status = ACTIVE,
        currency = "USD",
        type = "type",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        supplierAddress = SupplierAddress(
            city = "city",
            country = "us",
            state = "state",
            address = "Address 1",
            number = "1",
            postCode = "1111"
        ),
        contacts = setOf(),
        dcCodes = listOf("AA", "BB"),
        shipMethods = emptyList()
    )

    val supplierEntity = Supplier(
        id = UUID.randomUUID(),
        parentId = UUID.randomUUID(),
        market = "us",
        code = 11111,
        name = "Supplier 1",
        status = ACTIVE,
        currency = "USD",
        type = "type",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        supplierAddress = SupplierAddress(
            city = "city",
            country = "us",
            state = "state",
            address = "Address 1",
            number = "1",
            postCode = "1111",
        ),
        dcCodes = listOf("AA", "BB"),
    )
}
