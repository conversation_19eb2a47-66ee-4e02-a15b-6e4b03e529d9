package com.hellofresh.oms.orderManagementHttp.supplierSku

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.PriceRepository
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace.NONE
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
@DataJpaTest
@AutoConfigureTestDatabase(replace = NONE)
@Tag("integration")
@Sql(executionPhase = BEFORE_TEST_METHOD, value = ["/data/delete.sql", "/data/supplier-sku.sql"])
class PriceRepositoryTest(@Autowired val subject: PriceRepository) : AbstractIntegrationTest() {

    @Test
    fun `should select price only for given DC supplier-sku-id`() {
        // given
        val dc = "NJ"
        val supplierSkuIds = listOf(UUID.fromString("00000000-0000-0000-0000-000000000017"))
        // when
        val result = subject.findAllByDcCodesAndSupplierSkuIdIn(dc, supplierSkuIds)
        // then
        assertEquals(2, result.size)
        assertTrue(result.all { it.dcCodes.first() == dc })
        assertTrue(result.all { it.supplierSkuId == supplierSkuIds.first() })
    }

    @Test
    fun `should only return enabled prices`() {
        // given
        val skuPrice = subject.findById(UUID.fromString("00000020-0000-0000-0000-000000000000")).get()
        val supplierSkuIds = listOf(skuPrice.supplierSkuId)
        val dc = skuPrice.dcCodes.first()
        // when
        val result = subject.findAllByDcCodesAndSupplierSkuIdIn(dc, supplierSkuIds)
        // then
        assertEquals(0, result.size)
    }
}
