package com.hellofresh.oms.orderManagementHttp.order.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.grn.Grn
import com.hellofresh.oms.model.grn.GrnStateEnum
import com.hellofresh.oms.model.grn.PurchaseOrderDelivery
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractKafkaIntegrationTest
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.out.GoodsReceivedNoteRepository
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.outbox.out.OutboxRepository
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import java.time.LocalDateTime
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.web.servlet.MockMvc
import uk.org.webcompere.modelassert.json.JsonProviders.overrideObjectMapper

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
abstract class PurchaseOrderAbstractIntegrationTest : AbstractKafkaIntegrationTest() {

    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var purchaseOrderRepository: PurchaseOrderRepository

    @Autowired
    lateinit var supplierRepository: SupplierRepository

    @Autowired
    lateinit var outboxRepository: OutboxRepository

    @Autowired
    lateinit var goodsReceivedNoteRepository: GoodsReceivedNoteRepository

    @Autowired
    lateinit var skuRepository: SkuRepository

    @Autowired
    lateinit var emergencyReasonRepository: EmergencyReasonRepository

    @Autowired
    lateinit var distributionCenterRepository: DistributionCentersRepository

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @BeforeEach
    fun setUp() {
        overrideObjectMapper(objectMapper)
    }

    @Suppress("LongParameterList")
    fun savePurchaseOrderRevision(
        id: UUID = UUID.randomUUID(),
        poNumber: String,
        version: Int,
        supplierId: UUID,
        comment: String,
        yearWeek: YearWeek = YearWeek("2023-W18"),
        dcCode: String = "NJ",
        sendTime: LocalDateTime? = null,
        orderItems: Set<OrderItem> = emptySet(),
        isSynced: Boolean = false,
        emergencyReasonId: UUID = UUID.randomUUID(),
    ) = getPurchaseOrderEntity(
        poNumber = poNumber,
        id = id,
        yearWeek = yearWeek,
        dcCode = dcCode,
        version = version,
        orderItems = orderItems,
        sendTime = sendTime,
        supplierId = supplierId,
        comment = comment,
        isSynced = isSynced,
        emergencyReasonId = emergencyReasonId,
    ).apply {
        purchaseOrderRepository.save(this)
    }

    fun saveSupplier(
        id: UUID = UUID.randomUUID(),
        market: String = "dach"
    ) = getSupplierEntity(supplierId = id, market = market)
        .apply {
            supplierRepository.save(this)
        }

    fun saveOutboxItem(
        po1V1: PurchaseOrder,
        status: OutboxItemStatus,
        now: LocalDateTime = LocalDateTime.now(),
    ) {
        outboxRepository.save(
            OutboxItem.createItem(
                poId = po1V1.id,
                poNumber = po1V1.poNumber,
                now = now,
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                version = po1V1.version,
                status = status,
            ),
        )
    }

    fun saveGrn(
        grnId: UUID = UUID.randomUUID(),
        poNumber: String,
        grnState: GrnStateEnum,
        deliveries: List<PurchaseOrderDelivery> = emptyList(),
    ): Grn = goodsReceivedNoteRepository.save(
        Grn(
            id = grnId,
            poNumber = poNumber,
            dcCode = "NJ",
            deliveryStartTime = LocalDateTime.now().minusDays(3),
            deliveryEndTime = LocalDateTime.now(),
            wmsName = "WMS",
            reference = "${poNumber}_O1",
            state = grnState,
            deliveries = deliveries,
        ),
    )

    fun saveSku(
        id: UUID = UUID.randomUUID(),
        skuCode: String,
        skuName: String,
        market: String = "us",
    ): Sku = skuRepository.save(
        getSkuEntity(
            uuid = id,
            code = skuCode,
            name = skuName,
            market = market,
        ),
    )

    fun saveEmergencyReason(
        id: UUID = UUID.randomUUID(),
        name: String = "Test Emergency Reason",
        market: String = "us",
    ): EmergencyReason = emergencyReasonRepository.save(
        getEmergencyReasonEntity(uuid = id, name = name, market = market),
    )

    fun saveDistributionCenter(
        code: String = "NJ",
        market: String = "us",
    ): DistributionCenter = distributionCenterRepository.save(
        getDistributionCenterEntity(code = code, market = market),
    )
}
