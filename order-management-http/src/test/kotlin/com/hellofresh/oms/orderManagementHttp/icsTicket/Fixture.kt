package com.hellofresh.oms.orderManagementHttp.icsTicket

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.oms.model.icsTicket.IcsTicket
import com.hellofresh.oms.model.icsTicket.TicketPriority
import com.hellofresh.oms.model.icsTicket.TicketStatus
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketsDto
import java.time.LocalDateTime

object Fixture {
    const val TICKETS_RESPONSE = """
        {
          "tickets": [
            {
              "subject": "7 - Potential Stoppage Tomorrow - Prep/Kitting/Cooking & Production | 4 - Discard Request | Aurora - FM01 | 2025-W25 | Protein | |",
              "category": null,
              "id": 153436,
              "priority": 2,
              "status": 2,
              "source": 2,
              "created_at": "2025-06-11T07:41:20Z",
              "updated_at": "2025-06-11T07:41:23Z",
              "type": "Request",
              "description": "",
              "description_text": "",
              "custom_fields": {
                "production_impact": "7 - Potential Stoppage Tomorrow - Prep/Kitting/Cooking &amp; Production",
                "purchasing_category": "Protein",
                "service_catalog": "DC to Procurement Request",
                "site": "Aurora - FM01",
                "type_of_request": "4 - Discard Request",
                "request_type": "4 - Discard Request",
                "brand": "Factor",
                "po": "2518NJ765901_E1",
                "sku": "Sku Name test",
                "sku_code": "BAK-10-10033-7",
                "week": "2025-W25",
                "comments": "the product was damaged during handling & prep of the recipe.\t\t\t\t\t",
                "ticket_link": "https://hellofreshus.freshservice.com/public/tickets/b1661609357c980218196d7b54eb8e263f4c12e9ecfc113d0568027fa957fba8"
              }
            }
          ]
        }
    """

    const val EMPTY_TICKETS_RESPONSE = """
        {
            "tickets": []
        }
    """

    fun createIcsTicket(
        ticketId: Int = 123456789,
        poNumber: String = "2318NJ021001",
    ) =
        IcsTicket(
            market = "US Market",
            week = "2025-W10",
            bobCode = "bob_code_1",
            ticketId = ticketId,
            skuCode = "VEG-14-005501-9",
            poNumber = poNumber,
            poReference = "${poNumber}_E1",
            subject = "ICS Ticket Subject 1",
            ticketLink = "localhost/ics_ticket_link_1",
            priority = TicketPriority.LOW,
            requestType = "ICS_TICKET_REQUEST_TYPE",
            status = TicketStatus.CLOSED,
            type = "Request",
            productionImpact = "7 - Potential Stoppage Tomorrow",
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )

    fun createIcsTicketServiceResponse(): IcsTicketsDto =
        jacksonObjectMapper()
            .findAndRegisterModules()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .readValue(TICKETS_RESPONSE, IcsTicketsDto::class.java)
}
