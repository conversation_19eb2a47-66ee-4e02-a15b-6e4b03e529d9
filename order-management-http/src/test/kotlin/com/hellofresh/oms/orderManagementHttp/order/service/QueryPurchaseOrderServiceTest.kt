package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShippingMethodEnum
import com.hellofresh.oms.orderManagementHttp.order.getPageOf
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepositoryImpl
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepositoryImpl.PurchaseOrderFilter
import java.time.LocalDateTime
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.data.domain.Sort

class QueryPurchaseOrderServiceTest {
    private val purchaseOrderRepositoryMock = Mockito.mock(PurchaseOrderRepositoryImpl::class.java)

    private val subject = QueryPurchaseOrderService(purchaseOrderRepositoryMock)

    @ParameterizedTest
    @EnumSource(ListPurchaseOrdersSortEnumTestData::class)
    fun `should return paginated purchase orders`(testData: ListPurchaseOrdersSortEnumTestData) {
        // given
        val dcCodes = listOf("BC", "AB")
        val dcWeeks = listOf(YearWeek("2024-W23"))
        val purchaseOrderPage = getPageOf(listOf(getPurchaseOrderEntity()))
        val date: LocalDateTime = LocalDateTime.now()
        val shippingMethodEnum: ShippingMethodEnum = ShippingMethodEnum.CROSSDOCK

        whenever(
            purchaseOrderRepositoryMock.findAllPurchaseOrdersPaginated(
                PurchaseOrderFilter(
                    page = 0,
                    size = 10,
                    dcWeeks = dcWeeks,
                    dcCodes = dcCodes,
                    deliveryWindowStartFrom = date,
                    deliveryWindowStartTo = date,
                    shippingMethod = shippingMethodEnum,
                    categories = emptyList(),
                    sort = testData.givenEnum,
                    supplierIds = emptyList(),
                    poNumber = null,
                    status = null,
                    type = null,
                    userEmail = null,
                    sku = null,
                    sendStatus = null,
                    grnStatus = null,
                ),
            ),
        ).thenReturn(purchaseOrderPage)

        // when
        val result =
            subject.paginateBy(
                page = 0,
                size = 10,
                dcWeeks = dcWeeks,
                dcCodes = dcCodes,
                deliveryWindowStartFrom = date,
                deliveryWindowStartTo = date,
                sort = testData.givenEnum,
                shippingMethod = shippingMethodEnum,
                categories = emptyList(),
                supplierIds = emptyList(),
                poNumber = null,
                status = null,
                type = null,
                userEmail = null,
                sku = null,
                sendStatus = null,
                grnStatus = null,
            )
        verify(
            purchaseOrderRepositoryMock,
        ).findAllPurchaseOrdersPaginated(any())

        // then
        assertEquals(purchaseOrderPage, result)
    }

    enum class ListPurchaseOrdersSortEnumTestData(val givenEnum: ListPurchaseOrdersSortEnum?, val expectedSort: Sort) {
        PLUS_CREATED_AT(ListPurchaseOrdersSortEnum.PLUS_CREATED_AT, Sort.by(Sort.Direction.ASC, "created_at")),
        MINUS_CREATED_AT(ListPurchaseOrdersSortEnum.MINUS_CREATED_AT, Sort.by(Sort.Direction.DESC, "created_at")),
        PLUS_DELIVERY_WINDOW_START(
            ListPurchaseOrdersSortEnum.PLUS_DELIVERY_WINDOW_START,
            Sort.by(Sort.Direction.ASC, "expected_start_time"),
        ),
        MINUS_DELIVERY_WINDOW_START(
            ListPurchaseOrdersSortEnum.MINUS_DELIVERY_WINDOW_START,
            Sort.by(Sort.Direction.DESC, "expected_start_time"),
        ),
        PLUS_PO_NUMBER(ListPurchaseOrdersSortEnum.PLUS_PO_NUMBER, Sort.by(Sort.Direction.ASC, "po_number")),
        MINUS_PO_NUMBER(ListPurchaseOrdersSortEnum.MINUS_PO_NUMBER, Sort.by(Sort.Direction.DESC, "po_number")),
        NULL(null, Sort.by(Sort.Direction.DESC, "created_at")),
    }
}
