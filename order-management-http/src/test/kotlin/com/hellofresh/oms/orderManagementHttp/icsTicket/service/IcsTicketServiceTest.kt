package com.hellofresh.oms.orderManagementHttp.icsTicket.service

import com.hellofresh.oms.model.icsTicket.IcsTicket
import com.hellofresh.oms.orderManagementHttp.client.tapioca.config.WebClientConfiguration
import com.hellofresh.oms.orderManagementHttp.icsTicket.Fixture
import com.hellofresh.oms.orderManagementHttp.icsTicket.IcsTicketRepository
import com.hellofresh.oms.orderManagementHttp.icsTicket.service.IcsTicketService.Companion.UPDATED_SINCE_PATTERN
import java.time.Duration
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import kotlin.random.Random
import kotlin.test.assertEquals
import okhttp3.mockwebserver.Dispatcher
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import okhttp3.mockwebserver.RecordedRequest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.web.reactive.function.client.WebClient

@ExtendWith(MockitoExtension::class)
class IcsTicketServiceTest {

    private lateinit var mockServer: MockWebServer

    private lateinit var subject: IcsTicketService

    @Mock
    private lateinit var icsTicketRepository: IcsTicketRepository

    private val ticketsUrl = "/api/v2/tickets/?workspace_id=3"

    @BeforeEach
    fun setupMockServerAndClient() {
        MockWebServer().hostName
        mockServer = MockWebServer().also { it.start() }
        mockServer.dispatcher = object : Dispatcher() {
            override fun dispatch(request: RecordedRequest) =
                MockResponse()
                    .setBody(
                        if (request.requestUrl?.queryParameter("page") == "1") {
                            Fixture.TICKETS_RESPONSE
                        } else if (request.requestUrl?.queryParameter("page") == "2") {
                            Fixture.TICKETS_RESPONSE.replace("\"po\": \"2518NJ765901_E1\"", "\"po\": null")
                        } else {
                            Fixture.EMPTY_TICKETS_RESPONSE
                        },
                    )
                    .addHeader("Content-Type", "application/json")
        }

        subject = IcsTicketService(
            icsWebClient = WebClientConfiguration("").icsWebClient(
                WebClient.builder(),
                "",
            ),
            icsTicketsUrl = mockServer.url(ticketsUrl).toString(),
            updateOffsetDuration = Duration.ofDays(7),
            icsTicketRepository = icsTicketRepository,
        )
    }

    @AfterEach
    fun shutdownMockServers() = mockServer.shutdown()

    @Test
    fun `should fetch ICS tickets for given PO number`() {
        val poNumber = "2518NJ765901"
        whenever(icsTicketRepository.findByPoNumber(any()))
            .thenReturn(
                listOf(
                    Fixture.createIcsTicket(ticketId = Random.nextInt(), poNumber = poNumber),
                    Fixture.createIcsTicket(ticketId = Random.nextInt(), poNumber = poNumber),
                )
            )

        val result = subject.getIcsTickets(poNumber)

        verify(icsTicketRepository, times(1)).findByPoNumber(poNumber)
        assert(!result.isEmpty()) { "Expected non-empty result, but got: $result" }
        assertEquals(2, result.size)
        result.forEach { ticket ->
            assertEquals(poNumber, ticket.poNumber)
        }
    }

    @Test
    fun `should call saving list of ics tickets when fetching new update`() {
        val result = subject.fetchTickets(page = PAGE, pageSize = PAGE_SIZE, updatedSince = UPDATED_SINCE)

        verify(icsTicketRepository, times(1)).upsertAll(any<List<IcsTicket>>())

        assert(!result.isNullOrEmpty()) { "Expected non-empty result, but got: $result" }
    }

    @Test
    fun `should not upsert tickets if no updated ticket received`() {
        val page = 3 // Simulating a page with no new tickets

        val result = subject.fetchTickets(page = page, pageSize = PAGE_SIZE, updatedSince = UPDATED_SINCE)

        verify(icsTicketRepository, times(0)).upsertAll(any<List<IcsTicket>>())

        assert(result.isNullOrEmpty()) { "Expected empty result, but got: $result" }
    }

    @Test
    fun `should filter received tickets that do not have PO number`() {
        val page = 2 // Simulating a second page with tickets that have no PO number

        subject.fetchTickets(page = page, pageSize = PAGE_SIZE, updatedSince = UPDATED_SINCE)

        verify(icsTicketRepository, times(0)).upsertAll(any<List<IcsTicket>>())
    }

    @Test
    fun `should return the lastest updated_at if there a record available`() {
        whenever(icsTicketRepository.findLatestUpdatedAt()).thenReturn(LocalDateTime.now(ZoneOffset.UTC))

        val lastUpdatedAt = subject.resolveLatestFetchTime()

        verify(icsTicketRepository, times(1)).findLatestUpdatedAt()
        assertEquals(
            DateTimeFormatter.ofPattern(UPDATED_SINCE_PATTERN).format(LocalDateTime.now(ZoneOffset.UTC)),
            lastUpdatedAt,
        )
    }

    @Test
    fun `should return the default value if no record available to resolve latest updated_at`() {
        val updateOffsetDuration = Duration.ofDays(3)

        whenever(icsTicketRepository.findLatestUpdatedAt()).thenReturn(null)
        subject = IcsTicketService(
            icsWebClient = WebClientConfiguration("").icsWebClient(
                WebClient.builder(),
                "",
            ),
            icsTicketsUrl = mockServer.url(ticketsUrl).toString(),
            updateOffsetDuration = updateOffsetDuration,
            icsTicketRepository = icsTicketRepository,
        )

        val lastUpdatedAt = subject.resolveLatestFetchTime()

        verify(icsTicketRepository, times(1)).findLatestUpdatedAt()
        assertEquals(
            DateTimeFormatter.ofPattern(UPDATED_SINCE_PATTERN).format(
                LocalDateTime.now(ZoneOffset.UTC).minus(
                    updateOffsetDuration.toMillis(),
                    java.time.temporal.ChronoUnit.MILLIS
                ),
            ),
            lastUpdatedAt,
        )
    }

    companion object {
        private const val PAGE = 1
        private const val PAGE_SIZE = 10
        private const val UPDATED_SINCE = "2025-01-01"
    }
}
