package com.hellofresh.oms.orderManagementHttp.configuration

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractKafkaIntegrationTest.Initializer
import java.time.Duration
import java.time.Instant
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertTrue
import org.awaitility.Awaitility.await
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.assertAll
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.util.TestPropertyValues
import org.springframework.context.ApplicationContextInitializer
import org.springframework.context.ConfigurableApplicationContext
import org.springframework.context.annotation.Import
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.annotation.Rollback
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.transaction.annotation.Transactional
import org.testcontainers.containers.BindMode
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.output.Slf4jLogConsumer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName

@Tag("integration")
@SpringBootTest
@Import(KafkaConfiguration::class)
@ContextConfiguration(initializers = [Initializer::class])
@ActiveProfiles(profiles = ["default", "test", "integration"])
@Transactional
@Rollback
@DirtiesContext
abstract class AbstractKafkaIntegrationTest {

    companion object {
        private const val KAFKA_IMAGE_TAG = "6.2.0"
        private const val POSTGRES_IMAGE = "postgres:14.6-alpine"

        private val logger = LoggerFactory.getLogger(this::class.java)
        private val sharedNetwork = Network.newNetwork()

        val postgresql = PostgreSQLContainer<Nothing>(DockerImageName.parse(POSTGRES_IMAGE)).apply {
            withDatabaseName("order-management")
            withUsername("postgres")
            withPassword("postgres")
            withReuse(true)
            withLabel("reuse.UUID", UUID.randomUUID().toString())
            withClasspathResourceMapping(
                "postgres/docker-entrypoint-initdb",
                "/docker-entrypoint-initdb.d/",
                BindMode.READ_ONLY
            )
        }

        val kafka = KafkaContainer(
            DockerImageName.parse("confluentinc/cp-kafka:$KAFKA_IMAGE_TAG"),
        ).apply {
            withNetwork(sharedNetwork)
            withEnv("KAFKA_BROKER_ID", "1")
            withEnv("TOPIC_AUTO_CREATE", "true")
            withEnv("KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR", "1")
            withEnv("KAFKA_OFFSETS_TOPIC_NUM_PARTITIONS", "1")
            withEnv("KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS", "0")
            withReuse(true)
            withLabel("reuse.UUID", UUID.randomUUID().toString())
            withEnv("ALLOW_PLAINTEXT_LISTENER", "yes")
            withLogConsumer(Slf4jLogConsumer(logger).withSeparateOutputStreams())
        }

        val registry: GenericContainer<*> = GenericContainer(
            DockerImageName.parse("confluentinc/cp-schema-registry:$KAFKA_IMAGE_TAG"),
        ).apply {
            dependsOn(kafka)
            withNetwork(sharedNetwork)
            withExposedPorts(8081)
            withEnv("SCHEMA_REGISTRY_HOST_NAME", "schema-registry")
            withEnv("SCHEMA_REGISTRY_LISTENERS", "http://0.0.0.0:8081")
            withEnv(
                "SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS",
                "PLAINTEXT://${kafka.networkAliases[0]}:9092",
            )
            withReuse(true)
            withLabel("reuse.UUID", UUID.randomUUID().toString())
            waitingFor(Wait.forLogMessage(".*Server started.*", 1))
            withLogConsumer(Slf4jLogConsumer(logger).withSeparateOutputStreams())
        }

        fun getSchemaRegistryUrl() =
            "http://${registry.host}:${registry.firstMappedPort}"

        fun getKafkaBootstrapServers(): String = kafka.bootstrapServers
    }

    internal class Initializer : ApplicationContextInitializer<ConfigurableApplicationContext> {
        override fun initialize(configurableApplicationContext: ConfigurableApplicationContext) {
            val start = Instant.now()
            Stream.of(postgresql, kafka, registry).forEach { c -> c.start() }
            await().atMost(Duration.ofSeconds(30L))
                .untilAsserted {
                    assertAll(
                        { assertTrue("PostgreSQL not started yet") { postgresql.isRunning } },
                        { assertTrue("Kafka not started yet") { kafka.isRunning } },
                        { assertTrue("Schema Registry not started yet") { registry.isRunning } },
                    )
                }
            logger.info("🐳 TestContainers started in {}", Duration.between(start, Instant.now()))

            TestPropertyValues.of(
                "application.brokerAddress=${getKafkaBootstrapServers()}",
                "application.schemaRegistryUrl=${getSchemaRegistryUrl()}",
                "spring.datasource.url=${postgresql.jdbcUrl}",
                "spring.datasource.password=${postgresql.password}",
                "spring.datasource.username=${postgresql.username}",
                "spring.datasource.hikari.auto-commit=false",
            ).applyTo(configurableApplicationContext.environment)
        }
    }
}
