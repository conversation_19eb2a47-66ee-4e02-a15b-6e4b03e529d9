package com.hellofresh.oms.orderManagementHttp.sku

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import java.util.UUID
import kotlin.test.assertContains
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace.NONE
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Direction.ASC
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
@DataJpaTest
@AutoConfigureTestDatabase(replace = NONE)
@Tag("integration")
@Sql(executionPhase = BEFORE_TEST_METHOD, scripts = ["/data/delete.sql", "/data/sku.sql"])
class SkuRepositoryTest : AbstractIntegrationTest() {

    @Autowired
    lateinit var skuRepository: SkuRepository

    @Test
    fun `should return both associated and not associated skus`() {
        // Given
        val market = "us"
        val pageRequest = PageRequest.of(0, 100)
        val supplierSkuIds = listOf(
            UUID.fromString("00000000-0000-0000-0000-000000000001"),
            UUID.fromString("00000000-0000-0000-0000-000000000003"),
        )

        // When
        val result = skuRepository.findAllSkusForMarketAndSupplier(
            market,
            null,
            supplierSkuIds,
            includeAll = true,
            pageRequest,
        )
        assertEquals(6, result.content.size)
        assertEquals(UUID.fromString("00000000-0000-0000-0002-000000000000"), result.content[0].uuid)
        assertEquals(UUID.fromString("00000000-0000-0000-0004-000000000000"), result.content[1].uuid)
    }

    @Test
    fun `should return unassigned skus when no supplier-sku provided`() {
        // Given
        val market = "us"
        val pageRequest = PageRequest.of(0, 100)
        val supplierSkuIds = emptyList<UUID>()

        // When
        val result = skuRepository.findAllSkusForMarketAndSupplier(
            market,
            null,
            supplierSkuIds,
            includeAll = true,
            pageRequest,
        )
        // Then
        assertEquals(6, result.content.size)
    }

    @Test
    fun `should return empty result if no skus found`() {
        // Given
        val market = "xx"
        val pageRequest = PageRequest.of(0, 100)
        val supplierSkuIds = listOf(UUID.fromString("00000000-0000-0000-0000-000000000001"))

        // When
        val result = skuRepository.findAllSkusForMarketAndSupplier(
            market,
            null,
            supplierSkuIds,
            includeAll = true,
            pageRequest,
        )
        assertEquals(0, result.content.size)
    }

    @Test
    fun `should return skus that belong to specified market`() {
        // Given
        val market = "us"
        val pageRequest = PageRequest.of(0, 5)

        // When
        val result = skuRepository.findAllByMarketAndCodeOrNameLikeCaseInsensitive(
            market,
            null,
            pageRequest,
        )

        // Then
        assertTrue(result.hasContent())
        assertEquals(5, result.content.size)
        result.content.forEach { sku ->
            assertEquals(market, sku.market)
        }
    }

    @Test
    fun `should return nothing if no sku found for specified market`() {
        // Given
        val market = "na"
        val pageRequest = PageRequest.of(0, 5)

        // When
        val result = skuRepository.findAllByMarketAndCodeOrNameLikeCaseInsensitive(
            market,
            null,
            pageRequest,
        )

        // Then
        assertFalse(result.hasContent())
    }

    @Test
    fun `should return pageable skus that belong to specified market`() {
        // Given
        val market = "us"
        val pageRequest = PageRequest.of(1, 2)

        // When
        val result = skuRepository.findAllByMarketAndCodeOrNameLikeCaseInsensitive(
            market,
            null,
            pageRequest,
        )

        // Then
        assertTrue(result.hasContent())
        assertEquals(2, result.content.size)
        assertEquals(9, result.totalElements)
        result.content.forEach { sku ->
            assertEquals(market, sku.market)
        }
    }

    @Test
    fun `should return skus for specified market with search criteria`() {
        // Given
        val market = "us"
        val codeName = "pro"
        val pageRequest = PageRequest.of(0, 5)

        // When
        val result = skuRepository.findAllByMarketAndCodeOrNameLikeCaseInsensitive(
            market,
            codeName,
            pageRequest,
        )

        // Then
        assertTrue(result.hasContent())
        assertEquals(2, result.content.size)
        result.content.forEach { sku ->
            assertEquals(market, sku.market)
        }
    }

    @Test
    fun `should return empty skus if nothing found for specified market with search criteria`() {
        // Given
        val market = "us"
        val code = "not-available"
        val pageRequest = PageRequest.of(0, 5)

        // When
        val result = skuRepository.findAllByMarketAndCodeOrNameLikeCaseInsensitive(
            market,
            code,
            pageRequest,
        )

        // Then
        assertFalse(result.hasContent())
    }

    @Test
    fun `should return skus that belong to specified market - case insensitive`() {
        // Given
        val market = "US"
        val pageRequest = PageRequest.of(0, 5)

        // When
        val result = skuRepository.findAllByMarketAndCodeOrNameLikeCaseInsensitive(
            market,
            null,
            pageRequest,
        )

        // Then
        assertTrue(result.hasContent())
        assertEquals(5, result.content.size)
        result.content.forEach { sku ->
            assertEquals(market.lowercase(), sku.market.lowercase())
        }
    }

    @Test
    fun `should return skus for specified market with search criteria - case insensitive`() {
        // Given
        val market = "us"
        val codeName = "MILK"
        val pageRequest = PageRequest.of(0, 5)

        // When
        val result = skuRepository.findAllByMarketAndCodeOrNameLikeCaseInsensitive(
            market,
            codeName,
            pageRequest,
        )

        // Then
        assertTrue(result.hasContent())
        assertEquals(1, result.content.size)
        result.content.forEach { sku ->
            assertContains(sku.name, codeName, true)
        }
    }

    @Test
    fun `should return skus ordered by name`() {
        // Given
        val market = "us"
        val pageRequest = PageRequest.of(0, 10, Sort.by(ASC, "name"))
        val sortedSkuNames =
            listOf(
                "Chicken wings from New Jersey",
                "Chicken, Sausage - 12 Ounce (oz)",
                "DO NOT USE - 1",
                "DO NOT USE - 2",
                "SE Freebies Mixed Jan",
                "Coconut Milk - South, NO Additives",
                "No Supplier",
                "SE test",
                "Vegan burgers from New Jersey",
            ).sorted()

        // When
        val result =
            skuRepository.findAllByMarketAndCodeOrNameLikeCaseInsensitive(
                market,
                null,
                pageRequest,
            )

        // Then
        assertEquals(sortedSkuNames, result.content.map { it.name })
    }

    @Test
    fun `should return distinct list of categories by market`() {
        // given
        val givenMarket = "us"

        // when
        val categories = skuRepository.findDistinctCategoryByMarket(givenMarket)

        // then
        assertEquals(setOf("PTN", "OTH", "PRO", "BAK", "VEG"), categories)
    }
}
