package com.hellofresh.oms.orderManagementHttp.receiptOverride.service

import com.hellofresh.oms.model.imt.recipeOverride.ReceiptOverride
import com.hellofresh.oms.orderManagement.generated.api.model.UpsertReceiptOverrideRequest
import com.hellofresh.oms.orderManagementHttp.receiptOverride.ReceiptOverrideRepository
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class ReceiptOverrideServiceTest {

    @Mock
    private lateinit var receiptOverrideRepository: ReceiptOverrideRepository

    @InjectMocks
    private lateinit var receiptOverrideService: ReceiptOverrideService

    @Test
    fun `test upsertReceiptOverride`() {
        val request = UpsertReceiptOverrideRequest(
            quantityReceived = 100,
            casesReceived = 10,
            poNumber = UUID.randomUUID().toString(),
            skuCode = UUID.randomUUID().toString(),
        )
        whenever(receiptOverrideRepository.findByPoNumberAndSkuCode(any(), any())).thenReturn(null)
        whenever(receiptOverrideRepository.save(any()))
            .thenAnswer { it.getArgument<ReceiptOverride>(0) }

        receiptOverrideService.upsertReceiptOverride(request)

        verify(receiptOverrideRepository).save(any())
    }

    @Test
    fun `test deleteReceiptOverride`() {
        val poNumber = "poNumber"
        val skuCode = "skuCode"
        receiptOverrideService.deleteReceiptOverride(poNumber, skuCode)

        verify(receiptOverrideRepository).deleteByPoNumberAndSkuCode(eq(poNumber), eq(skuCode))
    }
}
