package com.hellofresh.oms.orderManagementHttp.viewstate.intake.integration

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.orderManagementHttp.viewstate.out.ViewStateRepository
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlMergeMode
import org.springframework.test.context.jdbc.SqlMergeMode.MergeMode
import org.springframework.test.json.JsonCompareMode
import org.springframework.test.web.servlet.patch
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath

class UpdateViewStateControllerTest : AbstractViewStateControllerTest() {

    @Autowired
    private lateinit var viewStateRepository: ViewStateRepository

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `should update all fields of a view state`() {
        val requestBody = """
            {
                "resource": "updated-orders",
                "name": "Updated Orders View",
                "state": {
                    "filterState": {
                        "status": "completed",
                        "priority": "high"
                    },
                    "colState": [
                        {
                            "colId": "status",
                            "sort": "desc"
                        }
                    ]
                }
            }
        """.trimIndent()

        mockMvc.patch("$VIEW_STATES_PATH/1") {
            contentType = APPLICATION_CONTENT
            content = requestBody
        }.andExpect {
            status { isOk() }
            content {
                json(
                    """
                    {
                        "id": 1
                    }
                    """.trimIndent(),
                    JsonCompareMode.LENIENT,
                )
            }
        }

        // Verify the update in the database
        val updatedViewState = viewStateRepository.findByUserIdAndId(
            UUID.fromString("3d40b713-b378-40cf-8ed8-dffbd6cfa8df"),
            1L,
        )
        requireNotNull(updatedViewState)
        assertEquals("updated-orders", updatedViewState.resource)
        assertEquals("Updated Orders View", updatedViewState.name)

        val expectedState = mapOf(
            "filterState" to mapOf(
                "status" to "completed",
                "priority" to "high",
            ),
            "colState" to listOf(
                mapOf(
                    "colId" to "status",
                    "sort" to "desc",
                ),
            ),
        )
        val actualState = objectMapper.readValue(updatedViewState.state, object : TypeReference<Map<String, Any>>() {})
        assertEquals(expectedState, actualState)
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `should update only the name of a view state`() {
        val requestBody = """
            {
                "name": "Renamed Orders View"
            }
        """.trimIndent()

        mockMvc.patch("$VIEW_STATES_PATH/1") {
            contentType = APPLICATION_CONTENT
            content = requestBody
        }.andExpect {
            status { isOk() }
            content {
                json(
                    """
                    {
                        "id": 1
                    }
                    """.trimIndent(),
                    JsonCompareMode.LENIENT,
                )
            }
        }

        // Verify the update in the database
        val updatedViewState = viewStateRepository.findByUserIdAndId(
            UUID.fromString("3d40b713-b378-40cf-8ed8-dffbd6cfa8df"),
            1L,
        )
        requireNotNull(updatedViewState)
        assertEquals("orders", updatedViewState.resource) // unchanged
        assertEquals("Renamed Orders View", updatedViewState.name) // updated

        val expectedState = mapOf(
            "filterState" to mapOf(
                "status" to "pending",
            ),
        )
        val actualState = objectMapper.readValue(updatedViewState.state, object : TypeReference<Map<String, Any>>() {})
        assertEquals(expectedState, actualState) // unchanged
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `should return 404 when trying to update another user's view state`() {
        val requestBody = """
            {
                "name": "Updated Name"
            }
        """.trimIndent()

        mockMvc.patch("$VIEW_STATES_PATH/4") {
            contentType = APPLICATION_CONTENT
            content = requestBody
        }.andExpect {
            status { isNotFound() }
        }
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `should return 400 when request body is invalid`() {
        val requestBody = """
            {
                "name": "Updated Name",
                not a valid json object
            }
        """.trimIndent()

        mockMvc.patch("$VIEW_STATES_PATH/1") {
            contentType = APPLICATION_CONTENT
            content = requestBody
        }.andExpect {
            status { isBadRequest() }
            jsonPath("$.message").value("Invalid JSON format")
        }
    }
}
