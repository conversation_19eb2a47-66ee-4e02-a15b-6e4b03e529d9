package com.hellofresh.oms.orderManagementHttp.client.tapioca

import com.hellofresh.oms.orderManagementHttp.client.tapioca.config.WebClientConfiguration
import com.hellofresh.oms.orderManagementHttp.client.tapioca.config.WebClientSettings
import com.hellofresh.oms.orderManagementHttp.client.tapioca.domain.SendPurchaseOrderRequest
import com.hellofresh.oms.orderManagementHttp.client.tapioca.exception.TapiocaClientException
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getTestUser
import java.time.Duration
import java.util.Base64
import java.util.UUID
import java.util.concurrent.TimeUnit.MILLISECONDS
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import okhttp3.mockwebserver.Dispatcher
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import okhttp3.mockwebserver.RecordedRequest
import okhttp3.mockwebserver.SocketPolicy
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.DynamicTest.dynamicTest
import org.junit.jupiter.api.TestFactory
import org.junit.jupiter.api.assertThrows
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatus.BAD_REQUEST
import org.springframework.http.HttpStatus.CONFLICT
import org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR
import org.springframework.http.HttpStatus.NOT_FOUND
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientRequestException

class TapiocaClientTest {
    private lateinit var mockServer: MockWebServer

    private lateinit var subject: TapiocaClient

    private val sendPoUrl = "/api/purchase-orders/${sendPurchaseOrderRequest.purchaseOrderId}/send"
    private val deletePoUrl = "/api/orders/$deletePurchaseOrderId"

    @BeforeEach
    fun setupMockServerAndClient() {
        MockWebServer().hostName
        mockServer = MockWebServer().also { it.start() }

        val sixtySecond = Duration.ofSeconds(60)
        val oneSecond = Duration.ofSeconds(1)
        subject = TapiocaClient(
            webClient = WebClientConfiguration("").let {
                val clientRegistrationRepository = it.clientRegistrationRepository(
                    authServiceBaseUrl = mockServer.url("/").toString(),
                    clientId = testClientId,
                    clientSecret = testClientSecret,
                )
                it.tapiocaWebClient(
                    WebClient.builder(),
                    clientRegistrationRepository,
                    WebClientSettings(sixtySecond, oneSecond, sixtySecond, sixtySecond, sixtySecond, sixtySecond),
                )
            },
            poSendUrl = mockServer.url(sendPoUrl).toString(),
            poDeleteUrl = mockServer.url(deletePoUrl).toString(),
        )
    }

    @AfterEach
    fun shutdownMockServers() = mockServer.shutdown()

    private fun restartMockServer() {
        shutdownMockServers()
        setupMockServerAndClient()
    }

    @TestFactory
    fun `should call Tapioca endpoint when`() =
        listOf(
            ensureTapiocaEndpointIsCalledTestFactory(
                "deletePurchaseOrder",
                { subject.deletePurchaseOrder(deletePurchaseOrderId, testUserId) },
                deletePoUrl,
                MockResponse().setResponseCode(HttpStatus.NO_CONTENT.value()),
            ),
            ensureTapiocaEndpointIsCalledTestFactory(
                "sendPurchaseOrder",
                { subject.sendPurchaseOrder(sendPurchaseOrderRequest) },
                sendPoUrl,
                MockResponse().setResponseCode(HttpStatus.CREATED.value()),
            ),
        )

    private fun ensureTapiocaEndpointIsCalledTestFactory(methodUnderTest: String, method: () -> Unit, path: String, mockResponse: MockResponse): DynamicTest =
        dynamicTest("using $methodUnderTest") {
            restartMockServer()
            // Given
            mockServerResponseWithAuth(path to mockResponse)
            // When
            method.invoke()
            // Then
            assertAuthServiceTokenEndpointCalled()
            assertTapiocaEndpointCalled(path)
        }

    // assertions
    private fun assertAuthServiceTokenEndpointCalled() {
        val nextRequest = mockServer.takeRequest(0, MILLISECONDS)
        assertNotNull(nextRequest, "No request found in the auth mock server queue")
        assertEquals("/token", nextRequest.path)
        assertEquals("grant_type=client_credentials", nextRequest.body.readUtf8())

        val base64EncodedCredentials = Base64.getEncoder().encodeToString(
            "$testClientId:$testClientSecret".toByteArray(),
        )
        assertEquals("Basic $base64EncodedCredentials", nextRequest.getHeader("Authorization"))
    }

    private fun assertTapiocaEndpointCalled(expectedPath: String) {
        val nextRequest = mockServer.takeRequest(0, MILLISECONDS)
        assertNotNull(nextRequest, "No requests found in the tapioca mock server queue")
        assertEquals(expectedPath, nextRequest.path)
        assertEquals("Bearer mock-access-token", nextRequest.headers["Authorization"])
    }

    @TestFactory
    fun `should throw a TapiocaClientException when`() =
        listOf(
            MockResponse().setResponseCode(NOT_FOUND.value()) to "endpoint doesn't exist",
            MockResponse().setResponseCode(BAD_REQUEST.value()) to "endpoint returns a bad request response",
            MockResponse().setResponseCode(CONFLICT.value()) to "endpoint returns a conflict response",
            MockResponse().setResponseCode(INTERNAL_SERVER_ERROR.value()).setBody(htmlBody) to "body exists, but it is not a json object",
        ).flatMap { exceptionTestFactory<TapiocaClientException>(it.first, it.second) }

    @TestFactory
    fun `should throw WebClientRequestException exception when`() =
        listOf(
            MockResponse().setSocketPolicy(SocketPolicy.NO_RESPONSE) to "encounters connection timeout",
            MockResponse().setSocketPolicy(SocketPolicy.DISCONNECT_AFTER_REQUEST) to "encounters broken socket connection",
        ).flatMap { exceptionTestFactory<WebClientRequestException>(it.first, it.second) }

    private inline fun <reified T : Throwable> exceptionTestFactory(
        response: MockResponse,
        nameOfTestCondition: String
    ): List<DynamicTest> =
        listOf(
            Triple(
                "deletePurchaseOrder",
                { subject.deletePurchaseOrder(deletePurchaseOrderId, testUserId) },
                deletePoUrl
            ),
            Triple("sendPurchaseOrder", { subject.sendPurchaseOrder(sendPurchaseOrderRequest) }, sendPoUrl),
        ).map { (methodUnderTest, method, path) ->
            dynamicTest("using $methodUnderTest, and $nameOfTestCondition") {
                restartMockServer()
                // given
                mockServerResponseWithAuth(path to response)
                // when
                assertThrows<T> { method.invoke() }
            }
        }

    private fun mockServerResponseWithAuth(pathToMock: Pair<String, MockResponse>) {
        mapMockServerResponses(
            mapOf(
                pathToMock,
                "/token" to mockResponse(
                    HttpStatus.OK.value(),
                    authServiceSuccessResponse,
                ),
            ),
        )
    }

    private fun mockResponse(
        responseCode: Int,
        responseBody: String? = null,
    ) = MockResponse().setResponseCode(responseCode).addHeader(
        HttpHeaders.CONTENT_TYPE,
        MediaType.APPLICATION_JSON,
    ).apply {
        responseBody?.let { setBody(it) }
    }

    private fun mapMockServerResponses(mockMapping: Map<String, MockResponse>) {
        mockServer.dispatcher = object : Dispatcher() {
            @Throws(InterruptedException::class)
            override fun dispatch(request: RecordedRequest): MockResponse {
                mockMapping.getOrDefault(request.path, MockResponse().setResponseCode(NOT_FOUND.value())).let {
                    return it
                }
            }
        }
    }

    companion object {
        private val sendPurchaseOrderRequest = SendPurchaseOrderRequest(
            getPurchaseOrderEntity().id,
            getTestUser().userId,
        )
        private val deletePurchaseOrderId = UUID.randomUUID()
        private val testUserId = UUID.randomUUID()
        const val testClientId = "mockClientID"
        const val testClientSecret = "mockClientSecret"
        val authServiceSuccessResponse = """
            {
                "access_token": "mock-access-token",
                "expires_in": 21600,
                "token_type": "Bearer"
            }
        """.trimIndent()

        private val htmlBody = """
                        <!DOCTYPE html>
                        <html>
                            <head>
                                <!-- head definitions go here -->
                            </head>
                            <body>
                                <!-- the content goes here -->
                            </body>
                        </html>
        """.trimIndent()
    }
}
