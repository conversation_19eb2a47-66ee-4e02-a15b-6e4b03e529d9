package com.hellofresh.oms.orderManagementHttp.supplierSku

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.SupplierSkuRepository
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace.NONE
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
@DataJpaTest
@AutoConfigureTestDatabase(replace = NONE)
@Tag("integration")
@Sql(executionPhase = BEFORE_TEST_METHOD, value = ["/data/delete.sql", "/data/supplier-sku.sql"])
class SupplierSkuRepositoryTest(
    @Autowired val supplierSkuRepository: SupplierSkuRepository
) : AbstractIntegrationTest() {

    @Test
    fun `should return suppliers skus matching either by parent id or supplier id`() {
        val result1 = supplierSkuRepository.findAllByParentSupplierIdIsInAndStatusIsNot(
            listOf(UUID.fromString("00000000-0000-0009-0000-000000000000"))
        )
        assertEquals(1, result1.size)
        assertEquals("00000000-0000-0000-0000-000000000022", result1.first().uuid.toString())

        val result2 = supplierSkuRepository.findAllByParentSupplierIdIsInAndStatusIsNot(
            listOf(UUID.fromString("00000000-0000-0010-0000-000000000000"))
        )
        assertEquals(1, result2.size)
        assertEquals("00000000-0000-0000-0000-000000000023", result2.first().uuid.toString())
    }
}
