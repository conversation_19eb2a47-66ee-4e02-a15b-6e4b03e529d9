package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.model.OutboxItemStatus.FAILED
import com.hellofresh.oms.model.OutboxItemStatus.PENDING
import com.hellofresh.oms.model.OutboxItemStatus.SENT
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.grn.GrnStateEnum.STATE_CLOSE
import com.hellofresh.oms.model.grn.GrnStateEnum.STATE_OPEN
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderGrnStatusFilterEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderSendStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderTypeEnum
import com.hellofresh.oms.orderManagementHttp.order.toResponseMap
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.DynamicTest.dynamicTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestFactory
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlMergeMode
import org.springframework.test.context.jdbc.SqlMergeMode.MergeMode
import org.springframework.test.web.servlet.get
import uk.org.webcompere.modelassert.json.JsonAssertions.json

@Suppress("LongMethod", "LargeClass")
class GetPurchaseOrderIntegrationTest : PurchaseOrderAbstractIntegrationTest() {
    @ParameterizedTest
    @EnumSource(ShippingMethodEnumTestCase::class)
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(
        scripts = [
            "/data/purchaseOrder.sql",
            "/data/orderItem.sql",
        ],
    )
    fun `should filter correctly based on shipping method`(testCase: ShippingMethodEnumTestCase) {
        // given
        val result =
            mockMvc.get(ORDERS_PATH) {
                param("dc_weeks", "2023-W18")
                param("dc_codes", "NJ")
                param("page", "0")
                param("size", "10")
                testCase.shippingMethod?.let { param("shipping_method", testCase.shippingMethod) }
                contentType = APPLICATION_CONTENT
            }.andExpect {
                status { isOk() }
            }.andReturn()!!

        val listResponse =
            objectMapper.readValue(
                result.response.contentAsString,
                ListPurchaseOrdersResponse::class.java,
            )

        assertEquals(
            expected = listResponse.purchaseOrders.map { it.id.toString() }.toSet(),
            actual = testCase.expectedPoIds.toSet(),
        )
    }

    @ParameterizedTest
    @EnumSource(ListPurchaseOrdersSortEnumTestCase::class)
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(
        scripts = [
            "/data/purchaseOrder.sql",
            "/data/orderItem.sql",
        ],
    )
    fun `should return a list of purchase orders with the correct sorting`(
        testCase: ListPurchaseOrdersSortEnumTestCase,
    ) {
        // given
        val result =
            mockMvc.get(ORDERS_PATH) {
                param("dc_weeks", "2023-W18")
                param("dc_codes", "NJ")
                param("page", "0")
                param("size", "10")
                param("sort", testCase.givenSortEnum.value)
                contentType = APPLICATION_CONTENT
            }.andExpect {
                status { isOk() }
            }.andReturn()!!

        val listResponse =
            objectMapper.readValue(
                result.response.contentAsString,
                ListPurchaseOrdersResponse::class.java,
            )

        assertEquals(0, listResponse.pageResult.number)
        assertEquals(10, listResponse.pageResult.pageSize)
        assertEquals(3, listResponse.pageResult.totalElements)
        assertEquals(1, listResponse.pageResult.totalPages)
        assertEquals(testCase.expectedFirstPurchaseOrderNumber, listResponse.purchaseOrders.first().poNumber)
        assertEquals(testCase.givenSortEnum, listResponse.pageResult.sort.first())
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(
        scripts = [
            "/data/purchaseOrder.sql",
            "/data/orderItem.sql",
        ],
    )
    fun `should return a 400 error when dc_week is formatted incorrectly`() {
        // given
        mockMvc.get(ORDERS_PATH) {
            param("dc_weeks", "2023--W18")
            param("dc_codes", "NJ")
            param("page", "0")
            param("size", "10")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isBadRequest() }
        }.andReturn()!!
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(
        scripts = [
            "/data/purchaseOrder.sql",
            "/data/orderItem.sql",
        ],
    )
    fun `should return when no delivery date window params are passed`() {
        // given
        val result =
            mockMvc.get(ORDERS_PATH) {
                param("dc_weeks", "2023-W18")
                param("dc_codes", "NJ")
                param("page", "0")
                param("size", "10")
                contentType = APPLICATION_CONTENT
            }.andExpect {
                status { isOk() }
            }.andReturn()!!

        val listResponse =
            objectMapper.readValue(
                result.response.contentAsString,
                ListPurchaseOrdersResponse::class.java,
            )
        assertEquals(3, listResponse.purchaseOrders.size)
        assertEquals(3, listResponse.pageResult.totalElements)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "2023-04-29T09:00:00, 2023-04-29T10:30:00, 2",
            "2023-04-29T09:00:00, 2023-04-29T11:30:00, 3",
            "null, 2023-04-29T11:30:00, 3",
            "null, 2023-04-29T10:30:00, 2",
            "2023-04-29T09:00:00, null, 3",
            "null, null, 3",
        ],
        nullValues = ["null"],
    )
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(
        scripts = [
            "/data/purchaseOrder.sql",
            "/data/orderItem.sql",
        ],
    )
    fun `should filter when delivery date params are passed`(
        windowFrom: String?,
        windowTo: String?,
        expectedSize: Int,
    ) {
        // given
        val result =
            mockMvc.get(ORDERS_PATH) {
                param("dc_weeks", "2023-W18")
                param("dc_codes", "NJ")
                param("page", "0")
                param("size", "10")
                windowFrom?.let { param("delivery_window_start_from", it) }
                windowTo?.let { param("delivery_window_start_to", it) }
                contentType = APPLICATION_CONTENT
            }.andExpect {
                status { isOk() }
            }.andReturn()!!

        val listResponse =
            objectMapper.readValue(
                result.response.contentAsString,
                ListPurchaseOrdersResponse::class.java,
            )
        assertEquals(expectedSize, listResponse.purchaseOrders.size)
        assertEquals(expectedSize, listResponse.pageResult.totalElements)
    }

    @TestFactory
    @Sql(
        scripts = [
            "/data/purchaseOrder.sql",
            "/data/orderItem.sql",
        ],
    )
    fun `should filter when status param is provided`() =
        listOf(
            Pair(PurchaseOrderStatusEnum.INITIATED, setOf("e3fa59aa-91c1-4c78-bfa5-000000000002")),
            Pair(PurchaseOrderStatusEnum.APPROVED, setOf("e3fa59aa-91c1-4c78-bfa5-000000000004")),
            Pair(PurchaseOrderStatusEnum.REJECTED, setOf("e3fa59aa-91c1-4c78-bfa5-000000000005")),
            Pair(PurchaseOrderStatusEnum.DELETED, emptySet()),
        ).map { (givenStatus, expectedPoIds) ->
            dynamicTest(
                "response POs should be [${expectedPoIds.joinToString(",")}] when status is [$givenStatus]",
            ) {
                // given
                val result =
                    mockMvc.get(ORDERS_PATH) {
                        param("dc_weeks", "2023-W18")
                        param("dc_codes", "NJ")
                        param("page", "0")
                        param("size", "10")
                        param("status", givenStatus.value)
                        contentType = APPLICATION_CONTENT
                    }.andExpect {
                        status { isOk() }
                    }.andReturn()!!

                val listResponse =
                    objectMapper.readValue(
                        result.response.contentAsString,
                        ListPurchaseOrdersResponse::class.java,
                    )
                assertEquals(expectedPoIds, listResponse.purchaseOrders.map { it.id.toString() }.toSet())
            }
        }

    @TestFactory
    @Sql(
        scripts = [
            "/data/purchaseOrder.sql",
            "/data/orderItem.sql",
        ],
    )
    fun `should filter when type param is provided`() =
        listOf(
            Pair(PurchaseOrderTypeEnum.PREORDER, setOf("e3fa59aa-91c1-4c78-bfa5-000000000005")),
            Pair(
                PurchaseOrderTypeEnum.EMERGENCY,
                setOf("e3fa59aa-91c1-4c78-bfa5-000000000004", "e3fa59aa-91c1-4c78-bfa5-000000000002"),
            ),
            Pair(PurchaseOrderTypeEnum.STANDARD, emptySet()),
        ).map { (givenType, expectedPoIds) ->
            dynamicTest(
                "response POs should be [${expectedPoIds.joinToString(",")}] when type is [$givenType]",
            ) {
                // given
                val result =
                    mockMvc.get(ORDERS_PATH) {
                        param("dc_weeks", "2023-W18")
                        param("dc_codes", "NJ")
                        param("page", "0")
                        param("size", "10")
                        param("type", givenType.value)
                        contentType = APPLICATION_CONTENT
                    }.andExpect {
                        status { isOk() }
                    }.andReturn()!!

                val listResponse =
                    objectMapper.readValue(
                        result.response.contentAsString,
                        ListPurchaseOrdersResponse::class.java,
                    )
                assertEquals(expectedPoIds, listResponse.purchaseOrders.map { it.id.toString() }.toSet())
            }
        }

    @Test
    fun `should return send status based on outbox item and sentTime`() {
        // given
        val givenSupplierId = UUID.randomUUID()
        val givenSupplier = saveSupplier(givenSupplierId)
        val givenYearWeek = YearWeek("2023-W18")
        val givenDcCode = "NJ"
        val po1V1 = savePurchaseOrderRevision(
            poNumber = "PO-467",
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )
        val po1V2 = savePurchaseOrderRevision(
            poNumber = "PO-467",
            version = 2,
            supplierId = givenSupplierId,
            comment = "second version",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )
        val po2V1 = savePurchaseOrderRevision(
            poNumber = "PO-897",
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )
        val po3V1 = savePurchaseOrderRevision(
            poNumber = "PO-004",
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )
        val po4V1 = savePurchaseOrderRevision(
            poNumber = "PO-584",
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )
        val po5V1 = savePurchaseOrderRevision(
            poNumber = "PO-005",
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
            sendTime = LocalDateTime.now(),
        )

        saveOutboxItem(po1V1, SENT)
        saveOutboxItem(po2V1, PENDING)
        saveOutboxItem(po3V1, FAILED)
        val now = LocalDateTime.now()
        saveOutboxItem(po4V1, PENDING, now.minusHours(4))
        saveOutboxItem(po4V1, FAILED, now.minusHours(2))
        saveOutboxItem(po4V1, SENT, now)

        // when
        val expectedResponse = listOf(
            // PO5V1 status is SENT, since it has a send time from OT
            po5V1.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.SENT),
            // PO4V1 status is SENT, since it is the latest outbox item created
            po4V1.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.SENT),
            po3V1.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.FAILED),
            po2V1.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.PENDING),
            // PO1V1 should not be present since a V2 already exists
            // PO1V2 status is NOT_SENT since there is no outbox item
            po1V2.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.NOT_SENT),
        )
        mockMvc.get(ORDERS_PATH) {
            param("dc_weeks", givenYearWeek.value)
            param("dc_codes", givenDcCode)
            param("page", "0")
            param("size", "10")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            content {
                string(
                    json()
                        .at("/purchase_orders")
                        .where()
                        .keysInAnyOrder()
                        .arrayInAnyOrder()
                        .isEqualTo(expectedResponse),
                )
            }
        }.andReturn()
    }

    @Test
    fun `should filter orders only with SENT status`() {
        val givenSupplierId = UUID.randomUUID()
        val givenSupplier = saveSupplier(givenSupplierId)
        val givenYearWeek = YearWeek("2023-W18")
        val givenDcCode = "NJ"

        val givenSentPurchaseOrder = savePurchaseOrderRevision(
            poNumber = "PO-897",
            version = 1,
            supplierId = givenSupplierId,
            comment = "any comment",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
            sendTime = LocalDateTime.now(),
        )

        savePurchaseOrderRevision(
            poNumber = "PO-014",
            version = 1,
            supplierId = givenSupplierId,
            comment = "any comment",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        savePurchaseOrderRevision(
            poNumber = "PO-159",
            version = 1,
            supplierId = givenSupplierId,
            comment = "any comment",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        savePurchaseOrderRevision(
            poNumber = "PO-237",
            version = 1,
            supplierId = givenSupplierId,
            comment = "any comment",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        val expectedResponse = listOf(
            givenSentPurchaseOrder.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.SENT),
        )

        // when
        val result = mockMvc.get(ORDERS_PATH) {
            param("dc_weeks", givenYearWeek.value)
            param("dc_codes", givenDcCode)
            param("page", "0")
            param("size", "10")
            param("send_status", "SENT")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            content {
                string(
                    json()
                        .at("/purchase_orders")
                        .where()
                        .keysInAnyOrder()
                        .arrayInAnyOrder()
                        .isEqualTo(expectedResponse),
                )
            }
        }
    }

    @Test
    fun `should filter orders only with NOT_SENT status`() {
        val givenSupplierId = UUID.randomUUID()
        val givenSupplier = saveSupplier(givenSupplierId)
        val givenYearWeek = YearWeek("2023-W18")
        val givenDcCode = "NJ"

        savePurchaseOrderRevision(
            poNumber = "PO-897",
            version = 1,
            supplierId = givenSupplierId,
            comment = "any comment",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
            sendTime = LocalDateTime.now(),
        )

        val notSentPurchaseOrder = savePurchaseOrderRevision(
            poNumber = "PO-014",
            version = 1,
            supplierId = givenSupplierId,
            comment = "any comment",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        val pendingPurchaseOrder = savePurchaseOrderRevision(
            poNumber = "PO-159",
            version = 1,
            supplierId = givenSupplierId,
            comment = "any comment",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        saveOutboxItem(pendingPurchaseOrder, PENDING)

        val failedPurchaseOrder = savePurchaseOrderRevision(
            poNumber = "PO-237",
            version = 1,
            supplierId = givenSupplierId,
            comment = "any comment",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        saveOutboxItem(failedPurchaseOrder, FAILED)

        val expectedResponse = listOf(
            notSentPurchaseOrder.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.NOT_SENT),
            // PENDING and FAILED are part of the NOT_SENT filter
            pendingPurchaseOrder.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.PENDING),
            failedPurchaseOrder.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.FAILED),
        )

        // when
        mockMvc.get(ORDERS_PATH) {
            param("dc_weeks", givenYearWeek.value)
            param("dc_codes", givenDcCode)
            param("page", "0")
            param("size", "10")
            param("send_status", "NOT_SENT")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            content {
                string(
                    json()
                        .at("/purchase_orders")
                        .where()
                        .keysInAnyOrder()
                        .arrayInAnyOrder()
                        .isEqualTo(expectedResponse),
                )
            }
        }.andReturn()
    }

    @Test
    fun `should filter orders only with OPENED grn status`() {
        val givenSupplierId = UUID.randomUUID()
        val givenSupplier = saveSupplier(givenSupplierId)
        val givenYearWeek = YearWeek("2023-W18")
        val givenDcCode = "NJ"

        val openedGrnPo = savePurchaseOrderRevision(
            poNumber = "PO-123",
            version = 1,
            supplierId = givenSupplierId,
            comment = "po with OPENED GRN",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        val closedGrnPo = savePurchaseOrderRevision(
            poNumber = "PO-857",
            version = 1,
            supplierId = givenSupplierId,
            comment = "po with CLOSED GRN",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        savePurchaseOrderRevision(
            poNumber = "PO-563",
            version = 1,
            supplierId = givenSupplierId,
            comment = "po with no GRN",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        saveGrn(poNumber = openedGrnPo.poNumber, grnState = STATE_OPEN)
        saveGrn(poNumber = closedGrnPo.poNumber, grnState = STATE_CLOSE)

        // when
        val result = mockMvc.get(ORDERS_PATH) {
            param("dc_weeks", givenYearWeek.value)
            param("dc_codes", givenDcCode)
            param("page", "0")
            param("size", "10")
            param("grn_status", "OPENED")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            content {
                string(
                    json()
                        .at("/purchase_orders")
                        .where()
                        .keysInAnyOrder()
                        .arrayInAnyOrder()
                        .isEqualTo(
                            listOf(
                                openedGrnPo.toResponseMap(
                                    supplier = givenSupplier,
                                    grnStatus = PurchaseOrderGrnStatusFilterEnum.OPENED,
                                ),
                            ),
                        ),
                )
            }
        }.andReturn()
    }

    @Test
    fun `should filter orders only with CLOSED grn status`() {
        val givenSupplierId = UUID.randomUUID()
        val givenSupplier = saveSupplier(givenSupplierId)
        val givenYearWeek = YearWeek("2023-W18")
        val givenDcCode = "NJ"

        val openedGrnPo = savePurchaseOrderRevision(
            poNumber = "PO-456",
            version = 1,
            supplierId = givenSupplierId,
            comment = "po with OPENED GRN",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        val closedGrnPo = savePurchaseOrderRevision(
            poNumber = "PO-998",
            version = 1,
            supplierId = givenSupplierId,
            comment = "po with CLOSED GRN",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        savePurchaseOrderRevision(
            poNumber = "PO-445",
            version = 1,
            supplierId = givenSupplierId,
            comment = "po with no GRN",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        saveGrn(poNumber = openedGrnPo.poNumber, grnState = STATE_OPEN)
        saveGrn(poNumber = closedGrnPo.poNumber, grnState = STATE_CLOSE)

        // when
        mockMvc.get(ORDERS_PATH) {
            param("dc_weeks", givenYearWeek.value)
            param("dc_codes", givenDcCode)
            param("page", "0")
            param("size", "10")
            param("grn_status", "CLOSED")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            content {
                string(
                    json()
                        .at("/purchase_orders")
                        .where()
                        .keysInAnyOrder()
                        .arrayInAnyOrder()
                        .isEqualTo(
                            listOf(
                                closedGrnPo.toResponseMap(
                                    supplier = givenSupplier,
                                    grnStatus = PurchaseOrderGrnStatusFilterEnum.CLOSED,
                                ),
                            ),
                        ),
                )
            }
        }.andReturn()
    }

    @Test
    fun `should respond with 404 when calling GET purchase order with an invalid po number`() {
        // given
        mockMvc.get("$ORDERS_PATH/{po_number}", "invalid-po-number") {
            accept = APPLICATION_CONTENT
        }.andExpect {
            status { isNotFound() }
        }.andReturn()
    }

    @Test
    fun `should respond with 200 when calling GET purchase order with a valid po number`() {
        // given
        val givenSupplierId = UUID.randomUUID()
        val givenSupplier = saveSupplier(givenSupplierId)
        val givenYearWeek = YearWeek("2023-W18")
        val givenDcCode = "AA"
        val givenPo = savePurchaseOrderRevision(
            poNumber = "PO-999",
            version = 1,
            supplierId = givenSupplierId,
            comment = "second version",
            yearWeek = givenYearWeek,
            dcCode = givenDcCode,
        )

        // when
        mockMvc.get("$ORDERS_PATH/{po_number}", givenPo.poNumber) {
            accept = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            content {
                string(
                    json()
                        .where()
                        .keysInAnyOrder()
                        .isEqualTo(givenPo.toResponseMap(givenSupplier, PurchaseOrderSendStatusEnum.NOT_SENT)),
                )
            }
        }.andReturn()
    }

    enum class ListPurchaseOrdersSortEnumTestCase(
        val givenSortEnum: ListPurchaseOrdersSortEnum,
        val expectedFirstPurchaseOrderNumber: String,
    ) {
        PLUS_CREATED_AT(
            givenSortEnum = ListPurchaseOrdersSortEnum.PLUS_CREATED_AT,
            expectedFirstPurchaseOrderNumber = "2318NJ021004",
        ),
        MINUS_CREATED_AT(
            givenSortEnum = ListPurchaseOrdersSortEnum.MINUS_CREATED_AT,
            expectedFirstPurchaseOrderNumber = "2318NJ021001",
        ),
        PLUS_DELIVERY_WINDOW_START(
            givenSortEnum = ListPurchaseOrdersSortEnum.PLUS_DELIVERY_WINDOW_START,
            expectedFirstPurchaseOrderNumber = "2318NJ021004",
        ),
        MINUS_DELIVERY_WINDOW_START(
            givenSortEnum = ListPurchaseOrdersSortEnum.MINUS_DELIVERY_WINDOW_START,
            expectedFirstPurchaseOrderNumber = "2318NJ021001",
        ),
        PLUS_PO_NUMBER(
            givenSortEnum = ListPurchaseOrdersSortEnum.PLUS_PO_NUMBER,
            expectedFirstPurchaseOrderNumber = "2318NJ021001",
        ),
        MINUS_PO_NUMBER(
            givenSortEnum = ListPurchaseOrdersSortEnum.MINUS_PO_NUMBER,
            expectedFirstPurchaseOrderNumber = "2318NJ021005",
        ),
    }

    enum class ShippingMethodEnumTestCase(
        val shippingMethod: String?,
        val expectedPoIds: List<String>,
    ) {
        CROSSDOCK(
            shippingMethod = "CROSSDOCK",
            expectedPoIds = listOf("e3fa59aa-91c1-4c78-bfa5-000000000004"),
        ),
        OTHER(
            shippingMethod = "OTHER",
            expectedPoIds = emptyList(),
        ),
        VENDOR(
            shippingMethod = "VENDOR",
            expectedPoIds = listOf("e3fa59aa-91c1-4c78-bfa5-000000000005", "e3fa59aa-91c1-4c78-bfa5-000000000002"),
        ),
        FREIGHT_ON_BOARD(
            shippingMethod = "FREIGHT_ON_BOARD",
            expectedPoIds = emptyList(),
        ),
        NO_FILTERS(
            shippingMethod = null,
            expectedPoIds = listOf(
                "e3fa59aa-91c1-4c78-bfa5-000000000005",
                "e3fa59aa-91c1-4c78-bfa5-000000000004",
                "e3fa59aa-91c1-4c78-bfa5-000000000002",
            ),
        ),
    }

    companion object {
        private const val ORDERS_PATH = "/orders"
        private val APPLICATION_CONTENT = MediaType.parseMediaType("application/json")
    }
}
