package com.hellofresh.oms.orderManagementHttp.supplier

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.Matchers.containsInRelativeOrder
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(scripts = ["/data/delete.sql", "/data/suppliers.sql"])
class SupplierApiTest(@Autowired private var mockMvc: MockMvc) : AbstractIntegrationTest() {

    @Test
    fun `should respond with sorted Suppliers when request valid`() {
        // given
        val expectedNames = arrayOf("Supplier 115845", "Supplier A", "Supplier B", "Supplier C", "Supplier D")

        // when
        mockMvc.get("/suppliers") {
            param("market", "us")
            param("page", "0")
            param("size", "5")
        }.andExpect {
            jsonPath("$.suppliers.length()", equalTo(5))
            jsonPath("$.suppliers[*].name", containsInRelativeOrder(*expectedNames))
        }
    }

    @Test
    fun `should respond with Suppliers when supplier name matches 'Supplier o'`() {
        // given
        val expectedNames = arrayOf("Supplier O", "Supplier Ohio")

        // when
        mockMvc.get("/suppliers") {
            param("market", "us")
            param("search", "lier o")
            param("page", "0")
            param("size", "5")
        }.andExpect {
            jsonPath("$.suppliers.length()", equalTo(2))
            jsonPath("$.suppliers[*].name", containsInRelativeOrder(*expectedNames))
        }
    }

    @Test
    fun `should not match any supplier when supplier name does not exist`() {
        // when
        mockMvc.get("/suppliers") {
            param("market", "us")
            param("search", "SomeSupplierNameThatDoesNotExist")
            param("page", "0")
            param("size", "5")
        }.andExpect {
            jsonPath("$.suppliers.length()", equalTo(0))
        }
    }
}
