package com.hellofresh.oms.orderManagementHttp.imports.integration

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@Tag("integration")
@SpringBootTest
@AutoConfigureMockMvc
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
class TemplatesIntegrationTest : AbstractIntegrationTest() {

    val templateFileContent = """
week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod,comments

    """.trimIndent()

    @Autowired
    lateinit var mockMvc: MockMvc

    @Test
    fun `should respond with template file`() {
        mockMvc.get("/static/templates/batch_create_po.csv")
            .andExpect {
                status { isOk() }
                content {
                    string(
                        templateFileContent
                    )
                }
            }
    }
}
