package com.hellofresh.oms.orderManagementHttp.communicationPreference.integration

import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.EMAIL
import com.hellofresh.oms.model.communicationPreference.CommunicationPreferenceEnum.EMAIL_AND_E2OPEN
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import java.util.UUID
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.Test
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.post

class CreateCommunicationPreferenceIntegrationTest : AbstractCommunicationPreferenceIntegrationTest() {
    @Test
    fun `should return bad request when neither market or dc or supplier is present`() {
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "EMAIL"
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isBadRequest() }
            jsonPath("$.message", equalTo("At least one of market, distribution center or supplier must be present"))
        }
    }

    @Test
    fun `should return bad request when dc with given code does not exist`() {
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "EMAIL",
                    "distribution_center": {
                        "code": "DC1"
                    }
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isBadRequest() }
            jsonPath("$.message", equalTo("A distribution center with the given code does not exist"))
        }
    }

    @Test
    fun `should return bad request when supplier with given id does not exist`() {
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "EMAIL",
                    "supplier": {
                        "id": "123e4567-e89b-12d3-a456-426614174000"
                    }
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isBadRequest() }
            jsonPath("$.message", equalTo("A supplier with the given id does not exist"))
        }
    }

    @Test
    fun `should return conflict when communication preference for the given market already exist`() {
        // given
        savePreference(
            preference = EMAIL,
            market = "us",
        )

        // when
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "E2OPEN",
                    "market": {
                        "code": "us"
                    }
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isConflict() }
            jsonPath("$.message", equalTo("A communication preference for the given market already exists"))
        }
    }

    @Test
    fun `should return conflict when communication preference for the given dc already exist`() {
        // given
        savePreference(
            preference = EMAIL_AND_E2OPEN,
            distributionCenter = getDistributionCenterEntity(code = "LK"),
        )

        // when
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "E2OPEN",
                    "distribution_center": {
                        "code": "LK"
                    }
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isConflict() }
            jsonPath(
                "$.message",
                equalTo("A communication preference for the given distribution center already exists"),
            )
        }
    }

    @Test
    fun `should return conflict when communication preference for the given supplier already exist`() {
        // given
        val givenSupplierId = UUID.randomUUID()
        savePreference(
            preference = EMAIL_AND_E2OPEN,
            supplier = getSupplierEntity(supplierId = givenSupplierId),
        )

        // when
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "E2OPEN",
                    "supplier": {
                        "id": "$givenSupplierId"
                    }
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isConflict() }
            jsonPath(
                "$.message",
                equalTo("A communication preference for the given supplier already exists"),
            )
        }
    }

    @Test
    fun `should return ok with id when valid market is provided`() {
        // when
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "EMAIL",
                    "market": {
                        "code": "us"
                    }
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.id", notNullValue())
        }
    }

    @Test
    fun `should return ok with id when existing distribution center is provided`() {
        // given
        val givenDcCode = "JH"
        saveDistributionCenter(givenDcCode)

        // when
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "EMAIL_AND_E2OPEN",
                    "distribution_center": {
                        "code": "$givenDcCode"
                    }
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.id", notNullValue())
        }
    }

    @Test
    fun `should return ok with id when existing supplier is provided`() {
        // given
        val givenSupplierId = UUID.randomUUID()
        saveSupplier(id = givenSupplierId)

        // when
        mockMvc.post(
            "/communication-preferences",
        ) {
            content = """
                {
                    "preference": "E2OPEN",
                    "supplier": {
                        "id": "$givenSupplierId"
                    }
                }
            """.trimIndent()
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.id", notNullValue())
        }
    }
}
