package com.hellofresh.oms.orderManagementHttp.distributionCenters

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterAddressType
import com.hellofresh.oms.model.DistributionCenterStatus
import com.hellofresh.oms.orderManagement.generated.api.model.DcAddressDto
import com.hellofresh.oms.orderManagement.generated.api.model.DistributionCenterDetailedDto
import java.time.LocalDateTime
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class DcDtoMapperTest {

    @Test
    fun`should correctly map upcoming data with dcDtoMapper`() {
        // given
        val dcAddress = DistributionCenterAddress(
            id = UUID.randomUUID(),
            dcCode = "NJ",
            number = "12",
            address = "Broadway Avenue",
            zip = "77777",
            city = "New Jersey",
            state = "New Jersey State",
            company = "Freeman Sachs",
            type = DistributionCenterAddressType.BILLING,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            countryCode = "US"
        )
        val dc = DistributionCenter(
            code = "NJ",
            status = DistributionCenterStatus.ACTIVE,
            name = "New Jersey",
            market = "us",
            addresses = listOf(dcAddress),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        // when
        val result = listOf(dc).mapToDto()

        // then
        assertEquals(
            listOf(
                DistributionCenterDetailedDto(
                    code = "NJ",
                    name = "New Jersey",
                    market = "us",
                    addresses = listOf(
                        DcAddressDto(
                            number = "12",
                            address = "Broadway Avenue",
                            zip = "77777",
                            city = "New Jersey",
                            state = "New Jersey State",
                            company = "Freeman Sachs",
                            type = "BILLING",
                            countryCode = "US"
                        )
                    )
                )
            ),
            result
        )
    }
}
