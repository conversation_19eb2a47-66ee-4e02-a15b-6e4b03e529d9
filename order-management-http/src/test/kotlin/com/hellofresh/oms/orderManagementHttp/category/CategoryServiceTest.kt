package com.hellofresh.oms.orderManagementHttp.category

import com.hellofresh.oms.orderManagementHttp.category.service.domain.CategoryService
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class CategoryServiceTest {
    @Mock lateinit var skuRepositoryMock: SkuRepository

    @InjectMocks lateinit var categoryService: CategoryService

    @Test fun `should call repository with correct market`() {
        // given
        val givenMarket = "beneluxfr"
        whenever(
            skuRepositoryMock.findDistinctCategoryByMarket(givenMarket),
        ).thenReturn(setOf("BAK", "BEV", "BKD", "BUC", "CON", "DAI"))

        // when
        val categories = categoryService.getCategories(givenMarket)

        // then
        assertEquals(
            setOf(
                "BAK",
                "BEV",
                "BKD",
                "BUC",
                "CON",
                "DAI",
            ),
            categories,
        )
    }
}
