package com.hellofresh.oms.orderManagementHttp.imports.parser

import com.hellofresh.oms.orderManagement.generated.api.model.LineError
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.contains
import org.hamcrest.Matchers.containsInAnyOrder
import org.hamcrest.Matchers.empty
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class BatchCreatePurchaseOrderParserTest {
    private lateinit var subject: BatchCreatePurchaseOrderParser

    private val maxLines = 50

    @BeforeEach fun setUp() {
        subject = BatchCreatePurchaseOrderParser(maxLines)
    }

    @Test
    fun `should return parsed objects and empty errors when the file is correct`() {
        // given
        val byteArrayInputStream = """
        week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
        2024-W37,IT,,Multi-Week,113000,2024-09-06,06:00,08:00,PCK-00-005297-1,1200,UNIT_TYPE,2.5,5,UNIT,0.10220,VENDOR
        2024-W37,IT,,Multi-Week,113000,2024-09-06,06:00,08:00,PCK-00-100200-1,1333,UNIT_TYPE,2.5,5,UNIT,0.10220,VENDOR
        ,,,,,,,,,,,,,,,
        """.trimIndent().byteInputStream()

        // when
        val result = subject.parse(byteArrayInputStream)

        // then
        assertThat(result.lineErrors, empty())
        assertThat(
            result.parsedLines,
            containsInAnyOrder(
                BatchOrderRowDto(
                    rowNumber = 2,
                    weekValue = "2024-W37",
                    distributionCenterValue = "IT",
                    customOrderNumberCode = "",
                    reason = "Multi-Week",
                    supplierCodeValue = "113000",
                    deliveryDate = "2024-09-06",
                    startTime = "06:00",
                    endTime = "08:00",
                    skuValue = "PCK-00-005297-1",
                    orderSize = "1200",
                    orderUnitValue = "UNIT_TYPE",
                    bufferValue = "2.5",
                    caseSize = "5",
                    caseUom = "UNIT",
                    price = "0.10220",
                    shipMethod = "VENDOR",
                    comments = "",
                ),
                BatchOrderRowDto(
                    rowNumber = 3,
                    weekValue = "2024-W37",
                    distributionCenterValue = "IT",
                    customOrderNumberCode = "",
                    reason = "Multi-Week",
                    supplierCodeValue = "113000",
                    deliveryDate = "2024-09-06",
                    startTime = "06:00",
                    endTime = "08:00",
                    skuValue = "PCK-00-100200-1",
                    orderSize = "1333",
                    orderUnitValue = "UNIT_TYPE",
                    bufferValue = "2.5",
                    caseSize = "5",
                    caseUom = "UNIT",
                    price = "0.10220",
                    shipMethod = "VENDOR",
                    comments = "",
                ),
            ),
        )
    }

    @Test
    fun `should return list of errors for all required fields in the file`() {
        // given
        val byteArrayInputStream = """
        week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
        2024-W37,,,,,,,,,,,,,,,
        ,IT,,,,,,,,,,,,,,
        """.trimIndent().byteInputStream()

        // when
        val result = subject.parse(byteArrayInputStream)

        // then
        assertThat(
            result.lineErrors,
            containsInAnyOrder(
                LineError(2, """value for "distributionCenter.value" is required"""),
                // customOrderNumberCode is not required
                LineError(2, """value for "reason" is required"""),
                LineError(2, """value for "supplierCode.value" is required"""),
                LineError(2, """value for "deliveryDate" is required"""),
                LineError(2, """value for "startTime" is required"""),
                LineError(2, """value for "endTime" is required"""),
                LineError(2, """value for "sku.value" is required"""),
                LineError(2, """value for "orderSize" is required"""),
                LineError(2, """value for "orderUnit.value" is required"""),
                LineError(2, """value for "buffer.value" is required"""),
                // case.size is not required
                LineError(2, """value for "case.uom" is required"""),
                LineError(2, """value for "price" is required"""),
                LineError(2, """value for "shipMethod" is required"""),

                LineError(3, """value for "week.value" is required"""),
                LineError(3, """value for "reason" is required"""),
                LineError(3, """value for "supplierCode.value" is required"""),
                LineError(3, """value for "deliveryDate" is required"""),
                LineError(3, """value for "startTime" is required"""),
                LineError(3, """value for "endTime" is required"""),
                LineError(3, """value for "sku.value" is required"""),
                LineError(3, """value for "orderSize" is required"""),
                LineError(3, """value for "orderUnit.value" is required"""),
                LineError(3, """value for "buffer.value" is required"""),
                LineError(3, """value for "case.uom" is required"""),
                LineError(3, """value for "price" is required"""),
                LineError(3, """value for "shipMethod" is required"""),
            ),
        )
    }

    @Test
    fun `should have error when customOrderNumberCode is provided`() {
        // given
        // when
        val result = subject.parse(createCsvInputStream(customOrderNumberCode = "any value"))

        // then
        assertThat(
            result.lineErrors,
            contains(
                LineError(
                    2,
                    """"customOrderNumberCode" is not supported at the moment.
                        | Please use the "PO Editor" for modifying a purchase order
                    """.trimMargin(),
                ),
            ),
        )
    }

    @Test
    @Suppress("MaxLineLength")
    fun `should have error when file has more lines than the limit`() {
        // given
        val header = "week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod"
        val lines =
            List(
                maxLines + 1
            ) { "2024-W37,IT,,Multi-Week,113000,2024-09-06,06:00,08:00,PCK-00-005297-1,1200,UNIT_TYPE,2.5,5,UNIT,0.10220,VENDOR" }
        val byteArrayInputStream = (listOf(header) + lines).joinToString("\n").byteInputStream()

        // when
        val exception = assertThrows<FileHasTooManyLinesException> {
            subject.parse(byteArrayInputStream)
        }

        // then
        assertThat(exception.message, equalTo("File has too many lines. The limit is $maxLines"))
    }

    @Suppress("LongParameterList")
    private fun createCsvInputStream(
        weekValue: String = "2024-W37",
        distributionCenterValue: String = "IT",
        customOrderNumberCode: String = "",
        reason: String = "Multi-Week",
        supplierCodeValue: String = "113000",
        deliveryDate: String = "2024-09-06",
        startTime: String = "06:00",
        endTime: String = "08:00",
        skuValue: String = "PCK-00-005297-1",
        orderSize: String = "1200",
        orderUnitValue: String = "UNIT_TYPE",
        bufferValue: String = "2.5",
        caseSize: String = "10",
        caseUom: String = "UNIT",
        price: String = "1.80",
        shipMethod: String = "VENDOR"
    ) = """
        week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
        $weekValue,$distributionCenterValue,$customOrderNumberCode,$reason,$supplierCodeValue,$deliveryDate,$startTime,$endTime,$skuValue,$orderSize,$orderUnitValue,$bufferValue,$caseSize,$caseUom,$price,$shipMethod
    """.trimIndent().byteInputStream()
}
