package com.hellofresh.oms.orderManagementHttp.configuration

import com.hellofresh.oms.orderManagementHttp.configuration.LocalSecurityConfiguration.LocalUserInjectionFilter
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt

class LocalSecurityConfigurationTest {

    @Test
    fun `should inject jwt user in the security context`() {
        // given
        val subject = LocalUserInjectionFilter()

        // when
        subject.doFilter(
            Mockito.mock(HttpServletRequest::class.java),
            Mockito.mock(HttpServletResponse::class.java),
            Mockito.mock(FilterChain::class.java),
        )

        // then
        val jwt = SecurityContextHolder.getContext().authentication.principal as Jwt
        assertNotNull(UUID.fromString(jwt.subject))
        assertEquals("<EMAIL>", jwt.getClaimAsString("email"))
    }
}
