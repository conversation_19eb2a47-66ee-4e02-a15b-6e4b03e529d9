package com.hellofresh.oms.orderManagementHttp.uom

import com.hellofresh.oms.orderManagementHttp.uom.Fixture.getUnitOfMeasureEntities
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class UnitOfMeasureServiceTest {
    @Mock
    private lateinit var unitOfMeasureRepository: UnitOfMeasureRepository

    @InjectMocks
    private lateinit var unitOfMeasureService: UnitOfMeasureService

    @Test
    fun `should return list of UOM when given market`() {
        // given
        val market = "us"
        val unitOfMeasureEntities = getUnitOfMeasureEntities()
        whenever(unitOfMeasureRepository.findAllByMarketIgnoreCase(market))
            .thenReturn(unitOfMeasureEntities)

        // when
        val actual = unitOfMeasureService.getUnitOfMeasureByMarket(market)

        // then
        unitOfMeasureEntities.zip(actual).forEach {
            assertAll(
                { assertEquals(it.first.uuid, it.second.uuid) },
                { assertEquals(it.first.name, it.second.name) },
                { assertEquals(it.first.market, it.second.market) },
                { assertEquals(it.first.type, it.second.type) },
            )
        }
    }
}
