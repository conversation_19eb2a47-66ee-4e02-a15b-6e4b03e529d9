package com.hellofresh.oms.orderManagementHttp.imports.service

import com.hellofresh.oms.model.importHistory.ImportHistory
import com.hellofresh.oms.model.importHistory.ImportHistoryStatus.INITIATED
import com.hellofresh.oms.model.importHistory.ImportHistorySummary.BatchPoCreation
import com.hellofresh.oms.model.importHistory.ImportHistoryType.BATCH_PO_CREATION
import com.hellofresh.oms.orderManagementHttp.authentication.LoggedInUserInfo
import com.hellofresh.oms.orderManagementHttp.imports.out.ImportHistoryRepository
import com.hellofresh.oms.orderManagementHttp.imports.out.PurchaseOrderHistoryRepository
import com.hellofresh.oms.orderManagementHttp.order.getTestUser
import java.time.Clock
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class ImportHistoryServiceTest {
    private lateinit var subject: ImportHistoryService

    private val importHistoryRepositoryMock: ImportHistoryRepository = mock()
    private val purchaseOrderHistoryRepository: PurchaseOrderHistoryRepository = mock()
    private val clock = Clock.fixed(Instant.parse("2024-12-11T15:00:00Z"), ZoneId.of("UTC"))

    @BeforeEach
    fun setUp() {
        subject = ImportHistoryService(importHistoryRepositoryMock, purchaseOrderHistoryRepository, clock)
    }

    @Test
    fun `should call repository with correct values`() {
        // given
        val givenUser = getTestUser(UUID.randomUUID(), "<EMAIL>")
        val givenPoNumbers = listOf("PO-1234")
        val givenMarketCodes = listOf("ca")
        val givenFilename = "some-file.csv"
        val importHistoryStub = getImportHistoryStub(givenFilename, givenUser, givenPoNumbers, givenMarketCodes)
        whenever(importHistoryRepositoryMock.save(any())).thenReturn(importHistoryStub)

        // when
        subject.createImportHistory(
            givenFilename,
            givenUser,
            givenPoNumbers,
            givenMarketCodes,
        )

        // then
        val argumentCaptor = argumentCaptor<ImportHistory>()
        verify(importHistoryRepositoryMock).save(argumentCaptor.capture())
        argumentCaptor.firstValue.apply {
            assertThat(type, equalTo(BATCH_PO_CREATION))
            assertThat(status, equalTo(INITIATED))
            assertThat(userId, equalTo(givenUser.userId))
            assertThat(userEmail, equalTo(givenUser.userEmail))
            assertThat(createdAt, equalTo(LocalDateTime.now(clock)))
            assertThat(updatedAt, equalTo(LocalDateTime.now(clock)))
        }
    }

    private fun getImportHistoryStub(
        filename: String,
        user: LoggedInUserInfo,
        purchaseOrders: List<String>,
        markets: List<String>
    ) =
        ImportHistory.createImportHistory(
            filename = filename,
            userId = user.userId,
            userEmail = user.userEmail,
            createdAt = LocalDateTime.now(clock),
            summary = BatchPoCreation(purchaseOrders, markets),
        )
}
