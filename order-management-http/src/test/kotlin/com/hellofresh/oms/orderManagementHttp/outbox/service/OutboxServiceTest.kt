package com.hellofresh.oms.orderManagementHttp.outbox.service

import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.orderManagementHttp.client.tapioca.TapiocaClient
import com.hellofresh.oms.orderManagementHttp.client.tapioca.domain.SendPurchaseOrderRequest
import com.hellofresh.oms.orderManagementHttp.client.tapioca.exception.TapiocaClientException
import com.hellofresh.oms.orderManagementHttp.exception.OrderingToolException
import com.hellofresh.oms.orderManagementHttp.exception.PurchaseOrderNotFoundException
import com.hellofresh.oms.orderManagementHttp.exception.PurchaseOrderNotSyncedException
import com.hellofresh.oms.orderManagementHttp.order.getOutboxItem
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getTestUser
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.outbox.out.OutboxRepository
import java.time.Clock
import java.time.Instant
import java.time.ZoneId
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.whenever

class OutboxServiceTest {

    private val purchaseOrderRepositoryMock = mock<PurchaseOrderRepository>()
    private val outboxRepositoryMock = mock<OutboxRepository>()
    private val tapiocaClientMock = mock<TapiocaClient>()
    private val purchaseOrderProducer = mock<PurchaseOrderProducer>()

    private val outboxService = OutboxService(
        purchaseOrderRepositoryMock,
        outboxRepositoryMock,
        tapiocaClientMock,
        purchaseOrderProducer,
        Clock.fixed(Instant.parse("2024-12-11T15:00:00Z"), ZoneId.of("UTC")),
    )

    @Test
    fun `should throw when po is not synced`() {
        // given
        val po = getPurchaseOrderEntity().copy(isSynced = false)
        val user = getTestUser()
        whenever(outboxRepositoryMock.save(any())).thenReturn(getOutboxItem())
        whenever(purchaseOrderRepositoryMock.findById(po.id)).thenReturn(Optional.of(po))

        // when
        assertThrows<PurchaseOrderNotSyncedException> { outboxService.sendOrder(po.id, user) }

        // then
        verifyNoInteractions(outboxRepositoryMock)
    }

    @Test
    fun `should throw when tapioca client throws`() {
        // given
        val po = getPurchaseOrderEntity().copy(isSynced = true)
        val user = getTestUser()
        val argumentCaptor = argumentCaptor<OutboxItem>()
        whenever(outboxRepositoryMock.save(argumentCaptor.capture())).thenReturn(getOutboxItem())
        whenever(purchaseOrderRepositoryMock.findById(po.id)).thenReturn(Optional.of(po))
        whenever(tapiocaClientMock.sendPurchaseOrder(SendPurchaseOrderRequest(po.id, user.userId))).thenAnswer {
            throw TapiocaClientException("some exception")
        }

        // when
        assertThrows<OrderingToolException> { outboxService.sendOrder(po.id, user) }

        // then
        verify(outboxRepositoryMock, times(1)).save(any())
        val persistedOutboxItem = argumentCaptor.firstValue
        assertEquals(OutboxItemStatus.FAILED, persistedOutboxItem.status)
        assertEquals(po.id, persistedOutboxItem.poId)
        assertEquals(po.poNumber, persistedOutboxItem.poNumber)
        assertEquals(user.userEmail, persistedOutboxItem.userEmail)
        assertEquals(user.userId, persistedOutboxItem.userId)
        assertNotNull(persistedOutboxItem.lastStatusChangeAt)
    }

    @Test
    fun `should save outbox item when sent`() {
        // given
        val po = getPurchaseOrderEntity().copy(isSynced = true)
        val user = getTestUser()

        whenever(purchaseOrderRepositoryMock.findById(po.id)).thenReturn(Optional.of(po))
        val argumentCaptor = argumentCaptor<OutboxItem>()
        whenever(outboxRepositoryMock.save(argumentCaptor.capture())).thenReturn(getOutboxItem())
        whenever(purchaseOrderRepositoryMock.save(any())).thenReturn(po)

        // when
        outboxService.sendOrder(po.id, user)

        // then
        val persistedOutboxItem = argumentCaptor.firstValue
        assertEquals(OutboxItemStatus.SENT, persistedOutboxItem.status)
        assertEquals(po.id, persistedOutboxItem.poId)
        assertEquals(po.poNumber, persistedOutboxItem.poNumber)
        assertEquals(user.userEmail, persistedOutboxItem.userEmail)
        assertEquals(user.userId, persistedOutboxItem.userId)
        assertNotNull(persistedOutboxItem.lastStatusChangeAt)
    }

    @Test
    fun `should get outbox items for po number`() {
        // given
        val poNumber = "2318NJ021004"
        val outboxItems = listOf(getOutboxItem(), getOutboxItem())
        whenever(purchaseOrderRepositoryMock.existsByPoNumber(poNumber)).thenReturn(true)
        whenever(outboxRepositoryMock.findAllByPoNumber(poNumber)).thenReturn(outboxItems)

        // when
        val result = outboxService.getOutboxItemsFor(poNumber)

        // then
        assertEquals(outboxItems, result)
    }

    @Test
    fun `should throw when po not found`() {
        // given
        val poNumber = "2318NJ021004"
        whenever(purchaseOrderRepositoryMock.existsByPoNumber(poNumber)).thenReturn(false)
        // when
        assertThrows<PurchaseOrderNotFoundException> { outboxService.getOutboxItemsFor(poNumber) }
    }
}
