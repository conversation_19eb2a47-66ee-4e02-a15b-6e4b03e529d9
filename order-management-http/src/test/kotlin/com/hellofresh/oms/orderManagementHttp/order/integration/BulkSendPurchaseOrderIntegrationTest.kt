package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.WorkerActionData.SendPurchaseOrder
import com.hellofresh.oms.model.WorkerActionStatus
import com.hellofresh.oms.orderManagementHttp.configuration.consumeAtLeast
import com.hellofresh.oms.orderManagementHttp.workerAction.WorkerActionRepository
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import com.statsig.sdk.Statsig
import java.time.Duration
import java.util.UUID
import kotlin.test.assertEquals
import org.apache.kafka.clients.consumer.Consumer
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.post

@SpringBootTest(
    properties = [
        "topics.purchase-order=public.supply.procurement.purchase-order.v2-bulk-send-test",
    ],
)
class BulkSendPurchaseOrderIntegrationTest : PurchaseOrderAbstractIntegrationTest() {
    @Autowired
    private lateinit var workerActionRepository: WorkerActionRepository

    @Value("\${topics.purchase-order}")
    private lateinit var purchaseOrderTopicName: String

    @Value("\${statsig.gates.enable-po-topic-v2}")
    private lateinit var enablePublisherForPoTopicStatsigGate: String

    @Autowired
    private lateinit var purchaseOrderConsumer: Consumer<String, PurchaseOrderEvent>

    @BeforeEach
    override fun setUp() {
        super.setUp()
        Statsig.overrideGate(enablePublisherForPoTopicStatsigGate, true)
        purchaseOrderConsumer.subscribe(listOf(purchaseOrderTopicName))
    }

    @Test
    @Suppress("LongMethod")
    fun `should create worker action and outbox item for each purchase order number`() {
        // given
        val supplier = saveSupplier(market = "us")
        val dc = saveDistributionCenter(market = "us", code = "GW")
        val emergencyReason = saveEmergencyReason(market = "us", name = "EMERGENCY")

        val po1 = savePurchaseOrderRevision(
            poNumber = "2501GW${IntRange(100000, 999999).random()}",
            version = 1,
            supplierId = supplier.id,
            comment = "purchase order number 1",
            isSynced = true,
            dcCode = dc.code,
            emergencyReasonId = emergencyReason.uuid,
        )
        val po2 = savePurchaseOrderRevision(
            poNumber = "2502GW${IntRange(100000, 999999).random()}",
            version = 1,
            supplierId = supplier.id,
            comment = "purchase order number 2",
            isSynced = true,
            dcCode = dc.code,
            emergencyReasonId = emergencyReason.uuid,
        )
        val po3 = savePurchaseOrderRevision(
            poNumber = "2503GW${IntRange(100000, 999999).random()}",
            version = 1,
            supplierId = supplier.id,
            comment = "purchase order number 3",
            isSynced = true,
            dcCode = dc.code,
            emergencyReasonId = emergencyReason.uuid,
        )

        // when
        mockMvc.post("/outbox") {
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "purchase_order_numbers": ["${po1.poNumber}", "${po2.poNumber}", "${po3.poNumber}"]
                }
            """
        }.andExpect {
            status { isOk() }
        }

        // then
        workerActionRepository.findAll()
            .map {
                it.status to objectMapper.treeToValue(it.payload, SendPurchaseOrder::class.java).purchaseOrderNumber
            }.toSet().let {
                assertEquals(
                    setOf(
                        WorkerActionStatus.PENDING to po1.poNumber,
                        WorkerActionStatus.PENDING to po2.poNumber,
                        WorkerActionStatus.PENDING to po3.poNumber,
                    ),
                    it,
                )
            }

        outboxRepository.findAll()
            .map { it.status to it.poNumber }.toSet().let {
                assertEquals(
                    setOf(
                        OutboxItemStatus.PENDING to po1.poNumber,
                        OutboxItemStatus.PENDING to po2.poNumber,
                        OutboxItemStatus.PENDING to po3.poNumber,
                    ),
                    it,
                )
            }

        // and then
        val messages = purchaseOrderConsumer.consumeAtLeast(1, Duration.ofSeconds(10))
        assertThat(
            messages.map { it.key() to it.value().eventType },
            Matchers.hasItems(
                po1.poNumber to PurchaseOrderEvent.Event.EVENT_STATUS_UPDATED,
                po2.poNumber to PurchaseOrderEvent.Event.EVENT_STATUS_UPDATED,
                po3.poNumber to PurchaseOrderEvent.Event.EVENT_STATUS_UPDATED,
            ),
        )
        assertThat(
            messages.find { it.key() == po1.poNumber }?.value()?.payload?.metadata?.state,
            Matchers.equalTo(PurchaseOrderEvent.PurchaseOrder.State.STATE_SENT),
        )
        assertThat(
            messages.find { it.key() == po2.poNumber }?.value()?.payload?.metadata?.state,
            Matchers.equalTo(PurchaseOrderEvent.PurchaseOrder.State.STATE_SENT),
        )
        assertThat(
            messages.find { it.key() == po3.poNumber }?.value()?.payload?.metadata?.state,
            Matchers.equalTo(PurchaseOrderEvent.PurchaseOrder.State.STATE_SENT),
        )
    }

    @Test
    fun `should respond with bad request when a purchase order does not exist`() {
        // given
        savePurchaseOrderRevision(
            poNumber = "po100",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "purchase order number 1",
            isSynced = true,
        )

        // when
        mockMvc.post("/outbox") {
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "purchase_order_numbers": ["po100", "po200"]
                }
            """
        }.andExpect {
            status { isBadRequest() }
            jsonPath("$.message") { value("Purchase order not found: po200") }
        }
    }
}
