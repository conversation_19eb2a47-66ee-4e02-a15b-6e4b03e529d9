package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagementHttp.goodsReceivedNote.service.GoodsReceivedNoteService
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.imports.service.ImportHistoryService
import com.hellofresh.oms.orderManagementHttp.order.getPageOf
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderCreateService
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderDeleteService
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderService
import com.hellofresh.oms.orderManagementHttp.order.service.PurchaseOrderUpdateService
import com.hellofresh.oms.orderManagementHttp.order.service.QueryPurchaseOrderService
import com.hellofresh.oms.orderManagementHttp.outbox.service.OutboxService
import com.hellofresh.oms.orderManagementHttp.sku.SkuService
import com.hellofresh.oms.orderManagementHttp.supplier.service.SupplierService
import com.hellofresh.oms.orderManagementHttp.workerAction.SendOrderScheduler
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.ArgumentMatchers.eq
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@ExtendWith(SpringExtension::class)
@WebMvcTest(PurchaseOrderController::class)
@Suppress("UnusedPrivateProperty", "MaxLineLength", "ForbiddenComment")
class PurchaseOrderControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockitoBean
    lateinit var purchaseOrderServiceMock: PurchaseOrderService

    @MockitoBean
    lateinit var purchaseOrderCreateServiceMock: PurchaseOrderCreateService

    @MockitoBean
    lateinit var purchaseOrderUpdateServiceMock: PurchaseOrderUpdateService

    @MockitoBean
    lateinit var purchaseOrderDeleteServiceMock: PurchaseOrderDeleteService

    @MockitoBean
    lateinit var queryPurchaseOrderServiceMock: QueryPurchaseOrderService

    @MockitoBean
    lateinit var supplierService: SupplierService

    @MockitoBean
    lateinit var goodsReceivedNoteService: GoodsReceivedNoteService

    @MockitoBean
    lateinit var outboxService: OutboxService

    @MockitoBean
    lateinit var workerService: SendOrderScheduler

    @MockitoBean
    // FIXME: @ExtendWith tests are trying to create @Component's such as ResponseConverters, therefore this service is required
    private lateinit var skuService: SkuService

    @MockitoBean
    @Suppress("UnusedPrivateProperty", "MaxLineLength", "ForbiddenComment")
    // FIXME: apparently @ExtendWith tests are trying to create Components such as BatchImportOrdersHistoryResponseConverter, therefore this service is required
    private lateinit var importHistoryService: ImportHistoryService

    @Test
    @WithJWTUser
    fun `should respond with 200 when sending a valid create PO request`() {
        // Given
        val payload = this::class.java.classLoader.getResource(
            "request/purchase-order/po_create_request.json",
        )!!.readText()
        whenever(purchaseOrderCreateServiceMock.processPurchaseOrder(any(), eq(false)))
            .thenReturn(getPurchaseOrderEntity())

        val request = post(ORDERS_PATH)
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)
            .content(payload)

        // when
        mockMvc.perform(request)
            .andExpectAll(
                *mutableListOf(
                    status().isOk,
                    jsonPath("$.id").isNotEmpty,
                ).toTypedArray(),
            )
    }

    @ParameterizedTest
    @ValueSource(
        strings = [
            "request/purchase-order/400_po_no_items.json",
            "request/purchase-order/400_po_wrong_case_packaging.json",
        ],
    )
    @WithJWTUser
    fun `should respond with 400 when sending an invalid create PO request`(resource: String) {
        // Given
        val payload = this::class.java.classLoader.getResource(resource)!!.readText()

        // When
        val request = post(ORDERS_PATH)
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)
            .content(payload)

        // Then
        mockMvc.perform(request).andExpect(status().isBadRequest)
    }

    @Test
    fun `should respond with 401 on invalid token`() {
        // Given
        val payload = this::class.java.classLoader.getResource(
            "request/purchase-order/po_create_request.json",
        )!!.readText()

        val jwtSecretKey = "invalid_secret_key_at_least_256_bits"
        val jwtMissingEmail = Jwts.builder().signWith(Keys.hmacShaKeyFor(jwtSecretKey.toByteArray()))
            .claim("id", "123")
            .compact()

        val request = post(ORDERS_PATH)
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $jwtMissingEmail")
            .content(payload)

        // Then
        mockMvc.perform(request).andExpect(status().isUnauthorized)
    }

    @Test
    fun `should respond with 401 on missing token`() {
        // Given
        val payload = this::class.java.classLoader.getResource(
            "request/purchase-order/po_create_request.json",
        )!!.readText()

        val request = post(ORDERS_PATH)
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)
            .content(payload)

        // Then
        mockMvc.perform(request).andExpect(status().isUnauthorized)
    }

    @Test
    @WithJWTUser
    fun `should respond with 400 when sending an invalid (wrong pattern) list PO request`() {
        // when
        val request = get(ORDERS_PATH)
            .param("dc_weeks", "2023-W18")
            .param("dc_codes", "NJ")
            .param("page", "0")
            .param("size", "10")
            .param("sort", "desc:whatever")
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)

        // when
        mockMvc.perform(request)
            .andExpectAll(
                *mutableListOf(
                    status().isBadRequest,
                ).toTypedArray(),
            )
    }

    @Test
    @WithJWTUser
    fun `should respond with 400 when sending an invalid (unexpected field name) list PO request`() {
        // when
        val request = get(ORDERS_PATH)
            .param("dc_weeks", "2023-W18")
            .param("dc_codes", "NJ")
            .param("page", "0")
            .param("size", "10")
            .param("sort", "+invalidField")
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)

        // when
        mockMvc.perform(request)
            .andExpectAll(
                *mutableListOf(
                    status().isBadRequest,
                ).toTypedArray(),
            )
    }

    @Test
    @WithJWTUser
    fun `should respond with 400 when sending invalid supplier_ids`() {
        // when
        val request = get(ORDERS_PATH)
            .param("dc_weeks", "2023-W18")
            .param("dc_codes", "NJ")
            .param("page", "0")
            .param("size", "10")
            .param("supplier_ids", UUID.randomUUID().toString(), "35661b1f-edd1-4ce2-invalid")
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)

        // when
        mockMvc.perform(request)
            .andExpectAll(
                *mutableListOf(
                    status().isBadRequest,
                ).toTypedArray(),
            )
    }

    @Test
    @WithJWTUser
    fun `should respond with 200 when sending valid supplier_ids`() {
        // given
        val givenDcWeek = "2023-W18"
        val givenDcCode = "NJ"
        val givenPage = 0
        val givenSize = 10
        val givenSupplierId = UUID.randomUUID()
        val givenSupplierIds = listOf(givenSupplierId)

        whenever(
            queryPurchaseOrderServiceMock.paginateBy(
                page = givenPage,
                size = givenSize,
                dcWeeks = listOf(YearWeek(givenDcWeek)),
                dcCodes = listOf(givenDcCode),
                deliveryWindowStartFrom = null,
                deliveryWindowStartTo = null,
                sort = null,
                shippingMethod = null,
                categories = null,
                supplierIds = givenSupplierIds,
                poNumber = null,
                status = null,
                type = null,
                userEmail = null,
                sku = null,
                sendStatus = null,
                grnStatus = null,
            ),
        ).thenReturn(
            getPageOf(
                listOf(getPurchaseOrderEntity(supplierId = givenSupplierId)),
            ),
        )
        whenever(supplierService.getSuppliersByIds(listOf(givenSupplierId)))
            .thenReturn(listOf(getSupplierEntity(supplierId = givenSupplierId, market = "dach")))

        // when
        val request = get(ORDERS_PATH)
            .param("dc_weeks", givenDcWeek)
            .param("dc_codes", givenDcCode)
            .param("page", givenPage.toString())
            .param("size", givenSize.toString())
            .param("supplier_ids", givenSupplierId.toString())
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)

        // when
        mockMvc.perform(request)
            .andExpectAll(
                *mutableListOf(
                    status().isOk,
                ).toTypedArray(),
            )
    }

    @ParameterizedTest
    @EnumSource(ListPurchaseOrdersSortEnum::class)
    @WithJWTUser
    fun `should respond with 200 when sending a valid sort on list PO request`(givenSort: ListPurchaseOrdersSortEnum) {
        // given
        val givenDcWeek = "2023-W18"
        val givenDcCode = "NJ"
        val givenPage = 0
        val givenSize = 10
        val givenSupplierId = UUID.randomUUID()

        whenever(
            queryPurchaseOrderServiceMock.paginateBy(
                page = givenPage,
                size = givenSize,
                dcWeeks = listOf(YearWeek(givenDcWeek)),
                dcCodes = listOf(givenDcCode),
                deliveryWindowStartFrom = null,
                deliveryWindowStartTo = null,
                sort = givenSort,
                shippingMethod = null,
                categories = null,
                supplierIds = null,
                poNumber = null,
                status = null,
                type = null,
                userEmail = null,
                sku = null,
                sendStatus = null,
                grnStatus = null,
            ),
        ).thenReturn(
            getPageOf(
                listOf(getPurchaseOrderEntity(supplierId = givenSupplierId)),
            ),
        )
        whenever(supplierService.getSuppliersByIds(listOf(givenSupplierId)))
            .thenReturn(listOf(getSupplierEntity(supplierId = givenSupplierId, market = "dach")))

        // when
        val request = get(ORDERS_PATH)
            .param("dc_weeks", givenDcWeek)
            .param("dc_codes", givenDcCode)
            .param("page", givenPage.toString())
            .param("size", givenSize.toString())
            .param("sort", givenSort.value)
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)

        // when
        mockMvc.perform(request)
            .andExpectAll(
                *mutableListOf(
                    status().isOk,
                ).toTypedArray(),
            )
    }

    @Test
    @WithJWTUser
    fun `should call outbox service sendOrders method when sendOrders was called`() {
        // given
        val poUuid = UUID.randomUUID()

        whenever(outboxService.sendOrder(any(), any())).thenReturn(poUuid)

        val poNumber = getPurchaseOrderEntity().poNumber

        // when
        val request = post("/orders/$poNumber/revisions/$poUuid/send")
            .with(csrf())
            .contentType(ORDER_CONTENT_TYPE)

        mockMvc.perform(request)
            .andExpectAll(
                *mutableListOf(
                    status().isOk,
                ).toTypedArray(),
            )

        // then
        verify(outboxService, times(1)).sendOrder(any(), any())
    }

    @Test
    @WithJWTUser
    fun `should call worker service sendBulkOrders method when sendBulkOrders was called`() {
        // given
        val request = post("/outbox")
            .with(csrf())
            .contentType("application/json")
            .content(
                """
                { "purchase_order_numbers": ["${getPurchaseOrderEntity().poNumber}"] }
                """.trimIndent(),
            )

        mockMvc.perform(request)
            .andExpectAll(
                *mutableListOf(
                    status().isOk,
                ).toTypedArray(),
            )

        // then
        verify(workerService, times(1)).queueBulkOrders(any(), any())
    }

    companion object {
        private const val ORDERS_PATH = "/orders"
        private const val ORDER_CONTENT_TYPE = "application/json"
    }
}
