package com.hellofresh.oms.orderManagementHttp.comments.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.orderManagement.generated.api.model.CreateCommentResponse
import com.hellofresh.oms.orderManagementHttp.comments.out.CommentRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlMergeMode
import org.springframework.test.context.jdbc.SqlMergeMode.MergeMode
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.testcontainers.shaded.org.hamcrest.CoreMatchers

class CreateCommentControllerTest : AbstractCommentControllerTest() {
    @Autowired
    lateinit var commentRepository: CommentRepository

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/comments.sql"])
    fun `should create comment successfully`() {
        // given
        val request = """
            {
                "domain": "test-domain",
                "resource_type": "sku",
                "resource_id": "DAI-10-10088-4",
                "comment": "Test comment",
                "dc": "NJ",
                "week": "2025-W10",
                "brand": "HF"
            }
        """.trimIndent()

        // when
        val mvcResult = mockMvc.post(COMMENTS_PATH) {
            contentType = APPLICATION_CONTENT
            content = request
        }.andExpect {
            status { isCreated() }
            jsonPath("$.id").exists()
        }.andReturn()

        // then
        val response = objectMapper.readValue(
            mvcResult.response.contentAsString,
            CreateCommentResponse::class.java,
        )
        val comment = commentRepository.findById(response.id).get()
        assertNotNull(comment)
        assertEquals("test-domain", comment.domain)
        assertEquals("sku", comment.resourceType)
        assertEquals("DAI-10-10088-4", comment.resourceId)
        assertEquals("Test comment", comment.comment)
        assertEquals("NJ", comment.dc)
        assertEquals("HF", comment.brand)
        assertEquals("2025-W10", comment.yearWeek.toString())
        assertEquals("<EMAIL>", comment.createdBy)
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/comments.sql"])
    fun `should return bad request when required fields are missing`() {
        // given
        val request = """
            {
                "domain": "",
                "resource_type": "",
                "resource_id": "",
                "comment": "",
                "dc": "",
                "week": "",
                "brand": ""
            }
        """.trimIndent()

        // when/then
        mockMvc.post(COMMENTS_PATH) {
            contentType = APPLICATION_CONTENT
            content = request
        }.andExpect {
            status { isBadRequest() }
        }
    }

    @ParameterizedTest
    @MethodSource("provideMissingRequiredFields")
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/comments.sql"])
    fun `should return bad request with specific message when field is missing`(
        field: Map<String, String?>,
        expectedStatus: HttpStatus
    ) {
        // given
        val defaultValues = mapOf(
            "domain" to "test-domain",
            "resource_type" to "sku",
            "resource_id" to "DAI-10-10088-4",
            "comment" to "Test comment",
            "dc" to "NJ",
            "week" to "2025-W10",
            "brand" to "HF",
        )
        val requestValues = defaultValues.mapValues { (defaultField, defaultValue) ->
            field[defaultField] ?: defaultValue
        }
        val request = objectMapper.writeValueAsString(requestValues)

        // when/then
        mockMvc.post(COMMENTS_PATH) {
            contentType = APPLICATION_CONTENT
            content = request
        }.andExpect {
            status { CoreMatchers.equalTo(expectedStatus) }
        }
    }

    companion object {
        @JvmStatic
        fun provideMissingRequiredFields() = listOf(
            Arguments.of(mapOf("domain" to null), HttpStatus.BAD_REQUEST),
            Arguments.of(mapOf("resource_type" to null), HttpStatus.BAD_REQUEST),
            Arguments.of(mapOf("resource_id" to null), HttpStatus.BAD_REQUEST),
            Arguments.of(mapOf("comment" to null), HttpStatus.BAD_REQUEST),
            Arguments.of(mapOf("dc" to null), HttpStatus.BAD_REQUEST),
            Arguments.of(mapOf("week" to null), HttpStatus.BAD_REQUEST),
            Arguments.of(mapOf("brand" to "XX"), HttpStatus.BAD_REQUEST),
            Arguments.of(mapOf("brand" to "GC"), HttpStatus.CREATED),
        )
    }
}
