package com.hellofresh.oms.orderManagementHttp.supplierSku

import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.Sku
import com.hellofresh.oms.model.SkuStatus
import com.hellofresh.oms.model.SupplierContact
import com.hellofresh.oms.model.SupplierSku
import com.hellofresh.oms.model.SupplierSkuPackaging
import com.hellofresh.oms.model.SupplierSkuPrice
import com.hellofresh.oms.model.SupplierSkuStatus
import com.hellofresh.oms.model.UOM.KG
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.model.supplier.SupplierStatus.ACTIVE
import com.hellofresh.oms.orderManagementHttp.sku.SkuService
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import com.hellofresh.oms.orderManagementHttp.supplierSku.intake.SupplierSkuSearch
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.SupplierSkuPackagingRepository
import com.hellofresh.oms.orderManagementHttp.supplierSku.out.SupplierSkuRepository
import com.hellofresh.oms.orderManagementHttp.supplierSku.service.PriceService
import com.hellofresh.oms.orderManagementHttp.supplierSku.service.SupplierSkuService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Optional
import java.util.UUID.randomUUID
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.data.domain.PageImpl

class SupplierSkuServiceTest {

    private val priceServiceMock: PriceService = mock(PriceService::class.java)
    private val skuServiceMock: SkuService = mock(SkuService::class.java)
    private val supplierRepositoryMock: SupplierRepository = mock(SupplierRepository::class.java)
    private val supplierSkuRepository: SupplierSkuRepository = mock(SupplierSkuRepository::class.java)
    private val supplierSkuPackagingRepository: SupplierSkuPackagingRepository =
        mock(SupplierSkuPackagingRepository::class.java)

    private val subject: SupplierSkuService = SupplierSkuService(
        priceService = priceServiceMock,
        skuService = skuServiceMock,
        supplierRepository = supplierRepositoryMock,
        supplierSkuRepository = supplierSkuRepository,
        supplierSkuPackagingRepository = supplierSkuPackagingRepository,
    )

    @Test
    fun `should throw exception when supplier not found`() {
        // given
        whenever(supplierRepositoryMock.findById(any())).thenReturn(Optional.empty())

        // when
        val exception = assertThrows<IllegalArgumentException> { subject.getSkus(supplierSkuSearch) }

        // then
        assertEquals("Supplier not found id: $SUPPLIER_ID", exception.message)
    }

    @Test
    fun `should return empty list when no skus found`() {
        // given
        whenever(supplierRepositoryMock.findById(any())).thenReturn(Optional.of(supplier))
        whenever(skuServiceMock.getSkusForMarketOrderBySupplierAssignedFirst(any(), any()))
            .thenReturn(PageImpl(mutableListOf<Sku>()))
        whenever(priceServiceMock.getActivePrices(any(), any(), any()))
            .thenReturn(emptyMap())
        whenever(supplierSkuRepository.findAllByParentSupplierIdIsInAndStatusIsNot(any(), any()))
            .thenReturn(emptyList())
        whenever(supplierSkuPackagingRepository.findAllBySupplierSkuIdIn(any()))
            .thenReturn(emptyList())

        // when
        val result = subject.getSkus(supplierSkuSearch)

        // then
        assertEquals(0, result.pageNumber)
        assertEquals(0, result.pageSize)
        assertEquals(0, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals("UNSORTED", result.sort.toString())
        assertEquals(emptyList(), result.supplierSkus)
    }

    @Test
    fun `should return value of supplierId for associated and null for other skus`() {
        // given
        val skuAssociated = sku.copy(uuid = randomUUID(), name = "With Supplier")
        val skuNotAssociated = sku.copy(uuid = randomUUID(), name = "NO Supplier")

        whenever(supplierRepositoryMock.findById(any())).thenReturn(Optional.of(supplier))
        whenever(skuServiceMock.getSkusForMarketOrderBySupplierAssignedFirst(any(), any()))
            .thenReturn(PageImpl(listOf(skuAssociated, skuNotAssociated)))
        whenever(priceServiceMock.getActivePrices(any(), any(), any()))
            .thenReturn(emptyMap())
        whenever(supplierSkuRepository.findAllByParentSupplierIdIsInAndStatusIsNot(any(), any()))
            .thenReturn(listOf(supplierSku.copy(skuId = skuAssociated.uuid)))
        whenever(supplierSkuPackagingRepository.findAllBySupplierSkuIdIn(listOf(supplierSku.uuid)))
            .thenReturn(listOf(skuPackaging.copy(supplierSkuId = supplierSku.uuid, casesPerPallet = 5)))

        // when
        val result = subject.getSkus(supplierSkuSearch.copy(includeAll = true))

        // then
        assertEquals(0, result.pageNumber)
        assertEquals(2, result.pageSize)
        assertEquals(2, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals("UNSORTED", result.sort.toString())
        assertEquals(SUPPLIER_ID, result.supplierSkus[0].supplierId)
        assertEquals(supplierSku.status, result.supplierSkus[0].status)
        assertEquals(5, result.supplierSkus[0].casesPerPallet)
        assertEquals("With Supplier", result.supplierSkus[0].name)
        assertEquals("NO Supplier", result.supplierSkus[1].name)
        assertNull(result.supplierSkus[1].supplierId)
        assertNull(result.supplierSkus[1].status)
    }

    @Test
    fun `should assign price to particular sku`() {
        // given
        val skuWithPrice = sku.copy(uuid = randomUUID(), name = "With Price")
        val skuNoPrice = sku.copy(uuid = randomUUID(), name = "NO Price")
        val supplierSkuWithPrice = supplierSku.copy(uuid = randomUUID(), skuId = skuWithPrice.uuid)
        val supplierSkuNoPrice = supplierSku.copy(uuid = randomUUID(), skuId = skuNoPrice.uuid)
        val price = supplierSkuPrice.copy(supplierSkuId = supplierSkuWithPrice.uuid)

        whenever(supplierRepositoryMock.findById(any())).thenReturn(Optional.of(supplier))
        whenever(skuServiceMock.getSkusForMarketOrderBySupplierAssignedFirst(any(), any()))
            .thenReturn(PageImpl(listOf(skuNoPrice, skuWithPrice)))
        whenever(priceServiceMock.getActivePrices(any(), any(), any())).thenReturn(
            mapOf(supplierSkuWithPrice.uuid to price),
        )
        whenever(supplierSkuRepository.findAllByParentSupplierIdIsInAndStatusIsNot(any(), any()))
            .thenReturn(listOf(supplierSkuWithPrice, supplierSkuNoPrice))

        // when
        val result = subject.getSkus(supplierSkuSearch)

        // then
        assertEquals(0, result.pageNumber)
        assertEquals(2, result.pageSize)
        assertEquals(2, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals("UNSORTED", result.sort.toString())

        assertEquals(SUPPLIER_ID, result.supplierSkus[1].supplierId)
        assertEquals("NO Price", result.supplierSkus[0].name)
        assertNull(result.supplierSkus[0].pricePermyriad)

        assertEquals(SUPPLIER_ID, result.supplierSkus[1].supplierId)
        assertEquals("With Price", result.supplierSkus[1].name)
        assertEquals(10, result.supplierSkus[1].pricePermyriad!!.value)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "case_type,10",
            "unit_type,1",
            "repackaging_type,1",
        ],
    )
    fun `should assign correct price type`(priceType: String, pricePermyriad: Int) {
        // given
        val sku = sku.copy(uuid = randomUUID())
        val supplierSku = supplierSku.copy(uuid = randomUUID(), skuId = sku.uuid)
        val price = supplierSkuPrice.copy(supplierSkuId = supplierSku.uuid, priceType = priceType)

        whenever(supplierRepositoryMock.findById(any())).thenReturn(Optional.of(supplier))
        whenever(skuServiceMock.getSkusForMarketOrderBySupplierAssignedFirst(any(), any()))
            .thenReturn(PageImpl(listOf(sku)))
        whenever(priceServiceMock.getActivePrices(any(), any(), any())).thenReturn(
            mapOf(supplierSku.uuid to price),
        )
        whenever(supplierSkuRepository.findAllByParentSupplierIdIsInAndStatusIsNot(any(), any()))
            .thenReturn(listOf(supplierSku))

        // when
        val result = subject.getSkus(supplierSkuSearch)

        // then
        assertEquals(0, result.pageNumber)
        assertEquals(1, result.pageSize)
        assertEquals(1, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals("UNSORTED", result.sort.toString())

        assertEquals(SUPPLIER_ID, result.supplierSkus[0].supplierId)
        assertEquals(pricePermyriad, result.supplierSkus[0].pricePermyriad!!.value)
    }

    companion object {
        private const val DC = "NJ"
        private const val MARKET = "us"
        private const val SUPPLIER_CODE = "12345"
        private val SUPPLIER_ID = randomUUID()
        private val SUPPLIER_PARENT_ID = randomUUID()
        val supplierSkuSearch = SupplierSkuSearch(
            includeAll = false,
            market = MARKET,
            // TODO check default
            search = "",
            supplierId = SUPPLIER_ID,
            dcCode = DC,
            deliveryDate = LocalDate.now(),
            page = 0,
            size = 100,
        )

        val sku = Sku(
            uuid = randomUUID(),
            market = MARKET,
            name = "name",
            code = "SKU-134-1234",
            status = SkuStatus.ACTIVE,
            brands = listOf("brand"),
            category = "category",
            uom = KG,
        )

        val supplierSkuPrice = SupplierSkuPrice(
            uuid = randomUUID(),
            supplierSkuId = randomUUID(),
            dcCodes = listOf(),
            startDate = LocalDateTime.now(),
            endDate = LocalDateTime.now().plusDays(5),
            enabled = true,
            priceType = "case_type",
            market = "us",
            currency = "USD",
            pricePermyriad = Permyriad(value = "1"),
            bufferPermyriad = Permyriad(value = "1"),
            caseSize = null,
            unitsPerCase = BigDecimal(10),
            casePrice = Permyriad(10),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )

        val supplierSku = SupplierSku(
            uuid = randomUUID(),
            parentSupplierId = SUPPLIER_PARENT_ID,
            skuId = randomUUID(),
            market = MARKET,
            status = SupplierSkuStatus.ACTIVE,
            supplierCode = SUPPLIER_CODE,
        )

        val supplier = SupplierExtended(
            id = SUPPLIER_ID,
            parentId = randomUUID(),
            market = "dach",
            code = SUPPLIER_CODE.toInt(),
            name = "Supplier Name",
            status = ACTIVE,
            currency = "EUR",
            type = "Manufacturer",
            createdAt = LocalDateTime.parse("2023-04-05T18:46:00"),
            updatedAt = LocalDateTime.parse("2023-04-05T18:46:00"),
            dcCodes = listOf("NJ"),
            supplierAddress = SupplierAddress(
                "Berlin",
                "Germany",
                state = "Berlin",
                address = "HelloFresh Str. 1",
                number = "1234",
                postCode = "1234",
            ),
            contacts = setOf(
                SupplierContact(
                    supplierId = SUPPLIER_ID,
                    email = "<EMAIL>",
                ),
            ),
            shipMethods = emptyList(),
        )

        val skuPackaging = SupplierSkuPackaging(
            supplierSkuId = SUPPLIER_ID,
            casesPerPallet = 0,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )
    }
}
