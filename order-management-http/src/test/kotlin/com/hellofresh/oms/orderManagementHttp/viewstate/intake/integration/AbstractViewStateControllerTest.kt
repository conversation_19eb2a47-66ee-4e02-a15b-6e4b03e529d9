package com.hellofresh.oms.orderManagementHttp.viewstate.intake.integration

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
abstract class AbstractViewStateControllerTest : AbstractIntegrationTest() {

    @Autowired
    lateinit var mockMvc: MockMvc

    companion object {
        protected const val VIEW_STATES_PATH = "/view-states"
        protected const val LOGGED_IN_USER_UUID = "0d6242f7-fe3e-46fc-a676-8f5de31c0381"
        protected const val MY_VIEW_STATE_ID = 1L
        protected const val OTHER_USER_VIEW_STATE_ID = 4L
        protected const val NON_EXISTING_VIEW_STATE_ID = 7L

        @JvmStatic
        protected val APPLICATION_CONTENT = MediaType.parseMediaType("application/json")
    }
}
