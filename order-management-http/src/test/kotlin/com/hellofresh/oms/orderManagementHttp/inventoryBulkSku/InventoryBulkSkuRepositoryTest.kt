package com.hellofresh.oms.orderManagementHttp.inventoryBulkSku

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import kotlin.test.assertTrue
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace.NONE
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
@DataJpaTest
@AutoConfigureTestDatabase(replace = NONE)
@Tag("integration")
@Sql(
    executionPhase = BEFORE_TEST_METHOD,
    scripts = [
        "/data/inventoryBulkSku.sql"
    ],
)
class InventoryBulkSkuRepositoryTest : AbstractIntegrationTest() {

    @Autowired
    lateinit var inventoryBulkSkuRepository: InventoryBulkSkuRepository

    @Test
    fun `should map to entity correctly`() {
        val inventoryBulkSku = inventoryBulkSkuRepository.findAll().first()
        assertTrue(inventoryBulkSku.brands.isNotEmpty())
    }
}
