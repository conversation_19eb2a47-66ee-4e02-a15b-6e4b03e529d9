package com.hellofresh.oms.orderManagementHttp.icsTicket.task

import com.hellofresh.oms.orderManagementHttp.icsTicket.Fixture
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketDto
import com.hellofresh.oms.orderManagementHttp.icsTicket.service.IcsTicketService
import com.hellofresh.oms.orderManagementHttp.icsTicket.service.IcsTicketService.Companion.UPDATED_SINCE_PATTERN
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.times
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class IcsTicketTaskTest {

    @Mock
    private lateinit var icsTicketService: IcsTicketService

    private lateinit var subject: IcsTicketTask

    @BeforeEach
    fun setUp() {
        subject = IcsTicketTask(icsTicketService, PAGE_SIZE)
    }

    @Test
    fun `should trigger fetching ics tickets`() {
        whenever(icsTicketService.resolveLatestFetchTime())
            .thenReturn(DateTimeFormatter.ofPattern(UPDATED_SINCE_PATTERN).format(LocalDateTime.now(ZoneOffset.UTC)))
        whenever(icsTicketService.fetchTickets(any(), any(), any()))
            .thenReturn(Fixture.createIcsTicketServiceResponse().tickets)
        subject.processFetchIcsTickets()
        verify(icsTicketService, times(1)).fetchTickets(any(), any(), any())
    }

    @Test
    fun `should re trigger fetching ics tickets until there is no more tickets`() {
        val pageSize = 1
        whenever(icsTicketService.resolveLatestFetchTime())
            .thenReturn(DateTimeFormatter.ofPattern(UPDATED_SINCE_PATTERN).format(LocalDateTime.now(ZoneOffset.UTC)))
        whenever(icsTicketService.fetchTickets(any(), any(), any()))
            .thenReturn(
                Fixture.createIcsTicketServiceResponse().tickets,
                emptyList()
            )
        subject = IcsTicketTask(icsTicketService, pageSize)

        subject.processFetchIcsTickets()
        verify(icsTicketService, times(2)).fetchTickets(any(), any(), any())
    }

    @Test
    fun `should re trigger fetching ics tickets until there is less tickets than page size`() {
        val pageSize = 2
        whenever(icsTicketService.resolveLatestFetchTime())
            .thenReturn(DateTimeFormatter.ofPattern(UPDATED_SINCE_PATTERN).format(LocalDateTime.now(ZoneOffset.UTC)))
        subject = IcsTicketTask(icsTicketService, pageSize)

        val tickets = mutableListOf<IcsTicketDto>()
        (1..pageSize).forEach { i ->
            tickets.add(Fixture.createIcsTicketServiceResponse().tickets.first())
        }

        whenever(icsTicketService.fetchTickets(any(), any(), any()))
            .thenReturn(
                tickets,
                Fixture.createIcsTicketServiceResponse().tickets
            )
        subject.processFetchIcsTickets()
        verify(icsTicketService, times(2)).fetchTickets(any(), any(), any())
    }

    companion object {
        private const val PAGE_SIZE = 10
    }
}
