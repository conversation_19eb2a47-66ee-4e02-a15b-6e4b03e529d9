package com.hellofresh.oms.orderManagementHttp.workerAction

import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerAction.Companion.createWorkerAction
import com.hellofresh.oms.model.WorkerActionData.SyncBatchOrder
import com.hellofresh.oms.model.WorkerActionType.SYNC_BATCH_ORDER
import com.hellofresh.oms.model.importHistory.ImportHistory
import com.hellofresh.oms.model.importHistory.ImportHistorySummary.BatchPoCreation
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getTestUser
import java.time.LocalDateTime
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class SyncBatchOrderSchedulerTest {
    @Mock
    private lateinit var workerActionRepository: WorkerActionRepository

    @InjectMocks
    private lateinit var subject: SyncBatchOrderScheduler

    @Test
    fun `should convert all purchase orders into worker actions and save`() {
        // given
        val poNumber1 = "PO-1"
        val givenPurchaseOrder = getPurchaseOrderEntity(poNumber = poNumber1, id = UUID.randomUUID())
        val givenMarket = "us"
        val givenLoggedInUser = getTestUser()
        val givenImportHistory = ImportHistory.createImportHistory(
            filename = "some-file.csv",
            userId = givenLoggedInUser.userId,
            userEmail = givenLoggedInUser.userEmail,
            createdAt = LocalDateTime.now(),
            summary = BatchPoCreation(
                purchaseOrders = listOf(poNumber1),
                markets = listOf(givenMarket),
            ),
        )
        whenever(workerActionRepository.save(any())).thenReturn(
            createWorkerAction(
                SyncBatchOrder(
                    purchaseOrderNumber = poNumber1,
                    importHistoryId = givenImportHistory.id,
                ),
                userEmail = givenImportHistory.userEmail,
                userId = givenImportHistory.userId,
            ),
        )

        // when
        subject.queueSyncBatchOrder(givenPurchaseOrder.poNumber, givenImportHistory)

        // then
        val argumentCaptor = argumentCaptor<WorkerAction>()
        verify(workerActionRepository).save(argumentCaptor.capture())

        val savedWorkerAction = argumentCaptor.firstValue
        assertThat(
            savedWorkerAction.actionType,
            equalTo(SYNC_BATCH_ORDER),
        )
        assertThat(
            savedWorkerAction.getPayload(),
            equalTo(SyncBatchOrder(poNumber1, givenImportHistory.id)),
        )
    }
}
