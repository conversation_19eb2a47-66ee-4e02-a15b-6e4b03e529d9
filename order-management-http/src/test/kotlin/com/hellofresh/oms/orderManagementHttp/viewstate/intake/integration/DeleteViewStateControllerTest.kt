package com.hellofresh.oms.orderManagementHttp.viewstate.intake.integration

import com.hellofresh.oms.orderManagementHttp.viewstate.out.ViewStateRepository
import java.util.UUID
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlMergeMode
import org.springframework.test.context.jdbc.SqlMergeMode.MergeMode
import org.springframework.test.web.servlet.delete

class DeleteViewStateControllerTest : AbstractViewStateControllerTest() {

    @Autowired
    private lateinit var repo: ViewStateRepository

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `deletes the view state`() {
        mockMvc.delete("$VIEW_STATES_PATH/1") {
            accept = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
            jsonPath("$.id") { value(MY_VIEW_STATE_ID) }
        }

        val deletedState = repo.findByUserIdAndId(
            userId = UUID.fromString(LOGGED_IN_USER_UUID),
            stateId = MY_VIEW_STATE_ID,
        )
        assertNull(deletedState)
    }

    @ParameterizedTest
    @MethodSource("invalidRequestProvider")
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/view_states.sql"])
    fun `returns not found on invalid state ids`(viewStateId: Long) {
        mockMvc.delete("$VIEW_STATES_PATH/$viewStateId") {
            accept = APPLICATION_CONTENT
        }.andExpect {
            status { isNotFound() }
        }
    }

    companion object {
        @JvmStatic
        fun invalidRequestProvider() = listOf(
            Arguments.of(OTHER_USER_VIEW_STATE_ID),
            Arguments.of(NON_EXISTING_VIEW_STATE_ID),
        )
    }
}
