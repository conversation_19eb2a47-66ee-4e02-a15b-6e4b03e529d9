package com.hellofresh.oms.orderManagementHttp.outbox

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus.SENT
import com.hellofresh.oms.orderManagement.generated.api.model.SendLogResponse
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.outbox.out.OutboxRepository
import java.time.LocalDateTime
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import uk.org.webcompere.modelassert.json.JsonAssertions.json
import uk.org.webcompere.modelassert.json.JsonProviders.overrideObjectMapper

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(
    executionPhase = BEFORE_TEST_METHOD,
    scripts = [
        "/data/purchaseOrder.sql",
    ],
)
@AutoConfigureWireMock(stubs = ["classpath:/stubs"])
class SendLogIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Autowired
    lateinit var outboxItemRepository: OutboxRepository

    private val currentTime = LocalDateTime.now()

    val outboxItem1 = OutboxItem(
        id = UUID.randomUUID(),
        poId = UUID.fromString("e3fa59aa-91c1-4c78-bfa5-000000000001"),
        poNumber = "2318NJ021004",
        status = SENT,
        userEmail = "<EMAIL>",
        userId = UUID.randomUUID(),
        lastStatusChangeAt = currentTime,
        createdAt = currentTime,
        version = 1,
    )
    val outboxItem2 = OutboxItem(
        id = UUID.randomUUID(),
        poId = UUID.fromString("e3fa59aa-91c1-4c78-bfa5-000000000002"),
        poNumber = "2318NJ021004",
        status = SENT,
        userEmail = "<EMAIL>",
        userId = UUID.randomUUID(),
        lastStatusChangeAt = currentTime,
        createdAt = currentTime,
        version = 1,
    )
    val outboxItem3 = OutboxItem(
        id = UUID.randomUUID(),
        poId = UUID.fromString("e3fa59aa-91c1-4c78-0000-************"),
        poNumber = "XXXXXXXXXXX",
        status = SENT,
        userEmail = "<EMAIL>",
        userId = UUID.randomUUID(),
        lastStatusChangeAt = currentTime,
        createdAt = currentTime,
        version = 1,
    )

    @BeforeEach
    fun setup() {
        listOf(
            outboxItem1,
            outboxItem2,
            outboxItem3,
        ).let(outboxItemRepository::saveAllAndFlush)
        // see: https://github.com/webcompere/model-assert?tab=readme-ov-file#custom-object-mappers
        overrideObjectMapper(objectMapper)
    }

    @Test
    fun `should return empty list of send log items when PO is found without send log items`() {
        val httpResponse = mockMvc.get(
            "/orders/{po_number}/send-log",
            "2318NJ021001",
        ) {
            accept(APPLICATION_CONTENT)
        }.andExpect {
            status().isOk
        }.andReturn()

        val response = objectMapper.readValue(httpResponse.response.contentAsString, SendLogResponse::class.java)
        assertThat(response.sendLogItems, Matchers.empty())
    }

    @Test
    fun `should return 404 when no PO is found`() {
        mockMvc.get(
            "/orders/{po_number}/send-log",
            "23xxxxx",
        ) {
            accept(APPLICATION_CONTENT)
        }.andExpect {
            status().isNotFound
        }
    }

    @Test
    fun `should return the list of send log items when found`() {
        val httpResponse = mockMvc.get(
            "/orders/{po_number}/send-log",
            "2318NJ021004",
        ) {
            accept(APPLICATION_CONTENT)
        }.andExpect {
            status().isOk
        }.andReturn()

        val response = objectMapper.readValue(httpResponse.response.contentAsString, SendLogResponse::class.java)
        assertThat(
            response.sendLogItems.map { it.poId }.toSet(),
            Matchers.equalTo(setOf(outboxItem1.poId, outboxItem2.poId)),
        )
    }

    @Test
    fun `should serialize properly`() {
        mockMvc.get(
            "/orders/{po_number}/send-log",
            "2318NJ021004",
        ) {
            accept(APPLICATION_CONTENT)
        }.andExpect {
            status().isOk
            content().string(
                json()
                    .at("/send_log_items")
                    .isEqualTo(
                        listOf(outboxItem1, outboxItem2).map {
                            mapOf(
                                "id" to it.id,
                                "po_id" to it.poId,
                                "po_number" to it.poNumber,
                                "user_email" to it.userEmail,
                                "user_id" to it.userId,
                                "last_status_change_at" to it.lastStatusChangeAt,
                                "created_at" to it.createdAt,
                                "version" to it.version,
                            )
                        },
                    ),
            )
        }
    }

    companion object {
        private val APPLICATION_CONTENT = MediaType.parseMediaType("application/json")
    }
}
