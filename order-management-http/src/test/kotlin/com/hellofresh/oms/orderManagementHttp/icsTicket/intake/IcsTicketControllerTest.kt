package com.hellofresh.oms.orderManagementHttp.icsTicket.intake

import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.icsTicket.Fixture.createIcsTicket
import com.hellofresh.oms.orderManagementHttp.icsTicket.service.IcsTicketService
import com.hellofresh.oms.orderManagementHttp.imports.service.ImportHistoryService
import com.hellofresh.oms.orderManagementHttp.sku.SkuService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@ExtendWith(SpringExtension::class)
@WebMvcTest(controllers = [IcsTicketController::class])
class IcsTicketControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockitoBean
    private lateinit var icsTicketService: IcsTicketService

    //    Following are unnecessary for this test, but are required since converters are not defined properly
    @MockitoBean
    @Suppress("UnusedPrivateProperty")
    private lateinit var skuService: SkuService

    @MockitoBean
    @Suppress("UnusedPrivateProperty")
    private lateinit var importHistoryService: ImportHistoryService

    @Test
    @WithJWTUser
    fun `should return 200 OK when request is valid`() {
        val poNumber = "po_number"

        whenever(icsTicketService.getIcsTickets(poNumber))
            .thenReturn(listOf(createIcsTicket()))

        mockMvc.perform(
            get(ICS_TICKETS_PATH)
                .param("po_number", poNumber)
        )
            .andExpect(status().isOk)
    }

    companion object {
        private const val ICS_TICKETS_PATH = "/ics-tickets"
    }
}
