package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.SkuStatus
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.getBillingAddress
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.orderManagementHttp.configuration.JTEConfiguration
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomain
import com.hellofresh.oms.orderManagementHttp.order.service.domain.PurchaseOrderForPdfTemplate
import java.io.File
import java.io.FileOutputStream
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.Locale
import java.util.UUID
import org.jsoup.Jsoup
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.core.io.ClassPathResource

class PdfServiceTest {
    private val templateEngine = JTEConfiguration().templateEngine()
    private val pdfService = PdfService(templateEngine)

    @Test
    @Disabled("Use this to manually test if PDF is properly generated")
    fun `should generate PDF`() {
        val purchaseOrder = getPoForPdfTemplate()
        val htmlContent = pdfService.renderHtmlContent(purchaseOrder)
        val pdfContent = pdfService.generatePdfFromHtml(htmlContent)
        FileOutputStream(File("output.pdf")).use {
            it.write(pdfContent)
        }
    }

    @Test
    fun `should map PurchaseOrderForPdfTemplate to html`() {
        // given
        val purchaseOrderForPdfTemplate = getPoForPdfTemplate()
        val expectedHtml = ClassPathResource("static/pdf/pdfGenerationGoldenFile.html").file.readText()

        // when
        val actualHtml = pdfService.renderHtmlContent(purchaseOrderForPdfTemplate)

        // then
        assertEquals(Jsoup.parse(expectedHtml).text(), Jsoup.parse(actualHtml).text())
    }

    @Test
    fun `should map generate HTML with no 'Additional comments' field`() {
        // given
        val purchaseOrderForPdfTemplate1 = getPoForPdfTemplate().copy(comment = null)
        val purchaseOrderForPdfTemplate2 = getPoForPdfTemplate().copy(comment = "")
        val purchaseOrderForPdfTemplate3 = getPoForPdfTemplate().copy(comment = "   ")

        // when
        val actualHtml1 = pdfService.renderHtmlContent(purchaseOrderForPdfTemplate1)
        val actualHtml2 = pdfService.renderHtmlContent(purchaseOrderForPdfTemplate2)
        val actualHtml3 = pdfService.renderHtmlContent(purchaseOrderForPdfTemplate3)

        // then
        assertFalse(Jsoup.parse(actualHtml1).text().contains("Additional comments"))
        assertFalse(Jsoup.parse(actualHtml2).text().contains("Additional comments"))
        assertFalse(Jsoup.parse(actualHtml3).text().contains("Additional comments"))
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "au,en-AU,1/6/25",
            "beneluxfr,fr-FR,01/06/2025",
            "ca,en-CA,2025-06-01",
            "dach,de-DE,01.06.25",
            "dkse,sv-SE,2025-06-01",
            "es,es-ES,1/6/25",
            "eu,en-UG,01/06/2025",
            "fr,fr-FR,01/06/2025",
            "gb,en-GB,01/06/2025",
            "ie,en-IE,01/06/2025",
            "it,it-IT,01/06/25",
            "nl,nl-NL,01-06-2025",
            "nz,en-NZ,1/06/25",
            "se,sv-SE,2025-06-01",
            "us,en-US,6/1/25",
        ]
    )
    fun `should map all available markets to correct locale`(
        market: String,
        localeLanguage: String,
        expectedDateFormat: String
    ) {
        val po = getPoForPdfTemplate(market, localeLanguage)
        assertEquals(expectedDateFormat, po.deliveryDate)
    }

    private fun getPoForPdfTemplate(market: String = "us", localeLanguage: String = "en-US") =
        PurchaseOrderForPdfTemplate.from(
            po = getPurchaseOrderEntity(
                poNumber = "2510NJ701454",
                version = 4,
                updatedAt = LocalDateTime.of(2025, 3, 7, 11, 3),
                yearWeek = YearWeek("2025-W10"),
                sendTime = LocalDateTime.of(2025, 3, 7, 13, 43),
                expectedStartTime = LocalDateTime.of(2025, 6, 1, 9, 0),
                expectedEndTime = LocalDateTime.of(2025, 6, 1, 12, 0),
                comment = """
                    | This is a test comment to meet the requested character count. It should be exactly 240 characters long,
                    | ensuring accuracy. Let’s add more words to reach the limit while maintaining readability and structure. Almost there! Just a few more w
                """.trimMargin(),
                shipMethod = VENDOR,
                totalPrice = Money(
                    currency = "USD",
                    amount = BigDecimal(190).setScale(MoneyDomain.PRECISION.value),
                ),
            ),
            supplier = getSupplierEntity(
                name = "Braga Family Fresh Farms",
                market = market,
                supplierAddress = SupplierAddress(
                    address = "PO Box 93960 CA",
                    city = "Soledad",
                    state = "CA",
                    postCode = "93960",
                    country = "USA",
                    number = ""
                )
            ),
            dcBillingAddress = getDistributionCenterEntity().getBillingAddress()!!,
            disclaimers = listOf(
                "By accepting this purchase order, Supplier agrees to the following terms:",
                "For HF - managed loads, Supplier shall enter relevant information using BluJay upon PO receipt. ",
                "For delivered loads, Supplier shall schedule delivery of the products using BluJay at " +
                    "least twenty-four (24) hours prior to delivery.",
            ),
            skus = listOf(
                getSkuEntity(
                    uuid = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                    code = "DRY-10-10980-1",
                    name = "Almonds, Sliced - 0.5 Ounce (oz)",
                    status = SkuStatus.ACTIVE
                ),
            ),
            locale = Locale.forLanguageTag(localeLanguage)
        )
}
