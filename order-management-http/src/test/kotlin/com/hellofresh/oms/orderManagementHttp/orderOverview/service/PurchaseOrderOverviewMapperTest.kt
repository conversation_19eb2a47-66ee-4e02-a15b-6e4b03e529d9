package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.acknowledgement.UnitOfMeasureAcknowledgementLineEnum.UNIT_OF_MEASURE_UNIT
import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewShipMethodEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PoOverviewStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrdersOverviewResponseInner
import com.hellofresh.oms.orderManagementHttp.orderOverview.Fixture
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll

@Suppress("LongMethod")
class PurchaseOrderOverviewMapperTest {

    @Test
    fun `should map purchase order overview model to api response`() {
        // given
        val purchaseOrderOverviewModel = Fixture.getPurchaseOrderOverviewModel()

        // when
        val purchaseOrderOverviewApiResponse = purchaseOrderOverviewModel.mapToPurchaseOrderOverviewModelToApiResponse()

        // then
        assertAll(
            { assertEquals(purchaseOrderOverviewModel.poId, purchaseOrderOverviewApiResponse.poId) },
            { assertEquals(purchaseOrderOverviewModel.poNumber, purchaseOrderOverviewApiResponse.poNumber) },
            { assertEquals(purchaseOrderOverviewModel.poVersion, purchaseOrderOverviewApiResponse.poVersion) },
            { assertEquals(PoOverviewStatusEnum.RECEIVED_ACCURATE, purchaseOrderOverviewApiResponse.status) },
            { assertEquals(purchaseOrderOverviewModel.supplierName, purchaseOrderOverviewApiResponse.supplierName) },
            { assertEquals(purchaseOrderOverviewModel.supplierCode, purchaseOrderOverviewApiResponse.supplierCode) },
            { assertEquals(purchaseOrderOverviewModel.skuName, purchaseOrderOverviewApiResponse.skuName) },
            { assertEquals(purchaseOrderOverviewModel.skuCode, purchaseOrderOverviewApiResponse.skuCode) },
            {
                assertEquals(
                    PurchaseOrdersOverviewResponseInner.RiskAssessment.NONE,
                    purchaseOrderOverviewApiResponse.riskAssessment,
                )
            },
            {
                assertEquals(
                    purchaseOrderOverviewModel.scheduledDeliveryDate,
                    purchaseOrderOverviewApiResponse.scheduledDeliveryDate,
                )
            },
            {
                assertEquals(
                    purchaseOrderOverviewModel.emergencyReason,
                    purchaseOrderOverviewApiResponse.emergencyReason,
                )
            },
            { assertEquals(purchaseOrderOverviewModel.creatorEmail, purchaseOrderOverviewApiResponse.creatorEmail) },
            { assertEquals(purchaseOrderOverviewModel.orderSize, purchaseOrderOverviewApiResponse.orderSize) },
            { assertEquals("1000.0000", purchaseOrderOverviewApiResponse.totalPrice.amount) },
            { assertEquals("USD", purchaseOrderOverviewApiResponse.totalPrice.currency) },
            { assertEquals(purchaseOrderOverviewModel.week, purchaseOrderOverviewApiResponse.week) },
            { assertEquals(purchaseOrderOverviewModel.dcCode, purchaseOrderOverviewApiResponse.dcCode) },
            { assertEquals(purchaseOrderOverviewModel.category, purchaseOrderOverviewApiResponse.category) },
            { assertEquals(PoOverviewShipMethodEnum.CROSSDOCK, purchaseOrderOverviewApiResponse.shippingMethod) },
            { assertEquals("17.5000", purchaseOrderOverviewApiResponse.casePrice.amount) },
            { assertEquals("USD", purchaseOrderOverviewApiResponse.casePrice.currency) },
            { assertEquals(purchaseOrderOverviewModel.caseSize, purchaseOrderOverviewApiResponse.caseSize) },
            { assertEquals(purchaseOrderOverviewModel.orderUnit, purchaseOrderOverviewApiResponse.orderUnit.value) },
            {
                assertEquals(
                    purchaseOrderOverviewModel.purchasingUnit,
                    purchaseOrderOverviewApiResponse.purchasingUnit.value,
                )
            },
            {
                assertEquals(
                    purchaseOrderOverviewModel.quantityOrdered,
                    purchaseOrderOverviewApiResponse.quantityOrdered,
                )
            },
            {
                assertEquals(
                    purchaseOrderOverviewModel.quantityReceived,
                    purchaseOrderOverviewApiResponse.quantityReceived,
                )
            },
            {
                assertEquals(
                    purchaseOrderOverviewModel.casesReceived,
                    purchaseOrderOverviewApiResponse.casesReceived,
                )
            },
            {
                assertEquals(
                    purchaseOrderOverviewModel.quantityReceived!! / BigDecimal(purchaseOrderOverviewModel.casesReceived!!),
                    purchaseOrderOverviewApiResponse.caseSizeReceived,
                )
            },
        )
    }

    @Test
    fun `should map Receipt Override data to api response`() {
        val purchaseOrderOverviewModel = Fixture.getPurchaseOrderOverviewModel()

        val purchaseOrderOverviewApiResponse = purchaseOrderOverviewModel.mapToPurchaseOrderOverviewModelToApiResponse()

        assertEquals(
            purchaseOrderOverviewModel.receiptOverrideQuantity,
            purchaseOrderOverviewApiResponse.receiptOverride.quantity
        )
        assertEquals(
            purchaseOrderOverviewModel.receiptOverrideCasesReceived,
            purchaseOrderOverviewApiResponse.receiptOverride.casesReceived
        )
        assertEquals(
            purchaseOrderOverviewModel.receiptOverrideDeliveryDate,
            purchaseOrderOverviewApiResponse.receiptOverride.deliveryDate
        )
        assertEquals(
            purchaseOrderOverviewModel.receiptOverrideDetails,
            purchaseOrderOverviewApiResponse.receiptOverride.details
        )
        assertEquals(
            purchaseOrderOverviewModel.receiptOverrideCreatedAt,
            purchaseOrderOverviewApiResponse.receiptOverride.createdAt
        )
        assertEquals(
            purchaseOrderOverviewModel.receiptOverrideUpdatedBy,
            purchaseOrderOverviewApiResponse.receiptOverride.updatedBy
        )
    }

    @Test
    fun `should set caseSizeReceived to null if quantityReceived or casesReceived is null`() {
        val baseModel = Fixture.getPurchaseOrderOverviewModel()

        val modelWithNullQuantity = baseModel.copy(quantityReceived = null, casesReceived = 5)
        val modelWithNullCases = baseModel.copy(quantityReceived = BigDecimal(10), casesReceived = null)
        val modelWithBothNull = baseModel.copy(quantityReceived = null, casesReceived = null)

        val apiResponseWithNullQuantity = modelWithNullQuantity.mapToPurchaseOrderOverviewModelToApiResponse()
        val apiResponseWithNullCases = modelWithNullCases.mapToPurchaseOrderOverviewModelToApiResponse()
        val apiResponseWithBothNull = modelWithBothNull.mapToPurchaseOrderOverviewModelToApiResponse()

        assertAll(
            { assertEquals(null, apiResponseWithNullQuantity.caseSizeReceived) },
            { assertEquals(null, apiResponseWithNullCases.caseSizeReceived) },
            { assertEquals(null, apiResponseWithBothNull.caseSizeReceived) },
        )
    }

    @Test
    fun `should map PurchaseOrderStatusDetails PO data to service response`() {
        // given
        val purchaseOrderStatusDetails = Fixture.createPurchaseOrderStatusDetails()

        // when
        val purchaseOrderOverviewModel = purchaseOrderStatusDetails.mapToPurchaseOrderOverviewModel("HF")

        // then
        assertAll(
            { assertEquals(purchaseOrderStatusDetails.poId, purchaseOrderOverviewModel.poId) },
            { assertEquals(purchaseOrderStatusDetails.poNumber, purchaseOrderOverviewModel.poNumber) },
            { assertEquals(purchaseOrderStatusDetails.version, purchaseOrderOverviewModel.poVersion) },
            {
                assertEquals(
                    PoOverviewStatusEnum.RECEIVED_ACCURATE,
                    purchaseOrderOverviewModel.status,
                )
            },
            { assertEquals(purchaseOrderStatusDetails.supplierName, purchaseOrderOverviewModel.supplierName) },
            { assertEquals(purchaseOrderStatusDetails.supplierCode, purchaseOrderOverviewModel.supplierCode) },
            { assertEquals(purchaseOrderStatusDetails.skuName, purchaseOrderOverviewModel.skuName) },
            { assertEquals(purchaseOrderStatusDetails.skuCode, purchaseOrderOverviewModel.skuCode) },
            {
                assertEquals(
                    purchaseOrderStatusDetails.expectedStartTime.toLocalDateTime(),
                    purchaseOrderOverviewModel.scheduledDeliveryDate,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.emergencyReason,
                    purchaseOrderOverviewModel.emergencyReason,
                )
            },
            { assertEquals(purchaseOrderStatusDetails.creatorEmail, purchaseOrderOverviewModel.creatorEmail) },
            { assertEquals(purchaseOrderStatusDetails.week, purchaseOrderOverviewModel.week) },
            { assertEquals(purchaseOrderStatusDetails.dcCode, purchaseOrderOverviewModel.dcCode) },
            { assertEquals(purchaseOrderStatusDetails.category, purchaseOrderOverviewModel.category) },
            { assertEquals(ShipMethodEnum.CROSSDOCK.toString(), purchaseOrderOverviewModel.shippingMethod) },
            { assertEquals(purchaseOrderStatusDetails.caseSize, purchaseOrderOverviewModel.caseSize) },
            {
                assertEquals(
                    purchaseOrderStatusDetails.receiptOverrideCasesReceived,
                    purchaseOrderOverviewModel.casesReceived,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.exportReceiptQuantityReceived,
                    purchaseOrderOverviewModel.quantityReceived,
                )
            },
            {
                assertEquals(
                    "${purchaseOrderStatusDetails.assignedBuyerFirstName} ${purchaseOrderStatusDetails.assignedBuyerLastName}",
                    purchaseOrderOverviewModel.assignedBuyer,
                )
            },
            {
                assertEquals(
                    purchaseOrderOverviewModel.phfDeliveryPercentOfForecast,
                    purchaseOrderStatusDetails.exportReceiptQuantityReceived
                        ?.divide(purchaseOrderStatusDetails.weeklyForecastedDemand, 4, RoundingMode.HALF_UP)
                        ?.multiply(BigDecimal(100))
                        ?.setScale(2, RoundingMode.HALF_UP)
                )
            },
        )
    }

    @Test
    fun `should map Receipt Override data to service response`() {
        val purchaseOrderStatusDetails = Fixture.createPurchaseOrderStatusDetails()

        val purchaseOrderOverviewModel = purchaseOrderStatusDetails.mapToPurchaseOrderOverviewModel("HF")

        assertEquals(
            purchaseOrderStatusDetails.receiptOverrideQuantity,
            purchaseOrderOverviewModel.receiptOverrideQuantity
        )
        assertEquals(
            purchaseOrderStatusDetails.receiptOverrideCasesReceived,
            purchaseOrderOverviewModel.receiptOverrideCasesReceived
        )
        assertEquals(
            purchaseOrderStatusDetails.receiptOverrideDeliveryDate?.toLocalDateTime(),
            purchaseOrderOverviewModel.receiptOverrideDeliveryDate
        )
        assertEquals(
            purchaseOrderStatusDetails.receiptOverrideDetails,
            purchaseOrderOverviewModel.receiptOverrideDetails
        )
        assertEquals(
            purchaseOrderStatusDetails.receiptOverrideCreatedAt?.toLocalDateTime(),
            purchaseOrderOverviewModel.receiptOverrideCreatedAt
        )
        assertEquals(
            purchaseOrderStatusDetails.receiptOverrideUpdatedBy,
            purchaseOrderOverviewModel.receiptOverrideUpdatedBy
        )
    }

    @Test
    fun `should map PurchaseOrderStatusDetails unavailable PO data`() {
        // given
        val purchaseOrderStatusDetails = Fixture.createPurchaseOrderStatusDetails().copy(
            supplierName = null,
            supplierCode = null,
            skuName = null,
            skuCode = null,
            emergencyReason = null,
            category = null,
            assignedBuyerFirstName = null,
            assignedBuyerLastName = null,
            weeklyForecastedDemand = null,
        )

        // when
        val purchaseOrderOverviewModel = purchaseOrderStatusDetails.mapToPurchaseOrderOverviewModel("HF")

        // then
        assertAll(
            { assertEquals("", purchaseOrderOverviewModel.supplierName) },
            { assertEquals("", purchaseOrderOverviewModel.supplierCode) },
            { assertEquals("", purchaseOrderOverviewModel.skuName) },
            { assertEquals("", purchaseOrderOverviewModel.skuCode) },
            { assertEquals("", purchaseOrderOverviewModel.emergencyReason) },
            { assertEquals("", purchaseOrderOverviewModel.category) },
            { assertEquals(null, purchaseOrderOverviewModel.assignedBuyer) },
            { assertEquals(BigDecimal.TEN, purchaseOrderOverviewModel.caseSize) },
            { assertEquals(null, purchaseOrderOverviewModel.phfDeliveryPercentOfForecast) },
        )
    }

    @Test
    fun `should map PurchaseOrderStatusDetails supplier portal data`() {
        // given
        val purchaseOrderStatusDetails = Fixture.createPurchaseOrderStatusDetails()

        // when
        val purchaseOrderOverviewModel = purchaseOrderStatusDetails.mapToPurchaseOrderOverviewModel("HF")

        // then
        assertAll(
            {
                assertEquals(
                    purchaseOrderStatusDetails.ackNumberOfUnits,
                    purchaseOrderOverviewModel.proposedQuantityCases,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.ackUnitsPerCase,
                    purchaseOrderOverviewModel.proposedUnitsPerCase,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.ackNumberOfUnits?.multiply(purchaseOrderStatusDetails.ackUnitsPerCase),
                    purchaseOrderOverviewModel.proposedQuantityUnits,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.asnShipmentTime?.toLocalDateTime(),
                    purchaseOrderOverviewModel.asnShipmentDate,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.asnPlannedDeliveryTime?.toLocalDateTime(),
                    purchaseOrderOverviewModel.asnPlannedDeliveryTime,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.asnShippedOrderSize?.toBigDecimal(),
                    purchaseOrderOverviewModel.asnShippedQuantityCases,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.asnShippedUnitType,
                    purchaseOrderOverviewModel.asnUnitsOfMeasure,
                )
            },
            {
                assertEquals(
                    purchaseOrderStatusDetails.asnShippedSize?.toBigDecimal(),
                    purchaseOrderOverviewModel.asnCaseSize,
                )
            },
        )
    }

    @Test
    fun `should map PurchaseOrderStatusDetails unavailable supplier portal data`() {
        // given
        val purchaseOrderStatusDetails = Fixture.createPurchaseOrderStatusDetails().copy(
            ackUom = UNIT_OF_MEASURE_UNIT.toString(),
            ackPromisedTime = null,
            asnShipmentTime = null,
            asnPlannedDeliveryTime = null,
            asnShippedOrderSize = null,
            asnShippedSize = null,
        )

        // when
        val purchaseOrderOverviewModel = purchaseOrderStatusDetails.mapToPurchaseOrderOverviewModel("HF")

        // then
        assertAll(
            { assertEquals(null, purchaseOrderOverviewModel.proposedQuantityCases) },
            { assertEquals(null, purchaseOrderOverviewModel.proposedUnitsPerCase) },
            { assertEquals(null, purchaseOrderOverviewModel.proposedDeliveryDate) },
            { assertEquals(null, purchaseOrderOverviewModel.asnShipmentDate) },
            { assertEquals(null, purchaseOrderOverviewModel.asnPlannedDeliveryTime) },
            { assertEquals(null, purchaseOrderOverviewModel.asnShippedQuantityCases) },
            { assertEquals(null, purchaseOrderOverviewModel.asnCaseSize) },
        )
    }

    @Test
    fun `should set riskAssessment to HIGH when shipped quantity or planned delivery time is out of range`() {
        val model = Fixture.getPurchaseOrderOverviewModel().copy(
            asnPlannedDeliveryTime = Fixture.getPurchaseOrderOverviewModel().scheduledDeliveryDate.plusDays(1),
            asnShippedQuantityUnits = BigDecimal(5),
            quantityOrdered = BigDecimal(10)
        )
        val response = model.mapToPurchaseOrderOverviewModelToApiResponse()
        assertEquals(PurchaseOrdersOverviewResponseInner.RiskAssessment.HIGH, response.riskAssessment)
    }

    @Test
    fun `should set riskAssessment to MEDIUM when case size or planned delivery time is below expected`() {
        val model = Fixture.getPurchaseOrderOverviewModel().copy(
            asnPlannedDeliveryTime = Fixture.getPurchaseOrderOverviewModel().scheduledDeliveryDate.minusDays(1),
            asnShippedQuantityUnits = Fixture.getPurchaseOrderOverviewModel().quantityOrdered,
            asnCaseSize = BigDecimal(5),
            caseSize = BigDecimal(10)
        )
        val response = model.mapToPurchaseOrderOverviewModelToApiResponse()
        assertEquals(PurchaseOrdersOverviewResponseInner.RiskAssessment.MEDIUM, response.riskAssessment)
    }

    @Test
    fun `should set riskAssessment to NONE when all ASN and PO values match expected`() {
        val model = Fixture.getPurchaseOrderOverviewModel().copy(
            asnPlannedDeliveryTime = Fixture.getPurchaseOrderOverviewModel().scheduledDeliveryDate,
            asnShippedQuantityUnits = Fixture.getPurchaseOrderOverviewModel().quantityOrdered,
            asnCaseSize = Fixture.getPurchaseOrderOverviewModel().caseSize
        )
        val response = model.mapToPurchaseOrderOverviewModelToApiResponse()
        assertEquals(PurchaseOrdersOverviewResponseInner.RiskAssessment.NONE, response.riskAssessment)
    }
}
