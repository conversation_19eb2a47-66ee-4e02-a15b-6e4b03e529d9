package com.hellofresh.oms.orderManagementHttp.supplierSku

import com.hellofresh.oms.orderManagement.generated.api.model.SupplierSkuApiResponse.Uom
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import java.time.LocalDate
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.hasItems
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType.APPLICATION_JSON
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(executionPhase = BEFORE_TEST_METHOD, scripts = ["/data/delete.sql", "/data/supplier-sku.sql"])
class SupplierSkuIntegrationApiTest : AbstractIntegrationTest() {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Test
    fun `should get valid supplier sku controller response`() {
        // given
        val ids = listOf(
            "00000000-0000-0000-0001-000000000000",
            "00000000-0000-0000-0002-000000000000",
            "00000000-0000-0000-0005-000000000000"
        )
        val skuNames = listOf(
            "PTN-10-10101-7",
            "OTH-33-121101-1",
            "PRO-00-105599-1"
        )

        // when
        mockMvc.get("/supplier/00000000-0001-0000-0000-000000000000/sku") {
            param("market", "us")
            param("dc_code", "SK")
            param("delivery_date", LocalDate.now().toString())
            param("search", "")
            param("page", "0")
            param("size", "100")
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(3))
            jsonPath("$.items[*].id", equalTo(ids))
            jsonPath("$.items[*].code", equalTo(skuNames))
            jsonPath("$.items[0].uom", equalTo(Uom.OZ.name))
            jsonPath("$.items[0].price.amount", equalTo("15802.368"))
            jsonPath("$.items[0].status", equalTo("ACTIVE"))
            jsonPath("$.items[0].skuCategory", equalTo("PTN"))
            jsonPath("$.items[0].cases_per_pallet", equalTo(5))
            jsonPath("$.items[1].cases_per_pallet", equalTo(null))
        }
    }

    @Test
    fun `should return assigned sku first in the list then unassigned`() {
        // given
        val supplierId = "00000000-0007-0000-0000-000000000000"

        // when
        mockMvc.get("/supplier/$supplierId/sku") {
            param("market", "us")
            param("dc_code", "SK")
            param("delivery_date", LocalDate.now().toString())
            param("search", "")
            param("include_all", "true")
            param("page", "0")
            param("size", "100")
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(5))
            jsonPath("$.items[0].name", equalTo("sort-order-with-price"))
            jsonPath("$.items[0].supplier_uuid", notNullValue())
            jsonPath("$.items[1].name", equalTo("sort-order-no-price"))
            jsonPath("$.items[2].name", equalTo("Chicken, Sausage - 12 Ounce (oz)"))
        }
    }

    @Test
    fun `should apply search to assigned skus`() {
        // given
        val supplierId = "00000000-0001-0000-0000-000000000000"

        // when
        mockMvc.get("/supplier/$supplierId/sku") {
            param("market", "us")
            param("dc_code", "SK")
            param("delivery_date", LocalDate.now().toString())
            param("include_all", "true")
            param("search", "PRO")
            param("page", "0")
            param("size", "100")
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.length()", equalTo(1))
            jsonPath("$.items.[*].code", hasItems("PRO-00-105599-1"))
        }
    }

    @Test
    fun `should return assigned when uom is null`() {
        // given
        val supplierId = "00000000-0008-0000-0000-000000000000"

        // when
        mockMvc.get("/supplier/$supplierId/sku") {
            param("market", "ca")
            param("dc_code", "NJ")
            param("delivery_date", LocalDate.now().toString())
            param("include_all", "true")
            param("page", "0")
            param("size", "100")
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items[0].uom", equalTo(null))
        }
    }

    @Test
    fun `should return only non archived skus`() {
        // given
        val supplierId = "00000000-0008-0000-0000-000000000000"

        // when
        mockMvc.get("/supplier/$supplierId/sku") {
            param("market", "jp")
            param("dc_code", "JP")
            param("delivery_date", LocalDate.now().toString())
            param("include_all", "true")
            param("page", "0")
            param("size", "100")
            contentType = APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonPath("$.items.size()", equalTo(1))
            jsonPath("$.items[0].id", equalTo("00000000-0000-0000-0003-000000000000"))
        }
    }
}
