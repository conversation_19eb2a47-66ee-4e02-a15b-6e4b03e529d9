package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem.Companion.createOrderItem
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType.CASE_TYPE
import com.hellofresh.oms.model.PackagingType.PALLET_TYPE
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.UOM.KG
import com.hellofresh.oms.model.UOM.LBS
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.toResponseMap
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import org.hamcrest.CoreMatchers.equalTo
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.get
import uk.org.webcompere.modelassert.json.JsonAssertions.json

class PurchaseOrderItemIntegrationTest : PurchaseOrderAbstractIntegrationTest() {
    @Test
    fun `should return not found when purchase order does not exist`() {
        // given
        val nonExistentPoNumber = "PO-123"

        // when
        mockMvc.get(ORDER_ITEMS_PATH, nonExistentPoNumber)
            .andExpect {
                // then
                status { isNotFound() }
            }
    }

    @Test
    fun `should return empty list when purchase order exist but no order item was found`() {
        // given
        val givenPoNumber = "PO-874"
        val givenSupplierId = saveSupplier(id = UUID.randomUUID()).id
        savePurchaseOrderRevision(
            poNumber = givenPoNumber,
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            orderItems = emptySet(),
        )

        // when
        mockMvc.get(ORDER_ITEMS_PATH, givenPoNumber)
            .andExpect {
                // then
                status { isOk() }
                content {
                    string(
                        json()
                            .at("/order_items")
                            .isEmpty(),
                    )
                }
            }
    }

    @Test
    @Suppress("LongMethod")
    fun `should respond with items sorted by sku name`() {
        // given
        val givenPoNumber = "PO-345"
        val givenSupplierId = saveSupplier(id = UUID.randomUUID()).id
        val givenPoId = UUID.randomUUID()
        val skuCherry = skuRepository.save(getSkuEntity(code = "SKU-123", name = "Cherry"))
        val skuApple = skuRepository.save(getSkuEntity(code = "SKU-345", name = "Apple"))
        val skuBanana = skuRepository.save(getSkuEntity(code = "SKU-789", name = "Banana"))
        val givenOrderItems = setOf(
            createOrderItem(
                poId = givenPoId,
                skuId = skuBanana.uuid,
                totalNumberOfUnits = BigDecimal.valueOf(10),
                price = Money(BigDecimal.valueOf(10.0), "USD"),
                buffer = Permyriad.fromPercent(10.0),
                totalPrice = Money(BigDecimal.valueOf(110.0), "USD"),
                packaging = Packaging(
                    packagingType = UNIT_TYPE,
                    unitOfMeasure = UNIT,
                    caseSize = null,
                ),
                currentTime = LocalDateTime.now(),
                casesPerPallet = null,
                changeReasonId = null,
            ),
            createOrderItem(
                poId = givenPoId,
                skuId = skuApple.uuid,
                totalNumberOfUnits = BigDecimal.valueOf(10),
                price = Money(BigDecimal.valueOf(10.0), "USD"),
                buffer = Permyriad.fromPercent(10.0),
                totalPrice = Money(BigDecimal.valueOf(110.0), "USD"),
                packaging = Packaging(
                    packagingType = UNIT_TYPE,
                    unitOfMeasure = UNIT,
                    caseSize = null,
                ),
                currentTime = LocalDateTime.now(),
                casesPerPallet = null,
                changeReasonId = null,
            ),
            createOrderItem(
                poId = givenPoId,
                skuId = skuCherry.uuid,
                totalNumberOfUnits = BigDecimal.valueOf(10),
                price = Money(BigDecimal.valueOf(10.0), "USD"),
                buffer = Permyriad.fromPercent(10.0),
                totalPrice = Money(BigDecimal.valueOf(110.0), "USD"),
                packaging = Packaging(
                    packagingType = UNIT_TYPE,
                    unitOfMeasure = UNIT,
                    caseSize = null,
                ),
                currentTime = LocalDateTime.now(),
                casesPerPallet = null,
                changeReasonId = null,
            ),
        )
        savePurchaseOrderRevision(
            id = givenPoId,
            poNumber = givenPoNumber,
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            orderItems = givenOrderItems,
        )

        // when
        mockMvc.get(ORDER_ITEMS_PATH, givenPoNumber)
            .andExpect {
                // then
                status { isOk() }
                jsonPath("$.order_items[0].sku.code", equalTo(skuApple.code))
                jsonPath("$.order_items[1].sku.code", equalTo(skuBanana.code))
                jsonPath("$.order_items[2].sku.code", equalTo(skuCherry.code))
            }
    }

    @Test
    fun `should respond with order items for unit type packaging`() {
        // given
        val givenPoNumber = "PO-345"
        val givenSupplierId = saveSupplier(id = UUID.randomUUID()).id
        val givenPoId = UUID.randomUUID()
        val givenSku = skuRepository.save(getSkuEntity())
        val givenOrderItems = setOf(
            createOrderItem(
                poId = givenPoId,
                skuId = givenSku.uuid,
                totalNumberOfUnits = BigDecimal.valueOf(10),
                price = Money(BigDecimal.valueOf(10.0), "USD"),
                buffer = Permyriad.fromPercent(10.0),
                totalPrice = Money(BigDecimal.valueOf(110.0), "USD"),
                packaging = Packaging(
                    packagingType = UNIT_TYPE,
                    unitOfMeasure = UNIT,
                    caseSize = null,
                ),
                currentTime = LocalDateTime.now(),
                casesPerPallet = null,
                changeReasonId = null,
            ),
        )
        savePurchaseOrderRevision(
            id = givenPoId,
            poNumber = givenPoNumber,
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            orderItems = givenOrderItems,
        )

        // when
        val expectedResponse = givenOrderItems.map {
            it.toResponseMap(givenSku)
        }

        mockMvc.get(ORDER_ITEMS_PATH, givenPoNumber)
            .andExpect {
                // then
                status { isOk() }
                content {
                    string(
                        json()
                            .at("/order_items")
                            .where()
                            .keysInAnyOrder()
                            .isEqualTo(expectedResponse),
                    )
                }
            }
    }

    @Test
    fun `should respond with order items for case type packaging`() {
        // given
        val givenPoNumber = "PO-346"
        val givenSupplierId = saveSupplier(id = UUID.randomUUID()).id
        val givenPoId = UUID.randomUUID()
        val givenSku = skuRepository.save(getSkuEntity())
        val givenOrderItems = setOf(
            createOrderItem(
                poId = givenPoId,
                skuId = givenSku.uuid,
                totalNumberOfUnits = BigDecimal.valueOf(10),
                price = Money(BigDecimal.valueOf(50.0), "USD"),
                buffer = Permyriad.fromPercent(10.0),
                totalPrice = Money(BigDecimal.valueOf(110.0), "USD"),
                packaging = Packaging(
                    packagingType = CASE_TYPE,
                    unitOfMeasure = LBS,
                    caseSize = BigDecimal.valueOf(10),
                ),
                currentTime = LocalDateTime.now(),
                casesPerPallet = null,
                changeReasonId = null,
            ),
        )
        savePurchaseOrderRevision(
            id = givenPoId,
            poNumber = givenPoNumber,
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            orderItems = givenOrderItems,
        )

        // when
        val expectedResponse = givenOrderItems.map {
            it.toResponseMap(givenSku)
        }

        mockMvc.get(ORDER_ITEMS_PATH, givenPoNumber)
            .andExpect {
                // then
                status { isOk() }
                content {
                    string(
                        json()
                            .at("/order_items")
                            .where()
                            .keysInAnyOrder()
                            .isEqualTo(expectedResponse),
                    )
                }
            }
    }

    @Test
    fun `should respond with order items for pallet type packaging`() {
        // given
        val givenPoNumber = "PO-347"
        val givenSupplierId = saveSupplier(id = UUID.randomUUID()).id
        val givenPoId = UUID.randomUUID()
        val givenSku = skuRepository.save(getSkuEntity())
        val givenOrderItems = setOf(
            createOrderItem(
                poId = givenPoId,
                skuId = givenSku.uuid,
                totalNumberOfUnits = BigDecimal.valueOf(100),
                price = Money(BigDecimal.valueOf(50.0), "USD"),
                buffer = Permyriad.fromPercent(10.0),
                totalPrice = Money(BigDecimal.valueOf(550.0), "USD"),
                packaging = Packaging(
                    packagingType = PALLET_TYPE,
                    unitOfMeasure = KG,
                    caseSize = BigDecimal.valueOf(10),
                ),
                currentTime = LocalDateTime.now(),
                casesPerPallet = 10,
                changeReasonId = null,
            ),
        )
        savePurchaseOrderRevision(
            id = givenPoId,
            poNumber = givenPoNumber,
            version = 1,
            supplierId = givenSupplierId,
            comment = "first version",
            orderItems = givenOrderItems,
        )

        // when
        val expectedResponse = givenOrderItems.map {
            it.toResponseMap(givenSku)
        }

        mockMvc.get(ORDER_ITEMS_PATH, givenPoNumber)
            .andExpect {
                // then
                status { isOk() }
                content {
                    string(
                        json()
                            .at("/order_items")
                            .where()
                            .keysInAnyOrder()
                            .isEqualTo(expectedResponse),
                    )
                }
            }
    }

    companion object {
        private const val ORDER_ITEMS_PATH = "/orders/{po_number}/items"
    }
}
