package com.hellofresh.oms.orderManagementHttp.configuration

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.get
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get

@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@Tag("integration")
@Sql(
    executionPhase = BEFORE_TEST_METHOD,
    scripts = ["/data/distributionCenters.sql"],
)
@AutoConfigureWireMock
@AutoConfigureMockMvc
class CustomJWTValidatorIntegrationTest(
    @Value("\${spring.security.oauth2.resourceserver.jwt.secret-key}") private val jwkSecretKey: String,
) : AbstractIntegrationTest() {

    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var wireMock: WireMockServer

    @BeforeEach
    fun beforeEach() {
        val rsaPublicJWK = Fixture.VALID_RSA_KEY_PAIR.toPublicJWK()
        val jwkResponse = """{"keys": [${rsaPublicJWK.toJSONString()}]}"""

        // return mock JWK response
        wireMock.stubFor(
            get("/.well-known/jwks.json")
                .willReturn(
                    aResponse()
                        .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                        .withBody(jwkResponse),
                ),
        )
    }

    @Test
    fun `should returns 200 with valid azure token`() {
        mockMvc.get("/distribution-centers") {
            param("market", "gb")
            contentType = V2_APPLICATION_CONTENT
            header(
                HttpHeaders.AUTHORIZATION,
                "Bearer ${Fixture.getAzureJwt(
                    Fixture.VALID_KEY_ID,
                    Fixture.VALID_RSA_KEY_PAIR,
                    "http://localhost:${wireMock.port()}"
                )}"
            )
        }.andExpect {
            status { isOk() }
        }
    }

    @Test
    fun `should returns 200 when getLoggedInUser is used`() {
        mockMvc.delete("/orders/PONUMBER123") {
            param("market", "gb")
            contentType = V2_APPLICATION_CONTENT
            header(
                HttpHeaders.AUTHORIZATION,
                "Bearer ${Fixture.getAzureJwt(
                    Fixture.VALID_KEY_ID,
                    Fixture.VALID_RSA_KEY_PAIR,
                    "http://localhost:${wireMock.port()}"
                )}"
            )
        }.andExpect {
            status { isNoContent() }
        }
    }

    @Test
    fun `should returns 401 without access token`() {
        mockMvc.get("/distribution-centers") {
            param("market", "gb")
            contentType = V2_APPLICATION_CONTENT
        }.andExpect {
            status { isUnauthorized() }
        }
    }

    @Test
    fun `should returns 401 with invalid azure token signature`() {
        mockMvc.get("/distribution-centers") {
            param("market", "gb")
            contentType = V2_APPLICATION_CONTENT
            header(
                HttpHeaders.AUTHORIZATION,
                "Bearer ${Fixture.getAzureJwt(
                    Fixture.INVALID_KEY_ID,
                    Fixture.INVALID_RSA_KEY_PAIR,
                    "http://localhost:${wireMock.port()}",
                )}"
            )
        }.andExpect {
            status { isUnauthorized() }
        }
    }

    @Test
    fun `should returns 200 with valid auth service token`() {
        mockMvc.get("/distribution-centers") {
            param("market", "gb")
            contentType = V2_APPLICATION_CONTENT
            header("Authorization", "Bearer ${Fixture.getAuthServiceJwt(jwkSecretKey)}")
        }.andExpect {
            status { isOk() }
        }
    }

    companion object {
        private val V2_APPLICATION_CONTENT = MediaType.parseMediaType("application/json")
    }
}
