package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.grn.GrnStateEnum
import com.hellofresh.oms.orderManagement.generated.api.model.CasePackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.CreatePurchaseOrderResponse
import com.hellofresh.oms.orderManagement.generated.api.model.EditDeliveryWindowDto
import com.hellofresh.oms.orderManagement.generated.api.model.EditPurchaseOrderRequest
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.model.MoneyDto
import com.hellofresh.oms.orderManagement.generated.api.model.PackagingTypeEnum.CASE
import com.hellofresh.oms.orderManagement.generated.api.model.PalletPackagingRequest
import com.hellofresh.oms.orderManagement.generated.api.model.PurchaseOrderStatusEnum
import com.hellofresh.oms.orderManagement.generated.api.model.UomEnum.KG
import com.hellofresh.oms.orderManagementHttp.configuration.consumeAtLeast
import com.hellofresh.oms.orderManagementHttp.order.casePackaging
import com.hellofresh.oms.orderManagementHttp.order.getDeliveryDateChangeReasonId
import com.hellofresh.oms.orderManagementHttp.order.getEditOrderItemRequest
import com.hellofresh.oms.orderManagementHttp.order.getModifiedEditPurchaseOrderRequest
import com.hellofresh.oms.orderManagementHttp.order.getOrderItem
import com.hellofresh.oms.orderManagementHttp.order.getOrderItemChangeReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEditRequest
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.palletPackaging
import com.hellofresh.oms.orderManagementHttp.order.service.domain.PackagingDomain
import com.hellofresh.oms.util.Calculator
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import com.statsig.sdk.Statsig
import io.micrometer.core.instrument.MeterRegistry
import java.math.BigDecimal
import java.time.Duration
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNull
import org.apache.kafka.clients.consumer.Consumer
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.comparesEqualTo
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasItems
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Named.named
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.patch

@SpringBootTest(
    properties = [
        "topics.purchase-order=public.supply.procurement.purchase-order.v2-modify-test",
    ]
)
@Sql(
    scripts = [
        "/data/emergencyReasons.sql",
        "/data/purchaseOrderSku.sql",
        "/data/changeReasons.sql",
        "/data/purchaseOrder.sql",
        "/data/orderItem.sql",
        "/data/suppliers.sql",
    ],
)
@AutoConfigureWireMock(stubs = ["classpath:/stubs"])
class ModifyPurchaseOrderIntegrationTest : PurchaseOrderAbstractIntegrationTest() {
    @Autowired
    private lateinit var meterRegistry: MeterRegistry

    @Value("\${topics.purchase-order}")
    private lateinit var purchaseOrderTopicName: String

    @Value("\${statsig.gates.enable-po-topic-v2}")
    private lateinit var enablePublisherForPoTopicStatsigGate: String

    @Autowired
    private lateinit var purchaseOrderConsumer: Consumer<String, PurchaseOrderEvent>

    @BeforeEach
    override fun setUp() {
        super.setUp()
        Statsig.overrideGate(enablePublisherForPoTopicStatsigGate, true)
        purchaseOrderConsumer.subscribe(listOf(purchaseOrderTopicName))
    }

    @AfterEach
    fun tearDown() {
        purchaseOrderConsumer.unsubscribe()
    }

    @Test
    fun `should edit new order from API`() {
        // given
        val purchaseOrder = getPurchaseOrderEntity(
            orderItems = setOf(
                getOrderItem(
                    id = UUID.fromString("26e074d4-0141-4033-bffd-000d2a9d3308"),
                    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                ),
            ),
        )

        val editPurchaseOrderRequest = getPurchaseOrderEditRequest(
            comment = "Trigger new version",
            orderItems = null,
        )
        val body = objectMapper.writeValueAsString(editPurchaseOrderRequest)

        val testPoNumber = "2318NJ021004"

        // when
        mockMvc.patch("$ORDERS_PATH/{po_number}", testPoNumber) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }

        // then
        val editedPurchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(testPoNumber)!!

        assertNotEquals(purchaseOrder.version, editedPurchaseOrder.version)
        assertNotEquals(purchaseOrder.orderItems.first().id, editedPurchaseOrder.orderItems.first().id)

        // and then
        val messages = purchaseOrderConsumer.consumeAtLeast(1, Duration.ofSeconds(10))
        assertThat(
            messages.map { it.key() to it.value().eventType },
            Matchers.hasItem(testPoNumber to PurchaseOrderEvent.Event.EVENT_MODIFIED),
        )
    }

    @Test
    fun `should edit new order with zero price from API`() {
        // given
        val purchaseOrder = getPurchaseOrderEntity(
            orderItems = setOf(
                getOrderItem(
                    id = UUID.fromString("26e074d4-0141-4033-bffd-000d2a9d3308"),
                    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                ),
            ),
        )

        val editPurchaseOrderRequest = getPurchaseOrderEditRequest(
            comment = "Trigger new version",
            orderItems = listOf(
                getEditOrderItemRequest(
                    packaging = casePackaging.copy(pricePerCase = MoneyDto("0.0000", "USD")),
                ),
            ),
        )
        val body = objectMapper.writeValueAsString(editPurchaseOrderRequest)

        val testPoNumber = "2318NJ021004"

        // when
        mockMvc.patch("$ORDERS_PATH/{po_number}", testPoNumber) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }

        // then
        val editedPurchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(testPoNumber)!!

        assertNotEquals(purchaseOrder.version, editedPurchaseOrder.version)
        assertNotEquals(purchaseOrder.orderItems.first().id, editedPurchaseOrder.orderItems.first().id)
        assertThat(editedPurchaseOrder.orderItems.first().price.amount, comparesEqualTo(BigDecimal.ZERO))
    }

    @Test
    fun `should mark PO as deleted`() {
        // given
        val testPoNumber = "2318NJ021004"

        // when
        mockMvc.delete("$ORDERS_PATH/$testPoNumber") {
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isNoContent() }
        }.andReturn()

        // then
        val result = mockMvc.get(ORDERS_PATH) {
            param("dc_weeks", "2023-W18")
            param("dc_codes", "NJ")
            param("page", "0")
            param("size", "10")
            contentType = APPLICATION_CONTENT
        }.andExpect {
            status { isOk() }
        }.andReturn()!!
        val purchaseOrderResponse = objectMapper.readValue(
            result.response.contentAsString,
            ListPurchaseOrdersResponse::class.java,
        ).purchaseOrders
        assertThat(
            purchaseOrderResponse.map {
                Pair(it.poNumber, it.status)
            }.toSet(),
            hasItems(Pair(testPoNumber, PurchaseOrderStatusEnum.DELETED)),
        )

        // and then
        val messages = purchaseOrderConsumer.consumeAtLeast(1, Duration.ofSeconds(10))
        assertThat(
            messages.map { it.key() to it.value().eventType },
            Matchers.hasItem(testPoNumber to PurchaseOrderEvent.Event.EVENT_CANCELLED),
        )
    }

    @Test
    fun `should not edit a purchase order when no changes are introduced`() {
        // given
        val purchaseOrder = getPurchaseOrderEntity(
            orderItems = setOf(
                getOrderItem(
                    id = UUID.fromString("26e074d4-0141-4033-bffd-000d2a9d3308"),
                    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                ),
            ),
        )

        val editPurchaseOrderRequest = getPurchaseOrderEditRequest(
            orderItems = listOf(
                getEditOrderItemRequest(
                    packaging = CasePackagingRequest(
                        numberOfCases = 38,
                        unitsPerCase = BigDecimal(12).setScale(Calculator.PRECISION),
                        pricePerCase = MoneyDto("5.0000", "USD"),
                        uom = KG,
                        packagingType = CASE,
                    ),
                    changeReasonId = null,
                    bufferPercent = BigDecimal(3),
                ),
            ),
        )
        val body = objectMapper.writeValueAsString(editPurchaseOrderRequest)

        val testPoNumber = "2318NJ021004"

        // when
        val result = mockMvc.patch("$ORDERS_PATH/{po_number}", testPoNumber) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }.andReturn()!!

        // then
        val purchaseOrderResponse = objectMapper.readValue(
            result.response.contentAsString,
            CreatePurchaseOrderResponse::class.java,
        )

        val editedPurchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(testPoNumber)!!

        // TODO: Revisit database precision of BigDecimal values.
        //  The precision of the database column is 4, which means it is fetched as a BigDecimal(scale = 4), and hence comparison fails.
        //  assertEquals(purchaseOrder, editedPurchaseOrder)

        assertEquals(purchaseOrder.id, purchaseOrderResponse.id)
        assertEquals(purchaseOrder.id, editedPurchaseOrder.id)
        assertEquals(purchaseOrder.version, editedPurchaseOrder.version)
    }

    @Suppress("LongMethod")
    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("editRequestProvider")
    fun `should edit PO with defined fields in the request`(request: EditPurchaseOrderRequest) {
        // given
        val body = objectMapper.writeValueAsString(request)
        val originalPo = getPurchaseOrderEntity()
        val testPoNumber = "2318NJ021004"

        // when
        val result = mockMvc.patch("$ORDERS_PATH/{po_number}", testPoNumber) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }.andReturn()!!

        // then
        val purchaseOrderResponse = objectMapper.readValue(
            result.response.contentAsString,
            CreatePurchaseOrderResponse::class.java,
        )

        val editedPurchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(testPoNumber)!!
        assertEquals(purchaseOrderResponse.id, editedPurchaseOrder.id)
        assertEquals(3, editedPurchaseOrder.version)
        assertNull(editedPurchaseOrder.sendTime)
        assertEquals(PurchaseOrderStatus.INITIATED, editedPurchaseOrder.status)
        assertEquals(UUID.fromString("3d40b713-b378-40cf-8ed8-dffbd6cfa8df"), editedPurchaseOrder.userId)
        assertEquals("<EMAIL>", editedPurchaseOrder.userEmail)

        assertEquals(
            request.deliveryWindow?.start ?: originalPo.expectedStartTime,
            editedPurchaseOrder.expectedStartTime,
        )
        assertEquals(request.deliveryWindow?.end ?: originalPo.expectedEndTime, editedPurchaseOrder.expectedEndTime)
        assertEquals(
            request.emergencyReasonId ?: originalPo.emergencyReasonUuid,
            editedPurchaseOrder.emergencyReasonUuid,
        )
        assertEquals(
            request.orderItems?.first()?.skuId ?: originalPo.orderItems.first().skuId,
            editedPurchaseOrder.orderItems.first().skuId,
        )
        assertNotEquals(
            originalPo.id,
            editedPurchaseOrder.orderItems.first().poId,
        )
        assertEquals(
            editedPurchaseOrder.id,
            editedPurchaseOrder.orderItems.first().poId,
        )

        assertEquals(
            request.orderItems?.first()?.changeReasonId ?: originalPo.orderItems.first().changeReasonId,
            editedPurchaseOrder.orderItems.first().changeReasonId,
        )
        assertEquals(request.comment ?: originalPo.comment, editedPurchaseOrder.comment)

        request.orderItems?.first()?.packaging?.let { packaging ->
            if (packaging is PalletPackagingRequest) {
                assertEquals(
                    packaging.casesPerPallet,
                    editedPurchaseOrder.orderItems.first().casesPerPallet,
                )
            } else {
                assertEquals(
                    originalPo.orderItems.first().casesPerPallet,
                    editedPurchaseOrder.orderItems.first().casesPerPallet,
                )
            }
        }

        assertEquals(
            request.orderItems?.first()?.packaging?.let {
                PackagingDomain.from(it).packagingType
            } ?: originalPo.orderItems.first().packaging.packagingType,
            editedPurchaseOrder.orderItems.first().packaging.packagingType,
        )
    }

    @Test
    fun `should create new order items when a new version is created`() {
        // given
        val purchaseOrder = getPurchaseOrderEntity(
            orderItems = setOf(
                getOrderItem(
                    id = UUID.fromString("26e074d4-0141-4033-bffd-000d2a9d3308"),
                    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                ),
            ),
        )

        val editPurchaseOrderRequest = getPurchaseOrderEditRequest(
            comment = "Trigger new version",
            orderItems = null,
        )
        val body = objectMapper.writeValueAsString(editPurchaseOrderRequest)

        val testPoNumber = "2318NJ021004"

        // when
        mockMvc.patch("$ORDERS_PATH/{po_number}", testPoNumber) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }.andReturn()!!

        // then
        val editedPurchaseOrder = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(testPoNumber)!!

        assertNotEquals(purchaseOrder.version, editedPurchaseOrder.version)
        assertNotEquals(purchaseOrder.orderItems.first().id, editedPurchaseOrder.orderItems.first().id)
    }

    @Test
    fun `should persist order item change reasons between versions for order items that have not changed`() {
        // given
        val skuIdToChange = UUID.fromString("7a291c6d-688a-41f3-b0ec-000000000000")
        val testPoNumber = "2318NJ021004"
        val changeReason1 = getOrderItemChangeReasonEntity().id
        val changeReason2 = UUID.fromString("bce43d77-dc02-4b90-8e42-607f3984960d")

        val firstEditPurchaseOrderRequest = getPurchaseOrderEditRequest(
            orderItems = listOf(
                getEditOrderItemRequest(
                    skuId = getOrderItem().skuId,
                    changeReasonId = changeReason1,
                ),
                getEditOrderItemRequest(
                    skuId = skuIdToChange,
                    changeReasonId = changeReason1,
                ),
            ),
        )

        mockMvc.patch("$ORDERS_PATH/{po_number}", testPoNumber) {
            contentType = APPLICATION_CONTENT
            content = objectMapper.writeValueAsString(firstEditPurchaseOrderRequest)
        }.andExpect {
            status { isOk() }
        }.andReturn()!!

        // given
        val secondEditPurchaseOrderRequest = getPurchaseOrderEditRequest(
            orderItems = listOf(
                getEditOrderItemRequest(
                    skuId = getOrderItem().skuId,
                    changeReasonId = changeReason1,
                ),
                getEditOrderItemRequest(
                    skuId = skuIdToChange,
                    changeReasonId = changeReason2, // <-- should change the change reason
                ),
            ),
        )

        // when
        mockMvc.patch("$ORDERS_PATH/{po_number}", testPoNumber) {
            contentType = APPLICATION_CONTENT
            content = objectMapper.writeValueAsString(secondEditPurchaseOrderRequest)
        }.andExpect {
            status { isOk() }
        }.andReturn()!!
        // then
        val actualPo = purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(testPoNumber)!!

        assertEquals(4, actualPo.version)
        assertEquals(changeReason1, actualPo.orderItems.find { it.skuId == getOrderItem().skuId }!!.changeReasonId)
        assertEquals(changeReason2, actualPo.orderItems.find { it.skuId == skuIdToChange }!!.changeReasonId)
    }

    @Test
    fun `should increment metric for edited po with existing grn`() {
        // given
        val editPurchaseOrderRequest = getPurchaseOrderEditRequest(
            comment = "Trigger new version",
            orderItems = listOf(
                getEditOrderItemRequest(
                    packaging = casePackaging.copy(pricePerCase = MoneyDto("0.0000", "USD")),
                ),
            ),
        )
        val body = objectMapper.writeValueAsString(editPurchaseOrderRequest)
        val testPoNumber = "2318NJ021004"
        saveGrn(poNumber = testPoNumber, grnState = GrnStateEnum.STATE_OPEN)

        // when
        mockMvc.patch("$ORDERS_PATH/{po_number}", testPoNumber) {
            contentType = APPLICATION_CONTENT
            content = body
        }.andExpect {
            status { isOk() }
        }.andReturn()

        // then
        assertThat(
            meterRegistry["edited_po_with_existing_grn"]
                .tags("po_status", "INITIATED")
                .tags("market", "us")
                .counter().count(),
            equalTo(1.0),
        )
    }

    companion object {
        private const val ORDERS_PATH = "/orders"
        private val APPLICATION_CONTENT = MediaType.parseMediaType("application/json")

        @Suppress("UnusedPrivateMember")
        @JvmStatic
        private fun editRequestProvider(): Stream<Arguments> {
            val modifiedRequest = getModifiedEditPurchaseOrderRequest()
            val baseRequests = listOf(
                getPurchaseOrderEditRequest(),
                EditPurchaseOrderRequest(),
            ) // full request, empty request
            val table = baseRequests.flatMap {
                listOf(
                    Pair("Shipping method", it.copy(shippingMethod = modifiedRequest.shippingMethod)),
                    Pair(
                        "Delivery dates",
                        it.copy(
                            deliveryWindow = EditDeliveryWindowDto(
                                start = modifiedRequest.deliveryWindow!!.start,
                                end = modifiedRequest.deliveryWindow!!.end,
                                changeReasonId = getDeliveryDateChangeReasonId(),
                            ),
                        ),
                    ),
                    Pair("Emergency reason", it.copy(emergencyReasonId = modifiedRequest.emergencyReasonId)),
                    Pair(
                        "Order items",
                        it.copy(orderItems = modifiedRequest.orderItems),
                    ),
                    Pair("Comment", it.copy(comment = modifiedRequest.comment)),
                    // test case for pallet type order item
                    Pair(
                        "Pallet type order item",
                        it.copy(
                            orderItems = listOf(
                                getEditOrderItemRequest(
                                    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                                    packaging = palletPackaging.copy(
                                        casesPerPallet = 10,
                                    ),
                                ),
                            ),
                        ),
                    ),
                )
            }
            return table.map { arguments(named(it.first, it.second)) }.stream()
        }
    }
}
