package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.orderManagementHttp.orderOverview.config.BrandDcConfig
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusViewRepository
import com.hellofresh.oms.orderManagementHttp.orderOverview.service.transferOrder.TransferOrderOverviewService
import java.math.BigDecimal
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`

class TransferOrderOverviewServiceTest {

    private val poOverviewConfigs = mock(PoOverviewConfigs::class.java)
    private val transferOrderStatusViewRepository = mock(TransferOrderStatusViewRepository::class.java)

    private val transferOrderOverviewService = TransferOrderOverviewService(
        poOverviewConfigs = poOverviewConfigs,
        transferOrderStatusViewRepository = transferOrderStatusViewRepository
    )

    @Test
    fun `should get overview with specific dc code`() {
        val transferOrderId = UUID.randomUUID()
        val mockStatusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-001",
            skuCode = "SKU-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC001",
            sourceDcCode = "DC002",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            week = "2024-W01",
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5L,
            caseSize = "2.0",
            packagingType = "case",
            casePrice = 2500L,
            quantityOrdered = 10,
            reasonText = "Stock transfer",
            shippingMethod = "Truck",
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe",
            totalPrice = 25000L,
            receiptStatus = "RECEIVED"
        )

        `when`(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC002"),
                dcWeeks = listOf("2024-W01")
            )
        ).thenReturn(listOf(mockStatusDetails))

        val result = transferOrderOverviewService.getOverview(
            dcCode = "DC002",
            brand = "hellofresh",
            dcWeeks = listOf("2024-W01")
        )

        assertEquals(1, result.size)
        assertEquals("SKU-001", result[0].skuCode)
        assertEquals("DC002", result[0].sourceDcCode)
        assertEquals("Test Product", result[0].skuName)
        assertEquals("Fresh", result[0].category)
        assertEquals(BigDecimal("10"), result[0].totalOrderedQuantity)
        assertEquals(1, result[0].transferOrderBreakdown.size)

        verify(transferOrderStatusViewRepository).findAllTransferOrderDetails(
            dcCodes = listOf("DC002"),
            dcWeeks = listOf("2024-W01")
        )
    }

    @Test
    fun `should get overview without dc code using brand configuration`() {
        val transferOrderId = UUID.randomUUID()
        val mockStatusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-002",
            skuCode = "SKU-002",
            status = "STATE_IN_TRANSIT",
            destinationDcCode = "DC003",
            sourceDcCode = "DC004",
            skuName = "Another Product",
            category = "Dairy",
            skuUom = "liter",
            week = "2024-W02",
            quantityReceived = BigDecimal("20.0"),
            casesReceived = 10L,
            caseSize = "2.0",
            packagingType = "case",
            casePrice = 5000L,
            quantityOrdered = 20,
            reasonText = "Regular transfer",
            shippingMethod = "Standard",
            assignedBuyerFirstName = "Jane",
            assignedBuyerLastName = "Smith",
            totalPrice = 100000L,
            receiptStatus = "IN_TRANSIT"
        )

        val brandDcConfig = BrandDcConfig(name = "hellofresh", dcs = listOf("DC004", "DC005"))
        `when`(poOverviewConfigs.brandDcConfig).thenReturn(mapOf("hellofresh" to brandDcConfig))

        `when`(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC004", "DC005"),
                dcWeeks = listOf("2024-W02")
            )
        ).thenReturn(listOf(mockStatusDetails))

        val result = transferOrderOverviewService.getOverview(
            dcCode = null,
            brand = "hellofresh",
            dcWeeks = listOf("2024-W02")
        )

        assertEquals(1, result.size)
        assertEquals("SKU-002", result[0].skuCode)
        assertEquals("DC004", result[0].sourceDcCode)

        verify(transferOrderStatusViewRepository).findAllTransferOrderDetails(
            dcCodes = listOf("DC004", "DC005"),
            dcWeeks = listOf("2024-W02")
        )
    }

    @Test
    fun `should return empty list when no transfer orders found`() {
        `when`(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001"),
                dcWeeks = listOf("2024-W01")
            )
        ).thenReturn(emptyList())

        val result = transferOrderOverviewService.getOverview(
            dcCode = "DC001",
            brand = "hellofresh",
            dcWeeks = listOf("2024-W01")
        )

        assertEquals(0, result.size)
    }
}
