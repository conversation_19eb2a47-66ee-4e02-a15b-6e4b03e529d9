package com.hellofresh.oms.orderManagementHttp.orderOverview.service

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusViewRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import java.math.BigDecimal
import java.util.UUID

class TransferOrderOverviewServiceTest {

    private val poOverviewConfigs = mock(PoOverviewConfigs::class.java)
    private val transferOrderStatusViewRepository = mock(TransferOrderStatusViewRepository::class.java)

    private val transferOrderOverviewService = TransferOrderOverviewService(
        poOverviewConfigs = poOverviewConfigs,
        transferOrderStatusViewRepository = transferOrderStatusViewRepository
    )

    @Test
    fun `should get overview with specific dc code`() {
        val transferOrderId = UUID.randomUUID()
        val mockStatusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-001",
            skuCode = "SKU-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC001",
            sourceDcCode = "DC002",
            skuName = "Test Product",
            category = "Fresh",
            skuUom = "kg",
            week = YearWeek("2024-W01"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 25.50",
            totalQuantity = BigDecimal("10.0"),
            reasonText = "Stock transfer",
            shippingMethod = "Truck",
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe"
        )

        `when`(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001"),
                dcWeeks = listOf("2024W01")
            )
        ).thenReturn(listOf(mockStatusDetails))

        val result = transferOrderOverviewService.getOverview(
            dcCode = "DC001",
            brand = "hellofresh",
            dcWeeks = listOf("2024W01")
        )

        assertEquals(1, result.size)
        assertEquals(transferOrderId, result[0].transferOrderId)
        assertEquals("TO-2024-001", result[0].transferOrderNumber)
        assertEquals("SKU-001", result[0].skuCode)
        assertEquals("STATE_OPEN", result[0].status)

        verify(transferOrderStatusViewRepository).findAllTransferOrderDetails(
            dcCodes = listOf("DC001"),
            dcWeeks = listOf("2024W01")
        )
    }

    @Test
    fun `should return empty list when no transfer orders found`() {
        `when`(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC001"),
                dcWeeks = listOf("2024W01")
            )
        ).thenReturn(emptyList())

        val result = transferOrderOverviewService.getOverview(
            dcCode = "DC001",
            brand = "hellofresh",
            dcWeeks = listOf("2024W01")
        )

        assertEquals(0, result.size)
    }


}
