package com.hellofresh.oms.orderManagementHttp.imports.integration

import com.hellofresh.oms.model.DistributionCenterAddress
import com.hellofresh.oms.model.DistributionCenterAddressType
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierSimpleEntity
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierSimpleRepository
import java.time.LocalDateTime.now
import java.util.UUID.randomUUID
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.mock.web.MockMultipartFile
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.multipart

@Suppress("MaxLineLength")
@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
class BatchCreateRoleValidationIntegrationTest : AbstractIntegrationTest() {

    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var distributionCentersRepository: DistributionCentersRepository

    @Autowired
    private lateinit var supplierSimpleRepository: SupplierSimpleRepository

    @Autowired
    private lateinit var emergencyReasonRepository: EmergencyReasonRepository

    @Autowired
    private lateinit var skuRepository: SkuRepository

    @Test
    @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", roleClaim = "purchasing.ca.all.manager")
    fun `given a user without a role to a market should respond with 403`() {
        // given
        val givenWeek = "2024-W44"
        val givenSupplierCode = "114318"
        val givenDeliveryDate = "2024-10-25"
        val givenStartTime = "9:00"
        val givenEndTime = "12:00"
        val givenShipMethod = "Vendor Delivered"
        val givenMarket = "us"
        val givenDc = saveDc(
            "SP",
            givenMarket,
            listOf(
                DistributionCenterAddress(
                    id = randomUUID(),
                    dcCode = "NJ",
                    number = "60",
                    address = "60 Lister Avenue",
                    zip = "07105",
                    city = "Newark",
                    state = "NJ",
                    company = "HelloFresh United States - NJ",
                    type = DistributionCenterAddressType.DELIVERY,
                    createdAt = now(),
                    updatedAt = now(),
                    countryCode = "US",
                ),
            ),
        )
        saveSupplier(givenSupplierCode.toInt(), givenMarket)
        val givenReason = saveReason("Multi-Week", givenMarket)
        saveSku("PCK-00-120387-1", givenMarket)
        val validFileContent = """
            week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
            $givenWeek,${givenDc.code},,${givenReason.name},$givenSupplierCode,$givenDeliveryDate,$givenStartTime,$givenEndTime,PCK-00-120387-1,533,UNIT_TYPE,0,,unit,0.0890,$givenShipMethod
        """.trimIndent().toByteArray()

        // when
        postBulkCreateFile(validFileContent, "true")
            .andExpect {
                // then
                status { isBadRequest() }
            }
    }

    private fun postBulkCreateFile(validFileContent: ByteArray, dryRun: String = "true") = mockMvc.multipart(
        "/imports/orders",
    ) {
        param("dry_run", dryRun)
        file(MockMultipartFile("file", "testFile.csv", "text/csv", validFileContent))
    }

    private fun saveDc(dcCode: String, market: String, addresses: List<DistributionCenterAddress> = emptyList()) = distributionCentersRepository.save(
        getDistributionCenterEntity(code = dcCode, market = market, addresses = addresses),
    )

    private fun saveSupplier(supplierCode: Int, market: String) =
        supplierSimpleRepository.save(getSupplierSimpleEntity(code = supplierCode, market = market))

    private fun saveReason(reason: String, market: String) = emergencyReasonRepository.save(
        getEmergencyReasonEntity(name = reason, market = market),
    )

    private fun saveSku(code: String, market: String) = skuRepository.save(
        getSkuEntity(code = code, market = market),
    )
}
