package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType.CASE_TYPE
import com.hellofresh.oms.model.PackagingType.PALLET_TYPE
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.orderManagement.generated.api.model.CreateOrderItemRequest
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagementHttp.order.caseItem
import com.hellofresh.oms.orderManagementHttp.order.casePackaging
import com.hellofresh.oms.orderManagementHttp.order.createPoRequestDto
import com.hellofresh.oms.orderManagementHttp.order.getCurrentTime
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getOrderItem
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.order.palletItem
import com.hellofresh.oms.orderManagementHttp.order.palletPackaging
import com.hellofresh.oms.orderManagementHttp.order.poRequestBody
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomain
import com.hellofresh.oms.orderManagementHttp.order.service.domain.MoneyDomainPrecision.Precision4
import com.hellofresh.oms.orderManagementHttp.order.service.domain.TotalPrice
import com.hellofresh.oms.orderManagementHttp.order.service.domain.toModel
import com.hellofresh.oms.orderManagementHttp.order.unitItem
import com.hellofresh.oms.orderManagementHttp.order.unitPackaging
import com.hellofresh.oms.util.Calculator
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class PurchaseOrderMapperTest {

    @Test
    fun `should map request with case items to entity`() {
        // when
        val entity = requestToEntity()

        // update Ids of orderItems
        val expectedEntityWithUpdatedIds =
            expectedEntityWithCaseItems.copy(
                orderItems = expectedEntityWithCaseItems.orderItems.map {
                    it.copy(
                        id = entity.orderItems.first().id,
                    )
                }.toSet(),
                deliveryDateChangeReasonId = null,
                yearWeek = YearWeek("2022-W10"),
                supplierId = entity.supplierId,
            )

        // then
        assertNotNull(entity, message = null)
        assertEquals(expectedEntityWithUpdatedIds.toString(), entity.toString())
    }

    @Test
    fun `should map request with pallet items to entity`() {
        // when
        val entity = requestToEntity(orderItems = listOf(palletItem))

        // update Ids of orderItems
        val expectedEntityWithUpdatedIds =
            expectedEntityWithPalletItems.copy(
                orderItems = expectedEntityWithPalletItems.orderItems.map {
                    it.copy(
                        id = entity.orderItems.first().id,
                    )
                }.toSet(),
                deliveryDateChangeReasonId = null,
                yearWeek = YearWeek("2022-W10"),
                supplierId = entity.supplierId,
            )

        // then
        assertNotNull(entity, message = null)
        assertEquals(expectedEntityWithUpdatedIds.toString(), entity.toString())
    }

    @Test
    fun `should map request with unit items to entity`() {
        // when
        val entity = requestToEntity(orderItems = listOf(unitItem))

        // update Ids of orderItems
        val expectedEntityWithUpdatedIds =
            expectedEntityWithUnitItems.copy(
                orderItems = expectedEntityWithUnitItems.orderItems.map {
                    it.copy(
                        id = entity.orderItems.first().id,
                    )
                }.toSet(),
                deliveryDateChangeReasonId = null,
                yearWeek = YearWeek("2022-W10"),
                supplierId = entity.supplierId,
            )

        // then
        assertNotNull(entity, message = null)
        assertEquals(expectedEntityWithUpdatedIds.toString(), entity.toString())
    }

    @Test
    fun `should map request with a null comment to entity`() {
        // when
        val entity = poRequestBody.copy(comment = null)

        // then
        assertNull(entity.comment)
    }

    private fun requestToEntity(
        orderItems: List<CreateOrderItemRequest> = listOf(caseItem),
    ) = createPoRequestDto.copy(
        orderItems = orderItems,
        deliveryWindow = DeliveryWindowDto(
            start = LocalDateTime.parse("2023-04-29T10:00:00.000000"),
            end = LocalDateTime.parse("2023-04-29T11:00:00.000000"),
        )
    ).toModel(
        poId = getPurchaseOrderEntity().id,
        poNumber = getPurchaseOrderEntity().poNumber,
        supplier = getSupplierEntity(
            supplierId = getPurchaseOrderEntity().supplierId,
            code = getPurchaseOrderEntity().supplierCode.toInt(),
            market = "dach",
        ),
        emergencyReason = getEmergencyReasonEntity(),
        now = getCurrentTime(),
        shippingAddress = getPurchaseOrderEntity().shippingAddress,
    )

    companion object {
        val expectedEntityWithCaseItems = getPurchaseOrderEntity().copy(
            version = 1,
            sendTime = null,
            comment = "Test comment",
            orderItems = listOf(
                getOrderItem().copy(
                    totalQty = BigDecimal(10).setScale(Calculator.PRECISION),
                    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                    price = casePackaging.pricePerCase.let {
                        MoneyDomain(amount = it.amount.toBigDecimal(), currency = it.currency, Precision4).toMoney()
                    },
                    totalPrice = TotalPrice.from(
                        numberOfItemsToPurchase = casePackaging.numberOfCases,
                        pricePerItem = casePackaging.pricePerCase.amount.toBigDecimal(),
                        currency = casePackaging.pricePerCase.currency,
                        Precision4,
                    ).toMoney(),
                    buffer = Permyriad(100),
                    correctionReason = null,
                    packaging = Packaging(
                        packagingType = CASE_TYPE,
                        caseSize = casePackaging.unitsPerCase,
                        unitOfMeasure = UNIT,
                    ),
                    rawQty = null,
                ),
            ).toSet(),
            totalPrice = Money(BigDecimal(10).setScale(4), "USD"),
        )

        val expectedEntityWithPalletItems = getPurchaseOrderEntity().copy(
            version = 1,
            sendTime = null,
            comment = "Test comment",
            orderItems = listOf(
                getOrderItem().copy(
                    totalQty = BigDecimal(10).setScale(Calculator.PRECISION),
                    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                    price = palletPackaging.pricePerCase.let {
                        MoneyDomain(amount = it.amount.toBigDecimal(), currency = it.currency, Precision4).toMoney()
                    },
                    totalPrice = TotalPrice.from(
                        numberOfItemsToPurchase = palletPackaging.casesPerPallet.times(palletPackaging.numberOfPallets),
                        pricePerItem = palletPackaging.pricePerCase.amount.toBigDecimal(),
                        currency = palletPackaging.pricePerCase.currency,
                        Precision4,
                    ).toMoney(),
                    buffer = Permyriad(100),
                    correctionReason = null,
                    packaging = Packaging(
                        packagingType = PALLET_TYPE,
                        caseSize = palletPackaging.unitsPerCase,
                        unitOfMeasure = UNIT,
                    ),
                    rawQty = null,
                    casesPerPallet = 1,
                ),
            ).toSet(),
            totalPrice = Money(BigDecimal(10).setScale(4), "USD"),
        )

        val expectedEntityWithUnitItems = getPurchaseOrderEntity().copy(
            version = 1,
            sendTime = null,
            comment = "Test comment",
            orderItems = listOf(
                getOrderItem().copy(
                    totalQty = BigDecimal(1).setScale(Calculator.PRECISION),
                    skuId = UUID.fromString("7a291c6d-688a-41f3-b0ec-7ff760aa7a6a"),
                    price = unitPackaging.pricePerUnit.let {
                        MoneyDomain(amount = it.amount.toBigDecimal(), currency = it.currency, Precision4).toMoney()
                    },
                    totalPrice = TotalPrice.from(
                        numberOfItemsToPurchase = unitPackaging.numberOfUnits,
                        pricePerItem = unitPackaging.pricePerUnit.amount.toBigDecimal(),
                        currency = unitPackaging.pricePerUnit.currency,
                        Precision4,
                    ).toMoney(),
                    buffer = Permyriad(100),
                    correctionReason = null,
                    packaging = Packaging(
                        packagingType = UNIT_TYPE,
                        caseSize = null,
                        unitOfMeasure = UNIT,
                    ),
                    rawQty = null,
                ),
            ).toSet(),
            totalPrice = Money(BigDecimal(10).setScale(4), "USD"),
        )
    }
}
