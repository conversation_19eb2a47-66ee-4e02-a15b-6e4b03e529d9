package com.hellofresh.oms.orderManagementHttp.comments.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.orderManagement.generated.api.model.CreateCommentResponse
import com.hellofresh.oms.orderManagementHttp.comments.out.CommentRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlMergeMode
import org.springframework.test.context.jdbc.SqlMergeMode.MergeMode
import org.springframework.test.web.servlet.patch
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath

class UpdateCommentControllerTest : AbstractCommentControllerTest() {
    @Autowired
    lateinit var commentRepository: CommentRepository

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/comments.sql"])
    fun `should update comment successfully`() {
        // given
        val commentId = "11111111-1111-1111-1111-111111111111"
        val request = """
            {
                "comment": "Updated test comment"
            }
        """.trimIndent()

        // when
        val mvcResult = mockMvc.patch("$COMMENTS_PATH/$commentId") {
            contentType = APPLICATION_CONTENT
            content = request
        }.andExpect {
            status { isOk() }
            jsonPath("$.id").value(commentId)
        }.andReturn()

        // then
        val response = objectMapper.readValue(
            mvcResult.response.contentAsString,
            CreateCommentResponse::class.java,
        )
        val comment = commentRepository.findById(response.id).get()
        assertEquals("Updated test comment", comment.comment)
        assertEquals("<EMAIL>", comment.updatedBy)
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/comments.sql"])
    fun `should return bad request when comment field is missing`() {
        // given
        val commentId = "11111111-1111-1111-1111-111111111111"
        val request = """
            {
                "resource_type": "po",
                "resource_id": "test-po"
            }
        """.trimIndent()

        // when/then
        mockMvc.patch("$COMMENTS_PATH/$commentId") {
            contentType = APPLICATION_CONTENT
            content = request
        }.andExpect {
            status { isBadRequest() }
        }
    }

    @Test
    @SqlMergeMode(MergeMode.MERGE)
    @Sql(scripts = ["/data/comments.sql"])
    fun `should return not found when comment does not exist`() {
        // given
        val nonExistentCommentId = "99999999-9999-9999-9999-999999999999"
        val request = """
            {
                "comment": "Updated test comment"
            }
        """.trimIndent()

        // when/then
        mockMvc.patch("$COMMENTS_PATH/$nonExistentCommentId") {
            contentType = APPLICATION_CONTENT
            content = request
        }.andExpect {
            status { isNotFound() }
        }
    }
}
