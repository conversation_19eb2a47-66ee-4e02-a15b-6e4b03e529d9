package com.hellofresh.oms.orderManagementHttp.category.intake

import com.hellofresh.oms.orderManagementHttp.category.service.domain.CategoryService
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.imports.service.ImportHistoryService
import com.hellofresh.oms.orderManagementHttp.sku.SkuService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WithJWTUser
@ExtendWith(SpringExtension::class)
@WebMvcTest(CategoryController::class)
@Suppress("UnusedPrivateProperty", "MaxLineLength", "ForbiddenComment")
class CategoryControllerTest {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockitoBean
    private lateinit var categoryServiceMock: CategoryService

    @MockitoBean
    // FIXME: @ExtendWith tests are trying to create @Component's such as ResponseConverters, therefore this service is required
    private lateinit var importHistoryService: ImportHistoryService

    @MockitoBean
    // FIXME: @ExtendWith tests are trying to create @Component's such as ResponseConverters, therefore this service is required
    private lateinit var skuService: SkuService

    @Test
    fun `should return list of categories by market`() {
        // given
        whenever(categoryServiceMock.getCategories("us"))
            .thenReturn(
                setOf(
                    "BAK",
                    "BEV",
                ),
            )

        // when
        val request = get("/categories")
            .param("market", "us")
            .with(csrf())

        mockMvc.perform(request)
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                    {
                        "items": [
                            {"code": "BAK"},
                            {"code": "BEV"}
                        ]
                    }
                    """.trimIndent(),
                ),
            )
    }
}
