package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.model.Origin
import com.hellofresh.oms.model.WorkerActionData.CreateAndSendPurchaseOrder
import com.hellofresh.oms.model.WorkerActionData.CreatePurchaseOrder
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.workerAction.WorkerActionRepository
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.post

@Suppress("LongMethod", "MaxLineLength")
@SpringBootTest(
    properties = [
        "security.allowed-issuers=test-client,another-test-client",
        "origin-issuers.planned=test-client",
    ],
)
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", iss = "test-client")
class ServicePurchaseOrderIntegrationTest : PurchaseOrderAbstractIntegrationTest() {

    @Autowired
    private lateinit var workerActionRepository: WorkerActionRepository

    @BeforeEach
    override fun setUp() {
        super.setUp()
        workerActionRepository.deleteAll()
    }

    @Nested
    inner class Create {
        @Test
        fun `should respond with CREATED given a valid request is provided and schedule a CREATE_ORDER task`() {
            // given
            val givenMarket = "us"
            val poId = UUID.randomUUID()
            val poNumber = "2503NJ${IntRange(100000, 999999).random()}"
            val userId = UUID.randomUUID()
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            saveSupplier(id = supplierId, market = givenMarket)
            saveSku(id = skuId, skuCode = "SKU-123", skuName = "Test SKU", market = givenMarket)
            saveEmergencyReason(id = emergencyReasonId, name = "Test reason", market = givenMarket)
            saveDistributionCenter(code = dcCode, market = givenMarket)

            // when
            postCreateOrder(
                content = """
                                {
                                  "purchase_order_identifier": {
                                    "id": "$poId",
                                    "po_number": "$poNumber"
                                  },
                                  "user_id": "$userId",
                                  "user_email": "$userEmail",
                                  "dc_code": "$dcCode",
                                  "dc_week": "2021-W01",
                                  "supplier_id": "$supplierId",
                                  "shipping_method": "VENDOR",
                                  "emergency_reason_id": "$emergencyReasonId",
                                  "delivery_window": {
                                    "start": "2021-10-01T08:00:00Z",
                                    "end": "2021-10-01T10:00:00Z"
                                  },
                                  "order_items": [
                                    {
                                      "sku_id": "$skuId",
                                      "buffer_percent": 0,
                                      "packaging": {
                                        "price_per_unit": {
                                          "amount": "0.96",
                                          "currency": "USD"
                                        },
                                        "number_of_units": "1200",
                                        "packaging_type": "UNIT"
                                      }
                                    }
                                  ]
                                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isCreated() }
                jsonPath("$.id") { value(poId.toString()) }
                jsonPath("$.po_number") { value(poNumber) }
            }

            // and then
            assertThat(
                workerActionRepository.findAll().map { it.actionType to it.getPayload() },
                equalTo(
                    listOf(
                        WorkerActionType.CREATE_ORDER to CreatePurchaseOrder(poId),
                    ),
                ),
            )

            assertThat(
                purchaseOrderRepository.findByIdOrNull(poId)?.origin,
                equalTo(Origin.PLANNED),
            )
        }

        @Test
        @WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df", iss = "another-test-client")
        fun `should respond create PO with OTHER origin when ISSUER is not under planned`() {
            // given
            val givenMarket = "us"
            val poId = UUID.randomUUID()
            val poNumber = "2503NJ${IntRange(100000, 999999).random()}"
            val userId = UUID.randomUUID()
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            saveSupplier(id = supplierId, market = givenMarket)
            saveSku(id = skuId, skuCode = "SKU-123", skuName = "Test SKU", market = givenMarket)
            saveEmergencyReason(id = emergencyReasonId, name = "Test reason", market = givenMarket)
            saveDistributionCenter(code = dcCode, market = givenMarket)

            // when
            postCreateOrder(
                content = """
                                {
                                  "purchase_order_identifier": {
                                    "id": "$poId",
                                    "po_number": "$poNumber"
                                  },
                                  "user_id": "$userId",
                                  "user_email": "$userEmail",
                                  "dc_code": "$dcCode",
                                  "dc_week": "2021-W01",
                                  "supplier_id": "$supplierId",
                                  "shipping_method": "VENDOR",
                                  "emergency_reason_id": "$emergencyReasonId",
                                  "delivery_window": {
                                    "start": "2021-10-01T08:00:00Z",
                                    "end": "2021-10-01T10:00:00Z"
                                  },
                                  "order_items": [
                                    {
                                      "sku_id": "$skuId",
                                      "buffer_percent": 0,
                                      "packaging": {
                                        "price_per_unit": {
                                          "amount": "0.96",
                                          "currency": "USD"
                                        },
                                        "number_of_units": "1200",
                                        "packaging_type": "UNIT"
                                      }
                                    }
                                  ]
                                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isCreated() }
                jsonPath("$.id") { value(poId.toString()) }
                jsonPath("$.po_number") { value(poNumber) }
            }

            // and then
            assertThat(
                workerActionRepository.findAll().map { it.actionType to it.getPayload() },
                equalTo(
                    listOf(
                        WorkerActionType.CREATE_ORDER to CreatePurchaseOrder(poId),
                    ),
                ),
            )

            assertThat(
                purchaseOrderRepository.findByIdOrNull(poId)?.origin,
                equalTo(Origin.OTHER),
            )
        }

        @Test
        fun `should respond with BAD_REQUEST when the PurchaseOrder id is not provided`() {
            // given
            val poNumber = "2503NJ${IntRange(100000, 999999).random()}"
            val userId = UUID.randomUUID()
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            // when
            postCreateOrder(
                content = """
                {
                  "purchase_order_identifier": {
                    "po_number": "$poNumber"
                  },
                  "user_id": "$userId",
                  "user_email": "$userEmail",
                  "dc_code": "$dcCode",
                  "dc_week": "2021-W01",
                  "supplier_id": "$supplierId",
                  "shipping_method": "VENDOR",
                  "emergency_reason_id": "$emergencyReasonId",
                  "delivery_window": {
                    "start": "2021-10-01T08:00:00Z",
                    "end": "2021-10-01T10:00:00Z"
                  },
                  "order_items": [
                    {
                      "sku_id": "$skuId",
                      "buffer_percent": 0,
                      "packaging": {
                        "price_per_unit": {
                          "amount": "0.96",
                          "currency": "USD"
                        },
                        "number_of_units": "1200",
                        "packaging_type": "UNIT"
                      }
                    }
                  ]
                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isBadRequest() }
            }
        }

        @Test
        fun `should respond with BAD_REQUEST when the PurchaseOrder number is not provided`() {
            // given
            val poId = UUID.randomUUID()
            val userId = UUID.randomUUID()
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            // when
            postCreateOrder(
                content = """
                {
                  "purchase_order_identifier": {
                    "id": "$poId",
                  },
                  "user_id": "$userId",
                  "user_email": "$userEmail",
                  "dc_code": "$dcCode",
                  "dc_week": "2021-W01",
                  "supplier_id": "$supplierId",
                  "shipping_method": "VENDOR",
                  "emergency_reason_id": "$emergencyReasonId",
                  "delivery_window": {
                    "start": "2021-10-01T08:00:00Z",
                    "end": "2021-10-01T10:00:00Z"
                  },
                  "order_items": [
                    {
                      "sku_id": "$skuId",
                      "buffer_percent": 0,
                      "packaging": {
                        "price_per_unit": {
                          "amount": "0.96",
                          "currency": "USD"
                        },
                        "number_of_units": "1200",
                        "packaging_type": "UNIT"
                      }
                    }
                  ]
                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isBadRequest() }
            }
        }

        @Test
        fun `should respond with BAD_REQUEST when the PurchaseOrder number is incorrect`() {
            // given
            val poId = UUID.randomUUID()
            val userId = UUID.randomUUID()
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            // when
            postCreateOrder(
                content = """
                {
                  "purchase_order_identifier": {
                    "id": "$poId",
                    "po_number": "INV-PO-123"
                  },
                  "user_id": "$userId",
                  "user_email": "$userEmail",
                  "dc_code": "$dcCode",
                  "dc_week": "2021-W01",
                  "supplier_id": "$supplierId",
                  "shipping_method": "VENDOR",
                  "emergency_reason_id": "$emergencyReasonId",
                  "delivery_window": {
                    "start": "2021-10-01T08:00:00Z",
                    "end": "2021-10-01T10:00:00Z"
                  },
                  "order_items": [
                    {
                      "sku_id": "$skuId",
                      "buffer_percent": 0,
                      "packaging": {
                        "price_per_unit": {
                          "amount": "0.96",
                          "currency": "USD"
                        },
                        "number_of_units": "1200",
                        "packaging_type": "UNIT"
                      }
                    }
                  ]
                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isBadRequest() }
                jsonPath("$.message") {
                    value("""Field "purchaseOrderIdentifier.poNumber" must match "^\d{4}[A-Za-z]{2}\d{6}$"""")
                }
            }
        }

        @Test
        fun `should respond with BAD_REQUEST when the user id is not provided`() {
            // given
            val poId = UUID.randomUUID()
            val poNumber = "2503NJ${IntRange(100000, 999999).random()}"
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            // when
            postCreateOrder(
                content = """
                {
                  "purchase_order_identifier": {
                    "id": "$poId",
                    "po_number": "$poNumber"
                  },
                  "user_email": "$userEmail",
                  "dc_code": "$dcCode",
                  "dc_week": "2021-W01",
                  "supplier_id": "$supplierId",
                  "shipping_method": "VENDOR",
                  "emergency_reason_id": "$emergencyReasonId",
                  "delivery_window": {
                    "start": "2021-10-01T08:00:00Z",
                    "end": "2021-10-01T10:00:00Z"
                  },
                  "order_items": [
                    {
                      "sku_id": "$skuId",
                      "buffer_percent": 0,
                      "packaging": {
                        "price_per_unit": {
                          "amount": "0.96",
                          "currency": "USD"
                        },
                        "number_of_units": "1200",
                        "packaging_type": "UNIT"
                      }
                    }
                  ]
                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isBadRequest() }
            }
        }

        @Test
        fun `should respond with BAD_REQUEST when the user email is not provided`() {
            // given
            val poId = UUID.randomUUID()
            val poNumber = "2503NJ${IntRange(100000, 999999).random()}"
            val userId = UUID.randomUUID()
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            // when
            postCreateOrder(
                content = """
                {
                  "purchase_order_identifier": {
                    "id": "$poId",
                    "po_number": "$poNumber"
                  },
                  "user_id": "$userId",
                  "dc_code": "$dcCode",
                  "dc_week": "2021-W01",
                  "supplier_id": "$supplierId",
                  "shipping_method": "VENDOR",
                  "emergency_reason_id": "$emergencyReasonId",
                  "delivery_window": {
                    "start": "2021-10-01T08:00:00Z",
                    "end": "2021-10-01T10:00:00Z"
                  },
                  "order_items": [
                    {
                      "sku_id": "$skuId",
                      "buffer_percent": 0,
                      "packaging": {
                        "price_per_unit": {
                          "amount": "0.96",
                          "currency": "USD"
                        },
                        "number_of_units": "1200",
                        "packaging_type": "UNIT"
                      }
                    }
                  ]
                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isBadRequest() }
            }
        }

        @Test
        fun `should respond with CONFLICT given a valid request is provided but the PurchaseOrder id already exist`() {
            // given
            val poId = UUID.randomUUID()
            val poNumber = "2505ZZ${IntRange(100000, 999999).random()}"
            val userId = UUID.randomUUID()
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            savePurchaseOrderRevision(
                id = poId,
                poNumber = "ANOTHER-PO-NUMBER",
                version = 1,
                supplierId = supplierId,
                comment = "Existing Purchase Order",
            )

            // when
            postCreateOrder(
                content = """
                {
                  "purchase_order_identifier": {
                    "id": "$poId",
                    "po_number": "$poNumber"
                  },
                  "user_id": "$userId",
                  "user_email": "$userEmail",
                  "dc_code": "$dcCode",
                  "dc_week": "2021-W01",
                  "supplier_id": "$supplierId",
                  "shipping_method": "VENDOR",
                  "emergency_reason_id": "$emergencyReasonId",
                  "delivery_window": {
                    "start": "2021-10-01T08:00:00Z",
                    "end": "2021-10-01T10:00:00Z"
                  },
                  "order_items": [
                    {
                      "sku_id": "$skuId",
                      "buffer_percent": 0,
                      "packaging": {
                        "price_per_unit": {
                          "amount": "0.96",
                          "currency": "USD"
                        },
                        "number_of_units": "1200",
                        "packaging_type": "UNIT"
                      }
                    }
                  ]
                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isConflict() }
                jsonPath("$.message") {
                    value("Purchase Order already exist for the given id and number. [id=$poId, poNumber=$poNumber]")
                }
            }
        }

        @Test
        fun `should respond with CONFLICT given a valid request is provided but the PurchaseOrder number already exist`() {
            // given
            val poId = UUID.randomUUID()
            val poNumber = "2505NJ${IntRange(100000, 999999).random()}"
            val userId = UUID.randomUUID()
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            savePurchaseOrderRevision(
                id = UUID.randomUUID(),
                poNumber = poNumber,
                version = 1,
                supplierId = supplierId,
                comment = "Existing Purchase Order",
            )

            // when
            postCreateOrder(
                content = """
                {
                  "purchase_order_identifier": {
                    "id": "$poId",
                    "po_number": "$poNumber"
                  },
                  "user_id": "$userId",
                  "user_email": "$userEmail",
                  "dc_code": "$dcCode",
                  "dc_week": "2021-W01",
                  "supplier_id": "$supplierId",
                  "shipping_method": "VENDOR",
                  "emergency_reason_id": "$emergencyReasonId",
                  "delivery_window": {
                    "start": "2021-10-01T08:00:00Z",
                    "end": "2021-10-01T10:00:00Z"
                  },
                  "order_items": [
                    {
                      "sku_id": "$skuId",
                      "buffer_percent": 0,
                      "packaging": {
                        "price_per_unit": {
                          "amount": "0.96",
                          "currency": "USD"
                        },
                        "number_of_units": "1200",
                        "packaging_type": "UNIT"
                      }
                    }
                  ]
                }
                """.trimIndent(),
                sendImmediately = false,
            ).andExpect {
                // then
                status { isConflict() }
                jsonPath("$.message") {
                    value("Purchase Order already exist for the given id and number. [id=$poId, poNumber=$poNumber]")
                }
            }
        }
    }

    @Nested
    inner class CreateAndSend {
        @Test
        fun `should respond with CREATED given a valid request is provided and schedule a CREATE_AND_SEND_ORDER task`() {
            // given
            val givenMarket = "us"
            val poId = UUID.randomUUID()
            val poNumber = "2503NJ${IntRange(100000, 999999).random()}"
            val userId = UUID.randomUUID()
            val userEmail = "<EMAIL>"
            val supplierId = UUID.randomUUID()
            val skuId = UUID.randomUUID()
            val emergencyReasonId = UUID.randomUUID()
            val dcCode = "ZZ"

            saveSupplier(id = supplierId, market = givenMarket)
            saveSku(id = skuId, skuCode = "SKU-123", skuName = "Test SKU", market = givenMarket)
            saveEmergencyReason(id = emergencyReasonId, name = "Test reason", market = givenMarket)
            saveDistributionCenter(code = dcCode, market = givenMarket)

            // when
            postCreateOrder(
                content = """
                                {
                                  "purchase_order_identifier": {
                                    "id": "$poId",
                                    "po_number": "$poNumber"
                                  },
                                  "user_id": "$userId",
                                  "user_email": "$userEmail",
                                  "dc_code": "$dcCode",
                                  "dc_week": "2021-W01",
                                  "supplier_id": "$supplierId",
                                  "shipping_method": "VENDOR",
                                  "emergency_reason_id": "$emergencyReasonId",
                                  "delivery_window": {
                                    "start": "2021-10-01T08:00:00Z",
                                    "end": "2021-10-01T10:00:00Z"
                                  },
                                  "order_items": [
                                    {
                                      "sku_id": "$skuId",
                                      "buffer_percent": 0,
                                      "packaging": {
                                        "price_per_unit": {
                                          "amount": "0.96",
                                          "currency": "USD"
                                        },
                                        "number_of_units": "1200",
                                        "packaging_type": "UNIT"
                                      }
                                    }
                                  ]
                                }
                """.trimIndent(),
                sendImmediately = true,
            ).andExpect {
                // then
                status { isCreated() }
                jsonPath("$.id") { value(poId.toString()) }
                jsonPath("$.po_number") { value(poNumber) }
            }

            // and then
            assertThat(
                workerActionRepository.findAll().map { it.actionType to it.getPayload() },
                equalTo(
                    listOf(
                        WorkerActionType.CREATE_AND_SEND_ORDER to CreateAndSendPurchaseOrder(poId),
                    ),
                ),
            )
        }
    }

    private fun postCreateOrder(content: String, sendImmediately: Boolean) = mockMvc.post(SERVICE_ORDERS_PATH) {
        param("send_immediately", sendImmediately.toString())
        contentType = MediaType.APPLICATION_JSON
        this.content = content
    }

    companion object {
        private const val SERVICE_ORDERS_PATH = "/service/orders"
    }
}
