package com.hellofresh.oms.orderManagementHttp.orderOverview.intake

import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.BrandDcConfig
import com.hellofresh.oms.orderManagementHttp.orderOverview.config.PoOverviewConfigs
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusDetails
import com.hellofresh.oms.orderManagementHttp.orderOverview.out.transferOrder.TransferOrderStatusViewRepository
import com.hellofresh.oms.model.YearWeek
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.math.BigDecimal
import java.util.UUID

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
class TransferOrderOverviewIntegrationTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockBean
    private lateinit var transferOrderStatusViewRepository: TransferOrderStatusViewRepository

    @MockBean
    private lateinit var poOverviewConfigs: PoOverviewConfigs

    @Test
    @WithJWTUser
    fun `should return transfer order overview with aggregated data`() {
        val transferOrderId1 = UUID.randomUUID()
        val transferOrderId2 = UUID.randomUUID()

        val statusDetails1 = TransferOrderStatusDetails(
            transferOrderId = transferOrderId1,
            transferOrderNumber = "TO-2024-001",
            skuCode = "SKU-001",
            status = "STATE_OPEN",
            destinationDcCode = "DC001",
            sourceDcCode = "DC002",
            skuName = "Integration Test Product",
            category = "Fresh",
            skuUom = "kg",
            week = YearWeek("2024-W01"),
            quantityReceived = BigDecimal("10.0"),
            casesReceived = 5,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 25.00",
            totalQuantity = BigDecimal("10.0"),
            reasonText = "Stock transfer",
            shippingMethod = "Truck",
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe"
        )

        val statusDetails2 = TransferOrderStatusDetails(
            transferOrderId = transferOrderId2,
            transferOrderNumber = "TO-2024-002",
            skuCode = "SKU-001",
            status = "STATE_DELIVERED",
            destinationDcCode = "DC003",
            sourceDcCode = "DC002",
            skuName = "Integration Test Product",
            category = "Fresh",
            skuUom = "kg",
            week = YearWeek("2024-W01"),
            quantityReceived = BigDecimal("15.0"),
            casesReceived = 7,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 37.50",
            totalQuantity = BigDecimal("15.0"),
            reasonText = "Stock transfer",
            shippingMethod = "Standard",
            assignedBuyerFirstName = "John",
            assignedBuyerLastName = "Doe"
        )

        whenever(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC002"),
                dcWeeks = listOf("2024-W01")
            )
        ).thenReturn(listOf(statusDetails1, statusDetails2))

        mockMvc.perform(
            get("/transfer-orders/overview")
                .param("dc_weeks", "2024-W01")
                .param("brand", "hellofresh")
                .param("dc_code", "DC002")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$.length()").value(1)) // Grouped by source DC and SKU
            .andExpect(jsonPath("$[0].sku_code").value("SKU-001"))
            .andExpect(jsonPath("$[0].source_dc_code").value("DC002"))
            .andExpect(jsonPath("$[0].sku_name").value("Integration Test Product"))
            .andExpect(jsonPath("$[0].category").value("Fresh"))
            .andExpect(jsonPath("$[0].packaging_type").value("CASE"))
            .andExpect(jsonPath("$[0].total_ordered_quantity").value(25.0)) // 10 + 15
            .andExpect(jsonPath("$[0].total_received_quantity").value(25.0)) // 10 + 15
            .andExpect(jsonPath("$[0].total_cases_received").value(12)) // 5 + 7
            .andExpect(jsonPath("$[0].assigned_buyer_first_name").value("John"))
            .andExpect(jsonPath("$[0].assigned_buyer_last_name").value("Doe"))
            .andExpect(jsonPath("$[0].transfer_order_breakdown").isArray)
            .andExpect(jsonPath("$[0].transfer_order_breakdown.length()").value(2))
            .andExpect(jsonPath("$[0].transfer_order_breakdown[0].transfer_order_id").value(transferOrderId1.toString()))
            .andExpect(jsonPath("$[0].transfer_order_breakdown[0].transfer_order_number").value("TO-2024-001"))
            .andExpect(jsonPath("$[0].transfer_order_breakdown[0].status").value("STATE_OPEN"))
            .andExpect(jsonPath("$[0].transfer_order_breakdown[1].transfer_order_id").value(transferOrderId2.toString()))
            .andExpect(jsonPath("$[0].transfer_order_breakdown[1].transfer_order_number").value("TO-2024-002"))
            .andExpect(jsonPath("$[0].transfer_order_breakdown[1].status").value("STATE_DELIVERED"))
    }

    @Test
    @WithJWTUser
    fun `should return transfer order overview using brand configuration when dc_code is not provided`() {
        val transferOrderId = UUID.randomUUID()

        val statusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-003",
            skuCode = "SKU-002",
            status = "STATE_IN_TRANSIT",
            destinationDcCode = "DC004",
            sourceDcCode = "DC005",
            skuName = "Brand Config Product",
            category = "Dairy",
            skuUom = "liter",
            week = YearWeek("2024-W02"),
            quantityReceived = BigDecimal("20.0"),
            casesReceived = 10,
            caseSize = BigDecimal("2.0"),
            packagingType = "case",
            priceAmount = "EUR 50.00",
            totalQuantity = BigDecimal("20.0"),
            reasonText = "Regular transfer",
            shippingMethod = "Express",
            assignedBuyerFirstName = "Jane",
            assignedBuyerLastName = "Smith"
        )

        val brandDcConfig = BrandDcConfig(dcs = listOf("DC005", "DC006"))
        whenever(poOverviewConfigs.brandDcConfig).thenReturn(mapOf("hellofresh" to brandDcConfig))

        whenever(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC005", "DC006"),
                dcWeeks = listOf("2024-W02")
            )
        ).thenReturn(listOf(statusDetails))

        mockMvc.perform(
            get("/transfer-orders/overview")
                .param("dc_weeks", "2024-W02")
                .param("brand", "hellofresh")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$.length()").value(1))
            .andExpect(jsonPath("$[0].sku_code").value("SKU-002"))
            .andExpect(jsonPath("$[0].source_dc_code").value("DC005"))
            .andExpect(jsonPath("$[0].sku_name").value("Brand Config Product"))
            .andExpect(jsonPath("$[0].category").value("Dairy"))
            .andExpect(jsonPath("$[0].total_ordered_quantity").value(20.0))
            .andExpect(jsonPath("$[0].transfer_order_breakdown.length()").value(1))
            .andExpect(jsonPath("$[0].transfer_order_breakdown[0].transfer_order_id").value(transferOrderId.toString()))
    }

    @Test
    @WithJWTUser
    fun `should return empty array when no transfer orders found`() {
        whenever(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC999"),
                dcWeeks = listOf("2024-W99")
            )
        ).thenReturn(emptyList())

        mockMvc.perform(
            get("/transfer-orders/overview")
                .param("dc_weeks", "2024-W99")
                .param("brand", "hellofresh")
                .param("dc_code", "DC999")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$").isEmpty)
    }

    @Test
    @WithJWTUser
    fun `should handle multiple dc_weeks parameter`() {
        val transferOrderId = UUID.randomUUID()

        val statusDetails = TransferOrderStatusDetails(
            transferOrderId = transferOrderId,
            transferOrderNumber = "TO-2024-004",
            skuCode = "SKU-003",
            status = "STATE_OPEN",
            destinationDcCode = "DC007",
            sourceDcCode = "DC008",
            skuName = "Multi Week Product",
            category = "Packaged",
            skuUom = "piece",
            week = YearWeek("2024-W03"),
            quantityReceived = BigDecimal("5.0"),
            casesReceived = null,
            caseSize = null,
            packagingType = "unit",
            priceAmount = "EUR 12.50",
            totalQuantity = BigDecimal("5.0"),
            reasonText = null,
            shippingMethod = null,
            assignedBuyerFirstName = null,
            assignedBuyerLastName = null
        )

        whenever(
            transferOrderStatusViewRepository.findAllTransferOrderDetails(
                dcCodes = listOf("DC008"),
                dcWeeks = listOf("2024-W03", "2024-W04")
            )
        ).thenReturn(listOf(statusDetails))

        mockMvc.perform(
            get("/transfer-orders/overview")
                .param("dc_weeks", "2024-W03,2024-W04")
                .param("brand", "hellofresh")
                .param("dc_code", "DC008")
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$").isArray)
            .andExpect(jsonPath("$.length()").value(1))
            .andExpect(jsonPath("$[0].sku_code").value("SKU-003"))
            .andExpect(jsonPath("$[0].packaging_type").value("UNIT"))
            .andExpect(jsonPath("$[0].total_cases_received").doesNotExist())
    }
}
