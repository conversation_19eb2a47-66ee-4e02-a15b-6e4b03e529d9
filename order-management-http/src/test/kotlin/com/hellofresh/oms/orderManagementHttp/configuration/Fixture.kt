package com.hellofresh.oms.orderManagementHttp.configuration

import com.nimbusds.jose.JWSAlgorithm
import com.nimbusds.jose.JWSHeader
import com.nimbusds.jose.crypto.MACSigner
import com.nimbusds.jose.crypto.RSASSASigner
import com.nimbusds.jose.jwk.KeyUse
import com.nimbusds.jose.jwk.RSAKey
import com.nimbusds.jose.jwk.gen.RSAKeyGenerator
import com.nimbusds.jwt.JWTClaimsSet.Builder
import com.nimbusds.jwt.SignedJWT
import java.util.Date
import java.util.UUID

object Fixture {
    const val USER_EMAIL = "<EMAIL>"
    const val USER_NAME = "Test Test"

    const val VALID_KEY_ID: String = "2bb03572-23f0-4321-bfa3-e5adf6260515"
    const val INVALID_KEY_ID: String = "3853d24b-355a-4a6b-babe-a9ae0bbf0e82"

    val VALID_RSA_KEY_PAIR: RSAKey = RSAKeyGenerator(2048)
        .keyUse(KeyUse.SIGNATURE)
        .algorithm(JWSAlgorithm.RS256)
        .keyID(VALID_KEY_ID)
        .generate()
    val INVALID_RSA_KEY_PAIR: RSAKey = RSAKeyGenerator(2048)
        .keyUse(KeyUse.SIGNATURE)
        .algorithm(JWSAlgorithm.RS256)
        .keyID(INVALID_KEY_ID)
        .generate()

    fun getAzureJwt(
        keyId: String,
        keyPair: RSAKey,
        issuerUri: String,
    ): String {
        val signer = RSASSASigner(keyPair)
        val claimsSet = Builder()
            .issuer(issuerUri)
            .subject("LtRWQ036JFPZQgsks")
            .claim("oid", UUID.randomUUID().toString())
            .claim("email", USER_EMAIL)
            .claim("name", USER_NAME)
            .expirationTime(Date(Date().time + 60 * 1000))
            .build()
        val signedJWT = SignedJWT(
            JWSHeader.Builder(JWSAlgorithm.RS256).keyID(keyId).build(),
            claimsSet
        )
        signedJWT.sign(signer)
        return signedJWT.serialize()
    }

    fun getAuthServiceJwt(secretKey: String): String {
        val signer = MACSigner(secretKey.toByteArray())
        val claimsSet = Builder()
            .claim("sub", UUID.randomUUID().toString())
            .claim("email", USER_EMAIL)
            .claim("metadata", mapOf("name" to USER_NAME))
            .expirationTime(Date(Date().time + 60 * 1000))
            .build()
        val header = JWSHeader.Builder(JWSAlgorithm.HS256).build()
        val signedJWT = SignedJWT(
            header,
            claimsSet
        )
        signedJWT.sign(signer)
        return signedJWT.serialize()
    }
}
