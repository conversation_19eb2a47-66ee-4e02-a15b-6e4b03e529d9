package com.hellofresh.oms.orderManagementHttp.supplier

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.Matchers.containsInRelativeOrder
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(scripts = ["/data/delete.sql", "/data/suppliers.sql"])
class SupplierIntegrationTest(@Autowired private var mockMvc: MockMvc) : AbstractIntegrationTest() {
    @Test
    fun `should respond with sorted Suppliers when request valid`() {
        // given
        val expectedNames = arrayOf("Supplier 115845", "Supplier A", "Supplier B", "Supplier C", "Supplier D")

        // when
        mockMvc.get("/suppliers") {
            accept(APPLICATION_CONTENT)
            param("market", "us")
            param("page", "0")
            param("size", "5")
        }.andExpect {
            jsonPath("$.suppliers.length()", equalTo(5))
            jsonPath("$.suppliers[*].name", containsInRelativeOrder(*expectedNames))
        }
    }

    @Test
    fun `should respond with Suppliers when supplier name matches 'Supplier o'`() {
        // given
        val expectedNames = arrayOf("Supplier O", "Supplier Ohio")

        // when
        mockMvc.get("/suppliers") {
            param("market", "us")
            param("search", "lier o")
            param("page", "0")
            param("size", "5")
        }.andExpect {
            jsonPath("$.suppliers.length()", equalTo(2))
            jsonPath("$.suppliers[*].name", containsInRelativeOrder(*expectedNames))
        }
    }

    @Test
    fun `should not match any supplier when supplier name does not exist`() {
        // when
        mockMvc.get("/suppliers") {
            param("market", "us")
            param("search", "SomeSupplierNameThatDoesNotExist")
            param("page", "0")
            param("size", "5")
        }.andExpect {
            jsonPath("$.suppliers.length()", equalTo(0))
        }
    }

    @Test
    fun `should return correct shipping methods`() {
        val supplierId = "d578e414-f301-4e0d-92f6-393f14abb145"
        mockMvc.get("/suppliers/$supplierId/shipping-methods")
            .andExpect {
                status { isOk() }
                jsonPath("$.default_shipping_method", equalTo("VENDOR"))
                jsonPath("$.shipping_methods.length()", equalTo(2))
                jsonPath("$.shipping_methods[0].supplier_id", equalTo("d578e414-f301-4e0d-92f6-393f14abb145"))
                jsonPath("$.shipping_methods[0].shipping_method", equalTo("VENDOR"))
                jsonPath("$.shipping_methods[0].market", equalTo("us"))

                jsonPath("$.shipping_methods[1].supplier_id", equalTo("d578e414-f301-4e0d-92f6-393f14abb145"))
                jsonPath("$.shipping_methods[1].shipping_method", equalTo("CROSSDOCK"))
                jsonPath("$.shipping_methods[1].market", equalTo("us"))
            }
    }

    @Test
    fun `should return default shipping method if supplierId wasn't found in DB`() {
        val supplierId = "00000000-0000-0000-0000-000000000000"
        mockMvc.get("/suppliers/$supplierId/shipping-methods")
            .andExpect {
                status { isOk() }
                jsonPath("$.shipping_methods.length()", equalTo(0))
                jsonPath("$.default_shipping_method", equalTo("VENDOR"))
            }
    }

    @Test
    fun `should respond with filtered Suppliers when search is provided`() {
        // given
        val search = "115845"
        val expectedNames = arrayOf(
            "Supplier 115845",
            "Supplier I",
            "Supplier Not Excluded",
            "Supplier O",
            "Supplier Ohio",
        )

        // when
        mockMvc.get("/suppliers") {
            accept(APPLICATION_CONTENT)
            param("market", "us")
            param("search", search)
            param("page", "0")
            param("size", "10")
        }.andExpect {
            jsonPath("$.suppliers.length()", equalTo(5))
            jsonPath("$.suppliers[*].name", containsInRelativeOrder(*expectedNames))
        }
    }

    companion object {
        private val APPLICATION_CONTENT = MediaType.parseMediaType("application/json")
    }
}
