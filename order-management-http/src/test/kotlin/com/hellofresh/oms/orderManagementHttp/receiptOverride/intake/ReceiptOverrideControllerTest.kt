package com.hellofresh.oms.orderManagementHttp.receiptOverride.intake

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.orderManagement.generated.api.model.UpsertReceiptOverrideRequest
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.helper.WithJWTUser
import com.hellofresh.oms.orderManagementHttp.receiptOverride.ReceiptOverrideRepository
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType.APPLICATION_JSON
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@WithJWTUser(userId = "3d40b713-b378-40cf-8ed8-dffbd6cfa8df")
@Sql(scripts = ["/data/receiptOverride.sql"])
class ReceiptOverrideControllerTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var receiptOverrideRepository: ReceiptOverrideRepository

    @Test
    fun `should create receipt override`() {
        val request = UpsertReceiptOverrideRequest(
            quantityReceived = 100,
            casesReceived = 10,
            poNumber = UUID.randomUUID().toString(),
            skuCode = UUID.randomUUID().toString(),
        )

        mockMvc.perform(
            put("/receipt-overrides")
                .contentType(APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        ).andExpect(status().isOk)

        val receiptOverride = receiptOverrideRepository.findByPoNumberAndSkuCode(request.poNumber, request.skuCode)
        assertNotNull(receiptOverride)
        assertEquals(request.quantityReceived, receiptOverride.quantity)
    }

    @Test
    fun `should update receipt override`() {
        val request = UpsertReceiptOverrideRequest(
            quantityReceived = 200,
            casesReceived = 20,
            poNumber = "2318NJ021001",
            skuCode = "VEG-14-005501-9"
        )

        mockMvc.perform(
            put("/receipt-overrides")
                .contentType(APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
        ).andExpect(status().isOk)

        val receiptOverride = receiptOverrideRepository.findByPoNumberAndSkuCode(request.poNumber, request.skuCode)
        assertNotNull(receiptOverride)
        assertEquals(request.quantityReceived, receiptOverride.quantity)
    }

    @Test
    fun `should delete receipt override`() {
        val poNumber = "2318NJ021001"
        val skuCode = "VEG-14-005501-9"

        val receiptOverride = receiptOverrideRepository.findByPoNumberAndSkuCode(poNumber, skuCode)
        assertNotNull(receiptOverride)

        mockMvc.perform(
            delete("/receipt-overrides")
                .param("poNumber", poNumber)
                .param("skuCode", skuCode)
                .contentType(APPLICATION_JSON)
        ).andExpect(status().isNoContent)

        val deletedReceiptOverride = receiptOverrideRepository.findByPoNumberAndSkuCode(poNumber, skuCode)
        assertNull(deletedReceiptOverride)
    }
}
