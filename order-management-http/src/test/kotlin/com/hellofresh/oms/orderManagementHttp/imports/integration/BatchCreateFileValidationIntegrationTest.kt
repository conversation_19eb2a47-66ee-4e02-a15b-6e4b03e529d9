package com.hellofresh.oms.orderManagementHttp.imports.integration

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import java.util.UUID
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.client.MultipartBodyBuilder
import org.springframework.test.web.reactive.server.WebTestClient
import org.springframework.web.reactive.function.BodyInserters

@Tag("integration")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = [
        "spring.servlet.multipart.max-file-size=100KB",
        "spring.servlet.multipart.max-request-size=100KB",
        "batch-imports.epo-max-lines=5",
    ],
)
class BatchCreateFileValidationIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    private lateinit var webTestClient: WebTestClient

    @Test
    fun `should respond with 413 when file size bigger than the limit`() {
        val multipartBody = BodyInserters.fromMultipartData(
            MultipartBodyBuilder().apply {
                part("file", ByteArray(1024 * 101)) // 101KB
                    .filename("testFile.csv")
            }.build(),
        )

        webTestClient.post()
            .uri("/imports/orders")
            .attribute("dry_run", true)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .header("Authorization", "Bearer ${getValidToken()}")
            .body(multipartBody)
            .exchange()
            .expectStatus().isEqualTo(HttpStatus.PAYLOAD_TOO_LARGE.value())
    }

    @Test
    fun `should respond with 413 when file has more than the limit of lines`() {
        val multipartBody = BodyInserters.fromMultipartData(
            MultipartBodyBuilder().apply {
                part(
                    "file",
                    """
                    week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
                    2024-W44,LI,,Internal Forecast Change,10017,2024-10-26,4:00,9:00,PTN-13-135445-5,65,case_type,0,36,units,86.4072,Vendor Delivered
                    2024-W44,LI,,Internal Forecast Change,10017,2024-10-26,4:00,9:00,PTN-13-135445-5,65,case_type,0,36,units,86.4072,Vendor Delivered
                    2024-W44,LI,,Internal Forecast Change,10017,2024-10-26,4:00,9:00,PTN-13-135445-5,65,case_type,0,36,units,86.4072,Vendor Delivered
                    2024-W44,LI,,Internal Forecast Change,10017,2024-10-26,4:00,9:00,PTN-13-135445-5,65,case_type,0,36,units,86.4072,Vendor Delivered
                    2024-W44,LI,,Internal Forecast Change,10017,2024-10-26,4:00,9:00,PTN-13-135445-5,65,case_type,0,36,units,86.4072,Vendor Delivered
                    2024-W44,LI,,Internal Forecast Change,10017,2024-10-26,4:00,9:00,PTN-13-135445-5,65,case_type,0,36,units,86.4072,Vendor Delivered
                    """.trimIndent(),
                ).filename("testFile.csv")
            }.build(),
        )

        webTestClient.post()
            .uri("/imports/orders")
            .attribute("dry_run", true)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .header("Authorization", "Bearer ${getValidToken()}")
            .body(multipartBody)
            .exchange()
            .expectStatus().isEqualTo(HttpStatus.PAYLOAD_TOO_LARGE.value())
    }

    @Test
    fun `should respond with 400 when the file is not valid`() {
        val multipartBody = BodyInserters.fromMultipartData(
            MultipartBodyBuilder().apply {
                part("file", ByteArray(1024 * 10)) // 10KB
                    .filename("testFile.csv")
            }.build(),
        )

        val response = webTestClient.post()
            .uri("/imports/orders")
            .attribute("dry_run", true)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .header("Authorization", "Bearer ${getValidToken()}")
            .body(multipartBody)
            .exchange()

        response
            .expectStatus()
            .isEqualTo(HttpStatus.BAD_REQUEST.value())

        response.expectBody().jsonPath("$.message")
            .isEqualTo("Import file is not correctly formatted. Use to the existing template")
    }

    @Test
    fun `should respond with 400 when the file has no lines`() {
        val multipartBody = BodyInserters.fromMultipartData(
            MultipartBodyBuilder().apply {
                part(
                    "file",
                    """
                    week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod
                    """.trimIndent(),
                )
                    .filename("testFile.csv")
            }.build(),
        )

        val response = webTestClient.post()
            .uri("/imports/orders")
            .attribute("dry_run", true)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .header("Authorization", "Bearer ${getValidToken()}")
            .body(multipartBody)
            .exchange()

        response
            .expectStatus()
            .isEqualTo(HttpStatus.BAD_REQUEST.value())

        response.expectBody().jsonPath("$.message")
            .isEqualTo("Import file is not correctly formatted. Use to the existing template")
    }

    @Test
    fun `should respond with 400 when file has incorrect comments`() {
        val multipartBody = BodyInserters.fromMultipartData(
            MultipartBodyBuilder().apply {
                part(
                    "file",
                    """
                    week.value,distributionCenter.value,customOrderNumberCode,reason,supplierCode.value,deliveryDate,startTime,endTime,sku.value,orderSize,orderUnit.value,buffer.value,case.size,case.uom,price,shipMethod,comments
                    2024-W45,MO,,Raw Ingredients (Bulk),111764,2024-10-30,06:00,08:00,PTN-33-001813-2,470,CASE_TYPE,0,18,unit,732.42,Vendor Delivered,valid comment
                    2024-W45,MO,,Raw Ingredients (Bulk),111764,2024-10-30,06:00,08:00,PTN-33-10002-2,111,CASE_TYPE,0,18,unit,1485.72,Vendor Delivered,
                    2024-W45,MO,,Raw Ingredients (Bulk),111764,2024-10-30,06:00,08:00,PTN-33-10001-2,155,CASE_TYPE,0,18,unit,745.56,Vendor Delivered
                    2024-W45,MO,,Raw Ingredients (Bulk),111764,2024-10-30,06:00,08:00,PTN-33-10001-2,110,CASE_TYPE,0,13,unit,400.56,Vendor Delivered
                    2024-W45,MO,,Raw Ingredients (Bulk),111652,2024-10-30,06:00,08:00,PTN-33-10017-2,80,CASE_TYPE,0,24,unit,770.16,Vendor Delivered,
                    2024-W45,MO,,Raw Ingredients (Bulk),111652,2024-10-30,06:00,08:00,PTN-33-10018-2,97,CASE_TYPE,0,18,unit,1128.15,Vendor Delivered
                    """.trimIndent(),
                )
                    .filename("testFile.csv")
            }.build(),
        )

        val response = webTestClient.post()
            .uri("/imports/orders")
            .attribute("dry_run", true)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .header("Authorization", "Bearer ${getValidToken()}")
            .body(multipartBody)
            .exchange()

        response
            .expectStatus()
            .isEqualTo(HttpStatus.BAD_REQUEST.value())

        response.expectBody().jsonPath("$.message")
            .isEqualTo("Import file is not correctly formatted. Use to the existing template")
    }

    private fun getValidToken() = Jwts.builder().signWith(Keys.hmacShaKeyFor(JWT_SECRET_KEY.toByteArray()))
        .claim("sub", UUID.randomUUID().toString())
        .claim("email", "<EMAIL>")
        .claim("metadata", mapOf("name" to "QA Test"))
        .compact()

    companion object {
        private const val JWT_SECRET_KEY = "some_long_secret_key_at_least_256_bits"
    }
}
