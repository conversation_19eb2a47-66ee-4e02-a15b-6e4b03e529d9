package com.hellofresh.oms.orderManagementHttp.imports.aggregator

import com.hellofresh.oms.model.DistributionCenter
import com.hellofresh.oms.model.EmergencyReason
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.supplier.Supplier
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryWindowDto
import com.hellofresh.oms.orderManagementHttp.imports.validator.BatchOrderRowValidatedDto
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierSimpleEntity
import java.time.LocalDateTime
import kotlin.random.Random
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class BatchCreatePurchaseOrderAggregatorTest {
    @InjectMocks
    private lateinit var aggregator: BatchCreatePurchaseOrderAggregator

    @Test
    fun `should aggregate rows when key fields match`() {
        // given
        val defaultPurchaseOrder = generateRandomValidatedDtoList()
        val differentYearWeek = generateRandomValidatedDtoList(yearWeek = YearWeek(2024, 11))
        val differentDc = generateRandomValidatedDtoList(dc = getDistributionCenterEntity("BB", market = "us"))
        val differentSupplier = generateRandomValidatedDtoList(supplier = getSupplierSimpleEntity(code = 56745841))
        val differentDeliveryWindow = generateRandomValidatedDtoList(
            deliveryWindow = DeliveryWindowDto(
                start = LocalDateTime.now(),
                end = LocalDateTime.now().plusDays(2),
            ),
        )
        val differentEmergencyReason = generateRandomValidatedDtoList(
            emergencyReason = getEmergencyReasonEntity(name = "Another reason"),
        )

        // when
        val allValidatedLines = defaultPurchaseOrder +
            differentYearWeek +
            differentDc +
            differentSupplier +
            differentDeliveryWindow +
            differentEmergencyReason

        val result = aggregator.aggregate(allValidatedLines)

        // then
        assertThat(
            result.toSet(),
            equalTo(
                setOf(
                    defaultPurchaseOrder.toPurchaseOrderDto(),
                    differentYearWeek.toPurchaseOrderDto(),
                    differentDc.toPurchaseOrderDto(),
                    differentSupplier.toPurchaseOrderDto(),
                    differentDeliveryWindow.toPurchaseOrderDto(),
                    differentEmergencyReason.toPurchaseOrderDto(),
                ),
            ),
        )
    }

    @Test
    fun `should aggregate items with comments considering only the first one`() {
        // given
        val givenYearWeek = YearWeek(2024, 10)
        val givenDc = getDistributionCenterEntity("AA", market = "us")
        val givenEmergencyReason = getEmergencyReasonEntity()
        val givenSupplier = getSupplierSimpleEntity(code = 8548454)
        val givenDeliveryWindow = DeliveryWindowDto(LocalDateTime.now().plusDays(1), LocalDateTime.now().plusDays(7))

        val linesToAggregate = listOf(
            generateRandomDtoLine(
                index = 0,
                yearWeek = givenYearWeek,
                dc = givenDc,
                emergencyReason = givenEmergencyReason,
                supplier = givenSupplier,
                deliveryWindow = givenDeliveryWindow,
                comments = "this comment should be considered",
            ),
            generateRandomDtoLine(
                index = 1,
                yearWeek = givenYearWeek,
                dc = givenDc,
                emergencyReason = givenEmergencyReason,
                supplier = givenSupplier,
                deliveryWindow = givenDeliveryWindow,
                comments = "this comment should be ignored",
            ),
            generateRandomDtoLine(
                index = 2,
                yearWeek = givenYearWeek,
                dc = givenDc,
                emergencyReason = givenEmergencyReason,
                supplier = givenSupplier,
                deliveryWindow = givenDeliveryWindow,
                comments = "this comment should also be ignored",
            ),
        )

        // when
        val aggregatedPurchaseOrders = aggregator.aggregate(linesToAggregate)

        // then only the first comment should be considered
        assertThat(aggregatedPurchaseOrders.map { it.comments }, equalTo(listOf("this comment should be considered")))
    }

    private fun List<BatchOrderRowValidatedDto>.toPurchaseOrderDto() =
        PurchaseOrderDto(
            yearWeek = first().yearWeek,
            distributionCenter = first().distributionCenter,
            emergencyReason = first().emergencyReason,
            supplier = first().supplier,
            deliveryWindow = first().deliveryWindow,
            shipMethod = first().shipMethod,
            orderItems = map {
                PurchaseOrderItemDto(
                    sku = it.sku,
                    orderSize = it.orderSize,
                    bufferValue = it.bufferValue,
                    caseSize = it.caseSize,
                    price = it.price,
                    packagingType = it.packagingType,
                    uom = it.uom,
                )
            },
        )

    private fun generateRandomValidatedDtoList(
        yearWeek: YearWeek = YearWeek(2024, 10),
        dc: DistributionCenter = getDistributionCenterEntity("AA", market = "us"),
        emergencyReason: EmergencyReason = getEmergencyReasonEntity(),
        supplier: Supplier = getSupplierSimpleEntity(code = 8548454),
        deliveryWindow: DeliveryWindowDto = DeliveryWindowDto(
            start = LocalDateTime.now().plusDays(1),
            end = LocalDateTime.now().plusDays(7),
        )
    ) = Random.nextInt(3, 10).let { size ->
        (1..size).map { index ->
            generateRandomDtoLine(index, yearWeek, dc, emergencyReason, supplier, deliveryWindow)
        }
    }

    @Suppress("LongParameterList")
    private fun generateRandomDtoLine(
        index: Int,
        yearWeek: YearWeek,
        dc: DistributionCenter,
        emergencyReason: EmergencyReason,
        supplier: Supplier,
        deliveryWindow: DeliveryWindowDto,
        comments: String? = null
    ) = BatchOrderRowValidatedDto(
        rowNumber = index,
        yearWeek = yearWeek,
        distributionCenter = dc,
        emergencyReason = emergencyReason,
        supplier = supplier,
        deliveryWindow = deliveryWindow,
        sku = getSkuEntity(code = "SKU-${Random.nextInt(100000, 200000)}"),
        orderSize = Random.nextInt(10, 100).toBigDecimal(),
        bufferValue = Random.nextInt(5, 30).toBigDecimal(),
        caseSize = Random.nextInt(1, 10).toBigDecimal(),
        price = Random.nextInt(10, 20).toBigDecimal(),
        packagingType = UNIT_TYPE,
        uom = UNIT,
        shipMethod = VENDOR,
        comments = comments,
    )
}
