package com.hellofresh.oms.orderManagementHttp.order.intake

import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.POType.EMERGENCY
import com.hellofresh.oms.model.POType.PREORDER
import com.hellofresh.oms.model.POType.STANDARD
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.supplier.SupplierAddress
import com.hellofresh.oms.model.supplier.SupplierExtended
import com.hellofresh.oms.orderManagement.generated.api.model.DeliveryAddressDto
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersResponse
import com.hellofresh.oms.orderManagement.generated.api.model.ListPurchaseOrdersSortEnum
import com.hellofresh.oms.orderManagement.generated.api.model.ShippingMethodResponse
import com.hellofresh.oms.orderManagement.generated.api.model.SupplierAddressDto
import com.hellofresh.oms.orderManagementHttp.order.getOrderItem
import com.hellofresh.oms.orderManagementHttp.order.getPageOf
import com.hellofresh.oms.orderManagementHttp.order.getPageResultDto
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.order.service.domain.fromSortOrder
import com.hellofresh.oms.orderManagementHttp.supplier.intake.from
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.SupplierShippingMethodDto
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.data.domain.Page
import org.springframework.data.domain.Sort

class ResponseMapperTest {
    private val skuId = UUID.fromString("00000000-0000-0000-0000-000000000005")
    private val skuId2 = UUID.fromString("00000000-0000-0000-0000-000000000006")

    private val purchaseOrder = getPurchaseOrderEntity().copy(
        id = UUID.fromString("00000000-0000-0000-0000-000000000001"),
        userId = UUID.fromString("00000000-0000-0000-0000-000000000002"),
        orderItems = setOf(
            getOrderItem(
                UUID.fromString("00000000-0000-0000-0000-000000000003"),
                Permyriad(20),
                skuId = skuId,
            ),
            getOrderItem(
                UUID.fromString("00000000-0000-0000-0000-000000000004"),
                Permyriad(30),
                skuId = skuId2,
            )
        ),
        comment = "test comment"
    )

    @Test
    fun `should transform YearWeek to String`() {
        assertEquals("2023-W18", purchaseOrder.yearWeek.toDto())
    }

    @Test
    fun `should transform POType to PurchaseOrderTypeEnum`() {
        POType.entries.map {
            when (it) {
                STANDARD -> assertEquals("STANDARD", it.toDto().toString())
                EMERGENCY -> assertEquals("EMERGENCY", it.toDto().toString())
                PREORDER -> assertEquals("PREORDER", it.toDto().toString())
            }
        }
    }

    @Test
    fun `should map purchase order page and supplier list to ListPurchaseOrderResponse`() {
        // given
        val purchaseOrderPage: Page<PurchaseOrder> = getPageOf(listOf(getPurchaseOrderEntity()))
        val supplier = getSupplierEntity(supplierId = getPurchaseOrderEntity().supplierId, market = "dach")
        val suppliers: List<SupplierExtended> = listOf(supplier)

        val result = ListPurchaseOrdersResponse::class.from(purchaseOrderPage, suppliers, emptyList(), emptyList())
        val expectedPage = getPageResultDto()

        // then
        kotlin.test.assertEquals(expectedPage.pageSize, result.pageResult.pageSize)
        kotlin.test.assertEquals(expectedPage.sort, result.pageResult.sort)
        kotlin.test.assertEquals(expectedPage.number, result.pageResult.number)
        kotlin.test.assertEquals(expectedPage.totalPages, result.pageResult.totalPages)
        kotlin.test.assertEquals(expectedPage.totalElements, result.pageResult.totalElements)
    }

    @Test
    fun `should transform PurchaseOrder to PurchaseOrderResponse`() {
        // given
        val purchaseOrder = getPurchaseOrderEntity().copy(comment = "abc")
        val purchaseOrderPage: Page<PurchaseOrder> = getPageOf(listOf(purchaseOrder))
        val supplier = getSupplierEntity(supplierId = getPurchaseOrderEntity().supplierId, market = "dach")
        val suppliers: List<SupplierExtended> = listOf(supplier)
        val result = ListPurchaseOrdersResponse::class.from(
            purchaseOrderPage,
            suppliers,
            emptyList(),
            emptyList(),
        ).purchaseOrders.first()

        // then
        Assertions.assertEquals(purchaseOrder.id, result.id)
        Assertions.assertEquals(purchaseOrder.poNumber, result.poNumber)
        Assertions.assertEquals(purchaseOrder.dcCode, result.dcCode)
        Assertions.assertEquals(purchaseOrder.sendTime, result.sendTime)
        Assertions.assertEquals(purchaseOrder.userEmail, result.userEmail)
        Assertions.assertEquals(purchaseOrder.version, result.version)
        Assertions.assertEquals(purchaseOrder.createdAt, result.createdAt)
        Assertions.assertEquals(purchaseOrder.updatedAt, result.updatedAt)
        Assertions.assertEquals(purchaseOrder.deliveryDateChangeReasonId, result.deliveryWindow.changeReasonId)

        Assertions.assertEquals(supplier.id, result.supplier?.id)
        Assertions.assertEquals(supplier.name, result.supplier?.name)
        Assertions.assertEquals(supplier.market, result.supplier?.market)
        assertSupplierAddresses(supplier.supplierAddress, result.supplier?.address!!)

        // Enums
        Assertions.assertEquals(purchaseOrder.type.toString(), result.poType.toString())
        Assertions.assertEquals(purchaseOrder.yearWeek.toString(), result.dcWeek)
        Assertions.assertEquals(purchaseOrder.status.toString(), result.status.toString())

        // Renamed fields
        Assertions.assertEquals(purchaseOrder.expectedEndTime, result.deliveryWindow.end)
        Assertions.assertEquals(purchaseOrder.expectedStartTime, result.deliveryWindow.start)

        // Emergency reason
        Assertions.assertEquals(purchaseOrder.emergencyReasonUuid, result.emergencyReasonId)

        // Shipping address
        assertDeliveryAddresses(purchaseOrder.shippingAddress, result.deliveryAddress)

        Assertions.assertEquals(purchaseOrder.comment, result.comment)

        // Order items
        Assertions.assertEquals(1, purchaseOrder.orderItems.count())
    }

    @ParameterizedTest
    @MethodSource("provideArguments")
    fun `should convert String with camel case to ListPurchaseOrdersSortEnum value`(
        property: String,
        direction: Sort.Direction,
        expected: ListPurchaseOrdersSortEnum
    ) {
        // when
        val result = ListPurchaseOrdersSortEnum::class.fromSortOrder(Sort.Order(direction, property))

        // then
        kotlin.test.assertEquals(expected, result)
    }

    @ParameterizedTest
    @MethodSource("provideShippingMethods")
    fun `should map ShipMethod to ShipMethodApi`(
        givenMethod: ShipMethodEnum,
        expectedMethod: com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum
    ) {
        // given
        val shipMethod = givenMethod

        // when
        val result = com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum::class.from(shipMethod)

        // then
        kotlin.test.assertEquals(expectedMethod, result)
    }

    @Test
    fun `should convert SupplierShippingMethodDto to ShippingMethodResponse`() {
        // given
        val supplierShippingMethodDto = SupplierShippingMethodDto(
            id = UUID.randomUUID(),
            supplierId = UUID.randomUUID(),
            dcCode = "dcCode",
            shippingMethod = VENDOR,
            market = "us",
        )

        // when
        val result = ShippingMethodResponse::class.from(supplierShippingMethodDto)

        // then
        kotlin.test.assertEquals(supplierShippingMethodDto.id, result.id)
        kotlin.test.assertEquals(supplierShippingMethodDto.supplierId, result.supplierId)
        kotlin.test.assertEquals(supplierShippingMethodDto.dcCode, result.dcCode)
        kotlin.test.assertEquals(
            com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.VENDOR,
            result.shippingMethod
        )
        kotlin.test.assertEquals(supplierShippingMethodDto.market, result.market)
    }

    private fun assertDeliveryAddresses(supplierAddress: ShippingAddress, address: DeliveryAddressDto) {
        kotlin.test.assertEquals(supplierAddress.city, address.city)
        kotlin.test.assertEquals(supplierAddress.countryCode, address.countryCode)
        kotlin.test.assertEquals(supplierAddress.locationName, address.locationName)
        kotlin.test.assertEquals(supplierAddress.postalCode, address.postalCode)
        kotlin.test.assertEquals(supplierAddress.region, address.region)
        kotlin.test.assertEquals(supplierAddress.streetAddress, address.address)
    }

    private fun assertSupplierAddresses(supplierAddress: SupplierAddress, address: SupplierAddressDto) {
        kotlin.test.assertEquals(supplierAddress.address, address.address)
        kotlin.test.assertEquals(supplierAddress.city, address.city)
        kotlin.test.assertEquals(supplierAddress.country, address.countryCode)
        kotlin.test.assertEquals(supplierAddress.number, address.number)
        kotlin.test.assertEquals(supplierAddress.postCode, address.postalCode)
        kotlin.test.assertEquals(supplierAddress.state, address.region)
    }

    companion object {
        @JvmStatic
        @Suppress("UnusedPrivateMember")
        fun provideArguments() = Stream.of(
            arguments("created_at", Sort.Direction.ASC, ListPurchaseOrdersSortEnum.PLUS_CREATED_AT),
            arguments("created_at", Sort.Direction.DESC, ListPurchaseOrdersSortEnum.MINUS_CREATED_AT),
            arguments(
                "expected_start_time",
                Sort.Direction.ASC,
                ListPurchaseOrdersSortEnum.PLUS_DELIVERY_WINDOW_START,
            ),
            arguments(
                "expected_start_time",
                Sort.Direction.DESC,
                ListPurchaseOrdersSortEnum.MINUS_DELIVERY_WINDOW_START,
            ),
            arguments("po_number", Sort.Direction.ASC, ListPurchaseOrdersSortEnum.PLUS_PO_NUMBER),
            arguments("po_number", Sort.Direction.DESC, ListPurchaseOrdersSortEnum.MINUS_PO_NUMBER),
        )

        @JvmStatic
        @Suppress("UnusedPrivateMember")
        fun provideShippingMethods() = Stream.of(
            arguments(
                ShipMethodEnum.VENDOR,
                com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.VENDOR
            ),
            arguments(
                ShipMethodEnum.OTHER,
                com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.OTHER
            ),
            arguments(
                ShipMethodEnum.CROSSDOCK,
                com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.CROSSDOCK
            ),
            arguments(
                ShipMethodEnum.FREIGHT_ON_BOARD,
                com.hellofresh.oms.orderManagement.generated.api.model.ShipMethodEnum.FREIGHT_ON_BOARD
            ),
        )
    }
}
