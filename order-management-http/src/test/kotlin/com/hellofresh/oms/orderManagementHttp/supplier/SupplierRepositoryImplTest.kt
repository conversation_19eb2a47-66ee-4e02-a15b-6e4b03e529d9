package com.hellofresh.oms.orderManagementHttp.supplier

import com.hellofresh.oms.model.supplier.SupplierStatus
import com.hellofresh.oms.orderManagement.generated.api.model.ListSuppliersSortEnum.PLUS_NAME
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.supplier.out.SupplierSimpleRepositoryImpl
import com.hellofresh.oms.orderManagementHttp.supplier.service.domain.toSortOrder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace.NONE
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.Sql.ExecutionPhase.BEFORE_TEST_METHOD
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
@DataJpaTest
@AutoConfigureTestDatabase(replace = NONE)
@Tag("integration")
@Sql(executionPhase = BEFORE_TEST_METHOD, scripts = ["/data/delete.sql", "/data/suppliers.sql"])
class SupplierRepositoryImplTest : AbstractIntegrationTest() {
    @Autowired
    private lateinit var supplierSimpleRepositoryImpl: SupplierSimpleRepositoryImpl

    @Test
    fun `should return all suppliers when given market`() {
        // given
        val market = "dach"

        // when
        val result = supplierSimpleRepositoryImpl.findAllSuppliersSimplePaginated(
            SupplierSimpleRepositoryImpl.SupplierSimpleFilter(
                market = market,
                search = "German supplier",
                page = 0,
                size = 10,
                sortOrder = PLUS_NAME.toSortOrder(),
            ),
        )

        // then
        assertEquals(1, result.content.size)
    }

    @Test
    fun `should exclude archived suppliers from query`() {
        // when
        val result = supplierSimpleRepositoryImpl.findAllSuppliersSimplePaginated(
            SupplierSimpleRepositoryImpl.SupplierSimpleFilter(
                market = "us",
                search = null,
                page = 0,
                size = 100,
                sortOrder = PLUS_NAME.toSortOrder(),
            ),
        )

        // then
        assertEquals(13, result.content.size)
        assert(result.content.all { it.status != SupplierStatus.ARCHIVED })
    }

    @Test
    fun `should filter by code and name when search string is provided`() {
        // given
        val search = "115845"

        // when
        val result = supplierSimpleRepositoryImpl.findAllSuppliersSimplePaginated(
            SupplierSimpleRepositoryImpl.SupplierSimpleFilter(
                market = "us",
                search = search,
                page = 0,
                size = 10,
                sortOrder = PLUS_NAME.toSortOrder(),
            ),
        )

        // then
        assertEquals(5, result.content.size)
    }

    @Test
    fun `should ignore search param if it's not passed or passed as an empty string`() {
        // given

        // when
        val result = supplierSimpleRepositoryImpl.findAllSuppliersSimplePaginated(
            SupplierSimpleRepositoryImpl.SupplierSimpleFilter(
                market = "us",
                search = "",
                page = 0,
                size = 100,
                sortOrder = PLUS_NAME.toSortOrder(),
            ),
        )

        // then
        assertEquals(13, result.content.size)
    }
}
