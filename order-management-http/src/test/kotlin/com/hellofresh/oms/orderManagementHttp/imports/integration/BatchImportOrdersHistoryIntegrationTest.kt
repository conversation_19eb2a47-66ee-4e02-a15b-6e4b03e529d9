package com.hellofresh.oms.orderManagementHttp.imports.integration

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.importHistory.ImportHistory
import com.hellofresh.oms.model.importHistory.ImportHistorySummary.BatchPoCreation
import com.hellofresh.oms.orderManagementHttp.imports.out.ImportHistoryRepository
import com.hellofresh.oms.orderManagementHttp.order.integration.PurchaseOrderAbstractIntegrationTest
import java.time.LocalDateTime
import java.util.UUID
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.get
import uk.org.webcompere.modelassert.json.JsonAssertions

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
@Suppress("LongMethod")
class BatchImportOrdersHistoryIntegrationTest : PurchaseOrderAbstractIntegrationTest() {

    @Autowired
    private lateinit var importHistoryRepository: ImportHistoryRepository

    @AfterEach
    fun tearDown() {
        purchaseOrderRepository.deleteAll()
        importHistoryRepository.deleteAll()
    }

    @Test
    fun `should respond with empty list when no import history exist for the given market`() {
        // when
        mockMvc.get("/imports/orders") {
            param("market", "dach")
            param("page", "0")
            param("size", "10")
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            content {
                string(
                    JsonAssertions.json()
                        .where()
                        .isEqualTo(
                            """
                            {
                              "items": [],
                              "page_result": {
                                "number": 0,
                                "page_size": 10,
                                "total_elements": 0,
                                "total_pages": 0
                              }
                            }
                            """.trimIndent(),
                        ),
                )
            }
        }
    }

    @Test
    fun `should respond with a single import history`() {
        // given
        val purchaseOrder = savePurchaseOrderRevision(
            poNumber = "2215NJ309619",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order",
            yearWeek = YearWeek("2025-W22"),
            dcCode = "NJ",
        )

        val purchaseOrder2 = savePurchaseOrderRevision(
            poNumber = "2215NJ309620",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 2",
            yearWeek = YearWeek("2025-W23"),
            dcCode = "AA",
        )

        val purchaseOrder3 = savePurchaseOrderRevision(
            poNumber = "2215NJ309655",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 3",
            yearWeek = YearWeek("2025-W24"),
            dcCode = "BB",
        )

        val importHistory = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(
                        purchaseOrder.poNumber,
                        purchaseOrder2.poNumber,
                        purchaseOrder3.poNumber,
                    ),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:13:31.0000"),
                filename = "filename.csv",
            ),
        )

        // when
        mockMvc.get("/imports/orders") {
            param("market", "us")
            param("page", "0")
            param("size", "10")
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            content {
                string(
                    JsonAssertions.json()
                        .where()
                        .keysInAnyOrder()
                        .isEqualTo(
                            """
                            {
                              "items": [
                                {
                                  "id": "${importHistory.id}",
                                  "filename": "${importHistory.filename}",
                                  "uploaded_by": "${importHistory.userEmail}",
                                  "uploaded_at": "2025-02-12T15:13:31",
                                  "distribution_centers": [
                                    "${purchaseOrder.dcCode}",
                                    "${purchaseOrder2.dcCode}",
                                    "${purchaseOrder3.dcCode}"
                                  ],
                                  "weeks": [
                                    "${purchaseOrder.yearWeek}",
                                    "${purchaseOrder2.yearWeek}",
                                    "${purchaseOrder3.yearWeek}"
                                  ],
                                  "purchase_orders": [
                                    "${purchaseOrder.poNumber}",
                                    "${purchaseOrder2.poNumber}",
                                    "${purchaseOrder3.poNumber}"
                                  ]
                                }
                              ],
                              "page_result": {
                                "number": 0,
                                "page_size": 10,
                                "total_elements": 1,
                                "total_pages": 1
                              }
                            }
                            """.trimIndent(),
                        ),
                )
            }
        }
    }

    @Test
    fun `should respond with a list of import history sorted by the uploadedAt descending`() {
        // given
        val purchaseOrder = savePurchaseOrderRevision(
            poNumber = "2215NJ309619",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order",
            yearWeek = YearWeek("2025-W22"),
            dcCode = "NJ",
        )

        val purchaseOrder2 = savePurchaseOrderRevision(
            poNumber = "2215NJ309620",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 2",
            yearWeek = YearWeek("2025-W23"),
            dcCode = "AA",
        )

        val purchaseOrder3 = savePurchaseOrderRevision(
            poNumber = "2215NJ309655",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 3",
            yearWeek = YearWeek("2025-W24"),
            dcCode = "BB",
        )

        val purchaseOrder4 = savePurchaseOrderRevision(
            poNumber = "2215NJ3096987",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 4",
            yearWeek = YearWeek("2025-W25"),
            dcCode = "CC",
        )

        val importHistory = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder.poNumber),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:13:31.0000"),
                filename = "filename.csv",
            ),
        )

        val importHistory2 = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder2.poNumber),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:14:00.0000"),
                filename = "filename2.csv",
            ),
        )

        val importHistory3 = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder3.poNumber),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:18:10.0000"),
                filename = "filename3.csv",
            ),
        )

        val importHistory4 = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder4.poNumber),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:17:11.0000"),
                filename = "filename.csv",
            ),
        )

        // when
        mockMvc.get("/imports/orders") {
            param("market", "us")
            param("page", "0")
            param("size", "10")
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            content {
                string(
                    JsonAssertions.json()
                        .where()
                        .keysInAnyOrder()
                        .isEqualTo(
                            """
                                {
                                  "items": [
                                    {
                                      "id": "${importHistory3.id}",
                                      "filename": "${importHistory3.filename}",
                                      "uploaded_by": "${importHistory3.userEmail}",
                                      "uploaded_at": "2025-02-12T15:18:10",
                                      "distribution_centers": [
                                        "${purchaseOrder3.dcCode}"
                                      ],
                                      "weeks": [
                                        "${purchaseOrder3.yearWeek}"
                                      ],
                                      "purchase_orders": [
                                        "${purchaseOrder3.poNumber}"
                                      ]
                                    },
                                    {
                                      "id": "${importHistory4.id}",
                                      "filename": "${importHistory4.filename}",
                                      "uploaded_by": "${importHistory4.userEmail}",
                                      "uploaded_at": "2025-02-12T15:17:11",
                                      "distribution_centers": [
                                        "${purchaseOrder4.dcCode}"
                                      ],
                                      "weeks": [
                                        "${purchaseOrder4.yearWeek}"
                                      ],
                                      "purchase_orders": [
                                        "${purchaseOrder4.poNumber}"
                                      ]
                                    },
                                    {
                                      "id": "${importHistory2.id}",
                                      "filename": "${importHistory2.filename}",
                                      "uploaded_by": "${importHistory2.userEmail}",
                                      "uploaded_at": "2025-02-12T15:14:00",
                                      "distribution_centers": [
                                        "${purchaseOrder2.dcCode}"
                                      ],
                                      "weeks": [
                                        "${purchaseOrder2.yearWeek}"
                                      ],
                                      "purchase_orders": [
                                        "${purchaseOrder2.poNumber}"
                                      ]
                                    },
                                    {
                                      "id": "${importHistory.id}",
                                      "filename": "${importHistory.filename}",
                                      "uploaded_by": "${importHistory.userEmail}",
                                      "uploaded_at": "2025-02-12T15:13:31",
                                      "distribution_centers": [
                                        "${purchaseOrder.dcCode}"
                                      ],
                                      "weeks": [
                                        "${purchaseOrder.yearWeek}"
                                      ],
                                      "purchase_orders": [
                                        "${purchaseOrder.poNumber}"
                                      ]
                                    }
                                  ],
                                  "page_result": {
                                    "number": 0,
                                    "page_size": 10,
                                    "total_elements": 4,
                                    "total_pages": 1
                                  }
                                }
                            """.trimIndent(),
                        ),
                )
            }
        }
    }

    @Test
    fun `should respond with a list of import history paginated`() {
        // given
        val purchaseOrder = savePurchaseOrderRevision(
            poNumber = "2215NJ309619",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order",
            yearWeek = YearWeek("2025-W22"),
            dcCode = "NJ",
        )

        val purchaseOrder2 = savePurchaseOrderRevision(
            poNumber = "2215NJ309620",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 2",
            yearWeek = YearWeek("2025-W23"),
            dcCode = "AA",
        )

        val purchaseOrder3 = savePurchaseOrderRevision(
            poNumber = "2215NJ309655",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 3",
            yearWeek = YearWeek("2025-W24"),
            dcCode = "BB",
        )

        val purchaseOrder4 = savePurchaseOrderRevision(
            poNumber = "2215NJ3096987",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 4",
            yearWeek = YearWeek("2025-W25"),
            dcCode = "CC",
        )

        importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder.poNumber),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:20:00.0000"),
                filename = "filename.csv",
            ),
        )

        importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder2.poNumber),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:16:00.0000"),
                filename = "filename2.csv",
            ),
        )

        val importHistory3 = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder3.poNumber),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:15:00.0000"),
                filename = "filename3.csv",
            ),
        )

        val importHistory4 = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder4.poNumber),
                    listOf("us")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:13:00.0000"),
                filename = "filename.csv",
            ),
        )

        // when
        mockMvc.get("/imports/orders") {
            param("market", "us")
            param("page", "1")
            param("size", "2")
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            content {
                string(
                    JsonAssertions.json()
                        .where()
                        .keysInAnyOrder()
                        .isEqualTo(
                            """
                                {
                                  "items": [
                                    {
                                      "id": "${importHistory3.id}",
                                      "filename": "${importHistory3.filename}",
                                      "uploaded_by": "${importHistory3.userEmail}",
                                      "uploaded_at": "2025-02-12T15:15:00",
                                      "distribution_centers": [
                                        "${purchaseOrder3.dcCode}"
                                      ],
                                      "weeks": [
                                        "${purchaseOrder3.yearWeek}"
                                      ],
                                      "purchase_orders": [
                                        "${purchaseOrder3.poNumber}"
                                      ]
                                    },
                                    {
                                      "id": "${importHistory4.id}",
                                      "filename": "${importHistory4.filename}",
                                      "uploaded_by": "${importHistory4.userEmail}",
                                      "uploaded_at": "2025-02-12T15:13:00",
                                      "distribution_centers": [
                                        "${purchaseOrder4.dcCode}"
                                      ],
                                      "weeks": [
                                        "${purchaseOrder4.yearWeek}"
                                      ],
                                      "purchase_orders": [
                                        "${purchaseOrder4.poNumber}"
                                      ]
                                    }
                                  ],
                                  "page_result": {
                                    "number": 1,
                                    "page_size": 2,
                                    "total_elements": 4,
                                    "total_pages": 2
                                  }
                                }
                            """.trimIndent(),
                        ),
                )
            }
        }
    }

    @Test
    fun `should respond with a list of import history only for the relevant market`() {
        // given
        val purchaseOrder = savePurchaseOrderRevision(
            poNumber = "2215NJ309619",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order",
            yearWeek = YearWeek("2025-W22"),
            dcCode = "NJ",
        )

        val purchaseOrder2 = savePurchaseOrderRevision(
            poNumber = "2215NJ309620",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 2",
            yearWeek = YearWeek("2025-W23"),
            dcCode = "AA",
        )

        val purchaseOrder3 = savePurchaseOrderRevision(
            poNumber = "2215NJ309655",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 3",
            yearWeek = YearWeek("2025-W24"),
            dcCode = "BB",
        )

        val purchaseOrder4 = savePurchaseOrderRevision(
            poNumber = "2215NJ3096987",
            version = 1,
            supplierId = UUID.randomUUID(),
            comment = "import history order 4",
            yearWeek = YearWeek("2025-W25"),
            dcCode = "CC",
        )

        val importHistory = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder.poNumber),
                    listOf("ca")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:20:00.0000"),
                filename = "filename.csv",
            ),
        )

        importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder2.poNumber),
                    listOf("benelux")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:16:00.0000"),
                filename = "filename2.csv",
            ),
        )

        val importHistory3 = importHistoryRepository.save(
            ImportHistory.createImportHistory(
                summary = BatchPoCreation(
                    listOf(purchaseOrder3.poNumber, purchaseOrder4.poNumber),
                    listOf("us", "ca")
                ),
                userEmail = "<EMAIL>",
                userId = UUID.randomUUID(),
                createdAt = LocalDateTime.parse("2025-02-12T15:15:00.0000"),
                filename = "filename3.csv",
            ),
        )

        // when
        mockMvc.get("/imports/orders") {
            param("market", "ca")
            param("page", "0")
            param("size", "10")
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            content {
                string(
                    JsonAssertions.json()
                        .where()
                        .keysInAnyOrder()
                        .isEqualTo(
                            """
                                {
                                  "items": [
                                    {
                                      "id": "${importHistory.id}",
                                      "filename": "${importHistory.filename}",
                                      "uploaded_by": "${importHistory.userEmail}",
                                      "uploaded_at": "2025-02-12T15:20:00",
                                      "distribution_centers": [
                                        "${purchaseOrder.dcCode}"
                                      ],
                                      "weeks": [
                                        "${purchaseOrder.yearWeek}"
                                      ],
                                      "purchase_orders": [
                                        "${purchaseOrder.poNumber}"
                                      ]
                                    },
                                    {
                                      "id": "${importHistory3.id}",
                                      "filename": "${importHistory3.filename}",
                                      "uploaded_by": "${importHistory3.userEmail}",
                                      "uploaded_at": "2025-02-12T15:15:00",
                                      "distribution_centers": [
                                        "${purchaseOrder3.dcCode}", "${purchaseOrder4.dcCode}"
                                      ],
                                      "weeks": [
                                        "${purchaseOrder3.yearWeek}", "${purchaseOrder4.yearWeek}"
                                      ],
                                      "purchase_orders": [
                                        "${purchaseOrder3.poNumber}", "${purchaseOrder4.poNumber}"
                                      ]
                                    }
                                  ],
                                  "page_result": {
                                    "number": 0,
                                    "page_size": 10,
                                    "total_elements": 2,
                                    "total_pages": 1
                                  }
                                }
                            """.trimIndent(),
                        ),
                )
            }
        }
    }
}
