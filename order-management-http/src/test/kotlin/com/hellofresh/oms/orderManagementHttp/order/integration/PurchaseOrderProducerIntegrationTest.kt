package com.hellofresh.oms.orderManagementHttp.order.integration

import com.hellofresh.oms.orderManagementHttp.configuration.AbstractKafkaIntegrationTest
import com.hellofresh.oms.orderManagementHttp.configuration.consumeAtLeast
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.emergencyReason.EmergencyReasonRepository
import com.hellofresh.oms.orderManagementHttp.order.getDistributionCenterEntity
import com.hellofresh.oms.orderManagementHttp.order.getEmergencyReasonEntity
import com.hellofresh.oms.orderManagementHttp.order.getOrderItem
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderEventType
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderProducer
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v2.PurchaseOrderEvent
import com.statsig.sdk.Statsig
import java.time.Duration
import java.util.UUID.randomUUID
import org.apache.kafka.clients.consumer.Consumer
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.empty
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest(
    properties = [
        "topics.purchase-order=public.supply.procurement.purchase-order.v2-producer-test",
    ],
)
class PurchaseOrderProducerIntegrationTest : AbstractKafkaIntegrationTest() {

    @Autowired
    private lateinit var subject: PurchaseOrderProducer

    @Autowired
    private lateinit var supplierRepository: SupplierRepository

    @Autowired
    private lateinit var distributionCentersRepository: DistributionCentersRepository

    @Autowired
    private lateinit var emergencyReasonRepository: EmergencyReasonRepository

    @Autowired
    private lateinit var skuRepository: SkuRepository

    @Autowired
    private lateinit var purchaseOrderConsumer: Consumer<String, PurchaseOrderEvent>

    @Value("\${topics.purchase-order}")
    private lateinit var purchaseOrderTopicName: String

    @Value("\${statsig.gates.enable-po-topic-v2}")
    private lateinit var enablePublisherForPoTopicStatsigGate: String

    @BeforeEach
    fun setUp() {
        supplierRepository.save(
            getSupplierEntity(
                supplierId = supplierId,
                name = "Test Supplier",
                market = market,
            ),
        )
        distributionCentersRepository.save(
            getDistributionCenterEntity(
                code = dcCode,
                market = market,
            ),
        )
        emergencyReasonRepository.save(
            getEmergencyReasonEntity(
                uuid = emergencyReasonId,
                market = market,
            ),
        )
        skuRepository.save(
            getSkuEntity(
                uuid = skuId,
                market = market,
            ),
        )
        purchaseOrderConsumer.subscribe(listOf(purchaseOrderTopicName))
    }

    @AfterEach
    fun tearDown() {
        purchaseOrderConsumer.unsubscribe()
    }

    @Test
    fun `should produce a kafka message on purchase order creation when feature flag is true`() {
        // given
        Statsig.overrideGate(enablePublisherForPoTopicStatsigGate, true)
        val givenPoNumber = "2503NJ${IntRange(100000, 999999).random()}"

        // when
        subject.publishPurchaseOrderEvent(
            getPurchaseOrderEntity(
                id = randomUUID(),
                poNumber = givenPoNumber,
                emergencyReasonId = emergencyReasonId,
                supplierId = supplierId,
                dcCode = dcCode,
                orderItems = setOf(
                    getOrderItem(id = randomUUID(), skuId = skuId),
                )
            ),
            PurchaseOrderEventType.CREATED,
        )

        // then
        val messages = purchaseOrderConsumer.consumeAtLeast(1, Duration.ofSeconds(10))
        assertThat(messages.map { it.key() }, equalTo(listOf(givenPoNumber)))
    }

    @Test
    fun `should not produce a kafka message on purchase order creation when feature flag is false`() {
        // given
        Statsig.overrideGate(enablePublisherForPoTopicStatsigGate, false)
        val givenPoNumber = "2503NJ${IntRange(100000, 999999).random()}"

        // when
        subject.publishPurchaseOrderEvent(
            getPurchaseOrderEntity(
                id = randomUUID(),
                poNumber = givenPoNumber,
                emergencyReasonId = emergencyReasonId,
                supplierId = supplierId,
                dcCode = dcCode,
                orderItems = setOf(
                    getOrderItem(id = randomUUID(), skuId = skuId),
                )
            ),
            PurchaseOrderEventType.CREATED,
        )

        // then
        val messages = purchaseOrderConsumer.consumeAtLeast(1, Duration.ofSeconds(10))
        assertThat(messages, empty())
    }

    companion object {
        private val supplierId = randomUUID()
        private val emergencyReasonId = randomUUID()
        private val skuId = randomUUID()
        private const val market = "US"
        private const val dcCode = "NJ"
    }
}
