package com.hellofresh.oms.orderManagementHttp.order.service

import com.hellofresh.oms.orderManagementHttp.configuration.PdfConfigurationProperties
import com.hellofresh.oms.orderManagementHttp.distributionCenters.DistributionCentersRepository
import com.hellofresh.oms.orderManagementHttp.order.billingAddress
import com.hellofresh.oms.orderManagementHttp.order.distributionCenter
import com.hellofresh.oms.orderManagementHttp.order.getOrderItem
import com.hellofresh.oms.orderManagementHttp.order.getPurchaseOrderEntity
import com.hellofresh.oms.orderManagementHttp.order.getSkuEntity
import com.hellofresh.oms.orderManagementHttp.order.getSupplierEntity
import com.hellofresh.oms.orderManagementHttp.order.out.PurchaseOrderRepository
import com.hellofresh.oms.orderManagementHttp.order.service.domain.PurchaseOrderForPdfTemplate
import com.hellofresh.oms.orderManagementHttp.sku.SkuRepository
import com.hellofresh.oms.orderManagementHttp.supplier.SupplierRepository
import java.util.Locale
import java.util.Optional.of
import java.util.UUID
import kotlin.random.Random
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

class PurchaseOrderReportServiceTest {
    private val purchaseOrderRepositoryMock = mock(PurchaseOrderRepository::class.java)
    private val supplierRepositoryMock = mock(SupplierRepository::class.java)
    private val distributionCentersRepositoryMock = mock(DistributionCentersRepository::class.java)
    private val pdfService = mock(PdfService::class.java)
    private val skuRepositoryMock = mock(SkuRepository::class.java)

    private val disclaimer = "disclaimer"
    private val localeLanguage = "en-US"

    private val subject = PurchaseOrderReportService(
        purchaseOrderRepositoryMock,
        supplierRepositoryMock,
        distributionCentersRepositoryMock,
        pdfService,
        PdfConfigurationProperties(
            disclaimerPerMarket = mapOf("us" to listOf(disclaimer)),
            localePerMarket = mapOf("us" to localeLanguage)
        ),
        skuRepositoryMock,
    )

    @Test
    fun `should generate PDF for Purchase Order`() {
        // given
        val supplier = getSupplierEntity(supplierId = UUID.randomUUID(), market = "us")
        val version = Random.nextInt(0, 10000)
        val dcBillingAddress = billingAddress
        val pdfBytes = ByteArray(0)
        val poId = UUID.randomUUID()
        val skuId = UUID.randomUUID()
        val purchaseOrderEntity = getPurchaseOrderEntity(
            id = poId,
            version = version,
            orderItems = setOf(getOrderItem(skuId = skuId)),
            supplierId = supplier.id
        )
        val pdfPurchaseOrder = PurchaseOrderForPdfTemplate.from(
            po = purchaseOrderEntity,
            supplier = supplier,
            dcBillingAddress = dcBillingAddress,
            disclaimers = listOf(disclaimer),
            skus = listOf(getSkuEntity(uuid = skuId)),
            locale = Locale.forLanguageTag(localeLanguage),
        )

        whenever(purchaseOrderRepositoryMock.findById(any())).thenReturn(of(purchaseOrderEntity))
        whenever(supplierRepositoryMock.findById(purchaseOrderEntity.supplierId)).thenReturn(of(supplier))
        whenever(
            distributionCentersRepositoryMock.findByCode(purchaseOrderEntity.dcCode),
        ).thenReturn(distributionCenter.copy(addresses = listOf(dcBillingAddress)))
        whenever(skuRepositoryMock.findAllById(any())).thenReturn(listOf(getSkuEntity(uuid = skuId)))
        whenever(pdfService.renderHtmlContent(any())).thenReturn("htmlContent")
        whenever(pdfService.generatePdfFromHtml("htmlContent")).thenReturn(pdfBytes)

        // when
        val result = subject.generatePurchaseOrderPdf(purchaseOrderEntity.id.toString())

        // then
        verify(purchaseOrderRepositoryMock).findById(purchaseOrderEntity.id)
        verify(supplierRepositoryMock).findById(purchaseOrderEntity.supplierId)
        verify(distributionCentersRepositoryMock).findByCode(purchaseOrderEntity.dcCode)
        verify(pdfService).renderHtmlContent(pdfPurchaseOrder)
        verify(pdfService).generatePdfFromHtml("htmlContent")
        assertEquals(Pair(pdfBytes, "purchase-order-${pdfPurchaseOrder.poNumber}"), result)
    }
}
