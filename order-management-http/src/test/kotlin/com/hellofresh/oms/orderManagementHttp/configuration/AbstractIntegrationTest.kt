package com.hellofresh.oms.orderManagementHttp.configuration

import java.time.Duration
import java.time.Instant
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.extension.ExtendWith
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.test.util.TestPropertyValues
import org.springframework.context.ApplicationContextInitializer
import org.springframework.context.ConfigurableApplicationContext
import org.springframework.test.annotation.Rollback
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.transaction.annotation.Transactional
import org.testcontainers.containers.BindMode
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.utility.DockerImageName

const val POSTGRES_TAG = "14.6-alpine"
const val POSTGRES_IMAGE = "postgres:$POSTGRES_TAG"

@ExtendWith(SpringExtension::class)
@Transactional
@Rollback
@ContextConfiguration(initializers = [AbstractIntegrationTest.Initializer::class])
@ActiveProfiles(profiles = ["default", "test", "integration"])
open class AbstractIntegrationTest {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(AbstractIntegrationTest::class.java)

        val postgreSQLContainer = PostgreSQLContainer<Nothing>(DockerImageName.parse(POSTGRES_IMAGE)).apply {
            withDatabaseName("order-management")
            withUsername("postgres")
            withPassword("postgres")
            withReuse(true)
            withLabel("reuse.UUID", UUID.randomUUID().toString())
            withClasspathResourceMapping(
                "postgres/docker-entrypoint-initdb",
                "/docker-entrypoint-initdb.d/",
                BindMode.READ_ONLY
            )
        }
    }

    internal class Initializer : ApplicationContextInitializer<ConfigurableApplicationContext> {
        override fun initialize(configurableApplicationContext: ConfigurableApplicationContext) {
            val start = Instant.now()
            Stream.of(postgreSQLContainer).parallel().forEach { c -> c.start() }
            logger.info("🐳 TestContainers started in {}", Duration.between(start, Instant.now()))

            TestPropertyValues.of(
                "spring.datasource.url=${postgreSQLContainer.jdbcUrl}",
                "spring.datasource.password=${postgreSQLContainer.password}",
                "spring.datasource.username=${postgreSQLContainer.username}",
                "spring.datasource.hikari.auto-commit=false",
            ).applyTo(configurableApplicationContext.environment)
        }
    }
}
