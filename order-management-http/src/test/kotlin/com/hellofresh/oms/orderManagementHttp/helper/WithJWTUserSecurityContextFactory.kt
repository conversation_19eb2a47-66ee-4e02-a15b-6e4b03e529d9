package com.hellofresh.oms.orderManagementHttp.helper

import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import java.time.Instant
import java.util.UUID
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken
import org.springframework.security.test.context.support.WithSecurityContext
import org.springframework.security.test.context.support.WithSecurityContextFactory

@WithSecurityContext(factory = WithJWTUserSecurityContextFactory::class)
annotation class WithJWTUser(
    val userId: String = "0d6242f7-fe3e-46fc-a676-8f5de31c0381",
    val userEmail: String = "<EMAIL>",
    val userName: String = "QA Test",
    val roleClaim: String = "purchasing.ca.all.manager",
    val iss: String = "test-client",
)

class WithJWTUserSecurityContextFactory : WithSecurityContextFactory<WithJWTUser> {

    private val jwtSecretKey = "some_long_secret_key_at_least_256_bits"

    override fun createSecurityContext(withJWTUser: WithJWTUser): SecurityContext {
        val jwt = getJwt(
            userId = withJWTUser.userId,
            userEmail = withJWTUser.userEmail,
            userName = withJWTUser.userName,
            roleClaim = withJWTUser.roleClaim,
            iss = withJWTUser.iss,
        )
        val context = SecurityContextHolder.createEmptyContext()
        context.authentication = JwtAuthenticationToken(jwt).apply { isAuthenticated = true }
        return context
    }

    private fun getJwt(
        userId: String = UUID.randomUUID().toString(),
        userEmail: String = "<EMAIL>",
        userName: String = "QA Test",
        roleClaim: String,
        iss: String,
    ): Jwt {
        val jwtToken = Jwts.builder().signWith(Keys.hmacShaKeyFor(jwtSecretKey.toByteArray()))
            .claim("sub", userId)
            .claim("email", userEmail)
            .claim("metadata", mapOf("name" to userName))
            .claim("roleclaim", roleClaim.split(","))
            .claim("iss", iss)
            .compact()

        val claims = Jwts.parser().verifyWith(Keys.hmacShaKeyFor(jwtSecretKey.toByteArray())).build().parseSignedClaims(
            jwtToken
        ).payload

        return Jwt("Bearer $jwtToken", Instant.now(), Instant.now().plusSeconds(100), claims, claims)
    }
}
