spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          secret-key: "some_long_secret_key_at_least_256_bits"
          issuer-uri: http://localhost:${wiremock.server.port}
          jwk-set-uri: http://localhost:${wiremock.server.port}/.well-known/jwks.json
  flyway:
    locations: "classpath:db/common"

management:
  tracing:
    enabled: false

tapioca:
  base-url: "http://localhost:${wiremock.server.port}"

auth-service:
  base-url: "http://localhost:${wiremock.server.port}"
  client-id: "client_id"
  client-secret: "client_secret"

ics:
  tickets:
    url: "http://localhost:${wiremock.server.port}"
    username: ics_tickets_username
    cron: "0 0 * * * *"

wiremock:
  server:
    port: 0

logging:
  level:
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG

statsig:
  api-key: "some_secret_key"
  environment: "development"

security:
  allowed-issuers: test-client,test-client-2
