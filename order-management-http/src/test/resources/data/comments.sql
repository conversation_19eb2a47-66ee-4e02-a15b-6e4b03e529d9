-- Clean up existing data
TRUNCATE TABLE comment RESTART IDENTITY CASCADE;

-- Insert test data with various combinations
INSERT INTO comment (id, source_id, brand, dc, resource_type, resource_id, comment, year_week, created_at, updated_at, created_by, updated_by, domain)
VALUES
    -- SK<PERSON> comments for test domain
    (
        '11111111-1111-1111-1111-111111111111',  -- id
        null, -- source_id
        'HF', -- brand
        'NJ', -- DC
        'sku', -- resource_type
        'DAI-10-10088-4', -- resource_id
        'Was not delivered correctly', -- comment
        '2025-W10', -- year_week
        '2025-01-01 01:01:01', -- created_at
        '2025-01-01 01:01:01', -- updated_at
        '<EMAIL>', -- created_by
        '<EMAIL>', -- updated_by
        'gpp-po-status' -- domain
    ),
    (
        '22222222-2222-2222-2222-222222222222', -- id
        null, -- source_id
        'HF', -- brand
        'TI', -- DC
        'sku', -- resource_type
        'DAI-10-10088-4', -- resource_id
        'Quality issues reported', -- comment
        '2025-W11', -- year_week
        '2025-01-02 02:02:02', -- created_at
        '2025-01-02 02:02:02', -- updated_at
        '<EMAIL>', -- created_by
        '<EMAIL>', -- updated_by
        'gpp-po-status' -- domain
    ),
    (
        '33333333-3333-3333-3333-333333333333', -- id
        null, -- source_id
        'GC', -- brand
        'SW', -- DC
        'sku', -- resource_type
        'PRO-10-11040-4', -- resource_id
        'Out of stock', -- comment
        '2025-W12', -- year_week
        '2025-01-03 03:03:03', -- created_at
        '2025-01-03 03:03:03', -- updated_at
        '<EMAIL>', -- created_by
        '<EMAIL>', -- updated_by
        'gpp-po-status' -- domain
    ),


    -- PO comments for PO Status domain
    (
        '44444444-4444-4444-4444-444444444444', -- id
        '52656', -- source_id
        'HF', -- brand
        'NJ', -- DC
        'po', -- resource_type
        '2520NJ031068_E5', -- resource_id
        'PO was sent', -- comment
        '2025-W10', -- year_week
        '2025-01-04 04:04:04', -- created_at
        '2025-01-04 04:04:04', -- updated_at
        '<EMAIL>', -- created_by
        '<EMAIL>', -- updated_by
        'imt' -- domain
    ),
    (
        '55555555-5555-5555-5555-555555555555', -- id
        '52657', -- source_id
        'HF', -- brand
        'VE', -- DC
        'po', -- resource_type
        '2520VE031069_E5', -- resource_id
        'PO was received', -- comment
        '2025-W10', -- year_week
        '2025-01-05 05:05:05', -- created_at
        '2025-01-05 05:05:05', -- updated_at
        '<EMAIL>', -- created_by
        '<EMAIL>', -- updated_by
        'imt' -- domain
    ),
    (
        '66666666-6666-6666-6666-666666666666', -- id
        '52697', -- source_id
        'GC', -- brand
        'NJ', -- DC
        'po', -- resource_type
        '2520NJ031070_E5', -- resource_id
        'PO was rejected', -- comment
        '2025-W11', -- year_week
        '2025-01-06 06:06:06', -- created_at
        '2025-01-06 06:06:06', -- updated_at
        '<EMAIL>', -- created_by
        '<EMAIL>', -- updated_by
        'imt' -- domain
    ),


    -- Comments for other domains
    (
        '77777777-7777-7777-7777-777777777777', -- id
        'src-7', -- source_id
        'HF', -- brand
        'NJ', -- DC
        'sku', -- resource_type
        'DAI-10-10089-4', -- resource_id
        'Special handling required', -- comment
        '2025-W10', -- year_week
        '2025-01-07 07:07:07', -- created_at
        '2025-01-07 07:07:07', -- updated_at
        '<EMAIL>', -- created_by
        '<EMAIL>', -- updated_by
        'gpp-ingredient-replenishment' -- domain
    ),
    (
        '88888888-8888-8888-8888-888888888888', -- id
        'src-8', -- source_id
        'GC', -- brand
        'VE', -- DC
        'po', -- resource_type
        '2520VE031071_E5', -- resource_id
        'Urgent delivery needed', -- comment
        '2025-W11', -- year_week
        '2025-01-08 08:08:08', -- created_at
        '2025-01-08 08:08:08', -- updated_at
        '<EMAIL>', -- created_by
        '<EMAIL>', -- updated_by
        'gpp-ingredient-replenishment' -- domain
    );
