DELETE FROM dc_address WHERE dc_code in ('AA', 'BB', 'CC', 'DD', 'EE');
DELETE FROM distribution_center WHERE code in ('AA', 'BB', 'CC', 'DD', 'EE');

INSERT INTO distribution_center(code, status, name, market)
VALUES ('EE', 'ACTIVE', 'Test DC 5', 'yy'),
       ('DD', 'INACTIVE', 'Test DC 4', 'xx'),
       ('CC', 'ACTIVE', 'Test DC 3', 'xx'),
       ('BB', 'ACTIVE', 'Test DC 2', 'xx'),
       ('AA', 'ACTIVE', 'Test DC 1', 'xx');

INSERT INTO dc_address (dc_code, number, address, zip, city, state, company, type, country_code)
VALUES ('AA', '111', 'Address 1', '12345', 'A city', 'NJ', null, 'DELIVERY', 'XX');

INSERT INTO dc_address (dc_code, number, address, zip, city, state, company, type, country_code)
VALUES ('BB', '222', 'Address 2', '12345', 'A city', 'NJ', null, 'DELIVERY', 'XX');

INSERT INTO dc_address (dc_code, number, address, zip, city, state, company, type, country_code)
VALUES ('CC', '333', 'Address 3', '12345', 'A city', 'NJ', null, 'DELIVERY', 'XX');

INSERT INTO dc_address (dc_code, number, address, zip, city, state, company, type, country_code)
VALUES ('DD', '444', 'Address 4', '12345', 'A city', 'NJ', null, 'DELIVERY', 'XX');

INSERT INTO dc_address (dc_code, number, address, zip, city, state, company, type, country_code)
VALUES ('EE', '555', 'Address 5', '12345', 'A city', 'NJ', null, 'DELIVERY', 'XX');
