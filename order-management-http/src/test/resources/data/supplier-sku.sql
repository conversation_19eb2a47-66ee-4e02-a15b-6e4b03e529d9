INSERT INTO public.supplier (id, parent_id, market, code, name, status, currency, type, created_at, updated_at, city, country, state, address, number, post_code, dc_codes)
VALUES
    ('00000000-0001-0000-0000-000000000000', '00000000-0000-0001-0000-000000000000', 'us', '60091', 'Barker''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', '<PERSON><PERSON>', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0002-0000-0000-000000000000', '00000000-0000-0002-0000-000000000000', 'us', '60092', 'Barker''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', '<PERSON><PERSON>', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0003-0000-0000-000000000000', '00000000-0000-0003-0000-000000000000', 'us', '60093', '<PERSON>''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', '<PERSON>ine', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0004-0000-0000-000000000000', '00000000-0000-0004-0000-000000000000', 'us', '60094', 'Barker''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', 'Geraldine', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0005-0000-0000-000000000000', '00000000-0000-0005-0000-000000000000', 'us', '60095', 'Barker''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', 'Geraldine', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0006-0000-0000-000000000000', '00000000-0000-0006-0000-000000000000', 'us', '60096', 'Barker''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', 'Geraldine', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0007-0000-0000-000000000000', '00000000-0000-0007-0000-000000000000', 'us', '60097', 'Barker''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', 'Geraldine', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0008-0000-0000-000000000000', '00000000-0000-0008-0000-000000000000', 'us', '60098', 'Barker''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', 'Geraldine', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0009-0000-0000-000000000000', '00000000-0000-0010-0000-000000000000', 'us', '60099', 'Barker''s', 'ACTIVE', 'NZD', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', 'Geraldine', 'NZ', 'NA', 'Shaw Road', '72', '7991', '{NZ}'),
    ('00000000-0010-0000-0000-000000000000', '00000000-0000-0011-0000-000000000000', 'jp', '60100', 'Japanese Baker', 'ACTIVE', 'YEN', 'Manufacturer', '2020-04-01 05:13:52.054459', '2021-11-15 09:30:15.166690', 'Tokyo', 'JP', 'NA', 'Tokyo st.', '72', '7991', '{JP}');

INSERT INTO public.supplier_sku (uuid, parent_supplier_id, sku_id, market, status, supplier_code)
VALUES ('00000000-0000-0000-0000-000000000000', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0001-000000000000', 'us', 'ACTIVE','60091'),
       ('00000000-0000-0000-0000-000000000001', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0002-000000000000', 'us', 'ACTIVE','60091'),
       ('00000000-0000-0000-0000-000000000002', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0003-000000000000', 'us', 'ACTIVE','60091'),
       ('00000000-0000-0000-0000-000000000003', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0004-000000000000', 'us', 'ACTIVE','60091'),
       ('00000000-0000-0000-0000-000000000004', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0005-000000000000', 'us', 'ACTIVE','60091'),
       ('00000000-0000-0000-0000-000000000005', '00000000-0000-0001-0000-000000000000', '00000000-0000-0000-0006-000000000000', 'us', 'ACTIVE','60091'),
       ('00000000-0000-0000-0000-000000000006', '00000000-0000-0002-0000-000000000000', '00000000-0000-0000-0007-000000000000', 'us', 'ACTIVE','60092'),
       ('00000000-0000-0000-0000-000000000007', '00000000-0000-0002-0000-000000000000', '00000000-0000-0000-0008-000000000000', 'us', 'ACTIVE','60092'),
       ('00000000-0000-0000-0000-000000000008', '00000000-0000-0002-0000-000000000000', '00000000-0000-0000-0009-000000000000', 'us', 'ACTIVE','60092'),
       ('00000000-0000-0000-0000-000000000009', '00000000-0000-0002-0000-000000000000', '00000000-0000-0000-0010-000000000000', 'us', 'ACTIVE', '60092'),
       ('00000000-0000-0000-0000-000000000010', '00000000-0000-0002-0000-000000000000', '00000000-0000-0000-0011-000000000000', 'us', 'ACTIVE', '60092'),
       ('00000000-0000-0000-0000-000000000011', '00000000-0000-0002-0000-000000000000', '00000000-0000-0000-0012-000000000000', 'us', 'ACTIVE', '60092'),
       ('00000000-0000-0000-0000-000000000012', '00000000-0000-0003-0000-000000000000', '00000000-0000-0000-0013-000000000000', 'us', 'ACTIVE', '60092'),
       ('00000000-0000-0000-0000-000000000015', '00000000-0000-0004-0000-000000000000', '00000000-0000-0000-0014-000000000000', 'us', 'ACTIVE', '60094'),
       ('00000000-0000-0000-0000-000000000016', '00000000-0000-0005-0000-000000000000', '00000000-0000-0000-0014-000000000000', 'us', 'ACTIVE', '60095'),
       ('00000000-0000-0000-0000-000000000017', '00000000-0000-0006-0000-000000000000', '00000000-0000-0000-0014-000000000000', 'us', 'ACTIVE', '60096'),
       ('00000000-0000-0000-0000-000000000018', '00000000-0000-0007-0000-000000000000', '00000000-0000-0000-0015-000000000000', 'us', 'ACTIVE', '60097'),
       ('00000000-0000-0000-0000-000000000019', '00000000-0000-0007-0000-000000000000', '00000000-0000-0000-0016-000000000000', 'us', 'ACTIVE', '60097'),
       ('00000000-0000-0000-0000-000000000020', '00000000-0000-0008-0000-000000000000', '00000000-0000-0000-0017-000000000000', 'us', 'ACTIVE', '60098'),
       ('00000000-0000-0000-0000-000000000021', '00000000-0000-0008-0000-000000000000', '00000000-0000-0000-0017-000000000000', 'us', 'ACTIVE', '60099'),
       ('00000000-0000-0000-0000-000000000022', '00000000-0000-0009-0000-000000000000', '00000000-0000-0000-0017-000000000000', 'us', 'ACTIVE', '60099'),
       ('00000000-0000-0000-0000-000000000023', '00000000-0000-0010-0000-000000000000', '00000000-0000-0000-0017-000000000000', 'us', 'ACTIVE', '60099'),
       ('00000000-0000-0000-0000-000000000024', '00000000-0000-0010-0000-000000000000', '00000000-0000-0000-0017-000000000000', 'us', 'ARCHIVED', '60099'),
       ('00000000-0000-0000-0000-000000000025', '00000000-0000-0011-0000-000000000000', '00000000-0000-0000-0018-000000000000', 'jp', 'ARCHIVED', '60100');

INSERT INTO public.sku (uuid, market, name, code, status, brands, category, uom )
VALUES ('00000000-0000-0000-0001-000000000000', 'us', 'Chicken, Sausage - 12 Ounce (oz)', 'PTN-10-10101-7', 'INACTIVE', '{HelloFresh}', 'PTN','OZ'),
       ('00000000-0000-0000-0002-000000000000', 'us', 'SE Freebies Mixed Jan', 'OTH-33-121101-1', 'ONBOARDING', '{HelloFresh}', 'OTH','OZ'),
       ('00000000-0000-0000-0003-000000000000', 'jp', 'aluminium coolpouch / アルミ保冷袋  - Msize', 'PCK-81-103553-3', 'ACTIVE', '{HelloFresh}', 'PCK','OZ'),
       ('00000000-0000-0000-0004-000000000000', 'gb', 'Teriyaki Sauce - 75g', 'PRO-10-80049-1', 'ACTIVE', '{HelloFresh,Green Chef}', 'PRO','OZ'),
       ('00000000-0000-0000-0005-000000000000', 'us', 'Coconut Milk - South, NO Additives', 'PRO-00-105599-1', 'ACTIVE', '{HelloFresh}', 'PRO','OZ'),
       ('00000000-0000-0000-0006-000000000000', 'ca', 'X3B- Tortillas, White Flour 6-inch (6pc)', 'BAK-10-003294-7', 'ACTIVE', '{HelloFresh,Chef’s Plate}', 'BAK','OZ'),
       ('00000000-0000-0000-0007-000000000000', 'beneluxfr', 'Persil plat et coriandre (10g) - FR', 'ABC-13-003354-4', 'ARCHIVED', '{HelloFresh}', 'PHF','OZ'),
       ('00000000-0000-0000-0008-000000000000', 'dach', 'Krustenschinken 180g - 72414 - QS', 'PTN-11-90550-2', 'INACTIVE', '{HelloFresh}', 'PTN','OZ'),
       ('00000000-0000-0000-0009-000000000000', 'beneluxfr', 'Pomme de terre Grenailles (250g) - FR', 'PHF-13-101290-4', 'ONBOARDING', '{HelloFresh}', 'PHF','OZ'),
       ('00000000-0000-0000-0014-000000000000', 'sk', 'SKU - with no price', 'PHF-13-00000-4', 'ONBOARDING', '{HelloFresh}', 'PHF','OZ'),
       ('00000000-0000-0000-0015-000000000000', 'us', 'sort-order-with-price', 'PHF-13-00000-5', 'ONBOARDING', '{HelloFresh}', 'PHF','OZ'),
       ('00000000-0000-0000-0016-000000000000', 'us', 'sort-order-no-price', 'PHF-13-00000-6', 'ONBOARDING', '{HelloFresh}', 'PHF','OZ'),
       (uuid_generate_v4(), 'ca', 'DO NOT USE - X3B- Bun, Brioche (1pc)', 'BAK-10-004062-1', 'ARCHIVED', '{Chef’s Plate,HelloFresh}', 'BAK','OZ'),
       ('00000000-0000-0000-0017-000000000000', 'ca', 'DO NOT USE - X3B- Bun, Brioche (1pc)', 'BAK-10-004062-1', 'ACTIVE', '{Chef’s Plate,HelloFresh}', 'BAK', null),
       ('00000000-0000-0000-0018-000000000000', 'jp', 'aluminium', 'PCK-81-103553-4', 'ARCHIVED', '{HelloFresh}', 'PCK','OZ');

INSERT INTO public.supplier_sku_price (uuid, supplier_sku_id, dc_codes, start_date, end_date, enabled, price_type, market, currency, price_permyriad, buffer_permyriad, case_size, case_price_permyriad, created_at, updated_at)
VALUES
    ('00000001-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 123456, 0, 1280, 158023680, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000002-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000001', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 2000, 0, 1280, 2560000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000003-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000002', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 3000, 0, 1280, 3840000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000004-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000003', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 4000, 0, 1280, 5120000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000005-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000004', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 5000, 0, 1280, 6400000 , '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000015-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000004', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 5500, 0, 1280, 7040000, '2020-02-26 09:19:33.113000', '2023-01-14 00:00:00.000000'),
    ('00000006-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000005', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 6000, 0, 1280, 7680000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000007-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000006', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 7000, 0, 1280, 7040000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000008-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000007', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 8000, 0, 1280, 10240000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000009-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000008', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 9000, 0, 1280, 11520000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000010-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000009', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 1100, 0, 1280, 1408000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000011-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000010', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 1200, 0, 1280, 1536000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000012-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000011', '{SK}', current_date, current_date, true, 'case_type', 'dkse', 'GBP', 1300, 0, 1280, 1664000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000013-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000016', '{SK}', '1991-07-13 09:19:33.113000', '1992-10-02 09:19:33.113000', true, 'case_type', 'dkse', 'GBP', 1300, 0, 1280, 1664000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000014-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000017', '{NJ}', current_date, current_date, true,'case_type', 'dkse', 'GBP', 1900, 0, 1280, 2432000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000016-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000018', '{SK}', current_date, current_date, true,'case_type', 'dkse', 'GBP', 1900, 0, 1280, 2432000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000017-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000020', '{NJ}', current_date, current_date, true,'case_type', 'dkse', 'GBP', 1900, 0, 1280, 2432000, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000018-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000017', '{NJ}', current_date, current_date, true,'case_type', 'dkse', 'GBP', 1900, 0, 1280, 2432000, '2020-02-26 09:19:33.113000', '1991-07-07 00:00:00.000000'),
    ('00000019-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000017', '{XX}', current_date, current_date, true,'case_type', 'dkse', 'GBP', 1900, 0, 1280, 2432000, '2020-02-26 09:19:33.113000', '1991-07-09 00:00:00.000000'),
    ('00000020-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000017', '{SK}', current_date, current_date, false, 'case_type', 'dkse', 'GBP', 123456, 0, 1280, 158023680, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000'),
    ('00000021-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000017', '{SK}', current_date, current_date, false, 'case_type', 'dkse', 'GBP', 123456, 0, 1280, 158023680, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000');

INSERT INTO public.supplier_sku_packaging (supplier_sku_id, cases_per_pallet, created_at, updated_at)
VALUES
    ('00000000-0000-0000-0000-000000000000', 5, '2020-02-26 09:19:33.113000', '2023-01-13 00:00:00.000000');
