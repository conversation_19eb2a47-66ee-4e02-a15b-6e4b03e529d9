-- Insert view states for the current user (<EMAIL>)
INSERT INTO view_state (id, user_id, user_email, resource, name, state, created_at, updated_at)
VALUES
    (1, '3d40b713-b378-40cf-8ed8-dffbd6cfa8df', '<EMAIL>', 'orders', 'My Orders View', '{"filterState": {"status": "pending"}}', NOW(), NOW()),
    (2, '3d40b713-b378-40cf-8ed8-dffbd6cfa8df', '<EMAIL>', 'orders', 'Completed Orders', '{"filterState": {"status": "completed"}}', NOW(), NOW()),
    (3, '3d40b713-b378-40cf-8ed8-dffbd6cfa8df', '<EMAIL>', 'other-view', 'Other View', '{"filterState": {"status": "active"}}', NOW(), NOW());

-- Insert view states for other users
INSERT INTO view_state (id, user_id, user_email, resource, name, state, created_at, updated_at)
VALUES
    (4, '22222222-2222-2222-2222-222222222222', '<EMAIL>', 'orders', 'Other User Orders', '{"filterState": {"status": "pending"}}', NOW(), NOW()),
    (5, '33333333-3333-3333-3333-333333333333', '<EMAIL>', 'orders', 'Another User View', '{"filterState": {"status": "processing"}}', NOW(), NOW()),
    (6, '22222222-2222-2222-2222-222222222222', '<EMAIL>', 'customers', 'Other User Customers', '{"filterState": {"status": "inactive"}}', NOW(), NOW());
