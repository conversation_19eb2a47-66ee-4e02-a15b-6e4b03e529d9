management:
  tracing:
    sampling:
      probability: 0.0
spring:
  flyway:
    locations: "classpath:db/common"

resilience4j:
  retry:
    instances:
      sendPurchaseOrder:
        maxAttempts: 3
        waitDuration: 100ms
      createPurchaseOrder:
        maxAttempts: 3
        waitDuration: 100ms

tapioca:
  base-url: "http://localhost:${wiremock.server.port}"

auth-service:
  base-url: "http://localhost:${wiremock.server.port}"
  client-id: "client_id"
  client-secret: "client_secret"

scheduler:
  # All tasks are disabled during testing
  #   see: org.springframework.scheduling.annotation.Scheduled.CRON_DISABLED
  # On tests where it has to be enabled, it should be done programmatically
  #   see: @SpringBootTest(properties = ["scheduler.task.cron=* * * * * *"])
  process-send-orders:
    cron: "-"
  process-sync-batch-orders:
    cron: "-"
  consolidate-sync-batch-orders:
    cron: "-"
  process-create-orders:
    cron: "-"
  process-create-and-send-orders:
    cron: "-"
  cleanup:
    cron: "-"

wiremock:
  server:
    port: 0
