INSERT INTO purchase_order (po_number, id, version, type, year_week, user_id, user_email, status, supplier_id, supplier_code, dc_code,
                                   shipping_method, location_name, street_address, city, region, postal_code, country_code, expected_start_time,
                                   expected_end_time, emergency_reason_uuid, created_at, updated_at, total_price_currency, total_price_amount)
VALUES ('PO_500', 'd6d1a101-6753-4d41-830b-b1730712f63d', 1, 'EMERGENCY', '2023-W26', 'e90fbded-9a1a-4dfe-9d66-1f25cc7d72c1',
        '<EMAIL>', 'INITIATED', '3c5bcb8b-5563-4b0f-ae2c-185d2dd6a91e', '110943', 'MO', 'VENDOR',
        'HelloFresh Norway AS', '20 Vanemveien', 'Moss', '', '1599', 'SE', '2023-06-27 10:00:00.000000', '2023-06-27 12:00:00.000000',
        'dbda1a8f-6d8f-4ee7-9f9f-993c45423334', now(), now(), 'EUR', '46494'),
       ('PO_200', '16aa8571-5a0b-4e93-815d-fcdf66536899', 1, 'EMERGENCY', '2023-W19', 'f0e869b2-3b01-4ccf-a4c6-96112170074b',
        '<EMAIL>', 'INITIATED', '6cc9fb1d-e640-41e7-a36e-9cb951a9ae81', '10432', 'ET', 'FREIGHT_ON_BOARD', 'EveryPlate - Texas',
        '1025 Post & Paddock St.', 'Grand Prairie', 'TX', '75050', 'US', '2023-05-01 10:00:00.000000', '2023-05-01 12:00:00.000000',
        '7a65379e-87b6-466d-ad26-1b9ccceafbe1', now(), now(), 'USD', '26132.4'),
       ('PO_409', '6d83c67f-790d-4325-bca0-4ca1ac40ccb6', 1, 'EMERGENCY', '2023-W19', 'f0e869b2-3b01-4ccf-a4c6-96112170074b',
        '<EMAIL>', 'INITIATED', 'a358d8d4-8960-4530-af01-ffdf7c8aae6f', '29170', 'TI', 'FREIGHT_ON_BOARD', 'United States - TX3',
        '2700 Market Street', 'Irving', 'TX', '75062', 'US', '2023-05-01 10:00:00.000000', '2023-05-01 12:00:00.000000',
        '7a65379e-87b6-466d-ad26-1b9ccceafbe1', now(), now(), 'USD', '39110.4');
