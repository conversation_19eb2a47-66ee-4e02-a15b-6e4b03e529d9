package com.hellofresh.purchaseordersynchronizer

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.POType.STANDARD
import com.hellofresh.oms.model.Packaging
import com.hellofresh.oms.model.PackagingType.CASE_TYPE
import com.hellofresh.oms.model.PackagingType.UNIT_TYPE
import com.hellofresh.oms.model.Permyriad
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import com.hellofresh.oms.model.PurchaseOrderStatus.INITIATED
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.ShipMethodEnum.VENDOR
import com.hellofresh.oms.model.ShippingAddress
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.model.WorkerActionData
import com.hellofresh.oms.model.WorkerActionStatus
import com.hellofresh.oms.model.YearWeek
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.random.Random

fun getDeliveryDateChangeReasonId(): UUID = UUID.fromString("37c2e641-ac77-4fec-8b17-83091d4a9b60")

@Suppress("LongParameterList")
fun getPurchaseOrderEntity(
    poNumber: String = "2153VE123456",
    id: UUID = UUID.randomUUID(),
    yearWeek: YearWeek = YearWeek("2022-W10"),
    status: PurchaseOrderStatus = INITIATED,
    dcCode: String = "VE",
    supplierCode: String = "10123",
    type: POType = STANDARD,
    bufferPermyriad: Permyriad = Permyriad(20),
    createdAt: LocalDateTime = LocalDateTime.now(),
    shipMethod: ShipMethodEnum = VENDOR,
    version: Int = 1,
    shippingAddress: ShippingAddress = ShippingAddress(
        "",
        "",
        "Verden",
        "",
        "27283",
        "DE",
    ),
    comment: String? = "a comment",
    deliveryDateChangeReasonId: UUID? = UUID.randomUUID(),
    emergencyReasonId: UUID = UUID.randomUUID()
) = PurchaseOrder(
    poNumber = poNumber,
    version = version,
    type = type,
    id = id,
    yearWeek = yearWeek,
    userId = UUID.randomUUID(),
    userEmail = "<EMAIL>",
    status = status,
    sendTime = LocalDateTime.of(2021, 1, 4, 10, 0),
    supplierId = UUID.fromString("45d82eca-280c-4e59-956d-66e354bb6781"),
    supplierCode = supplierCode,
    dcCode = dcCode,
    shippingMethod = shipMethod,
    shippingAddress = shippingAddress,
    expectedStartTime = LocalDateTime.of(2021, 1, 4, 10, 0),
    expectedEndTime = LocalDateTime.of(2021, 1, 4, 10, 0),
    orderItems = setOf(getOrderItem(id, bufferPermyriad)),
    totalPrice = Money(BigDecimal(250), "EUR"),
    createdAt = createdAt,
    updatedAt = LocalDateTime.now(),
    emergencyReasonUuid = emergencyReasonId,
    comment = comment,
    isSynced = false,
    deliveryDateChangeReasonId = deliveryDateChangeReasonId,
)

fun getPurchaseOrderVersionAndType(
    version: Int = 1,
    type: POType = STANDARD,
    id: UUID = UUID.randomUUID(),
    status: PurchaseOrderStatus = INITIATED,
    poNumber: String = "PO-1"
) = PurchaseOrderVersionAndType(
    version = version,
    type = type,
    id = id,
    status = status,
    poNumber = poNumber,
)

fun getOrderItem(
    id: UUID = UUID.randomUUID(),
    buffer: Permyriad = Permyriad(0),
    packaging: Packaging = Packaging(UNIT_TYPE, null, UNIT),
) = OrderItem(
    poId = id,
    skuId = UUID.randomUUID(),
    totalQty = BigDecimal(5),
    price = Money(BigDecimal(50), "EUR"),
    totalPrice = Money(BigDecimal(250), "EUR"),
    buffer = buffer,
    correctionReason = "NA",
    packaging = packaging,
    createdAt = LocalDateTime.now(),
    updatedAt = LocalDateTime.now(),
    changeReasonId = null,
    rawQty = BigDecimal(5),
    casesPerPallet = 5,
)

fun getSendWorkerActionData(
    purchaseOrderNumber: String = "PO_500",
    bulkId: UUID = UUID.randomUUID(),
    outboxItemId: UUID = UUID.randomUUID(),
) = WorkerActionData.SendPurchaseOrder(
    purchaseOrderNumber = purchaseOrderNumber,
    bulkId = bulkId,
    outboxItemId = outboxItemId,
)

@Suppress("LongParameterList")
fun getSendWorkerAction(
    userEmail: String = "<EMAIL>",
    userId: UUID = UUID.randomUUID(),
    lastStatusChangeAt: LocalDateTime = LocalDateTime.now(),
    createdAt: LocalDateTime = LocalDateTime.now(),
    status: WorkerActionStatus = WorkerActionStatus.PENDING,
    lastError: String? = null,
    poNumber: String = "PO_" + Random.nextInt(),
    bulkId: UUID = UUID.randomUUID(),
    outboxItemId: UUID = UUID.randomUUID(),
    workerActionData: WorkerActionData.SendPurchaseOrder = getSendWorkerActionData(
        purchaseOrderNumber = poNumber,
        bulkId = bulkId,
        outboxItemId = outboxItemId,
    )
) = workerActionData.toWorkerAction(
    userEmail = userEmail,
    userId = userId,
).copy(
    lastStatusChangeAt = lastStatusChangeAt,
    createdAt = createdAt,
    status = status,
    lastError = lastError,
)

fun getPoWithOneItemForUom(uom: UOM) = getPurchaseOrderEntity()
    .copy(orderItems = setOf(getOrderItem(packaging = Packaging(CASE_TYPE, BigDecimal(1), uom))))

fun getTestUserId(): UUID = getLoggedInUser().userId

private fun getLoggedInUser() =
    object {
        val userId = UUID.fromString("3c0a5325-0492-4dc1-bf29-cc7d44630a5b")
        val userEmail = "<EMAIL>"
        val userName = "name"
    }
