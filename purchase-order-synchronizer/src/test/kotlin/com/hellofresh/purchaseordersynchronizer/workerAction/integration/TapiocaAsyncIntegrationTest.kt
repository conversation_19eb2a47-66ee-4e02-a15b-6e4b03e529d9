package com.hellofresh.purchaseordersynchronizer.workerAction.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.put
import com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionStatus
import com.hellofresh.oms.model.WorkerActionStatus.PENDING
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.task.WorkerActionTask
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import java.time.Clock
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.springframework.test.context.jdbc.Sql

@SpringBootTest
@Tag("integration")
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
@Sql(scripts = ["/data/empty_db.sql", "/data/queue/purchase_orders.sql"])
@ActiveProfiles(profiles = ["default", "test", "integration"])
@AutoConfigureWireMock
class TapiocaAsyncIntegrationTest {

    @Autowired
    lateinit var wireMock: WireMockServer

    @Autowired
    lateinit var workerActionRepository: WorkerActionRepository

    @MockitoSpyBean
    lateinit var workerActionTask: WorkerActionTask

    @BeforeEach
    fun setUp() {
        stubSuccessAuthRequest()
    }

    @AfterEach
    fun tearDown() {
        wireMock.resetAll()
        workerActionRepository.deleteAll()
    }

    private fun stubSuccessAuthRequest() {
        wireMock.stubFor(
            post("/token")
                .willReturn(
                    aResponse()
                        .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .withBody(
                            """
                                {
                                    "token_type": "Bearer",
                                    "access_token": "eyJleHAiOjE2Mzg5MDg4NTMsImlhdCI6MTYzODg4NzI1MywiaXNzIjoiMWI1ZWVkMWYtMDNkZC00ZjlkLThkMzAtMmEwOGY5Mzk3NjU1IiwianRpIjoiMWZiYjBmMmEtMmI4ZS00NjJlLTljYTctMWZhNDUzZWYxOTM1In0",
                                    "expires_in": 21600
                                }
                            """.trimIndent(),
                        ),
                ),
        )
    }

    fun stubSuccessCreateOrderResponse(poId: UUID = UUID.randomUUID()) {
        wireMock.stubFor(
            post(urlPathMatching(CREATE_ORDER_PATH))
                .willReturn(
                    createOrderSuccessResponseBody(poId),
                ),
        )
    }

    fun createOrderSuccessResponseBody(poId: UUID = UUID.randomUUID()): ResponseDefinitionBuilder = aResponse()
        .withStatus(201)
        .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        .withBody(
            """
            {
                "data": {
                    "status": "success",
                    "uuid": "$poId"
                },
                "status": 201
            }
            """.trimIndent(),
        )

    fun stubErrorCreateOrderResponse(statusCode: Int = 500) {
        wireMock.stubFor(
            post(urlPathMatching(CREATE_ORDER_PATH))
                .willReturn(
                    aResponse().withStatus(statusCode),
                ),
        )
    }

    fun stubSuccessSendOrderResponse() {
        wireMock.stubFor(
            put(urlPathMatching(SEND_ORDER_PATH))
                .willReturn(
                    aResponse().withStatus(200),
                ),
        )
    }

    fun stubErrorSendOrderResponse(statusCode: Int = 500) {
        wireMock.stubFor(
            put(urlPathMatching(SEND_ORDER_PATH))
                .willReturn(
                    aResponse().withStatus(statusCode),
                ),
        )
    }

    fun persistWorkerAction(
        createdAt: String,
        payload: String,
        type: WorkerActionType,
        status: WorkerActionStatus = PENDING,
    ) = workerActionRepository.save(
        WorkerAction(
            id = UUID.randomUUID(),
            status = status,
            userEmail = "<EMAIL>",
            userId = UUID.randomUUID(),
            lastStatusChangeAt = LocalDateTime.now(clockFromTimeString(createdAt)),
            createdAt = LocalDateTime.now(clockFromTimeString(createdAt)),
            lastError = null,
            actionType = type,
            payload = jsonNode(payload),
        ),
    )

    private fun jsonNode(jsonString: String) = ObjectMapper().readTree(jsonString)

    private fun clockFromTimeString(dateTime: String): Clock = Clock.fixed(Instant.parse(dateTime), ZoneId.of("UTC"))

    companion object {
        const val CREATE_ORDER_PATH = "/orders"
        const val SEND_ORDER_PATH = "/purchase-orders/(.*)/send"
    }
}
