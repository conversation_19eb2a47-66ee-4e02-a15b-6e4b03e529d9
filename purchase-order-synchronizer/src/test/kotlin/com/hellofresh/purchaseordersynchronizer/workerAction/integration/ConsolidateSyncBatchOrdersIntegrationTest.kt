package com.hellofresh.purchaseordersynchronizer.workerAction.integration

import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData.SyncBatchOrder
import com.hellofresh.oms.model.WorkerActionStatus
import com.hellofresh.oms.model.importHistory.ImportHistory
import com.hellofresh.oms.model.importHistory.ImportHistoryStatus
import com.hellofresh.oms.model.importHistory.ImportHistorySummary
import com.hellofresh.purchaseordersynchronizer.PurchaseOrderRepository
import com.hellofresh.purchaseordersynchronizer.getPurchaseOrderEntity
import com.hellofresh.purchaseordersynchronizer.imports.out.ImportHistoryRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.task.WorkerActionTask
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import java.time.Duration
import java.time.LocalDateTime
import java.util.UUID
import org.awaitility.Awaitility.await
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.mockito.kotlin.atLeast
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.springframework.test.context.jdbc.Sql

@SpringBootTest(properties = ["scheduler.consolidate-sync-batch-orders.cron=* * * * * *"])
@Tag("integration")
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
@Sql(scripts = ["/data/empty_db.sql", "/data/queue/purchase_orders.sql"])
@ActiveProfiles(profiles = ["default", "test", "integration"])
@AutoConfigureWireMock
class ConsolidateSyncBatchOrdersIntegrationTest {

    @Autowired
    private lateinit var purchaseOrderRepository: PurchaseOrderRepository

    @Autowired
    private lateinit var importHistoryRepository: ImportHistoryRepository

    @Autowired
    private lateinit var workerActionRepository: WorkerActionRepository

    @MockitoSpyBean
    private lateinit var workerActionTask: WorkerActionTask

    @AfterEach
    fun tearDown() {
        purchaseOrderRepository.deleteAll()
        importHistoryRepository.deleteAll()
        workerActionRepository.deleteAll()
    }

    @ParameterizedTest
    @EnumSource(TestCase::class)
    fun `given specific workerAction statuses the importHistory status should`(testCase: TestCase) {
        // given
        val givenPoNumber = "PO-${IntRange(100, 500).random()}"
        val givenUserId = UUID.randomUUID()
        val givenUserEmail = "<EMAIL>"
        val givenImportHistory = ImportHistory.createImportHistory(
            filename = "purchase_orders.csv",
            userId = givenUserId,
            userEmail = givenUserEmail,
            createdAt = LocalDateTime.now(),
            summary = ImportHistorySummary.BatchPoCreation(
                purchaseOrders = listOf(givenPoNumber),
                markets = listOf("beneluxfr")
            ),
        )
        val givenImportHistoryId = givenImportHistory.id
        val givenWorkerAction = SyncBatchOrder(
            importHistoryId = givenImportHistoryId,
            purchaseOrderNumber = givenPoNumber,
        )
        purchaseOrderRepository.save(
            getPurchaseOrderEntity(
                poNumber = givenPoNumber,
                deliveryDateChangeReasonId = null,
            ),
        )
        importHistoryRepository.save(givenImportHistory)
        testCase.givenWorkerActionStatuses.forEach {
            workerActionRepository.save(
                WorkerAction.createWorkerAction(givenWorkerAction, givenUserEmail, givenUserId)
                    .copy(status = it),
            )
        }

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).consolidateBatchSync()
            }

        // then
        await().atMost(Duration.ofSeconds(SyncBatchOrderIntegrationTest.AWAIT_TIMEOUT))
            .untilAsserted {
                assertThat(
                    importHistoryRepository.findAll().map { it.id to it.status },
                    equalTo(listOf(givenImportHistoryId to testCase.expectedImportHistoryStatus)),
                )
            }
    }

    enum class TestCase(
        val givenWorkerActionStatuses: List<WorkerActionStatus>,
        val expectedImportHistoryStatus: ImportHistoryStatus
    ) {
        // Nothing should be done when no worker action is found for the given import history
        DO_NOTHING_WHEN_EMPTY(listOf(), ImportHistoryStatus.INITIATED),

        // Nothing should be done when all worker actions are still PENDING
        DO_NOTHING_WHEN_PENDING(
            listOf(WorkerActionStatus.PENDING, WorkerActionStatus.PENDING),
            ImportHistoryStatus.INITIATED,
        ),

        DO_NOTHING_WHEN_PENDING_AND_FAILED(
            listOf(WorkerActionStatus.PENDING, WorkerActionStatus.FAILED),
            ImportHistoryStatus.INITIATED,
        ),

        DO_NOTHING_WHEN_PENDING_AND_PROCESSED(
            listOf(WorkerActionStatus.PENDING, WorkerActionStatus.PROCESSED),
            ImportHistoryStatus.INITIATED,
        ),

        // Import history should be set to SUCCEEDED when all worker actions are PROCESSED
        SET_SUCCEEDED_WHEN_ALL_ARE_PROCESSED(
            listOf(WorkerActionStatus.PROCESSED, WorkerActionStatus.PROCESSED),
            ImportHistoryStatus.SUCCEEDED,
        ),

        // Import history should be set to FAILED when all worker actions are FAILED
        SET_FAILED_WHEN_ALL_ARE_FAILED(
            listOf(WorkerActionStatus.FAILED, WorkerActionStatus.FAILED),
            ImportHistoryStatus.FAILED,
        ),

        // Import history should be set to PARTIALLY_SUCCEEDED when at least one worker action is FAILED
        SET_PARTIALLY_SUCCEEDED_WHEN_PROCESSED_AND_FAILED(
            listOf(WorkerActionStatus.PROCESSED, WorkerActionStatus.FAILED),
            ImportHistoryStatus.PARTIALLY_SUCCEEDED,
        ),
    }

    companion object {
        const val AWAIT_TIMEOUT = 10L
    }
}
