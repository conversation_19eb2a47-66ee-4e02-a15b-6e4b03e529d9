package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData
import com.hellofresh.oms.model.WorkerActionStatus.FAILED
import com.hellofresh.oms.model.WorkerActionStatus.PROCESSED
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.purchaseordersynchronizer.PurchaseOrderRepository
import com.hellofresh.purchaseordersynchronizer.client.tapioca.TapiocaClient
import com.hellofresh.purchaseordersynchronizer.client.tapioca.domain.SendPurchaseOrderRequest
import com.hellofresh.purchaseordersynchronizer.client.tapioca.exception.TapiocaClientException
import com.hellofresh.purchaseordersynchronizer.getPurchaseOrderVersionAndType
import com.hellofresh.purchaseordersynchronizer.getSendWorkerAction
import com.hellofresh.purchaseordersynchronizer.getSendWorkerActionData
import com.hellofresh.purchaseordersynchronizer.outbox.OutboxRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import java.time.Clock
import kotlin.test.Test
import kotlin.test.assertEquals
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsString
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.not
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class SendOrderServiceTest {
    @Mock
    private lateinit var sendWorkerActionRepositoryMock: WorkerActionRepository

    @Mock
    private lateinit var purchaseOrderRepositoryMock: PurchaseOrderRepository

    @Mock
    private lateinit var tapiocaClientMock: TapiocaClient

    @Mock
    private lateinit var workerActionMetricService: WorkerActionMetricService

    @Mock
    private lateinit var outboxRepositoryMock: OutboxRepository

    private lateinit var subject: SendOrderService

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        subject = SendOrderService(
            workerActionMetricService = workerActionMetricService,
            workerActionRepository = sendWorkerActionRepositoryMock,
            purchaseOrderRepository = purchaseOrderRepositoryMock,
            tapiocaClient = tapiocaClientMock,
            outboxRepository = outboxRepositoryMock,
            clock = Clock.systemDefaultZone(),
        )
    }

    @Test
    fun `should save SendWorkerAction as PROCESSED and call Tapioca when an Action is successfully processed`() {
        // Given
        val sendWorkerActionData: WorkerActionData.SendPurchaseOrder = getSendWorkerActionData()
        val givenWorkerAction: WorkerAction = getSendWorkerAction(workerActionData = sendWorkerActionData)
        val givenLastStatusChangeAt = givenWorkerAction.lastStatusChangeAt
        whenever(sendWorkerActionRepositoryMock.getPendingByType(WorkerActionType.SEND_ORDER))
            .thenReturn(listOf(givenWorkerAction))
        val purchaseOrderVersionAndType = getPurchaseOrderVersionAndType()
        whenever(
            purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(sendWorkerActionData.purchaseOrderNumber),
        )
            .thenReturn(purchaseOrderVersionAndType)

        // When
        subject.processPendingTasks()

        // Then
        verify(tapiocaClientMock, times(1)).sendPurchaseOrder(
            SendPurchaseOrderRequest(
                purchaseOrderId = purchaseOrderVersionAndType.id,
                senderId = givenWorkerAction.userId,
            ),
        )

        val captor = argumentCaptor<WorkerAction>()
        verify(sendWorkerActionRepositoryMock, times(1)).save(captor.capture())
        assertEquals(PROCESSED, captor.firstValue.status)
        assertThat(captor.firstValue.lastStatusChangeAt, not(equalTo(givenLastStatusChangeAt)))
        verify(workerActionMetricService, times(1)).recordTimeInQueueForAction(any())
        verify(workerActionMetricService, times(1)).recordProcessedTimeForAction(any())
        verify(outboxRepositoryMock, times(1)).save(any())
    }

    @Test
    fun `should save SendWorkerAction and OutboxItem as FAILED when Tapioca throws an Exception`() {
        // Given
        val givenPoNumber = "PO_500"
        val sendWorkerActionData: WorkerActionData.SendPurchaseOrder =
            getSendWorkerActionData(purchaseOrderNumber = givenPoNumber)
        val givenWorkerAction: WorkerAction = getSendWorkerAction(workerActionData = sendWorkerActionData)
        val givenLastStatusChangeAt = givenWorkerAction.lastStatusChangeAt
        whenever(sendWorkerActionRepositoryMock.getPendingByType(WorkerActionType.SEND_ORDER))
            .thenReturn(listOf(givenWorkerAction))
        val purchaseOrderVersionAndType = getPurchaseOrderVersionAndType(poNumber = givenPoNumber)
        whenever(
            purchaseOrderRepositoryMock.findFirstByPoNumberOrderByVersionDesc(sendWorkerActionData.purchaseOrderNumber),
        )
            .thenReturn(purchaseOrderVersionAndType)
        whenever(tapiocaClientMock.sendPurchaseOrder(any())).thenThrow(TapiocaClientException("Unexpected error"))
        whenever(sendWorkerActionRepositoryMock.save(any())).thenAnswer { it.arguments[0] }

        // When
        subject.processPendingTasks()

        // Then
        val captor = argumentCaptor<WorkerAction>()
        verify(sendWorkerActionRepositoryMock, times(1)).save(captor.capture())
        val action = captor.firstValue

        assertThat(action.status, equalTo(FAILED))
        assertThat(action.lastError, containsString("TapiocaClientException: Unexpected error"))
        assertThat(captor.firstValue.lastStatusChangeAt, not(equalTo(givenLastStatusChangeAt)))
        verify(workerActionMetricService, times(1)).recordTimeInQueueForAction(any())
        verify(workerActionMetricService, times(0)).recordProcessedTimeForAction(any())

        val outboxCaptor = argumentCaptor<OutboxItem>()
        verify(outboxRepositoryMock, times(1)).save(outboxCaptor.capture())
        val outboxItem = outboxCaptor.firstValue
        assertThat(outboxItem.status, equalTo(OutboxItemStatus.FAILED))
        assertThat(outboxItem.poNumber, equalTo(sendWorkerActionData.purchaseOrderNumber))
    }
}
