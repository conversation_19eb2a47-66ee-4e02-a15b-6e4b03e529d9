package com.hellofresh.purchaseordersynchronizer.workerAction.integration

import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData.CreatePurchaseOrder
import com.hellofresh.purchaseordersynchronizer.workerAction.integration.ConsolidateSyncBatchOrdersIntegrationTest.Companion.AWAIT_TIMEOUT
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.task.WorkerActionTask
import io.zonky.test.db.AutoConfigureEmbeddedDatabase
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseProvider.ZONKY
import io.zonky.test.db.AutoConfigureEmbeddedDatabase.DatabaseType.POSTGRES
import java.time.Duration
import java.time.LocalDateTime
import java.util.UUID
import org.awaitility.Awaitility.await
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.mockito.kotlin.atLeast
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@SpringBootTest(
    properties = [
        "scheduler.cleanup.cron=* * * * * *",
        "scheduler.cleanup.max-retention-days=#{ T(com.hellofresh.purchaseordersynchronizer.workerAction.integration.CleanupIntegrationTest).MAX_RETENTION_DAYS}",
    ],
)
@Tag("integration")
@AutoConfigureEmbeddedDatabase(type = POSTGRES, provider = ZONKY)
@ActiveProfiles(profiles = ["default", "test", "integration"])
@AutoConfigureWireMock
@Suppress("MaxLineLength")
class CleanupIntegrationTest {
    @Autowired
    private lateinit var workerActionRepository: WorkerActionRepository

    @MockitoSpyBean
    private lateinit var workerActionTask: WorkerActionTask

    @AfterEach
    fun tearDown() {
        workerActionRepository.deleteAll()
    }

    @Test
    fun `should delete worker action tasks above the max retention days and keep the ones under`() {
        // given
        val now = LocalDateTime.now()
        val underMaxRetention = setOf(
            createStubWorkerAction(now.minusDays(MAX_RETENTION_DAYS).plusDays(2)),
            createStubWorkerAction(now.minusDays(MAX_RETENTION_DAYS).plusHours(5)),
            createStubWorkerAction(now.minusDays(MAX_RETENTION_DAYS).plusMinutes(10)),
        )
        val aboveMaxRetention = setOf(
            createStubWorkerAction(now.minusDays(MAX_RETENTION_DAYS)),
            createStubWorkerAction(now.minusDays(MAX_RETENTION_DAYS + 1)),
        )

        workerActionRepository.saveAll(
            underMaxRetention + aboveMaxRetention,
        )

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).cleanup()
            }

        // then
        await().atMost(Duration.ofSeconds(SyncBatchOrderIntegrationTest.AWAIT_TIMEOUT))
            .untilAsserted {
                assertThat(
                    workerActionRepository.findAll().map { it.id }.toSet(),
                    equalTo(underMaxRetention.map { it.id }.toSet()),
                )
            }
    }

    private fun createStubWorkerAction(createdAt: LocalDateTime) =
        WorkerAction.createWorkerAction(
            CreatePurchaseOrder(UUID.randomUUID()),
            "<EMAIL>",
            UUID.randomUUID(),
        ).copy(createdAt = createdAt)

    companion object {
        const val MAX_RETENTION_DAYS = 1L
    }
}
