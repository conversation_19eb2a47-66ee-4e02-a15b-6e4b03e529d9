package com.hellofresh.purchaseordersynchronizer.workerAction.integration

import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.put
import com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching
import com.github.tomakehurst.wiremock.stubbing.Scenario
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.WorkerActionData
import com.hellofresh.oms.model.WorkerActionStatus.FAILED
import com.hellofresh.oms.model.WorkerActionStatus.PENDING
import com.hellofresh.oms.model.WorkerActionStatus.PROCESSED
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.oms.model.WorkerActionType.SEND_ORDER
import com.hellofresh.purchaseordersynchronizer.outbox.OutboxRepository
import java.time.Duration
import java.util.UUID
import org.awaitility.Awaitility.await
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.empty
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.atLeast
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest(properties = ["scheduler.process-send-orders.cron=* * * * * *"])
class SendOrderIntegrationTest : TapiocaAsyncIntegrationTest() {

    @Autowired
    private lateinit var outboxRepository: OutboxRepository

    @AfterEach
    override fun tearDown() {
        super.tearDown()
        outboxRepository.deleteAll()
    }

    @Test
    fun `should successfully process the PENDING SendWorkerAction entry and ignore the FAILED`() {
        // given
        val givenBulkId = UUID.randomUUID()

        listOf("PO_500" to PENDING, "PO_200" to FAILED, "PO_400" to PROCESSED)
            .map { (poNumber, status) ->
                persistWorkerAction(
                    type = SEND_ORDER,
                    createdAt = "2024-07-17T14:00:00Z",
                    payload = """{
                        |"purchaseOrderNumber":"$poNumber",
                        |"bulkId":"$givenBulkId",
                        |"outboxItemId":"${UUID.randomUUID()}"}
                    """.trimMargin(),
                    status = status,
                )
            }

        stubSuccessSendOrderResponse()

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT)).untilAsserted {
            verify(workerActionTask, atLeast(1)).processSendOrders()
        }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT)).untilAsserted {
            val unprocessedRequests = workerActionRepository.getPendingByType(WorkerActionType.SEND_ORDER)
            assertThat(unprocessedRequests, empty())
        }

        val allItemsStatus = workerActionRepository.findAll().map { it.status }.toSet()
        assertThat(allItemsStatus, equalTo(setOf(FAILED, PROCESSED)))

        val outboxItems = outboxRepository.findAll().map { Pair(it.poNumber, it.status) }.toSet()
        assertThat(
            outboxItems,
            equalTo(setOf(Pair("PO_500", OutboxItemStatus.SENT))),
        )
    }

    @Test
    fun `should mark SendWorkerAction and OutboxItem as FAILED when tapioca responds with error`() {
        // given
        val givenPoNumber = "PO_500"
        val givenBulkId = UUID.randomUUID()
        val givenOutboxItemId = UUID.randomUUID()

        persistWorkerAction(
            type = SEND_ORDER,
            createdAt = "2024-07-17T14:00:00Z",
            payload = """{
            |"purchaseOrderNumber":"$givenPoNumber",
            |"bulkId":"$givenBulkId",
            |"outboxItemId":"$givenOutboxItemId"}
            """.trimMargin(),
        )

        stubErrorSendOrderResponse()

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT)).untilAsserted {
            verify(workerActionTask, atLeast(1)).processSendOrders()
        }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT)).untilAsserted {
            val unprocessedRequests = workerActionRepository.findAll().map {
                Pair(
                    (it.getPayload() as WorkerActionData.SendPurchaseOrder).purchaseOrderNumber,
                    it.status,
                )
            }.toSet()
            assertThat(unprocessedRequests, equalTo(setOf(Pair(givenPoNumber, FAILED))))
        }
        val outboxItems = outboxRepository.findAll().map { Pair(it.poNumber, it.status) }.toSet()
        assertThat(
            outboxItems,
            equalTo(setOf(Pair("PO_500", OutboxItemStatus.FAILED))),
        )
    }

    @Test
    @Suppress("LongMethod")
    fun `should mark SendWorkerAction and OutboxItem as PROCESSED when tapioca responds with error then success`() {
        // given
        val givenPoNumber = "PO_500"
        val givenBulkId = UUID.randomUUID()
        val givenOutboxItemId = UUID.randomUUID()

        persistWorkerAction(
            type = SEND_ORDER,
            createdAt = "2024-07-21T14:00:00Z",
            payload = """{
            |"purchaseOrderNumber":"$givenPoNumber",
            |"bulkId":"$givenBulkId",
            |"outboxItemId":"$givenOutboxItemId"}
            """.trimMargin(),
        )

        // first two requests will fail, third will succeed
        wireMock.stubFor(
            put(urlPathMatching(SEND_ORDER_PATH))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs(Scenario.STARTED)
                .willReturn(
                    aResponse().withStatus(500),
                ).willSetStateTo("RETRY"),
        )
        wireMock.stubFor(
            put(urlPathMatching(SEND_ORDER_PATH))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs("RETRY")
                .willReturn(
                    aResponse().withStatus(500),
                ).willSetStateTo("RETRY_SUCCESS"),
        )
        wireMock.stubFor(
            put(urlPathMatching(SEND_ORDER_PATH))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs("RETRY_SUCCESS")
                .willReturn(
                    aResponse().withStatus(200),
                ),
        )

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT)).untilAsserted {
            verify(workerActionTask, atLeast(1)).processSendOrders()
        }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT)).untilAsserted {
            val unprocessedRequests = workerActionRepository.findAll().map {
                Pair(
                    (it.getPayload() as WorkerActionData.SendPurchaseOrder).purchaseOrderNumber,
                    it.status,
                )
            }.toSet()
            assertThat(unprocessedRequests, equalTo(setOf(Pair(givenPoNumber, PROCESSED))))
        }
        val outboxItems = outboxRepository.findAll().map { Pair(it.poNumber, it.status) }.toSet()
        assertThat(
            outboxItems,
            equalTo(setOf(Pair("PO_500", OutboxItemStatus.SENT))),
        )
    }

    companion object {
        const val AWAIT_TIMEOUT = 10L
    }
}
