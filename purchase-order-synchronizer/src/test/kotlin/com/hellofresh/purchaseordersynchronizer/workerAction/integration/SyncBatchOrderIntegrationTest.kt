package com.hellofresh.purchaseordersynchronizer.workerAction.integration

import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching
import com.github.tomakehurst.wiremock.stubbing.Scenario
import com.hellofresh.oms.model.WorkerActionData
import com.hellofresh.oms.model.WorkerActionStatus.FAILED
import com.hellofresh.oms.model.WorkerActionStatus.PENDING
import com.hellofresh.oms.model.WorkerActionStatus.PROCESSED
import com.hellofresh.oms.model.WorkerActionType
import java.time.Duration
import java.util.UUID
import org.awaitility.Awaitility.await
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.empty
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.mockito.kotlin.atLeast
import org.mockito.kotlin.verify
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest(properties = ["scheduler.process-sync-batch-orders.cron=* * * * * *"])
class SyncBatchOrderIntegrationTest : TapiocaAsyncIntegrationTest() {
    @Test
    @Suppress("LongMethod")
    fun `should successfully process the PENDING CreateWorkerAction entry and ignore the FAILED and PROCESSED`() {
        // given
        val importHistoryId = UUID.randomUUID()
        listOf("PO_500" to PENDING, "PO_200" to FAILED, "PO_400" to PROCESSED)
            .map { (poNumber, status) ->
                persistWorkerAction(
                    type = WorkerActionType.SYNC_BATCH_ORDER,
                    createdAt = "2024-07-17T14:00:00Z",
                    payload = """{
                        |"purchaseOrderNumber":"$poNumber",
                        |"importHistoryId":"$importHistoryId"
                        |}
                    """.trimMargin(),
                    status = status,
                )
            }

        stubSuccessCreateOrderResponse()

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processSyncBatchOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val unprocessedRequests = workerActionRepository.getPendingByType(WorkerActionType.SYNC_BATCH_ORDER)
                assertThat(unprocessedRequests, empty())
            }

        val allItemsStatus = workerActionRepository.findAll().map { it.status }.toSet()
        assertThat(allItemsStatus, equalTo(setOf(FAILED, PROCESSED)))
    }

    @Test
    fun `should mark CreateWorkerAction as FAILED when tapioca responds with error`() {
        // given
        val givenPoNumber = "PO_500"
        val importHistoryId = UUID.randomUUID()

        persistWorkerAction(
            type = WorkerActionType.SYNC_BATCH_ORDER,
            createdAt = "2024-07-17T14:00:00Z",
            payload = """{
                |"purchaseOrderNumber":"$givenPoNumber",
                |"importHistoryId":"$importHistoryId"
                |}
            """.trimMargin(),
            status = PENDING,
        )

        stubErrorCreateOrderResponse()

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processSyncBatchOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val unprocessedRequests = workerActionRepository.findAll().map {
                    Pair(
                        (it.getPayload() as WorkerActionData.SyncBatchOrder).purchaseOrderNumber,
                        it.status,
                    )
                }.toSet()
                assertThat(unprocessedRequests, equalTo(setOf(Pair(givenPoNumber, FAILED))))
            }
    }

    @Test
    @Suppress("LongMethod")
    fun `should mark CreateWorkerAction as PROCESSED when tapioca responds with error then success`() {
        // given
        val givenPoNumber = "PO_500"
        val importHistoryId = UUID.randomUUID()

        persistWorkerAction(
            type = WorkerActionType.SYNC_BATCH_ORDER,
            createdAt = "2024-07-21T14:00:00Z",
            payload = """{
                |"purchaseOrderNumber":"$givenPoNumber",
                |"importHistoryId":"$importHistoryId"
                |}
            """.trimMargin(),
            status = PENDING,
        )

        // first two requests will fail, third will succeed
        wireMock.stubFor(
            post(urlPathMatching("/orders"))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs(Scenario.STARTED)
                .willReturn(
                    aResponse().withStatus(500),
                )
                .willSetStateTo("RETRY"),
        )
        wireMock.stubFor(
            post(urlPathMatching("/orders"))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs("RETRY")
                .willReturn(
                    aResponse().withStatus(500),
                )
                .willSetStateTo("RETRY_SUCCESS"),
        )
        wireMock.stubFor(
            post(urlPathMatching("/orders"))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs("RETRY_SUCCESS")
                .willReturn(
                    createOrderSuccessResponseBody(),
                ),
        )

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processSyncBatchOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val unprocessedRequests = workerActionRepository.findAll().map {
                    Pair(
                        (it.getPayload() as WorkerActionData.SyncBatchOrder).purchaseOrderNumber,
                        it.status,
                    )
                }.toSet()
                assertThat(unprocessedRequests, equalTo(setOf(Pair(givenPoNumber, PROCESSED))))
            }
    }

    companion object {
        const val AWAIT_TIMEOUT = 10L
    }
}
