package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionStatus
import com.hellofresh.oms.model.WorkerActionType
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Clock
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class WorkerActionMetricServiceTest {
    private lateinit var meterRegistry: MeterRegistry

    private lateinit var clock: Clock

    private lateinit var subject: WorkerActionMetricService
    private val nowTimestamp = "2024-07-17T14:00:52Z"

    @BeforeEach
    fun setUp() {
        meterRegistry = SimpleMeterRegistry()
        clock = clockFromTimeString(nowTimestamp)
        subject = WorkerActionMetricService(meterRegistry, clock)
    }

    @Test
    fun `should record worker_action_process_time with difference between lastStatusChangeAt and createdAt`() {
        // given
        val givenPoNumber = "2241GR007902"
        val givenBulkId = UUID.randomUUID()
        val givenPayload = jsonNode("""{"purchaseOrderNumber":"$givenPoNumber","bulkId":"$givenBulkId"}""")
        val givenWorkerAction =
            WorkerAction(
                id = UUID.randomUUID(),
                actionType = WorkerActionType.SEND_ORDER,
                status = WorkerActionStatus.PROCESSED,
                createdAt = LocalDateTime.now(clockFromTimeString("2024-07-17T14:00:00Z")),
                lastStatusChangeAt = LocalDateTime.now(clockFromTimeString("2024-07-17T14:00:35Z")),
                userId = UUID.randomUUID(),
                userEmail = "<EMAIL>",
                payload = givenPayload,
            )

        // when
        subject.recordProcessedTimeForAction(givenWorkerAction)

        // then
        assertThat(
            meterRegistry.get("worker_action_processed_time").summary().totalAmount(),
            Matchers.equalTo(35_000.0),
        )
    }

    @Test
    fun `should record worker_action_time_in_queue with differnce between now and createdAt`() {
        // given
        val givenPoNumber = "2241GR007902"
        val givenBulkId = UUID.randomUUID()
        val givenPayload = jsonNode("""{"purchaseOrderNumber":"$givenPoNumber","bulkId":"$givenBulkId"}""")
        val givenWorkerAction =
            WorkerAction(
                id = UUID.randomUUID(),
                actionType = WorkerActionType.SEND_ORDER,
                status = WorkerActionStatus.PROCESSED,
                createdAt = LocalDateTime.now(clockFromTimeString("2024-07-17T14:00:00Z")),
                lastStatusChangeAt = LocalDateTime.now(clockFromTimeString("2024-07-17T14:00:35Z")),
                userId = UUID.randomUUID(),
                userEmail = "<EMAIL>",
                payload = givenPayload,
            )

        // when
        subject.recordTimeInQueueForAction(givenWorkerAction)

        // then
        assertThat(meterRegistry.get("worker_action_time_in_queue").summary().totalAmount(), Matchers.equalTo(52_000.0))
    }

    private fun clockFromTimeString(dateTime: String): Clock = Clock.fixed(Instant.parse(dateTime), ZoneId.of("UTC"))

    private fun jsonNode(jsonString: String) = ObjectMapper().readTree(jsonString)
}
