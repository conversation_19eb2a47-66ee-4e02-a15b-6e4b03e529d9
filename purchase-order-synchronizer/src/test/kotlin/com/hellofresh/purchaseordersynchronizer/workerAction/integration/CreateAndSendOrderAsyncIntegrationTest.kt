package com.hellofresh.purchaseordersynchronizer.workerAction.integration

import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.WorkerActionData.CreateAndSendPurchaseOrder
import com.hellofresh.oms.model.WorkerActionStatus.FAILED
import com.hellofresh.oms.model.WorkerActionStatus.PARTIALLY_PROCESSED
import com.hellofresh.oms.model.WorkerActionStatus.PROCESSED
import com.hellofresh.oms.model.WorkerActionType.CREATE_AND_SEND_ORDER
import com.hellofresh.purchaseordersynchronizer.outbox.OutboxRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.integration.CreateOrderIntegrationTest.Companion.AWAIT_TIMEOUT
import java.time.Duration
import java.util.UUID
import org.awaitility.Awaitility.await
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.empty
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.atLeast
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@Suppress("MaxLineLength", "LongMethod")
@SpringBootTest(properties = ["scheduler.process-create-and-send-orders.cron=* * * * * *"])
class CreateAndSendOrderAsyncIntegrationTest : TapiocaAsyncIntegrationTest() {

    @Autowired private lateinit var outboxRepository: OutboxRepository

    @AfterEach override fun tearDown() {
        super.tearDown()
        outboxRepository.deleteAll()
    }

    @Test
    fun `should mark action as PROCESSED when Tapioca responds with success for both create and send`() {
        // given
        val givenPoId = UUID.fromString("d6d1a101-6753-4d41-830b-b1730712f63d")

        stubSuccessCreateOrderResponse(givenPoId)
        stubSuccessSendOrderResponse()

        // when
        persistWorkerAction(
            type = CREATE_AND_SEND_ORDER,
            createdAt = "2025-07-08T14:00:00Z",
            payload = """{"purchaseOrderId":"$givenPoId"}""",
        )

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processCreateAndSendOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val existingActions = workerActionRepository.findAll().map {
                    Pair(
                        (it.getPayload() as CreateAndSendPurchaseOrder).purchaseOrderId,
                        it.status,
                    )
                }.toSet()
                val expectedActions = setOf(Pair(givenPoId, PROCESSED))
                assertThat(existingActions, equalTo(expectedActions))
            }

        val outboxItems = outboxRepository.findAll().map { Pair(it.poNumber, it.status) }.toSet()
        assertThat(
            outboxItems,
            equalTo(setOf(Pair("PO_500", OutboxItemStatus.SENT))),
        )
    }

    @Test
    fun `should mark action as PARTIALLY_PROCESSED when Tapioca responds with success for create but with error for send`() {
        // given
        val givenPoId = UUID.fromString("d6d1a101-6753-4d41-830b-b1730712f63d")

        stubSuccessCreateOrderResponse(givenPoId)
        stubErrorSendOrderResponse(400)

        // when
        persistWorkerAction(
            type = CREATE_AND_SEND_ORDER,
            createdAt = "2025-07-08T14:00:00Z",
            payload = """{"purchaseOrderId":"$givenPoId"}""",
        )

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processCreateAndSendOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val existingActions = workerActionRepository.findAll().map {
                    Pair(
                        (it.getPayload() as CreateAndSendPurchaseOrder).purchaseOrderId,
                        it.status,
                    )
                }.toSet()
                val expectedActions = setOf(Pair(givenPoId, PARTIALLY_PROCESSED))
                assertThat(existingActions, equalTo(expectedActions))
            }

        val outboxItems = outboxRepository.findAll().map { Pair(it.poNumber, it.status) }.toSet()
        assertThat(
            outboxItems,
            equalTo(setOf(Pair("PO_500", OutboxItemStatus.FAILED))),
        )
    }

    @Test
    fun `should mark action as FAILED when Tapioca responds with error for create`() {
        // given
        val givenPoId = UUID.fromString("d6d1a101-6753-4d41-830b-b1730712f63d")

        stubErrorCreateOrderResponse(400)
        // no need to stub send order response as create order will fail

        // when
        persistWorkerAction(
            type = CREATE_AND_SEND_ORDER,
            createdAt = "2025-07-08T14:00:00Z",
            payload = """{"purchaseOrderId":"$givenPoId"}""",
        )

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processCreateAndSendOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val existingActions = workerActionRepository.findAll().map {
                    Pair(
                        (it.getPayload() as CreateAndSendPurchaseOrder).purchaseOrderId,
                        it.status,
                    )
                }.toSet()
                val expectedActions = setOf(Pair(givenPoId, FAILED))
                assertThat(existingActions, equalTo(expectedActions))
            }

        val outboxItems = outboxRepository.findAll().map { Pair(it.poNumber, it.status) }.toSet()
        assertThat(
            outboxItems,
            empty(),
        )
    }
}
