package com.hellofresh.purchaseordersynchronizer.workerAction.integration

import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching
import com.github.tomakehurst.wiremock.stubbing.Scenario
import com.hellofresh.oms.model.WorkerActionData.CreatePurchaseOrder
import com.hellofresh.oms.model.WorkerActionStatus.FAILED
import com.hellofresh.oms.model.WorkerActionStatus.PENDING
import com.hellofresh.oms.model.WorkerActionStatus.PROCESSED
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.oms.model.WorkerActionType.CREATE_ORDER
import java.time.Duration
import java.util.UUID
import org.awaitility.Awaitility.await
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.empty
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.mockito.kotlin.atLeast
import org.mockito.kotlin.verify
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest(properties = ["scheduler.process-create-orders.cron=* * * * * *"])
class CreateOrderIntegrationTest : TapiocaAsyncIntegrationTest() {

    @Test
    @Suppress("LongMethod")
    fun `should successfully process the PENDING CreateOrder entry and ignore the FAILED`() {
        // given
        val givenPoId = "d6d1a101-6753-4d41-830b-b1730712f63d"
        val failedPoId = "16aa8571-5a0b-4e93-815d-fcdf66536899"
        val processedPoId = "6d83c67f-790d-4325-bca0-4ca1ac40ccb6"
        listOf(
            givenPoId to PENDING,
            failedPoId to FAILED,
            processedPoId to PROCESSED,
        ).map { (poId, status) ->
            persistWorkerAction(
                type = CREATE_ORDER,
                createdAt = "2024-07-21T14:00:00Z",
                payload = """{"purchaseOrderId":"$poId"}""",
                status = status,
            )
        }

        stubSuccessCreateOrderResponse(UUID.fromString(givenPoId))

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processCreateOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val unprocessedRequests = workerActionRepository.getPendingByType(WorkerActionType.CREATE_ORDER)
                assertThat(unprocessedRequests, empty())
            }

        val allItemsStatus = workerActionRepository.findAll().map {
            (it.getPayload() as CreatePurchaseOrder).purchaseOrderId to it.status
        }.toSet()
        assertThat(
            allItemsStatus,
            equalTo(
                setOf(
                    Pair(UUID.fromString(givenPoId), PROCESSED),
                    Pair(UUID.fromString(failedPoId), FAILED),
                    Pair(UUID.fromString(processedPoId), PROCESSED),
                ),
            ),
        )
    }

    @Test
    fun `should mark CreateOrder as FAILED when tapioca responds with error`() {
        // given
        val givenPoId = UUID.fromString("d6d1a101-6753-4d41-830b-b1730712f63d")

        persistWorkerAction(
            type = CREATE_ORDER,
            createdAt = "2024-07-21T14:00:00Z",
            payload = """{"purchaseOrderId":"$givenPoId"}""",
        )

        stubErrorCreateOrderResponse()

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processCreateOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val unprocessedRequests = workerActionRepository.findAll().map {
                    Pair(
                        (it.getPayload() as CreatePurchaseOrder).purchaseOrderId,
                        it.status,
                    )
                }.toSet()
                assertThat(unprocessedRequests, equalTo(setOf(Pair(givenPoId, FAILED))))
            }
    }

    @Test
    @Suppress("LongMethod")
    fun `should mark CreateOrder as PROCESSED when tapioca responds with error then success`() {
        // given
        val givenPoId = UUID.fromString("d6d1a101-6753-4d41-830b-b1730712f63d")

        persistWorkerAction(
            type = CREATE_ORDER,
            createdAt = "2024-07-21T14:00:00Z",
            payload = """{"purchaseOrderId":"$givenPoId"}""",
        )

        wireMock.stubFor(
            post(urlPathMatching(CREATE_ORDER_PATH))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs(Scenario.STARTED)
                .willReturn(
                    aResponse().withStatus(500),
                )
                .willSetStateTo("RETRY"),
        )
        wireMock.stubFor(
            post(urlPathMatching(CREATE_ORDER_PATH))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs("RETRY")
                .willReturn(
                    aResponse().withStatus(500),
                )
                .willSetStateTo("RETRY_SUCCESS"),
        )
        wireMock.stubFor(
            post(urlPathMatching(CREATE_ORDER_PATH))
                .inScenario("Tapioca Retry")
                .whenScenarioStateIs("RETRY_SUCCESS")
                .willReturn(
                    createOrderSuccessResponseBody(givenPoId),
                ),
        )

        // when
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                verify(workerActionTask, atLeast(1)).processCreateOrders()
            }

        // then
        await().atMost(Duration.ofSeconds(AWAIT_TIMEOUT))
            .untilAsserted {
                val unprocessedRequests = workerActionRepository.findAll().map {
                    Pair(
                        (it.getPayload() as CreatePurchaseOrder).purchaseOrderId,
                        it.status,
                    )
                }.toSet()
                assertThat(unprocessedRequests, equalTo(setOf(Pair(givenPoId, PROCESSED))))
            }
    }

    companion object {
        const val AWAIT_TIMEOUT = 10L
    }
}
