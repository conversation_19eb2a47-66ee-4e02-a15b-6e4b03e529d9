package com.hellofresh.purchaseordersynchronizer.config

import java.time.Clock
import java.time.Instant
import java.time.ZoneId
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ClockConfiguration {
    @Bean
    fun clock() =
        object : Clock() {
            override fun instant(): Instant = Instant.parse("2024-07-17T14:00:48Z")

            override fun withZone(zone: ZoneId?): Clock = TODO("Not yet implemented")

            override fun getZone(): ZoneId = ZoneId.of("UTC")
        }
}
