package com.hellofresh.purchaseordersynchronizer

import com.hellofresh.oms.model.Money
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.UOM
import com.hellofresh.oms.model.UOM.UNIT
import com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.model.TapiocaShippingMethod
import com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.model.TapiocaUom
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments.arguments
import org.junit.jupiter.params.provider.MethodSource

@TestInstance(PER_CLASS)
class PurchaseOrderMapperTest {

    @Test
    fun `should map PO to tapioca request`() {
        // Given
        val purchaseOrderEntity = getPurchaseOrderEntity()

        // When
        val tapiocaRequest = purchaseOrderEntity.mapToCreatePurchaseOrderTapioca()

        // Then
        assertEquals(purchaseOrderEntity.poNumber, tapiocaRequest.orderNumber)
        assertEquals(purchaseOrderEntity.id, tapiocaRequest.orderId)
        assertEquals(purchaseOrderEntity.supplierCode, tapiocaRequest.supplierCode)
        assertEquals(purchaseOrderEntity.dcCode, tapiocaRequest.distributionCenterCode)
        assertEquals(purchaseOrderEntity.expectedStartTime, tapiocaRequest.expectedArrivalTime)
        assertEquals(purchaseOrderEntity.expectedEndTime, tapiocaRequest.expectedArrivalEndTime)
        assertEquals(purchaseOrderEntity.userId, tapiocaRequest.createdBy)
        assertEquals(purchaseOrderEntity.emergencyReasonUuid, tapiocaRequest.emergencyReasonUuid)
        assertEquals(purchaseOrderEntity.yearWeek.value, tapiocaRequest.week)
        assertEquals(TapiocaShippingMethod.VENDOR_DELIVERED, tapiocaRequest.shippingMethod)
        assertTrue(tapiocaRequest.ignoreBuffer)
        assertNotNull(tapiocaRequest.items)
        assertEquals(purchaseOrderEntity.comment, tapiocaRequest.comments)
        assertFalse(tapiocaRequest.isRevision)
        assertEquals(purchaseOrderEntity.deliveryDateChangeReasonId, tapiocaRequest.deliveryDateReasonUuid)
        assertEquals(purchaseOrderEntity.supplierId, tapiocaRequest.supplierUuid)

        tapiocaRequest.items.forEach {
            val item = purchaseOrderEntity.orderItems.first { poItem ->
                poItem.skuId == it.productSkuId
            }
            assertAll(
                { assertEquals(item.totalQty, it.qty) },
                { assertEquals(item.price.amount, it.agreedPrice) },
                { assertEquals(item.packaging.unitOfMeasure.name, it.packingUnit.name) },
                { assertEquals(item.buffer.toPercent(), it.bufferPercentage) },
                { assertEquals(item.packaging.caseSize ?: BigDecimal.ZERO, it.packingSize) },
                { assertEquals(item.packaging.packagingType.toString(), it.orderUnit.toString()) },
                { assertEquals(item.packaging.packagingType.toString(), it.orderUnit.toString()) },
            )
        }
    }

    @Test
    fun `syncs back zeros as tapioca minimal prices`() {
        // Given
        val purchaseOrderEntity = getPurchaseOrderEntity()
            .copy(
                totalPrice = Money(BigDecimal.ZERO, "EUR"),
                orderItems = setOf(
                    getOrderItem().copy(
                        price = Money(BigDecimal.ZERO, "EUR"),
                        totalPrice = Money(BigDecimal.ZERO, "EUR"),
                    ),
                ),
            )

        // When
        val tapiocaRequest = purchaseOrderEntity.mapToCreatePurchaseOrderTapioca()

        assertEquals(BigDecimal("0.0000000001"), tapiocaRequest.items.first().agreedPrice)
    }

    @Test
    fun `should calculate the correct total case quantity`() {
        // Given
        // For this to accurately result in the issue that was found on staging, the scaling needs to be like this.
        val totalCaseQuantity = BigDecimal(165).setScale(10)
        val caseSize = BigDecimal(5.3035)
        val totalQuantity = caseSize.multiply(totalCaseQuantity).setScale(10, RoundingMode.HALF_EVEN)
        assertEquals(
            BigDecimal(875.0775).setScale(4, RoundingMode.HALF_EVEN),
            totalQuantity.setScale(4, RoundingMode.HALF_EVEN),
        )

        val purchaseOrderEntity = getPurchaseOrderEntity().copy(
            orderItems = getPurchaseOrderEntity().orderItems.map {
                it.copy(
                    totalQty = totalQuantity,
                    packaging = it.packaging.copy(caseSize = caseSize, packagingType = PackagingType.CASE_TYPE),
                )
            }.toSet(),
        )

        // When
        val tapiocaRequest = purchaseOrderEntity.mapToCreatePurchaseOrderTapioca()

        tapiocaRequest.items.forEach {
            assertAll(
                // 166 is the number that was being incorrectly derived on staging.
                { assertNotEquals(166, it.qty.setScale(0, RoundingMode.HALF_EVEN).toInt()) },
                { assertEquals(totalCaseQuantity.setScale(0), it.qty) },
                { assertEquals(caseSize, it.packingSize) },
            )
        }
    }

    @Test
    fun `should set isRevision flag to true when version is not 1`() {
        // Given
        val purchaseOrderEntity = getPurchaseOrderEntity(version = 2)

        // When
        val tapiocaRequest = purchaseOrderEntity.mapToCreatePurchaseOrderTapioca()

        // Then
        assertTrue(tapiocaRequest.isRevision)
    }

    @ParameterizedTest
    @MethodSource("unitOfMeasureProvider")
    fun `should map each unit of measure as expected`(uom: UOM, expectedUom: TapiocaUom) {
        // given
        val purchaseOrderEntity = getPoWithOneItemForUom(uom)

        // when
        val result = purchaseOrderEntity.mapToCreatePurchaseOrderTapioca()

        // then
        assertEquals(expectedUom, result.items.first().packingUnit)
    }

    @Test
    fun `should throw IllegalArgumentException when EmergencyReasonUuid is null`() {
        // given
        val purchaseOrderEntity = getPurchaseOrderEntity().copy(emergencyReasonUuid = null)

        // then
        val exception = assertThrows<IllegalArgumentException> {
            purchaseOrderEntity.mapToCreatePurchaseOrderTapioca()
        }
        assertEquals("EmergencyReasonUuid can not be null", exception.message)
    }

    @Suppress("UnusedPrivateMember")
    private fun unitOfMeasureProvider() = Stream.of(
        arguments(UNIT, TapiocaUom.UNIT),
        arguments(UOM.GAL, TapiocaUom.GAL),
        arguments(UOM.KG, TapiocaUom.KG),
        arguments(UOM.L, TapiocaUom.L),
        arguments(UOM.OZ, TapiocaUom.OZ),
        arguments(UOM.LBS, TapiocaUom.LBS),
    )
}
