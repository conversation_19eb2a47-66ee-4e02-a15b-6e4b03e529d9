---
spring:
  application:
    name: order-management-service
  datasource:
    driverClassName: org.postgresql.Driver
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      connection-timeout: 30000 # 30 seconds
      idle-timeout: 300000 # 5 minutes
      maximum-pool-size: 10
      minimum-idle: 2
      connection-test-query: "SELECT 1"
      validation-timeout: 5000 # 5 second
      max-lifetime: 1800000 # 30 minutes
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    hibernate.ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    open-in-view: false

resilience4j:
  retry:
    instances:
      createPurchaseOrder:
        maxAttempts: 3
        waitDuration: 2s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2
        retryExceptions:
          - com.hellofresh.purchaseordersynchronizer.client.tapioca.exception.TapiocaClientException
          - org.springframework.web.reactive.function.client.WebClientRequestException
        ignoreExceptions:
          - com.hellofresh.purchaseordersynchronizer.client.tapioca.exception.TapiocaConflictException
      sendPurchaseOrder:
        maxAttempts: 3
        waitDuration: 2s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2
        retryExceptions:
          - com.hellofresh.purchaseordersynchronizer.client.tapioca.exception.TapiocaClientException
          - org.springframework.web.reactive.function.client.WebClientRequestException

tapioca:
  base-url: ${TAPIOCA_BASE_URL}
  po-create-url: /orders
  po-send-url: /purchase-orders/{poId}/send


auth-service:
  base-url: ${AUTH_SERVICE_BASE_URL}
  client-id: ${AUTH_SERVICE_CLIENT_ID}
  client-secret: ${AUTH_SERVICE_CLIENT_SECRET}

webclient:
  connection-timeout: 5s
  response-timeout: 5s
  max-idle-time: 20s
  max-life-time: 60s
  pending-acquire-timeout: 60s
  evict-in-background: 120s

scheduler:
  process-create-orders:
    cron: "* * * * * *"
  process-send-orders:
    cron: "* * * * * *"
  process-sync-batch-orders:
    cron: "* * * * * *"
  process-create-and-send-orders:
    cron: "* * * * * *"
  consolidate-sync-batch-orders:
    cron: "0 */1 * * * *"
  cleanup:
    cron: "0 0 0 * * *"
    max-retention-days: 14

management:
  endpoint:
    health:
      show-details: "ALWAYS"
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState,diskSpace,db,ping
  endpoints:
    access:
      default: read_only
    web:
      exposure:
        include: "*"
  server:
    port: 8081
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  info:
    git:
      mode: full
      enabled: true
  tracing:
    propagation:
      type: B3,W3C
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://${OTLP_EXPORTER_HOST:localhost}:9411/api/v2/spans

logging:
  pattern:
    level: "%5p [%MDC{traceId},%MDC{spanId}]"
