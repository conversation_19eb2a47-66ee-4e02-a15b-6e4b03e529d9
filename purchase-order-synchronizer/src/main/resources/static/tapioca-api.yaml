swagger: "2.0"
info:
    title: Tapioca API
    description: API description for Tapioca.
    version: 1.0.0

definitions:
  CreatePurchaseOrderTapioca:
    title: Purchase Order Creation request for Tapioca
    description: Purchase order created via Order Planning Service
    type: object
    properties:
      orderNumber:
        type: string
        description: An order number, not including revision eg. 2150DE501496
      orderId:
        type: string
        description: 'A unique order and revision identifier in form of UUIDv4 '
        format: uuid
      supplierCode:
        type: string
        description: A numeric string, uniquely identifying supplier
      distributionCenterCode:
        type: string
        description: A two letter string, identifying Distribution Center
      expectedArrivalTime:
        type: string
        format: "date-time"
        example: "2021-12-31T10:00:00"
        description: The starting datetime for the delivery slot
      expectedArrivalEndTime:
        type: string
        format: "date-time"
        example: "2021-12-31T12:00:00"
      createdBy:
        type: string
        description: Unique identifier for the user creating the order
        format: uuid
      week:
        type: string
        description: Week number ex 2021-W41
      emergencyReasonUuid:
        type: string
        description: Unique identifier for emergency reason
        format: uuid
      shippingMethod:
        $ref: "#/definitions/TapiocaShippingMethod"
        description: The Shipping method required.
      items:
        description: Array of Items
        type: array
        items:
          $ref: "#/definitions/CreatePurchaseOrderTapiocaItem"
        minItems: 1
      ignoreBuffer:
        type: boolean
        description: Telling Tapioca not to apply the buffer when saving the order items
        default: false
      comments:
        type: string
        description: A comment attached to the order.
      isRevision:
          type: boolean
          description: Whether the order creation is a new revision of an existing order.
      deliveryDateReasonUuid:
          type: string
          description: Unique identifier for delivery date reason
          format: uuid
      supplierUuid:
          type: string
          description: Unique identifier for supplier
          format: uuid
    required:
      - orderNumber
      - supplierCode
      - distributionCenterCode
      - expectedArrivalTime
      - expectedArrivalEndTime
      - createdBy
      - week
      - items
      - orderId
      - ignoreBuffer
      - emergencyReasonUuid
      - isRevision
      - supplierUuid # This is not required on Tapioca side, but we are always sending it.

  CreatePurchaseOrderResponseTapioca:
    title: Purchase Order Creation response from Tapioca
    type: object
    properties:
      data:
        type: "object"
        $ref: "#/definitions/CreatePurchaseOrderResponseDataTapioca"
      message:
        type: "string"
      status:
        type: "integer"
    required:
      - status

  CreatePurchaseOrderResponseDataTapioca:
    title: Purchase Order Creation response data from Tapioca
    type: object
    properties:
      status:
        type: "string"
      uuid:
        type: "string"
        format: "uuid"
    required:
      - status
      - uuid

  CreatePurchaseOrderTapiocaItem:
    type: object
    properties:
      productSkuId:
        type: string
        format: uuid
        description: A string representing the unique SKU UUID
      qty:
        type: number
        description: Number of ordered items measured in 'orderUnit' (i.e units
          or cases)
      agreedPrice:
        type: number
        description: Agreed price per order unit (i.e per unit or case)
      bufferPercentage:
        type: number
        description: Percentage of extra amount to be ordered
      orderUnit:
        $ref: "#/definitions/OrderUnit"
        description: Packaging type used for placing the order [CASE_TYPE|UNIT_TYPE]
      packingSize:
        type: number
        description: Number of items in abn individual order unit (i.e number of
          units in a case)
      packingUnit:
          $ref: "#/definitions/TapiocaUom"
          description: Unit of measure used for bulk order ex. KG, L etc.
      reasonUuid:
        type: string
        format: uuid
        description: A string representing the unique order item change UUID
      palletSize:
        type: integer
        description: Whole number of cases per pallet
    required:
      - productSkuId
      - qty
      - packingSize
      - agreedPrice
      - bufferPercentage
      - packingUnit

  TapiocaShippingMethod:
    type: string
    enum:
      - "Vendor Delivered"
      - "Freight On Board"
      - "Crossdock"
      - "Others"
  OrderUnit:
    type: string
    enum:
      - "CASE_TYPE"
      - "UNIT_TYPE"
  TapiocaUom:
      type: string
      enum:
          - "unit"
          - "kg"
          - "L"
          - "gal"
          - "lbs"
          - "oz"
