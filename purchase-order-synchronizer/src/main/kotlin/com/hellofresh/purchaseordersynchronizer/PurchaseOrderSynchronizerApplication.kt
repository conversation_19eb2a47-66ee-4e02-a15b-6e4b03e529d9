package com.hellofresh.purchaseordersynchronizer

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.scheduling.annotation.EnableAsync

@SpringBootApplication
@EntityScan(basePackages = ["com.hellofresh.oms.model"])
@ConfigurationPropertiesScan
@EnableAsync
class PurchaseOrderSynchronizerApplication

@Suppress("SpreadOperator")
fun main(args: Array<String>) {
    runApplication<PurchaseOrderSynchronizerApplication>(*args)
}
