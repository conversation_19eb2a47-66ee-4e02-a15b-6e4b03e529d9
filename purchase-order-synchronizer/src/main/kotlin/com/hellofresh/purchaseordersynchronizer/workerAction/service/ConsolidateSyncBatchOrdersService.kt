package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.oms.model.WorkerActionData.SyncBatchOrder
import com.hellofresh.oms.model.WorkerActionStatus.FAILED
import com.hellofresh.oms.model.WorkerActionStatus.PENDING
import com.hellofresh.oms.model.WorkerActionStatus.PROCESSED
import com.hellofresh.oms.model.importHistory.ImportHistory
import com.hellofresh.oms.model.importHistory.ImportHistoryStatus
import com.hellofresh.purchaseordersynchronizer.imports.out.ImportHistoryRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import java.time.Clock
import java.time.LocalDateTime
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ConsolidateSyncBatchOrdersService(
    private val importHistoryRepository: ImportHistoryRepository,
    private val workerActionRepository: WorkerActionRepository,
    private val clock: Clock,
) {
    @Suppress("MaxLineLength")
    fun consolidateBatchSyncOrders() {
        importHistoryRepository.findByStatus(ImportHistoryStatus.INITIATED)
            .forEach { importHistory ->
                logger.info(
                    "Consolidating worker actions for import history. [importHistoryId={}]",
                    importHistory.id,
                )

                val workerActions = workerActionRepository.findByImportHistoryId(importHistory.id.toString())
                    .filter { it.getPayload() is SyncBatchOrder }

                if (workerActions.isEmpty()) {
                    logger.warn(
                        "No worker actions found for import history. [importHistoryId={}]",
                        importHistory.id,
                    )
                    return
                }

                when {
                    workerActions.all { it.status == FAILED } -> {
                        logger.error(
                            "All worker actions have FAILED status. Import history set to FAILED. [importHistoryId={}, workerActions={}]",
                            importHistory.id,
                            workerActions.joinToString { it.id.toString() },
                        )
                        updateImportHistory(importHistory, ImportHistoryStatus.FAILED)
                    }

                    workerActions.all { it.status == PROCESSED } -> {
                        logger.info(
                            "All worker actions have PROCESSED status. Import history set to SUCCEEDED. [importHistoryId={}, workerActions={}]",
                            importHistory.id,
                            workerActions.joinToString { it.id.toString() },
                        )
                        updateImportHistory(importHistory, ImportHistoryStatus.SUCCEEDED)
                    }

                    workerActions.any { it.status == PENDING } -> {
                        logger.info(
                            "Some worker actions have PENDING status. Import history status remains INITIATED. [importHistoryId={}, workerActions={}]",
                            importHistory.id,
                            workerActions.joinToString { it.id.toString() },
                        )
                    }

                    workerActions.any { it.status == FAILED } -> {
                        logger.error(
                            "Some worker actions have FAILED status. Import history set to PARTIALLY_SUCCEEDED. [importHistoryId={}, workerActions={}]",
                            importHistory.id,
                            workerActions.joinToString { it.id.toString() },
                        )
                        updateImportHistory(importHistory, ImportHistoryStatus.PARTIALLY_SUCCEEDED)
                    }
                }
            }
    }

    private fun updateImportHistory(
        importHistory: ImportHistory,
        status: ImportHistoryStatus
    ) {
        importHistoryRepository.save(
            importHistory.copy(
                status = status,
                updatedAt = LocalDateTime.now(clock),
            ),
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
