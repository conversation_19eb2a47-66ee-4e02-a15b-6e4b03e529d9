package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData.CreateAndSendPurchaseOrder
import com.hellofresh.oms.model.WorkerActionStatus
import com.hellofresh.oms.model.WorkerActionStatus.FAILED
import com.hellofresh.oms.model.WorkerActionStatus.PARTIALLY_PROCESSED
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.purchaseordersynchronizer.PurchaseOrderRepository
import com.hellofresh.purchaseordersynchronizer.client.tapioca.TapiocaClient
import com.hellofresh.purchaseordersynchronizer.client.tapioca.domain.SendPurchaseOrderRequest
import com.hellofresh.purchaseordersynchronizer.mapToCreatePurchaseOrderTapioca
import com.hellofresh.purchaseordersynchronizer.outbox.OutboxRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import java.time.Clock
import java.time.LocalDateTime
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
@Suppress("TooGenericExceptionCaught")
class CreateAndSendService(
    private val workerActionMetricService: WorkerActionMetricService,
    private val workerActionRepository: WorkerActionRepository,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val tapiocaClient: TapiocaClient,
    private val outboxRepository: OutboxRepository,
    clock: Clock,
) : WorkerActionService(workerActionMetricService, workerActionRepository, clock) {
    fun processPendingTasks() =
        workerActionRepository
            .getPendingByType(WorkerActionType.CREATE_AND_SEND_ORDER)
            .map { it to (it.getPayload() as CreateAndSendPurchaseOrder).purchaseOrderId }
            .forEach { (action, poId) -> processCreateAndSendOrderAction(action, poId) }

    private fun processCreateAndSendOrderAction(
        action: WorkerAction,
        poId: UUID
    ) {
        try {
            val po = purchaseOrderRepository.findById(poId).orElseThrow {
                IllegalArgumentException("PurchaseOrder not found. [orderId=$poId]")
            }
            workerActionMetricService.recordTimeInQueueForAction(action)

            createPurchaseOrder(po)
            sendPurchaseOrder(po, action)

            saveActionAsProcessed(action)
        } catch (e: SendException) {
            handleException(action, poId, e, PARTIALLY_PROCESSED)
        } catch (e: Throwable) {
            handleException(action, poId, e, FAILED)
        }
    }

    private fun createPurchaseOrder(po: PurchaseOrder) {
        tapiocaClient.createPurchaseOrder(po.mapToCreatePurchaseOrderTapioca())
    }

    private fun sendPurchaseOrder(po: PurchaseOrder, action: WorkerAction) {
        try {
            tapiocaClient.sendPurchaseOrder(
                SendPurchaseOrderRequest(
                    purchaseOrderId = po.id,
                    senderId = action.userId,
                ),
            )
            persistOutboxItemFor(po, action, OutboxItemStatus.SENT)
        } catch (e: Throwable) {
            persistOutboxItemFor(po, action, OutboxItemStatus.FAILED)
            throw SendException("Unable to send Purchase Order to Tapioca", e)
        }
    }

    private fun handleException(action: WorkerAction, poId: UUID, e: Throwable, status: WorkerActionStatus) {
        logger.error("Error processing CREATE_AND_SEND_ORDER action. [actionId={}, orderId={}]", action.id, poId, e)
        workerActionMetricService.recordFailedAction(action)
        saveActionWithException(action, e, status)
    }

    private fun persistOutboxItemFor(
        po: PurchaseOrder,
        action: WorkerAction,
        status: OutboxItemStatus,
    ) {
        outboxRepository.save(
            OutboxItem.createItem(
                poId = po.id,
                poNumber = po.poNumber,
                now = LocalDateTime.now(),
                userEmail = action.userEmail,
                userId = action.userId,
                version = po.version,
                status = status,
            ),
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}

class SendException(message: String, cause: Throwable) : RuntimeException(message, cause)
