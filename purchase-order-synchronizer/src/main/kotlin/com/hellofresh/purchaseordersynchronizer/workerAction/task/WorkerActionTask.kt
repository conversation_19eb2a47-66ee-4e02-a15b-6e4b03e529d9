package com.hellofresh.purchaseordersynchronizer.workerAction.task

import com.hellofresh.purchaseordersynchronizer.workerAction.service.CleanupService
import com.hellofresh.purchaseordersynchronizer.workerAction.service.ConsolidateSyncBatchOrdersService
import com.hellofresh.purchaseordersynchronizer.workerAction.service.CreateAndSendService
import com.hellofresh.purchaseordersynchronizer.workerAction.service.CreateOrderService
import com.hellofresh.purchaseordersynchronizer.workerAction.service.SendOrderService
import com.hellofresh.purchaseordersynchronizer.workerAction.service.SyncBatchOrderService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class WorkerActionTask(
    val createOrderService: CreateOrderService,
    val sendOrderService: SendOrderService,
    val syncBatchOrderService: SyncBatchOrderService,
    val consolidateSyncBatchOrdersService: ConsolidateSyncBatchOrdersService,
    val cleanupService: CleanupService,
    val createAndSendService: CreateAndSendService,
) {
    @Scheduled(cron = "\${scheduler.process-create-orders.cron}")
    @SchedulerLock(name = "processCreateOrders", lockAtLeastFor = "PT1S", lockAtMostFor = "PT5M")
    fun processCreateOrders() = createOrderService.processPendingTasks()

    @Scheduled(cron = "\${scheduler.process-send-orders.cron}")
    @SchedulerLock(name = "processSendOrders", lockAtLeastFor = "PT1S", lockAtMostFor = "PT5M")
    fun processSendOrders() = sendOrderService.processPendingTasks()

    @Scheduled(cron = "\${scheduler.process-create-and-send-orders.cron}")
    @SchedulerLock(name = "processCreateAndSendOrders", lockAtLeastFor = "PT1S", lockAtMostFor = "PT5M")
    fun processCreateAndSendOrders() = createAndSendService.processPendingTasks()

    /**
     * Process orders created using Batch Import.
     * They are first created in OMS and then in Tapioca in an asynchronous way.
     */
    @Scheduled(cron = "\${scheduler.process-sync-batch-orders.cron}")
    @SchedulerLock(name = "processSyncBatchOrders", lockAtLeastFor = "PT1S", lockAtMostFor = "PT5M")
    fun processSyncBatchOrders() = syncBatchOrderService.processPendingTasks()

    /**
     * Consolidate orders synchronized by `processSyncBatchOrders`.
     * After batch orders are processed separately, they need to be consolidated
     * and ImportHistory updated accordingly.
     */
    @Scheduled(cron = "\${scheduler.consolidate-sync-batch-orders.cron}")
    @SchedulerLock(name = "consolidateBatchSync", lockAtLeastFor = "PT1S", lockAtMostFor = "PT5M")
    fun consolidateBatchSync() = consolidateSyncBatchOrdersService.consolidateBatchSyncOrders()

    @Scheduled(cron = "\${scheduler.cleanup.cron}")
    @SchedulerLock(name = "consolidateBatchSync", lockAtLeastFor = "PT1S", lockAtMostFor = "PT5M")
    fun cleanup() = cleanupService.cleanup()
}
