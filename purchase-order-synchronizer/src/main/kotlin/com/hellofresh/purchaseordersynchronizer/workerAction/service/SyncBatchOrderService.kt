package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData.SyncBatchOrder
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.purchaseordersynchronizer.PurchaseOrderRepository
import com.hellofresh.purchaseordersynchronizer.PurchaseOrderVersionAndType
import com.hellofresh.purchaseordersynchronizer.client.tapioca.TapiocaClient
import com.hellofresh.purchaseordersynchronizer.mapToCreatePurchaseOrderTapioca
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import java.time.Clock
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class SyncBatchOrderService(
    private val workerActionMetricService: WorkerActionMetricService,
    private val workerActionRepository: WorkerActionRepository,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val tapiocaClient: <PERSON>pioca<PERSON><PERSON>,
    clock: Clock,
) : WorkerActionService(workerActionMetricService, workerActionRepository, clock) {
    fun processPendingTasks() {
        workerActionRepository
            .getPendingByType(WorkerActionType.SYNC_BATCH_ORDER)
            .map {
                val poNumber = (it.getPayload() as SyncBatchOrder).purchaseOrderNumber
                // !! was added since CreateWorkerActionService creates the WorkerAction only if the PO exists
                Pair(it, purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(poNumber)!!)
            }
            .forEach { (action: WorkerAction, po: PurchaseOrderVersionAndType) ->
                processBatchOrderAction(action, po)
            }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun processBatchOrderAction(action: WorkerAction, purchaseOrder: PurchaseOrderVersionAndType) {
        try {
            workerActionMetricService.recordTimeInQueueForAction(action)
            createBatchOrderActionInTapioca(purchaseOrder)
            saveActionAsProcessed(action)
        } catch (e: Throwable) {
            logger.error(
                "Error processing CREATE_ORDER action. [actionId={}, purchaseOrder={}]",
                action.id,
                purchaseOrder.poNumber,
                e,
            )
            saveActionWithException(action, e)
        }
    }

    private fun createBatchOrderActionInTapioca(purchaseOrder: PurchaseOrderVersionAndType) =
        purchaseOrderRepository.findById(purchaseOrder.id).get()
            .let {
                logger.info(
                    "Creating PurchaseOrder in Tapioca. [orderNumber={}]",
                    purchaseOrder.poNumber,
                )
                tapiocaClient.createPurchaseOrder(it.mapToCreatePurchaseOrderTapioca())
            }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
