package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.purchaseordersynchronizer.workerAction.service.WorkerActionMetricService.WorkerActionMetricType.FAILED
import com.hellofresh.purchaseordersynchronizer.workerAction.service.WorkerActionMetricService.WorkerActionMetricType.PROCESSED_TIME
import com.hellofresh.purchaseordersynchronizer.workerAction.service.WorkerActionMetricService.WorkerActionMetricType.TIME_IN_QUEUE
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.DistributionSummary
import io.micrometer.core.instrument.MeterRegistry
import java.time.Clock
import java.time.LocalDateTime
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service

@Service
class WorkerActionMetricService(
    private val meterRegistry: MeterRegistry,
    private val clock: Clock,
) {
    @Async
    fun recordFailedAction(workerAction: WorkerAction) {
        Counter
            .builder(FAILED.metricName)
            .tag("type", workerAction.actionType.name)
            .description(FAILED.description)
            .register(meterRegistry)
            .increment()
    }

    @Async
    fun recordTimeInQueueForAction(workerAction: WorkerAction) =
        recordSummaryMetricForAction(
            TIME_IN_QUEUE,
            workerAction,
            timeDifferenceInMillis(workerAction.createdAt, LocalDateTime.now(clock)),
        )

    @Async
    fun recordProcessedTimeForAction(workerAction: WorkerAction) =
        recordSummaryMetricForAction(
            PROCESSED_TIME,
            workerAction,
            timeDifferenceInMillis(workerAction.createdAt, workerAction.lastStatusChangeAt),
        )

    @Suppress("SpreadOperator")
    private fun recordSummaryMetricForAction(
        metric: WorkerActionMetricType,
        workerAction: WorkerAction,
        processedTimeInMillis: Double,
    ) = DistributionSummary
        .builder(metric.metricName)
        .tag("status", workerAction.status.name)
        .tag("type", workerAction.actionType.name)
        .description(metric.description)
        .register(meterRegistry)
        .record(processedTimeInMillis)

    private fun timeDifferenceInMillis(
        initialDateTime: LocalDateTime,
        finalDateTime: LocalDateTime,
    ) = (
        finalDateTime.toInstant(clock.zone.rules.getOffset(finalDateTime)).toEpochMilli() -
            initialDateTime.toInstant(clock.zone.rules.getOffset(initialDateTime)).toEpochMilli()
        ).toDouble()

    private enum class WorkerActionMetricType(val metricName: String, val description: String) {
        FAILED(
            "worker_action_failed_task",
            "When the worker task is failed and won't be retried"
        ),
        PROCESSED_TIME(
            "worker_action_processed_time",
            "How long it takes for a worker action to be processed since it was queued",
        ),
        TIME_IN_QUEUE(
            "worker_action_time_in_queue",
            "How long a worker action has been in the queue before being successfully processed",
        ),
    }
}
