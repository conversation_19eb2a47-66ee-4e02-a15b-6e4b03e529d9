package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData.CreatePurchaseOrder
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.purchaseordersynchronizer.PurchaseOrderRepository
import com.hellofresh.purchaseordersynchronizer.client.tapioca.TapiocaClient
import com.hellofresh.purchaseordersynchronizer.mapToCreatePurchaseOrderTapioca
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import java.time.Clock
import java.util.UUID
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class CreateOrderService(
    private val workerActionMetricService: WorkerActionMetricService,
    private val workerActionRepository: WorkerActionRepository,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val tapiocaClient: TapiocaClient,
    clock: Clock,
) : WorkerActionService(workerActionMetricService, workerActionRepository, clock) {

    fun processPendingTasks() =
        workerActionRepository
            .getPendingByType(WorkerActionType.CREATE_ORDER)
            .map {
                val poId = (it.getPayload() as CreatePurchaseOrder).purchaseOrderId
                Pair(it, poId)
            }
            .forEach { (action: WorkerAction, poId: UUID) ->
                processCreateOrderAction(action, poId)
            }

    @Suppress("TooGenericExceptionCaught")
    private fun processCreateOrderAction(
        action: WorkerAction,
        poId: UUID
    ) {
        try {
            val po = purchaseOrderRepository.findById(poId).orElseThrow {
                IllegalArgumentException("PurchaseOrder not found. [orderId=$poId]")
            }
            workerActionMetricService.recordTimeInQueueForAction(action)
            tapiocaClient.createPurchaseOrder(po.mapToCreatePurchaseOrderTapioca())
            saveActionAsProcessed(action)
        } catch (e: Throwable) {
            logger.error(
                "Error processing CREATE_ORDER action. [actionId={}, orderId={}]",
                action.id,
                poId,
                e,
            )
            workerActionMetricService.recordFailedAction(action)
            saveActionWithException(action, e)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
