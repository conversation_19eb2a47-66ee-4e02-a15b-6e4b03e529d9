package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionStatus
import com.hellofresh.oms.model.WorkerActionStatus.FAILED
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import java.time.Clock
import java.time.LocalDateTime

abstract class WorkerActionService(
    private val workerActionMetricService: WorkerActionMetricService,
    private val workerActionRepository: WorkerActionRepository,
    private val clock: Clock,
) {
    fun saveActionWithException(
        action: WorkerAction,
        exception: Throwable,
        status: WorkerActionStatus = FAILED,
    ): WorkerAction = action.copy(
        status = status,
        lastStatusChangeAt = LocalDateTime.now(clock),
        lastError = exception.stackTraceToString(),
    ).let {
        workerActionRepository.save(it)
    }

    fun saveActionAsProcessed(action: WorkerAction) =
        action.copy(
            status = WorkerActionStatus.PROCESSED,
            lastStatusChangeAt = LocalDateTime.now(clock),
        ).let {
            workerActionRepository.save(it)
            workerActionMetricService.recordProcessedTimeForAction(it)
        }
}
