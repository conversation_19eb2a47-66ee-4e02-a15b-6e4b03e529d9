package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.oms.model.OutboxItem
import com.hellofresh.oms.model.OutboxItemStatus
import com.hellofresh.oms.model.OutboxItemStatus.FAILED
import com.hellofresh.oms.model.OutboxItemStatus.SENT
import com.hellofresh.oms.model.WorkerAction
import com.hellofresh.oms.model.WorkerActionData.SendPurchaseOrder
import com.hellofresh.oms.model.WorkerActionType
import com.hellofresh.purchaseordersynchronizer.PurchaseOrderRepository
import com.hellofresh.purchaseordersynchronizer.PurchaseOrderVersionAndType
import com.hellofresh.purchaseordersynchronizer.client.tapioca.TapiocaClient
import com.hellofresh.purchaseordersynchronizer.client.tapioca.domain.SendPurchaseOrderRequest
import com.hellofresh.purchaseordersynchronizer.outbox.OutboxRepository
import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import java.time.Clock
import java.time.LocalDateTime
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class SendOrderService(
    private val workerActionMetricService: WorkerActionMetricService,
    private val workerActionRepository: WorkerActionRepository,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val tapiocaClient: TapiocaClient,
    private val outboxRepository: OutboxRepository,
    clock: Clock,
) : WorkerActionService(workerActionMetricService, workerActionRepository, clock) {
    fun processPendingTasks() =
        workerActionRepository
            .getPendingByType(WorkerActionType.SEND_ORDER)
            .map {
                val poNumber = (it.getPayload() as SendPurchaseOrder).purchaseOrderNumber
                // !! was added since SendWorkerActionService creates the WorkerAction only if the PO exists
                Pair(it, purchaseOrderRepository.findFirstByPoNumberOrderByVersionDesc(poNumber)!!)
            }
            .forEach { (action: WorkerAction, po: PurchaseOrderVersionAndType) ->
                processSendOrderAction(action, po)
            }

    @Suppress("TooGenericExceptionCaught")
    private fun processSendOrderAction(
        action: WorkerAction,
        po: PurchaseOrderVersionAndType
    ) {
        try {
            workerActionMetricService.recordTimeInQueueForAction(action)
            handleSendWorkerAction(action, po)
            saveActionAsProcessed(action)
            persistOutboxItemFor(po, action, SENT)
        } catch (e: Throwable) {
            logger.error(
                "Error processing SEND_ORDER action. [actionId={}, purchaseOrder={}]",
                action.id,
                po.poNumber,
                e
            )
            workerActionMetricService.recordFailedAction(action)
            saveActionWithException(action, e)
            persistOutboxItemFor(po, action, FAILED)
        }
    }

    private fun persistOutboxItemFor(
        po: PurchaseOrderVersionAndType,
        action: WorkerAction,
        status: OutboxItemStatus,
    ) {
        outboxRepository.save(
            OutboxItem.createItem(
                poId = po.id,
                poNumber = po.poNumber,
                now = LocalDateTime.now(),
                userEmail = action.userEmail,
                userId = action.userId,
                version = po.version,
                status = status,
            ),
        )
    }

    private fun handleSendWorkerAction(
        workerAction: WorkerAction,
        purchaseOrder: PurchaseOrderVersionAndType,
    ) {
        logger.info(
            "Sending PurchaseOrder to Tapioca. [orderNumber={}, orderId={}]",
            purchaseOrder.poNumber,
            purchaseOrder.id
        )
        tapiocaClient.sendPurchaseOrder(
            SendPurchaseOrderRequest(
                purchaseOrderId = purchaseOrder.id,
                senderId = workerAction.userId,
            ),
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
