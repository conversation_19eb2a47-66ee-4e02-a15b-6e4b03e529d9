package com.hellofresh.purchaseordersynchronizer.workerAction.service

import com.hellofresh.purchaseordersynchronizer.workerAction.out.WorkerActionRepository
import java.time.LocalDateTime
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class CleanupService(
    private val workerActionRepository: WorkerActionRepository,
    @Value("\${scheduler.cleanup.max-retention-days}") private val maxRetentionDays: Long,
) {
    fun cleanup() =
        workerActionRepository.deleteByCreatedAtBefore(LocalDateTime.now().minusDays(maxRetentionDays)).let {
            if (it > 0) {
                logger.info("Deleted $it WorkerActions older than $maxRetentionDays days")
            } else {
                logger.info("No WorkerActions older than $maxRetentionDays days to delete")
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
