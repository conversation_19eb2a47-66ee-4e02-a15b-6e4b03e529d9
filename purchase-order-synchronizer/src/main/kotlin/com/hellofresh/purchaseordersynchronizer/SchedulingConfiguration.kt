package com.hellofresh.purchaseordersynchronizer

import java.util.TimeZone
import javax.sql.DataSource
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider.Configuration.Builder
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.scheduling.annotation.EnableScheduling

@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT5M")
@Configuration
class SchedulingConfiguration {

    @Bean
    fun lockProvider(dataSource: DataSource) = JdbcTemplateLockProvider(
        Builder()
            .withJdbcTemplate(JdbcTemplate(dataSource))
            .withTimeZone(TimeZone.getTimeZone("UTC"))
            .build(),
    )
}
