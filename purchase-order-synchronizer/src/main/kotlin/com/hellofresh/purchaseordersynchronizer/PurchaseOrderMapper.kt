package com.hellofresh.purchaseordersynchronizer

import com.hellofresh.oms.model.OrderItem
import com.hellofresh.oms.model.PackagingType
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.ShipMethodEnum
import com.hellofresh.oms.model.UOM as OmsUom
import com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.model.CreatePurchaseOrderTapioca
import com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.model.CreatePurchaseOrderTapiocaItem
import com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.model.OrderUnit
import com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.model.TapiocaShippingMethod
import com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.model.TapiocaUom
import com.hellofresh.oms.util.Calculator
import java.math.BigDecimal

fun PurchaseOrder.mapToCreatePurchaseOrderTapioca() = CreatePurchaseOrderTapioca(
    orderNumber = this.poNumber,
    orderId = this.id,
    supplierCode = this.supplierCode,
    distributionCenterCode = this.dcCode,
    expectedArrivalTime = this.expectedStartTime,
    expectedArrivalEndTime = this.expectedEndTime,
    createdBy = this.userId,
    week = this.yearWeek.value,
    shippingMethod = this.shippingMethod.toTapiocaShippingMethod(),
    items = this.orderItems.map { it.mapToCreatePurchaseOrderTapiocaItemsUsingTotalAmounts() },
    ignoreBuffer = true,
    emergencyReasonUuid = this.emergencyReasonUuid ?: throw IllegalArgumentException(
        "EmergencyReasonUuid can not be null"
    ),
    comments = comment,
    isRevision = this.version != 1,
    deliveryDateReasonUuid = this.deliveryDateChangeReasonId,
    supplierUuid = this.supplierId
)

private fun ShipMethodEnum.toTapiocaShippingMethod(): TapiocaShippingMethod = when (this) {
    ShipMethodEnum.VENDOR -> TapiocaShippingMethod.VENDOR_DELIVERED
    ShipMethodEnum.CROSSDOCK -> TapiocaShippingMethod.CROSSDOCK
    ShipMethodEnum.FREIGHT_ON_BOARD -> TapiocaShippingMethod.FREIGHT_ON_BOARD
    ShipMethodEnum.OTHER -> TapiocaShippingMethod.OTHERS
}

private fun OrderItem.mapToCreatePurchaseOrderTapiocaItemsUsingTotalAmounts() = CreatePurchaseOrderTapiocaItem(
    productSkuId = this.skuId,
    qty = Calculator.calculateTotalCaseQuantity(this.totalQty, this.packaging),
    agreedPrice = this.price.toTapioca(),
    bufferPercentage = this.buffer.toPercent(),
    packingSize = this.packaging.caseSize ?: BigDecimal.ZERO,
    orderUnit = this.packaging.packagingType.toTapiocaPackagingType(),
    packingUnit = this.packaging.unitOfMeasure.toTapiocaUnitOfMeasure(),
    reasonUuid = this.changeReasonId,
    palletSize = this.casesPerPallet
)

private fun PackagingType.toTapiocaPackagingType(): OrderUnit = when (this) {
    PackagingType.UNIT_TYPE -> OrderUnit.UNIT_TYPE
    PackagingType.CASE_TYPE -> OrderUnit.CASE_TYPE
    PackagingType.PALLET_TYPE -> OrderUnit.CASE_TYPE
}

private fun OmsUom.toTapiocaUnitOfMeasure() = when (this) {
    OmsUom.UNIT -> TapiocaUom.UNIT
    OmsUom.KG -> TapiocaUom.KG
    OmsUom.LBS -> TapiocaUom.LBS
    OmsUom.GAL -> TapiocaUom.GAL
    OmsUom.L -> TapiocaUom.L
    OmsUom.OZ -> TapiocaUom.OZ
}
