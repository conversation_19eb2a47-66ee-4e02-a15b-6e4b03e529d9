package com.hellofresh.purchaseordersynchronizer.client.tapioca.config

import io.netty.channel.ChannelOption
import io.netty.handler.logging.LogLevel
import java.util.function.Supplier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.client.reactive.ReactorClientHttpConnector
import org.springframework.security.oauth2.client.AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.InMemoryReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.registration.ClientRegistration
import org.springframework.security.oauth2.client.registration.InMemoryReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction
import org.springframework.security.oauth2.core.AuthorizationGrantType
import org.springframework.web.reactive.function.client.WebClient
import reactor.netty.channel.MicrometerChannelMetricsRecorder
import reactor.netty.http.client.HttpClient
import reactor.netty.resources.ConnectionProvider
import reactor.netty.transport.logging.AdvancedByteBufFormat

@Configuration
class WebClientConfiguration(
    @Value("\${tapioca.base-url}/") val baseUrl: String,
) {

    @Bean("tapiocaWebClient")
    fun tapiocaWebClient(
        webClientBuilder: WebClient.Builder,
        clientRegistrationRepository: ReactiveClientRegistrationRepository,
        webClientSettings: WebClientSettings,
    ): WebClient = webClientBuilder
        .baseUrl(baseUrl)
        .clientConnector(
            ReactorClientHttpConnector(
                getHttpClient(webClientSettings).metrics(
                    true,
                    Supplier { MicrometerChannelMetricsRecorder("tapioca_channel", "create_po") }
                ),
            ),
        ).filter(oauth2Filter(clientRegistrationRepository))
        .build()

    @Bean
    fun clientRegistrationRepository(
        @Value("\${auth-service.base-url}") authServiceBaseUrl: String,
        @Value("\${auth-service.client-id}") clientId: String,
        @Value("\${auth-service.client-secret}") clientSecret: String
    ): ReactiveClientRegistrationRepository = InMemoryReactiveClientRegistrationRepository(
        ClientRegistration.withRegistrationId("auth-service")
            .tokenUri("$authServiceBaseUrl/token")
            .clientId(clientId)
            .clientSecret(clientSecret)
            .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
            .build(),
    )

    private fun getHttpClient(setting: WebClientSettings): HttpClient {
        val provider = ConnectionProvider.builder("tapioca-webclient-pool")
            .maxIdleTime(setting.maxIdleTime)
            .maxLifeTime(setting.maxLifeTime)
            .pendingAcquireTimeout(setting.pendingAcquireTimeout)
            .evictInBackground(setting.evictInBackground)
            .metrics(true)
            .build()

        return HttpClient.create(provider)
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, setting.connectionTimeout.toMillis().toInt())
            .responseTimeout(setting.responseTimeout)
            .wiretap("reactor.netty", LogLevel.DEBUG, AdvancedByteBufFormat.SIMPLE)
    }

    private fun oauth2Filter(
        clientRegistrationRepository: ReactiveClientRegistrationRepository
    ): ServerOAuth2AuthorizedClientExchangeFilterFunction {
        val authorizedClientManager = AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager(
            clientRegistrationRepository,
            InMemoryReactiveOAuth2AuthorizedClientService(clientRegistrationRepository),
        )

        return ServerOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager).also {
            it.setDefaultClientRegistrationId("auth-service")
        }
    }
}
