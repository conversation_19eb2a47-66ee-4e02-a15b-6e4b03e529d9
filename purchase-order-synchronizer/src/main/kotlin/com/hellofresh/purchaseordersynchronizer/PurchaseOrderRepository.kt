package com.hellofresh.purchaseordersynchronizer

import com.hellofresh.oms.model.POType
import com.hellofresh.oms.model.PurchaseOrder
import com.hellofresh.oms.model.PurchaseOrderStatus
import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface PurchaseOrderRepository : JpaRepository<PurchaseOrder, UUID> {
    fun findFirstByPoNumberOrderByVersionDesc(poNumber: String): PurchaseOrderVersionAndType?
}

data class PurchaseOrderVersionAndType(
    val version: Int,
    val type: POType,
    val id: UUID,
    val poNumber: String,
    val status: PurchaseOrderStatus,
)
