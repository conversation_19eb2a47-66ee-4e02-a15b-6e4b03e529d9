import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.kotlin.jpa)
    id("jacoco")
    id("com.google.cloud.tools.jib")
    alias(libs.plugins.openapi.generator)
}

group = "$group.purchase-order-synchronizer"

extra["springCloudVersion"] = libs.versions.springCloud.get()

dependencies {
    implementation(project(":model"))

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.security:spring-security-oauth2-client")
    implementation("org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j")
    implementation("io.zipkin.reporter2:zipkin-reporter-brave")
    implementation(libs.shedlock.spring)
    implementation(libs.shedlock.provider.jdbc.template)
    implementation("org.jetbrains.kotlin:kotlin-reflect")

    // validation
    implementation("org.springframework.boot:spring-boot-starter-validation")

    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("com.fasterxml.jackson.core:jackson-annotations")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    // tracing
    implementation("io.micrometer:micrometer-tracing-bridge-otel")
    implementation("io.micrometer:micrometer-registry-prometheus")
    implementation("io.opentelemetry:opentelemetry-exporter-zipkin")
    implementation(libs.datasource.micrometer.spring.boot)

    // Logging
    implementation("org.springframework.boot:spring-boot-starter-log4j2")
    implementation("org.apache.logging.log4j:log4j-layout-template-json")
    implementation("org.springframework.boot:spring-boot-properties-migrator")

    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("org.flywaydb:flyway-database-postgresql")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    // test
    testImplementation(kotlin("test"))
    testImplementation(project(":db-migration"))
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.cloud:spring-cloud-starter-contract-stub-runner")
    testImplementation("org.awaitility:awaitility")
    testImplementation(libs.squareup.okhttp3.mockwebserver)
    testImplementation(libs.squareup.okhttp3.okhttp)
    testImplementation(libs.mockito.kotlin)
    testImplementation("org.flywaydb:flyway-core")
    testImplementation(libs.zonky.embedded.database)
    testRuntimeOnly(libs.zonky.embedded.postgres)
}

dependencyManagement {
    imports {
        mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
    }
}

configurations {
    implementation {
        exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
    }
    testImplementation {
        exclude(group = "ch.qos.logback", module = "logback-classic")
    }
}

// generated sources of openapi generate task
val generatedSourcesDir = "${project.layout.buildDirectory.get().asFile}/generated/openapi"
sourceSets.getByName("main").java {
    srcDir("$generatedSourcesDir/src/main/kotlin")
}
sourceSets.getByName("main").resources {
    srcDir("../logging/resources")
}

tasks {
    compileKotlin {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_21
        }
    }
    compileTestKotlin {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_21
        }
    }

    test {
        useJUnitPlatform {
            excludeTags("integration")
        }
    }
    openApiGenerate.configure {
        this.generatorName.set("kotlin-spring")
        this.inputSpec.set("$projectDir/src/main/resources/static/tapioca-api.yaml")
        this.skipValidateSpec.set(true)
        this.outputs.cacheIf { false }
        this.outputs.upToDateWhen { false }
        this.outputDir.set(generatedSourcesDir)
        this.apiPackage.set("com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.api")
        this.modelPackage.set("com.hellofresh.oms.purchaseordersynchronizer.tapioca.generated.model")
        this.configOptions.put("interfaceOnly", "true")
        this.configOptions.put("enumPropertyNaming", "UPPERCASE")
        this.configOptions.put("documentationProvider", "none")
        this.configOptions.put("useSpringBoot3", "true")
        this.typeMappings.put("double", "java.math.BigDecimal")
        this.typeMappings.put("java.time.OffsetDateTime", "java.time.LocalDateTime")
        this.additionalProperties.put("useTags", true)
    }
    compileKotlin.configure {
        dependsOn("openApiGenerate")
    }

    test.configure {
        systemProperty("spring.profiles.active", "test")
    }
}

task<Test>("integrationTest") {
    description = "Runs integration tests."
    group = "verification"
    shouldRunAfter(tasks.test)
    useJUnitPlatform {
        includeTags("integration")
    }
}

jib {
    from {
        image = project.property("jib.from.image").toString()
    }
    to {
        image = "order-management-http:latest"
    }
    container {
        project.findProperty("jib.container.jvmFlags")?.toString()?.split(' ')?.let {
            jvmFlags = it
        }
    }
}

buildscript {
    configurations.all {
        resolutionStrategy {
            // Workaround for https://github.com/OpenAPITools/openapi-generator/issues/15876
            force("org.yaml:snakeyaml:2.4")
            // Workaround for https://github.com/OpenAPITools/openapi-generator/issues/18753
            force("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.2")
        }
    }
}
