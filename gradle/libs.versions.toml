[versions]
kotlin = "2.2.0"
springBoot = "3.4.5"
springCloud = "2024.0.0"
detekt = "1.23.8"
jackson = "2.19.1"
jjwt = "0.12.6"
shedlock = "5.16.0"
testcontainers = "1.18.3"
okhttp3 = "4.12.0"
jte = "3.2.1"
openPdf = "2.2.4"
flying-saucer = "9.13.0"
proto-google-common = "2.54.1"

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-jpa = { id = "org.jetbrains.kotlin.plugin.jpa", version.ref = "kotlin" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }

avro = { id = "com.github.davidmc24.gradle.plugin.avro", version = "1.9.1" }
detekt-gradle = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
gradle-git-properties = { id = "com.gorylenko.gradle-git-properties", version = "2.5.0" }
jacocolog = { id = "org.barfuin.gradle.jacocolog", version = "3.1.0" }
openapi-generator = { id = "org.openapi.generator", version = "7.14.0" }
sonarqube = { id = "org.sonarqube", version = "5.1.0.4882" }
spring-boot = { id = "org.springframework.boot", version.ref = "springBoot" }
spring-dependency-management = { id = "io.spring.dependency-management", version = "1.1.7" }

[libraries]
# API
springdoc-openapi = { module = "org.springdoc:springdoc-openapi-starter-webmvc-ui", version = "2.8.9" }
jjwt-gson = { module = "io.jsonwebtoken:jjwt-gson", version.ref = "jjwt" }
jjwt-impl = { module = "io.jsonwebtoken:jjwt-impl", version.ref = "jjwt" }
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind", version.ref = "jackson" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jackson" }
kotlin-csv = { module = "com.jsoizo:kotlin-csv-jvm", version = "1.10.0" }

# PDF
jte = { module = "gg.jte:jte", version.ref = "jte" }
jte-spring-boot-starter = { module = "gg.jte:jte-spring-boot-starter-3", version.ref = "jte" }
jte-kotlin = { module = "gg.jte:jte-kotlin", version.ref = "jte" }
openpdf = { module = "com.github.librepdf:openpdf", version.ref = "openPdf" }
flying-saucer-core = { module = "org.xhtmlrenderer:flying-saucer-core", version.ref = "flying-saucer" }
flying-saucer-pdf = { module = "org.xhtmlrenderer:flying-saucer-pdf", version.ref = "flying-saucer" }

# Database
shedlock-spring = { module = "net.javacrumbs.shedlock:shedlock-spring", version.ref = "shedlock" }
shedlock-provider-jdbc-template = { module = "net.javacrumbs.shedlock:shedlock-provider-jdbc-template", version.ref = "shedlock" }
datasource-micrometer-spring-boot = { module = "net.ttddyy.observation:datasource-micrometer-spring-boot", version = "1.1.2" }
hibernate-core = { module = "org.hibernate:hibernate-core", version = "6.6.20.Final" }
spring-data-commons = { module = "org.springframework.data:spring-data-commons", version = "3.5.1" }
postgresql = { module = "org.postgresql:postgresql", version = "42.7.7" }

# Kafka, Avro and Proto
avro = { module = "org.apache.avro:avro", version = "1.12.0" }
kafka-streams-avro-serde = { module = "io.confluent:kafka-streams-avro-serde", version = "7.9.2" }
hf-schema-registry = { module = "com.hellofresh:schema-registry", version = "0.1.2688" }

# Mail
sendgrid = { module = "com.sendgrid:sendgrid-java", version = "4.10.3"}

apache-commons-lang3 = { module = "org.apache.commons:commons-lang3", version = "3.17.0" }

# Testing
junit-jupiter-params = { module = "org.junit.jupiter:junit-jupiter-params", version = "5.13.3" }
mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version = "4.1.0" }
hamcrest = { module = "org.hamcrest:hamcrest-library", version = "2.2" }
zonky-embedded-database = { module = "io.zonky.test:embedded-database-spring-test", version = "2.6.0" }
zonky-embedded-postgres = { module = "io.zonky.test:embedded-postgres", version = "2.1.0" }
model-assert = { module = "uk.org.webcompere:model-assert", version = "1.1.0" }
statsig-serversdk = { module = "com.statsig:serversdk", version = "1.36.4" }
squareup-okhttp3-okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp3" }
squareup-okhttp3-mockwebserver = { module = "com.squareup.okhttp3:mockwebserver", version.ref = "okhttp3" }
jsoup = { module = "org.jsoup:jsoup", version = "1.21.1" }

vladmihalcea-hibernate-types = { module = "com.vladmihalcea:hibernate-types-60", version = "2.21.1" }
